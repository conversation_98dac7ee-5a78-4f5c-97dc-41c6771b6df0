import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Info } from 'lucide-react-native';
import { BaseCard } from '@/components/base/BaseCard';
import Colors from '@/constants/colors';

interface DiagnosisSummaryProps {
  diagnosis: {
    thickness?: string;
    density?: string;
    tone?: string;
    reflect?: string;
    porosity?: string;
    damage?: string;
    grayPercentage?: number;
    level?: number;
  };
}

export const DiagnosisSummary: React.FC<DiagnosisSummaryProps> = ({ diagnosis }) => {
  // Función helper para formatear valores
  const formatValue = (value: unknown): string => {
    if (value === undefined || value === null || value === '') return 'No especificado';
    return String(value);
  };

  // Función para obtener color según el valor de daño
  const getDamageColor = (damage = '') => {
    switch (damage.toLowerCase()) {
      case 'severo':
        return Colors.light.error;
      case 'moderado':
        return Colors.light.warning;
      case 'leve':
        return Colors.light.success;
      case 'ninguno':
        return Colors.light.success;
      default:
        return Colors.light.gray;
    }
  };

  // Función para obtener color según la porosidad
  const getPorosityColor = (porosity = '') => {
    switch (porosity.toLowerCase()) {
      case 'alta':
        return Colors.light.warning;
      case 'media':
        return Colors.light.primary;
      case 'baja':
        return Colors.light.success;
      default:
        return Colors.light.gray;
    }
  };

  return (
    <BaseCard style={styles.container} variant="beige" shadow="sm">
      <View style={styles.header}>
        <Info size={16} color={Colors.light.primary} />
        <Text style={styles.title}>Resumen del Diagnóstico Actual</Text>
      </View>

      <View style={styles.content}>
        {/* Primera fila */}
        <View style={styles.row}>
          <View style={styles.item}>
            <Text style={styles.label}>Nivel</Text>
            <Text style={styles.value}>{formatValue(diagnosis.level || diagnosis.tone)}</Text>
          </View>
          <View style={styles.item}>
            <Text style={styles.label}>Reflejo</Text>
            <Text style={styles.value}>{formatValue(diagnosis.reflect)}</Text>
          </View>
          <View style={styles.item}>
            <Text style={styles.label}>Grosor</Text>
            <Text style={styles.value}>{formatValue(diagnosis.thickness)}</Text>
          </View>
        </View>

        {/* Segunda fila */}
        <View style={styles.row}>
          <View style={styles.item}>
            <Text style={styles.label}>Densidad</Text>
            <Text style={styles.value}>{formatValue(diagnosis.density)}</Text>
          </View>
          <View style={styles.item}>
            <Text style={styles.label}>Porosidad</Text>
            <Text style={[styles.value, { color: getPorosityColor(diagnosis.porosity) }]}>
              {formatValue(diagnosis.porosity)}
            </Text>
          </View>
          <View style={styles.item}>
            <Text style={styles.label}>Daño</Text>
            <Text style={[styles.value, { color: getDamageColor(diagnosis.damage) }]}>
              {formatValue(diagnosis.damage)}
            </Text>
          </View>
        </View>

        {/* Canas si existen */}
        {diagnosis.grayPercentage !== undefined && diagnosis.grayPercentage > 0 && (
          <View style={styles.graySection}>
            <Text style={styles.grayLabel}>Canas:</Text>
            <Text style={styles.grayValue}>{diagnosis.grayPercentage}%</Text>
          </View>
        )}
      </View>

      <Text style={styles.disclaimer}>
        Este resumen será utilizado para evaluar la viabilidad del color deseado
      </Text>
    </BaseCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  content: {
    gap: 12,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  item: {
    flex: 1,
    alignItems: 'center',
  },
  label: {
    fontSize: 11,
    color: Colors.light.gray,
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  value: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  graySection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 6,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    marginTop: 4,
  },
  grayLabel: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  grayValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  disclaimer: {
    fontSize: 11,
    color: Colors.light.gray,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 12,
  },
});
