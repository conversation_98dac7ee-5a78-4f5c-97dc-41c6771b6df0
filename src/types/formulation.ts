/**
 * Represents a specific product mix in a formulation.
 * @example { productId: 'wella-koleston-7-1', productName: 'Koleston Perfect 7/1', quantity: 60, unit: 'gr' }
 */
export interface ProductMix {
  productId: string; // Unique product ID, to link with inventory
  productName: string; // Human-readable product name
  quantity: number;
  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';
}

/**
 * Describes a specific application technique.
 */
export interface ApplicationTechnique {
  name: string; // E.g., "Global Application", "Root Touch-up", "Freehand Balayage"
  description: string; // Detailed description of how to perform the technique
}

/**
 * Represents a complete step within the coloring process.
 * Can be a mix and application, a wash, a treatment, etc.
 */
export interface FormulationStep {
  stepNumber: number; // Step order (1, 2, 3...)
  stepTitle: string; // Clear step title, e.g., "1. Preparation and Root Application"
  mix?: ProductMix[]; // Product mix for this step (optional, can be a step without mix)
  technique?: ApplicationTechnique; // Technique to use (optional)
  instructions: string; // Detailed instructions and tips for this step
  processingTime?: number; // Processing time in minutes for this step (optional)
}

/**
 * The main object representing the complete and structured formula.
 * This is the JSON format that the AI should return.
 */
export interface Formulation {
  formulaTitle: string; // General title, e.g., "Intense Ash Blonde on Natural Base"
  summary: string; // A summary of the colorist's overall strategy
  steps: FormulationStep[]; // An array with all steps to follow
  totalTime: number; // Total estimated time in minutes for the entire service
  warnings?: string[]; // Important warnings or recommended strand tests
}

// Existing interfaces maintained for compatibility
export interface ViabilityAnalysis {
  score: 'safe' | 'caution' | 'risky';
  factors: {
    levelDifference: number;
    hairHealth: 'good' | 'fair' | 'poor';
    chemicalHistory: string[];
    estimatedSessions: number; // unified: equals sessionPlan.recommended
  };
  warnings: string[];
  recommendations: string[];
  // Unified session plan used across all screens
  sessionPlan?: { min: number; recommended: number; max: number; reasons: string[] };
}

export interface FormulaCost {
  items: {
    product: string;
    amount: string;
    unitCost: number;
    totalCost: number;
  }[];
  totalMaterialCost: number;
  suggestedServicePrice: number;
  profitMargin: number;
  hasAllRealCosts?: boolean; // true only if ALL products have real prices from inventory
}

export interface BrandConversion {
  fromBrand: string;
  toBrand: string;
  conversions: {
    originalShade: string;
    equivalentShade: string;
    accuracy: 'exact' | 'close' | 'approximate';
  }[];
}

export interface PreviousFormula {
  id: string;
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
}

export interface ColorFormula {
  id?: string;
  brand: string;
  line: string;
  colors: {
    tone: string; // Changed from shade to tone for consistency
    amount: number; // Changed to number
  }[];
  developerVolume: number;
  developerRatio: string;
  additives: string[];
  processingTime: number;
  technique?: string;
  formulaText?: string;
}
