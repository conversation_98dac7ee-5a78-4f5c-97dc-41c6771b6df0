import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface WelcomeScreenProps {
  starters: string[];
  onSuggestionSelect: (suggestion: string) => void;
}

const getContextualQuestion = () => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 12) {
    return '¿En qué puedo ayudarte esta mañana?';
  } else if (hour >= 12 && hour < 19) {
    return '¿En qué puedo ayudarte esta tarde?';
  } else {
    return '¿En qué puedo ayudarte esta noche?';
  }
};

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ starters, onSuggestionSelect }) => {
  const question = getContextualQuestion();

  return (
    <View style={styles.welcomeContainer}>
      <View style={styles.welcomeContent}>
        <View style={styles.welcomeLogo}>
          <Text style={styles.welcomeLogoText}>S</Text>
        </View>
        <Text style={styles.welcomeQuestion}>{question}</Text>
        {starters.length > 0 && (
          <View style={styles.suggestionChips}>
            {starters.map((starter, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionChip}
                onPress={() => onSuggestionSelect(starter)}
              >
                <Text style={styles.suggestionChipText}>{starter}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  welcomeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing['2xl'],
    paddingVertical: BeautyMinimalTheme.spacing.xl,
  },
  welcomeContent: {
    alignItems: 'center',
    width: '100%',
    maxWidth: 480,
  },
  welcomeLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: BeautyMinimalTheme.spacing['2xl'],
    ...BeautyMinimalTheme.shadows.soft,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '30',
  },
  welcomeLogoText: {
    fontSize: 32,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.beautyColors.salonier[500],
    textAlign: 'center',
  },
  welcomeQuestion: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.charcoal,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing['2xl'],
    lineHeight:
      BeautyMinimalTheme.typography.sizes.title * BeautyMinimalTheme.typography.lineHeights.normal,
    letterSpacing: -0.2,
  },
  suggestionChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    marginTop: BeautyMinimalTheme.spacing.md,
  },
  suggestionChip: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: BeautyMinimalTheme.radius.xl,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.whisper + '60',
    ...BeautyMinimalTheme.shadows.subtle,
  },
  suggestionChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.slate,
    letterSpacing: 0.1,
  },
});

export default React.memo(WelcomeScreen);
