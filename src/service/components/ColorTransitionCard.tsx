import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ChevronRight } from 'lucide-react-native';
import { BeautyCard } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import Colors from '@/constants/colors';
import { hairColorMap } from '@/constants/hair-colors';
import { getToneNameFromLevel } from '@/utils/professional-colorimetry';

// Copied from FormulationStep.tsx
const FALLBACK_COLORS = {
  primary: '#5c3825',
  secondary: '#d4a574',
};

interface AnalysisResult {
  overallTone?: string;
  averageLevel?: number;
  averageDepthLevel?: number;
  zones?: {
    [zoneName: string]: {
      tone?: string;
      level?: number;
      [key: string]: unknown;
    };
  };
  [key: string]: unknown;
}

interface DesiredResult {
  general: {
    overallLevel?: string;
    overallTone?: string;
    technique?: string;
  };
  [key: string]: unknown;
}

const getHairColorFromAnalysis = (analysisResult: AnalysisResult): string | null => {
  if (!analysisResult) return null;
  const level = analysisResult.averageLevel || analysisResult.averageDepthLevel || 5;
  const tone = analysisResult.overallTone;
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }
  const specificToneName = getToneNameFromLevel(level);
  if (specificToneName && hairColorMap[specificToneName]) {
    return hairColorMap[specificToneName];
  }
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }
  return null;
};

const getHairColorFromDesired = (desiredResult: DesiredResult): string | null => {
  if (!desiredResult?.general) return null;
  const level = desiredResult.general.overallLevel || 8;
  const tone = desiredResult.general.overallTone;
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }
  const specificToneName = getToneNameFromLevel(level);
  if (specificToneName && hairColorMap[specificToneName]) {
    return hairColorMap[specificToneName];
  }
  if (tone && hairColorMap[tone]) {
    return hairColorMap[tone];
  }
  return null;
};

interface ColorTransitionCardProps {
  analysisResult?: AnalysisResult;
  desiredAnalysisResult?: DesiredResult;
}

export const ColorTransitionCard: React.FC<ColorTransitionCardProps> = ({
  analysisResult,
  desiredAnalysisResult,
}) => {
  if (!analysisResult || !desiredAnalysisResult) {
    return null;
  }

  return (
    <BeautyCard variant="default" style={styles.colorTransitionCard}>
      <View style={styles.colorTransitionHeader}>
        <Text style={styles.colorTransitionTitle}>Transformación de Color</Text>
      </View>
      <View style={styles.colorTransitionContent}>
        <View style={styles.colorBox}>
          <View
            style={[
              styles.colorSample,
              {
                backgroundColor:
                  getHairColorFromAnalysis(analysisResult) || FALLBACK_COLORS.primary,
              },
            ]}
          />
          <Text style={styles.colorLevel}>
            Nivel {analysisResult.averageLevel || analysisResult.averageDepthLevel || 5}
          </Text>
          <Text style={styles.colorTone}>{analysisResult.overallTone || 'Castaño'}</Text>
        </View>
        <ChevronRight size={24} color={Colors.light.primary} style={styles.arrowIcon} />
        <View style={styles.colorBox}>
          <View
            style={[
              styles.colorSample,
              {
                backgroundColor:
                  getHairColorFromDesired(desiredAnalysisResult) || FALLBACK_COLORS.secondary,
              },
            ]}
          />
          <Text style={styles.colorLevel}>
            Nivel {desiredAnalysisResult.general.overallLevel || 8}
          </Text>
          <Text style={styles.colorTone}>
            {desiredAnalysisResult.general.overallTone || 'Rubio'}
          </Text>
        </View>
      </View>
      {desiredAnalysisResult.general.technique && (
        <View style={styles.techniqueIndicator}>
          <Text style={styles.techniqueLabel}>
            Técnica: {desiredAnalysisResult.general.technique}
          </Text>
        </View>
      )}
    </BeautyCard>
  );
};

const styles = StyleSheet.create({
  colorTransitionCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  colorTransitionHeader: {
    marginBottom: 16,
  },
  colorTransitionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  colorTransitionContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  colorBox: {
    alignItems: 'center',
  },
  colorSample: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
    borderWidth: 3,
    borderColor: Colors.light.border,
  },
  colorLevel: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  colorTone: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  arrowIcon: {
    marginHorizontal: 20,
  },
  techniqueIndicator: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  techniqueLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    textAlign: 'center',
  },
});
