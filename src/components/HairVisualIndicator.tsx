import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import Colors from '@/constants/colors';
import { hairColorMap } from '@/constants/hair-colors';

type IndicatorType = 'thickness' | 'density' | 'color' | 'state' | 'none';

interface HairVisualIndicatorProps {
  type: IndicatorType;
  value: string;
  size?: 'small' | 'medium';
}

export default function HairVisualIndicator({
  type,
  value,
  size = 'small',
}: HairVisualIndicatorProps) {
  const renderThicknessIndicator = () => {
    let lineHeight = 1;
    switch (value) {
      case 'Fino':
        lineHeight = 1;
        break;
      case 'Medio':
        lineHeight = 2;
        break;
      case 'Grueso':
        lineHeight = 3;
        break;
      default:
        return null;
    }

    return (
      <View
        style={[
          styles.thicknessLine,
          { height: lineHeight },
          size === 'medium' && styles.mediumThicknessWidth,
        ]}
      />
    );
  };

  const renderDensityIndicator = () => {
    let bars = 3;
    let spacing = 3;

    switch (value) {
      case 'Baja':
        bars = 3;
        spacing = 3;
        break;
      case 'Media':
        bars = 4;
        spacing = 2;
        break;
      case 'Alta':
        bars = 5;
        spacing = 1;
        break;
      default:
        return null;
    }

    const getBarStyle = (index: number) => [
      styles.densityBar,
      index < bars - 1 && { marginRight: spacing },
    ];

    return (
      <View style={styles.densityContainer}>
        {Array.from({ length: bars }).map((_, i) => (
          <View key={i} style={getBarStyle(i)} />
        ))}
      </View>
    );
  };

  const renderColorIndicator = (colorHex?: string) => {
    const color = colorHex || hairColorMap[value] || Colors.light.gray;

    return (
      <View
        style={[
          styles.colorCircle,
          { backgroundColor: color },
          size === 'medium' && styles.colorCircleMedium,
        ]}
      />
    );
  };

  const renderStateIndicator = () => {
    let symbol = '●';

    switch (value) {
      case 'Natural':
        symbol = '●';
        break;
      case 'Teñido':
        symbol = '◐';
        break;
      case 'Decolorado':
        symbol = '○';
        break;
      case 'Con mechas':
        symbol = '◑';
        break;
      case 'Permanente':
        symbol = '∿';
        break;
      case 'Alisado':
        symbol = '═';
        break;
      case 'Mixto':
        symbol = '◉';
        break;
      default:
        symbol = '●';
    }

    return (
      <Text style={[styles.stateSymbol, size === 'medium' && styles.stateSymbolMedium]}>
        {symbol}
      </Text>
    );
  };

  switch (type) {
    case 'thickness':
      return renderThicknessIndicator();
    case 'density':
      return renderDensityIndicator();
    case 'color':
      return renderColorIndicator();
    case 'state':
      return renderStateIndicator();
    default:
      return null;
  }
}

const styles = StyleSheet.create({
  thicknessLine: {
    width: 16,
    backgroundColor: Colors.light.text,
    borderRadius: 2,
  },
  densityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 12,
  },
  densityBar: {
    width: 1.5,
    height: 10,
    backgroundColor: Colors.light.text,
    borderRadius: 0.5,
  },
  colorCircle: {
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 0.5,
    borderColor: Colors.light.border,
  },
  colorCircleMedium: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  stateSymbol: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 16,
  },
  stateSymbolMedium: {
    fontSize: 16,
    lineHeight: 18,
  },
  mediumThicknessWidth: {
    width: 20,
  },
});
