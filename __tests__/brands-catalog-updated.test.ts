/**
 * Tests para verificar que el catálogo actualizado funciona correctamente
 * Actualizado después de las mejoras del catálogo de marcas
 */

import {
  getBrandsWithFormulableLines,
  getColorLinesByBrandId,
  searchFormulableBrands,
  getBrandById,
  validateBrandLines,
  professionalHairColorBrands,
  getBrandLinesStats,
} from '../src/constants/brands-data';

describe('Catálogo de Marcas Actualizado', () => {
  describe('Estadísticas Generales', () => {
    test('debe tener al menos 75 marcas formulables', () => {
      const formulableBrands = getBrandsWithFormulableLines();
      expect(formulableBrands.length).toBeGreaterThanOrEqual(75);
    });

    test('debe tener al menos 150 líneas de coloración', () => {
      const stats = getBrandLinesStats();
      expect(stats.colorLines).toBeGreaterThanOrEqual(150);
    });

    test('debe tener menos de 250 errores críticos', () => {
      const validation = validateBrandLines();
      expect(validation.issues.length).toBeLessThan(250);
    });
  });

  describe('Marcas Principales del Listado', () => {
    const expectedBrands = [
      { id: 'wella', name: 'Wella Professionals', expectedLines: 4 },
      { id: 'schwarzkopf', name: 'Schwarzkopf Professional', expectedLines: 6 },
      { id: 'goldwell', name: 'Goldwell', expectedLines: 4 },
      { id: 'loreal', name: "L'Oréal Professionnel", expectedLines: 4 },
      { id: 'alfaparf', name: 'Alfaparf Milano', expectedLines: 4 },
      { id: 'salerm', name: 'Salerm Cosmetics', expectedLines: 3 },
      { id: 'lendan', name: 'Lendan', expectedLines: 3 },
      { id: 'montibello', name: 'Montibello', expectedLines: 3 },
      { id: 'redken', name: 'Redken', expectedLines: 6 },
      { id: 'matrix', name: 'Matrix', expectedLines: 5 },
      { id: 'joico', name: 'Joico', expectedLines: 5 },
      { id: 'paul-mitchell', name: 'Paul Mitchell', expectedLines: 5 },
      { id: 'pravana', name: 'Pravana', expectedLines: 5 },
      { id: 'kenra', name: 'Kenra Professional', expectedLines: 3 },
      { id: 'j-beverly-hills', name: 'J Beverly Hills', expectedLines: 2 },
      { id: 'guy-tang-mydentity', name: 'Guy Tang #Mydentity', expectedLines: 4 },
    ];

    test.each(expectedBrands)('$name debe estar en marcas formulables', ({ id, name }) => {
      const formulableBrands = getBrandsWithFormulableLines();
      const brand = formulableBrands.find(b => b.id === id);

      expect(brand).toBeDefined();
      expect(brand?.name).toBe(name);
    });

    test.each(expectedBrands)(
      '$name debe tener al menos $expectedLines líneas de coloración',
      ({ id, expectedLines }) => {
        const colorLines = getColorLinesByBrandId(id);
        expect(colorLines.length).toBeGreaterThanOrEqual(expectedLines);
      }
    );
  });

  describe('Líneas Específicas del Listado', () => {
    test("L'Oréal debe tener las líneas específicas", () => {
      const colorLines = getColorLinesByBrandId('loreal');
      const lineNames = colorLines.map(line => line.name);

      expect(lineNames).toContain('Majirel');
      expect(lineNames).toContain('iNOA');
      expect(lineNames).toContain('Dia Light');
      expect(lineNames).toContain('Dia Color');
    });

    test('Schwarzkopf debe tener las líneas IGORA', () => {
      const colorLines = getColorLinesByBrandId('schwarzkopf');
      const lineNames = colorLines.map(line => line.name);

      expect(lineNames).toContain('IGORA ROYAL');
      expect(lineNames).toContain('IGORA ROYAL Absolutes');
      expect(lineNames).toContain('IGORA ROYAL Highlifts');
      expect(lineNames).toContain('IGORA ZERO AMM');
      expect(lineNames).toContain('IGORA VIBRANCE');
      expect(lineNames).toContain('BLONDME Colour');
    });

    test('Wella debe tener las líneas con tecnología ME+', () => {
      const colorLines = getColorLinesByBrandId('wella');
      const lineNames = colorLines.map(line => line.name);

      expect(lineNames).toContain('Koleston Perfect');
      expect(lineNames).toContain('Illumina Color');
      expect(lineNames).toContain('Color Touch');
      expect(lineNames).toContain('Shinefinity');
    });

    test('Paul Mitchell debe tener líneas veganas', () => {
      const colorLines = getColorLinesByBrandId('paul-mitchell');
      const lineNames = colorLines.map(line => line.name);

      expect(lineNames).toContain('The Color XG');
      expect(lineNames).toContain('CoverSmart (Color XG)');
      expect(lineNames).toContain('THE COLOR 10');
      expect(lineNames).toContain('The Demi');
      expect(lineNames).toContain('Crema XG');
    });

    test('Guy Tang #Mydentity debe existir como nueva marca', () => {
      const brand = getBrandById('guy-tang-mydentity');
      expect(brand).toBeDefined();
      expect(brand?.name).toBe('Guy Tang #Mydentity');

      const colorLines = getColorLinesByBrandId('guy-tang-mydentity');
      expect(colorLines.length).toBe(4);
    });
  });

  describe('Funcionalidad de Búsqueda', () => {
    test('debe encontrar marcas por nombre parcial', () => {
      const results = searchFormulableBrands('wella');
      expect(results.length).toBeGreaterThan(0);
      expect(results.some(b => b.name.toLowerCase().includes('wella'))).toBe(true);
    });

    test('debe encontrar marcas por país', () => {
      const results = searchFormulableBrands('spain');
      expect(results.length).toBeGreaterThan(0);
      expect(results.some(b => b.country.toLowerCase() === 'spain')).toBe(true);
    });

    test('debe manejar búsquedas vacías', () => {
      const results = searchFormulableBrands('');
      expect(results.length).toBeGreaterThanOrEqual(75);
    });
  });

  describe('Validación de Datos', () => {
    test('todas las líneas formulables deben tener categoría', () => {
      const formulableBrands = getBrandsWithFormulableLines();

      formulableBrands.forEach(brand => {
        brand.lines.forEach(line => {
          if (line.isColorLine) {
            expect(line.category).toBeDefined();
            expect(line.category).not.toBe('');
          }
        });
      });
    });

    test('todas las líneas formulables deben tener isColorLine: true', () => {
      const formulableBrands = getBrandsWithFormulableLines();

      formulableBrands.forEach(brand => {
        const colorLines = brand.lines.filter(line => line.isColorLine === true);
        expect(colorLines.length).toBeGreaterThan(0);
      });
    });

    test('categorías deben ser válidas', () => {
      const validCategories = [
        'hair-color',
        'bleaching',
        'treatment',
        'developer',
        'styling',
        'other',
      ];

      professionalHairColorBrands.forEach(brand => {
        brand.lines.forEach(line => {
          if (line.category) {
            expect(validCategories).toContain(line.category);
          }
        });
      });
    });
  });

  describe('Distribución Geográfica', () => {
    test('debe tener marcas de múltiples países', () => {
      const formulableBrands = getBrandsWithFormulableLines();
      const countries = [...new Set(formulableBrands.map(b => b.country))];

      expect(countries.length).toBeGreaterThan(10);
      expect(countries).toContain('Spain');
      expect(countries).toContain('Germany');
      expect(countries).toContain('USA');
      expect(countries).toContain('France');
      expect(countries).toContain('Italy');
    });

    test('España debe tener al menos 5 marcas formulables', () => {
      const formulableBrands = getBrandsWithFormulableLines();
      const spanishBrands = formulableBrands.filter(b => b.country === 'Spain');

      expect(spanishBrands.length).toBeGreaterThanOrEqual(5);
    });
  });
});
