import { StyleSheet } from 'react-native';
import Colors from '@/constants/colors';

export const instructionStyles = StyleSheet.create({
  screenContent: {
    flex: 1,
    paddingBottom: 20,
  },

  // Checklist Screen Styles
  checklistHeader: {
    padding: 30,
    alignItems: 'center',
  },
  checklistHeaderTitle: {
    fontSize: 18,
    color: Colors.light.textLight,
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: Colors.light.backgroundOpacity30,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    color: Colors.light.textLight,
    fontSize: 14,
    marginTop: 10,
  },
  checklistContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  checkItemInfo: {
    flex: 1,
  },
  checkItemName: {
    fontSize: 15,
    color: Colors.light.text,
    fontWeight: '500',
  },
  checkItemZone: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  typeIndicator: {
    width: 8,
    height: 40,
    borderRadius: 4,
    marginLeft: 12,
  },
  productGroup: {
    marginBottom: 20,
  },
  productGroupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  productGroupIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  productGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    flex: 1,
  },
  animatedCheckmark: {
    // Container for animated checkmark - no additional styling needed
  },

  // Mixing Screen Styles
  mixingHeader: {
    alignItems: 'center',
    padding: 30,
  },
  mixingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  mixingAnimation: {
    alignItems: 'center',
    marginBottom: 30,
  },
  bowlContainer: {
    alignItems: 'center',
  },
  bowl: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: Colors.light.surface,
    borderWidth: 3,
    borderColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  mixture: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  mixtureGradient: {
    flex: 1,
  },
  bowlLabel: {
    marginTop: 15,
    fontSize: 14,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  mixingBrush: {
    position: 'absolute',
    top: '40%',
    left: '40%',
  },
  colorSwirl: {
    position: 'absolute',
  },
  colorSwirlLarge: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primaryLight,
    opacity: 0.5,
    top: '20%',
    left: '10%',
  },
  colorSwirlSmall: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.accent,
    opacity: 0.7,
    bottom: '25%',
    right: '15%',
  },
  stepsContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  mixStep: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 10,
    position: 'relative',
  },
  mixStepActive: {
    backgroundColor: Colors.light.primaryTransparent5,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  stepIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepIconActive: {
    backgroundColor: Colors.light.primary,
  },
  stepEmoji: {
    fontSize: 20,
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.gray,
  },
  stepTextActive: {
    color: Colors.light.text,
    fontWeight: '500',
  },
  stepCheck: {
    position: 'absolute',
    right: 15,
  },
  mixtureInfo: {
    backgroundColor: Colors.light.surface,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
  },
  mixtureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 10,
  },
  mixtureDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
});
