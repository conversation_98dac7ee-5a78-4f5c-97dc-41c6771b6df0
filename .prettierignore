# Dependencies
node_modules/

# Build outputs
dist/
build/
.expo/
.expo-shared/
web-build/

# Generated files
*.tgz
*.tar.gz

# Coverage directory
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional npm cache directory
.npm

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache
.cache/

# Temporary folders
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Metro
.metro-health-check*

# TypeScript cache
*.tsbuildinfo

# Babel cache
.babel-cache/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Compiled output
*.d.ts
*.js.map

# Documentation
CHANGELOG.md

# Lint-staged exclusions (keep in sync with ESLint ignores)
scripts/
supabase/functions/
archive/
**/*.legacy.*
**/*.backup*
**/*.config.js