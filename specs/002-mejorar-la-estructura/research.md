# Research: File Structure Best Practices for React Native/Expo Projects

## Research Questions

Since the Technical Context was fully specified, the main research areas focus on:

1. **React Native project organization best practices**
2. **Expo-specific folder structure recommendations**
3. **Multi-platform asset organization (iOS, Android, Web)**
4. **Zustand store organization patterns**
5. **Supabase Edge Functions project structure**
6. **Offline-first architecture file organization**

## Research Findings

### 1. React Native Project Organization

**Decision**: Feature-based organization with clear separation of concerns
**Rationale**:

- Scales better than file-type organization as projects grow
- Makes it easier to locate related functionality
- Supports code splitting and lazy loading
- Aligns with React Native community best practices

**Structure Pattern**:

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI primitives (buttons, inputs)
│   └── forms/          # Form-specific components
├── screens/            # Screen-level components
├── navigation/         # Navigation configuration
├── services/           # API calls, business logic
├── stores/             # Zustand state management
├── utils/              # Pure utility functions
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
└── constants/          # App constants and configuration
```

**Alternatives considered**:

- File-type organization (components/, screens/, utils/ at root)
- Domain-driven organization (auth/, inventory/, etc.)

### 2. Expo-Specific Considerations

**Decision**: Follow Expo CLI conventions with custom organization
**Rationale**:

- Expo expects certain files in specific locations
- Custom organization can coexist with Expo requirements
- Supports Expo Router file-based routing if needed

**Key Requirements**:

- `app.json` at root for Expo configuration
- `assets/` folder for app icons and splash screens
- Platform-specific code in organized structure

### 3. Multi-Platform Asset Organization

**Decision**: Platform-agnostic with platform-specific overrides
**Rationale**:

- Reduces duplication for shared assets
- Allows platform-specific optimizations when needed
- Follows React Native asset resolution patterns

**Structure**:

```
assets/
├── images/
│   ├── common/         # Shared across platforms
│   ├── ios/           # iOS-specific assets
│   ├── android/       # Android-specific assets
│   └── web/           # Web-specific assets
├── fonts/
└── icons/
```

### 4. Zustand Store Organization

**Decision**: Feature-based stores with clear naming
**Rationale**:

- Each domain gets its own store
- Prevents monolithic state management
- Easier to test and maintain
- Supports offline-first patterns

**Pattern**:

```
stores/
├── auth-store.ts
├── inventory-store.ts
├── service-store.ts
├── salon-config-store.ts
└── index.ts            # Store exports
```

### 5. Supabase Edge Functions Structure

**Decision**: Separate backend organization with clear API boundaries
**Rationale**:

- Edge Functions are separate deployment units
- Each function should be independently deployable
- Clear separation from client code

**Structure**:

```
supabase/
├── functions/
│   ├── analyze-hair/
│   ├── generate-formula/
│   └── chat-assistant/
├── migrations/
└── config.toml
```

### 6. Offline-First Architecture Support

**Decision**: Clear separation of online/offline logic
**Rationale**:

- Offline-first requires careful state management
- Clear boundaries between local and remote operations
- Support for sync queues and conflict resolution

**Key Patterns**:

- Local state in Zustand stores
- Sync services for online/offline coordination
- Queue management for pending operations

## Configuration and Tool Organization

### Development Tools

```
.vscode/               # VS Code settings
.specify/              # Specification tools
docs/                  # Project documentation
scripts/               # Build and utility scripts
```

### Configuration Files

```
├── package.json       # Dependencies and scripts
├── tsconfig.json      # TypeScript configuration
├── babel.config.js    # Babel configuration
├── metro.config.js    # Metro bundler configuration
├── app.json           # Expo configuration
└── .env.example       # Environment variables template
```

## Testing Structure

**Decision**: Mirror main code structure in tests
**Rationale**:

- Easy to locate tests for specific modules
- Supports both unit and integration testing
- Clear separation of test types

**Structure**:

```
__tests__/
├── components/
├── screens/
├── services/
├── stores/
├── utils/
└── integration/
```

## Final Recommendations

1. **Adopt feature-based organization** for main source code
2. **Maintain Expo conventions** for configuration and assets
3. **Separate backend (Supabase) from frontend** code
4. **Use clear naming conventions** for files and folders
5. **Keep documentation close to code** where relevant
6. **Support offline-first patterns** in organization
7. **Plan for team growth** with clear module boundaries
