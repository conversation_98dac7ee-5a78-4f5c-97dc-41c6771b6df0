/**
 * FORMULA GENERATION LOADING COMPONENT
 *
 * Professional chemical mixing animation for formula generation
 * Shows test tubes, chemical reactions, and color mixing
 * Integrates with professional beauty theme
 */

import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  withDelay,
  interpolate,
  interpolateColor,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { TestTube2, Beaker, Palette, CheckCircle } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { MicroInteractions } from '@/constants/micro-interactions';
import { ColorConstants } from '@/styles/colors';

export type FormulaGenerationStage =
  | 'analyzing-hair'
  | 'calculating-ratios'
  | 'mixing-chemicals'
  | 'validating-safety'
  | 'optimizing-formula'
  | 'complete';

interface FormulaGenerationLoadingProps {
  stage: FormulaGenerationStage;
  progress?: number; // 0-100
  estimatedTime?: number; // seconds
  onStageComplete?: (stage: FormulaGenerationStage) => void;
  showChemicalDetails?: boolean;
}

const STAGE_CONFIG = {
  'analyzing-hair': {
    message: 'Analizando características capilares...',
    icon: TestTube2,
    color: BeautyMinimalTheme.beautyColors.sage[500],
    duration: 2000,
    chemicals: ['H2O2', 'NH3', 'PPD'],
  },
  'calculating-ratios': {
    message: 'Calculando proporciones químicas...',
    icon: Beaker,
    color: BeautyMinimalTheme.beautyColors.amethyst[600],
    duration: 1800,
    chemicals: ['20 vol', '30 vol', '40 vol'],
  },
  'mixing-chemicals': {
    message: 'Mezclando componentes activos...',
    icon: Palette,
    color: BeautyMinimalTheme.beautyColors.rose[500],
    duration: 2500,
    chemicals: ['Base', 'Toner', 'Developer'],
  },
  'validating-safety': {
    message: 'Validando compatibilidad química...',
    icon: TestTube2,
    color: BeautyMinimalTheme.beautyColors.sage[600],
    duration: 1500,
    chemicals: ['pH', 'Safety', 'Allergy'],
  },
  'optimizing-formula': {
    message: 'Optimizando resultado final...',
    icon: Beaker,
    color: BeautyMinimalTheme.beautyColors.amethyst[500],
    duration: 1200,
    chemicals: ['Color', 'Time', 'Result'],
  },
  complete: {
    message: 'Fórmula generada exitosamente',
    icon: CheckCircle,
    color: BeautyMinimalTheme.beautyColors.mint,
    duration: 1000,
    chemicals: ['Ready'],
  },
} as const;

// Professional hair color palette for chemical visualization
const CHEMICAL_COLORS = {
  developer: BeautyMinimalTheme.neutrals.silver,
  ammonia: ColorConstants.HAIR_AMMONIA,
  base: ColorConstants.HAIR_BASE,
  toner: ColorConstants.HAIR_TONER,
  bleach: ColorConstants.HAIR_BLEACH,
  dye: ColorConstants.HAIR_DYE,
} as const;

/**
 * CHEMICAL MIXING ANIMATION
 * Shows test tubes with bubbling chemical reactions
 */
const ChemicalMixingAnimation: React.FC<{
  stage: FormulaGenerationStage;
  size: number;
}> = ({ stage, size }) => {
  const bubble1 = useSharedValue(0);
  const bubble2 = useSharedValue(0);
  const bubble3 = useSharedValue(0);
  const liquidLevel = useSharedValue(0);
  const mixingRotation = useSharedValue(0);
  const steamEffect = useSharedValue(0);

  const stageConfig = STAGE_CONFIG[stage];

  useEffect(() => {
    // Reset animations when stage changes
    bubble1.value = 0;
    bubble2.value = 0;
    bubble3.value = 0;
    liquidLevel.value = 0;
    mixingRotation.value = 0;
    steamEffect.value = 0;

    if (stage === 'analyzing-hair') {
      // Gentle bubbling for analysis
      bubble1.value = withRepeat(
        withSequence(withTiming(1, { duration: 1000 }), withTiming(0, { duration: 300 })),
        -1,
        false
      );
    } else if (stage === 'calculating-ratios') {
      // Precise liquid measuring
      liquidLevel.value = withTiming(0.7, {
        duration: stageConfig.duration,
        easing: Easing.out(Easing.quad),
      });
    } else if (stage === 'mixing-chemicals') {
      // Active mixing with multiple bubbles
      bubble1.value = withRepeat(
        withSequence(withTiming(1, { duration: 600 }), withTiming(0, { duration: 200 })),
        -1,
        false
      );

      bubble2.value = withRepeat(
        withDelay(
          200,
          withSequence(withTiming(1, { duration: 800 }), withTiming(0, { duration: 300 }))
        ),
        -1,
        false
      );

      bubble3.value = withRepeat(
        withDelay(
          400,
          withSequence(withTiming(1, { duration: 700 }), withTiming(0, { duration: 250 }))
        ),
        -1,
        false
      );

      mixingRotation.value = withRepeat(
        withTiming(360, {
          duration: MicroInteractions.rotations.medium,
          easing: Easing.linear,
        }),
        -1,
        false
      );
    } else if (stage === 'validating-safety') {
      // Safety validation with careful testing
      steamEffect.value = withRepeat(
        withSequence(withTiming(1, { duration: 1200 }), withTiming(0, { duration: 800 })),
        -1,
        false
      );
    } else if (stage === 'optimizing-formula') {
      // Final optimization
      liquidLevel.value = withTiming(0.9, { duration: 1000 });
      steamEffect.value = withRepeat(withSpring(0.8, MicroInteractions.springs.gentle), -1, true);
    } else if (stage === 'complete') {
      // Success animation
      bubble1.value = withSequence(
        withSpring(1.5, MicroInteractions.springs.bouncy),
        withSpring(0, MicroInteractions.springs.tight)
      );
      liquidLevel.value = withSpring(1, MicroInteractions.springs.bouncy);
    }
  }, [
    stage,
    stageConfig.duration,
    bubble1,
    bubble2,
    bubble3,
    liquidLevel,
    mixingRotation,
    steamEffect,
  ]);

  const bubbleStyle1 = useAnimatedStyle(() => ({
    opacity: bubble1.value,
    transform: [
      { scale: interpolate(bubble1.value, [0, 1], [0.3, 1.2]) },
      { translateY: interpolate(bubble1.value, [0, 1], [0, -15]) },
    ],
  }));

  const bubbleStyle2 = useAnimatedStyle(() => ({
    opacity: bubble2.value,
    transform: [
      { scale: interpolate(bubble2.value, [0, 1], [0.3, 1.2]) },
      { translateY: interpolate(bubble2.value, [0, 1], [0, -15]) },
    ],
  }));

  const bubbleStyle3 = useAnimatedStyle(() => ({
    opacity: bubble3.value,
    transform: [
      { scale: interpolate(bubble3.value, [0, 1], [0.3, 1.2]) },
      { translateY: interpolate(bubble3.value, [0, 1], [0, -15]) },
    ],
  }));

  const liquidStyle = useAnimatedStyle(() => ({
    height: `${interpolate(liquidLevel.value, [0, 1], [10, 80])}%`,
    backgroundColor: interpolateColor(
      liquidLevel.value,
      [0, 0.3, 0.7, 1],
      [CHEMICAL_COLORS.developer, CHEMICAL_COLORS.ammonia, CHEMICAL_COLORS.base, stageConfig.color]
    ),
  }));

  const mixingStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${mixingRotation.value}deg` }],
  }));

  const steamStyle = useAnimatedStyle(() => ({
    opacity: steamEffect.value,
    transform: [
      { scale: interpolate(steamEffect.value, [0, 1], [0.8, 1.3]) },
      { translateY: interpolate(steamEffect.value, [0, 1], [0, -10]) },
    ],
  }));

  const IconComponent = stageConfig.icon;

  return (
    <View style={[styles.chemicalContainer, { width: size, height: size }]}>
      {/* Test tube container */}
      <View style={styles.testTube}>
        {/* Liquid level */}
        <Animated.View style={[styles.liquid, liquidStyle]} />

        {/* Chemical bubbles */}
        <Animated.View style={[styles.chemicalBubble, styles.bubble1Position, bubbleStyle1]} />
        <Animated.View style={[styles.chemicalBubble, styles.bubble2Position, bubbleStyle2]} />
        <Animated.View style={[styles.chemicalBubble, styles.bubble3Position, bubbleStyle3]} />

        {/* Steam effect */}
        {(stage === 'validating-safety' || stage === 'optimizing-formula') && (
          <>
            <Animated.View style={[styles.steam, styles.steam1Position, steamStyle]} />
            <Animated.View style={[styles.steam, styles.steam2Position, steamStyle]} />
          </>
        )}
      </View>

      {/* Center icon with mixing rotation */}
      <Animated.View style={[styles.iconContainer, mixingStyle]}>
        <IconComponent size={size * 0.25} color={stageConfig.color} />
      </Animated.View>
    </View>
  );
};

/**
 * CHEMICAL FORMULA DISPLAY
 * Shows the chemical components being processed
 */
const ChemicalFormula: React.FC<{
  stage: FormulaGenerationStage;
}> = ({ stage }) => {
  const fadeIn = useSharedValue(0);
  const stageConfig = STAGE_CONFIG[stage];

  useEffect(() => {
    fadeIn.value = 0;
    fadeIn.value = withDelay(300, withTiming(1, { duration: 800 }));
  }, [stage, fadeIn]);

  const formulaStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ translateY: interpolate(fadeIn.value, [0, 1], [10, 0]) }],
  }));

  return (
    <Animated.View style={[styles.formulaContainer, formulaStyle]}>
      <Text style={styles.formulaTitle}>Componentes:</Text>
      <View style={styles.chemicalList}>
        {stageConfig.chemicals.map((chemical, _index) => (
          <View key={chemical} style={styles.chemicalItem}>
            <View style={[styles.chemicalIndicator, { backgroundColor: stageConfig.color }]} />
            <Text style={styles.chemicalName}>{chemical}</Text>
          </View>
        ))}
      </View>
    </Animated.View>
  );
};

/**
 * MAIN FORMULA GENERATION LOADING COMPONENT
 */
export const FormulaGenerationLoading: React.FC<FormulaGenerationLoadingProps> = ({
  stage,
  progress,
  estimatedTime,
  onStageComplete,
  showChemicalDetails = true,
}) => {
  const fadeIn = useSharedValue(0);
  const stageConfig = STAGE_CONFIG[stage];
  const hapticTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Fade in animation
    fadeIn.value = withSpring(1, MicroInteractions.springs.gentle);

    // Haptic feedback on stage change
    if (stage === 'mixing-chemicals') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } else if (stage === 'complete') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Set up stage completion timeout
    if (onStageComplete && stage !== 'complete') {
      hapticTimeoutRef.current = setTimeout(() => {
        onStageComplete(stage);
      }, stageConfig.duration);
    }

    return () => {
      if (hapticTimeoutRef.current) {
        clearTimeout(hapticTimeoutRef.current);
      }
    };
  }, [stage, onStageComplete, stageConfig.duration, fadeIn]);

  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ scale: fadeIn.value }],
  }));

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      {/* Main chemical animation */}
      <ChemicalMixingAnimation stage={stage} size={100} />

      {/* Stage message */}
      <Text style={styles.stageMessage}>{stageConfig.message}</Text>

      {/* Progress information */}
      {typeof progress === 'number' && (
        <Text style={styles.progressText}>{Math.round(progress)}% completado</Text>
      )}

      {/* Estimated time */}
      {estimatedTime && <Text style={styles.timeText}>Tiempo estimado: {estimatedTime}s</Text>}

      {/* Chemical details */}
      {showChemicalDetails && <ChemicalFormula stage={stage} />}

      {/* Professional subtitle */}
      <Text style={styles.subtitle}>⚗️ Formulación química profesional con IA</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.xl,
  },

  // Chemical mixing animation
  chemicalContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  testTube: {
    width: 30,
    height: 70,
    borderWidth: 2,
    borderColor: BeautyMinimalTheme.neutrals.silver,
    borderRadius: 15,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    overflow: 'hidden',
    position: 'relative',
  },
  liquid: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderRadius: 13,
  },
  chemicalBubble: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
  },
  steam: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: BeautyMinimalTheme.neutrals.mist,
  },
  iconContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    bottom: -10,
  },

  // Chemical bubble positions
  bubble1Position: {
    bottom: '30%',
    left: '25%',
  },
  bubble2Position: {
    bottom: '25%',
    right: '30%',
  },
  bubble3Position: {
    bottom: '35%',
    left: '50%',
  },

  // Steam positions
  steam1Position: {
    top: -5,
    left: '20%',
  },
  steam2Position: {
    top: -8,
    right: '25%',
  },

  // Chemical formula display
  formulaContainer: {
    marginTop: BeautyMinimalTheme.spacing.md,
    alignItems: 'center',
  },
  formulaTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  chemicalList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  chemicalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    gap: BeautyMinimalTheme.spacing.xs / 2,
  },
  chemicalIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  chemicalName: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },

  // Text styles
  stageMessage: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  progressText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  timeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  subtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: BeautyMinimalTheme.spacing.sm,
  },
});

export default FormulaGenerationLoading;
