/**
 * Performance Benchmarking Suite for Salonier Transformation
 *
 * Comprehensive performance testing to measure response times,
 * memory usage, and system efficiency across all enhanced components.
 */

import { brandService } from '../src/services/brandService';
import { brandInventoryIntegration } from '../src/services/brandInventoryIntegration';
import { BrandContextEnhancer } from '../supabase/functions/salonier-assistant/services/brand-context-enhancer';
import { EnhancedPrompts } from '../supabase/functions/salonier-assistant/utils/enhanced-prompts';

export class PerformanceBenchmarkSuite {
  private results: BenchmarkResult[] = [];
  private startTime = 0;

  /**
   * Run comprehensive performance benchmark
   */
  async runComprehensiveBenchmark(): Promise<BenchmarkReport> {
    console.log('🚀 Starting Comprehensive Performance Benchmark...\n');

    this.startTime = Date.now();
    this.results = [];

    // Core Database Operations
    await this.benchmarkDatabaseOperations();

    // Brand Intelligence Performance
    await this.benchmarkBrandIntelligence();

    // AI Enhancement Performance
    await this.benchmarkAIEnhancements();

    // Integration Layer Performance
    await this.benchmarkIntegrationLayer();

    // Memory and Resource Usage
    await this.benchmarkResourceUsage();

    // Concurrent Load Testing
    await this.benchmarkConcurrentLoad();

    const totalTime = Date.now() - this.startTime;

    return this.generateReport(totalTime);
  }

  private async benchmarkDatabaseOperations(): Promise<void> {
    console.log('🗄️  Benchmarking Database Operations...');

    // Single brand lookup
    await this.measureOperation(
      'db_single_brand_lookup',
      async () => {
        return await brandService.getBrandByName('Wella Professionals');
      },
      { target: 200, category: 'database' }
    );

    // All brands fetch
    await this.measureOperation(
      'db_all_brands_fetch',
      async () => {
        return await brandService.getAllBrands();
      },
      { target: 500, category: 'database' }
    );

    // Product lines for brand
    await this.measureOperation(
      'db_product_lines_fetch',
      async () => {
        const brand = await brandService.getBrandByName('Wella Professionals');
        return await brandService.getLinesByBrandId(brand!.id);
      },
      { target: 300, category: 'database' }
    );

    // Fuzzy brand search
    await this.measureOperation(
      'db_fuzzy_brand_search',
      async () => {
        return await brandService.searchBrands('Well');
      },
      { target: 250, category: 'database' }
    );

    // Multiple brand lookups
    await this.measureOperation(
      'db_multiple_brand_lookups',
      async () => {
        const brands = ['Wella Professionals', "L'Oréal Professionnel", 'Matrix'];
        const results = [];
        for (const brandName of brands) {
          results.push(await brandService.getBrandByName(brandName));
        }
        return results;
      },
      { target: 400, category: 'database' }
    );

    console.log('   ✅ Database operations benchmarked\n');
  }

  private async benchmarkBrandIntelligence(): Promise<void> {
    console.log('🧠 Benchmarking Brand Intelligence...');

    // Single product context generation
    await this.measureOperation(
      'brand_single_context',
      async () => {
        return await BrandContextEnhancer.generateBrandContext(['Wella Koleston Perfect 7.0']);
      },
      { target: 150, category: 'brand_intelligence' }
    );

    // Multiple products context generation
    await this.measureOperation(
      'brand_multiple_context',
      async () => {
        const products = [
          'Wella Koleston Perfect 7.0',
          "L'Oréal Majirel 6.35",
          'Schwarzkopf Igora Royal 8-00',
        ];
        return await BrandContextEnhancer.generateBrandContext(products);
      },
      { target: 300, category: 'brand_intelligence' }
    );

    // Brand detection accuracy
    await this.measureOperation(
      'brand_detection_speed',
      async () => {
        const mixedProducts = [
          'Wella Koleston Perfect 7.0',
          'Some Generic Product',
          "L'Oréal Majirel 6.35",
          'Unknown Brand Shade',
        ];
        return await BrandContextEnhancer.generateBrandContext(mixedProducts);
      },
      { target: 250, category: 'brand_intelligence' }
    );

    // Large product list processing
    await this.measureOperation(
      'brand_large_product_list',
      async () => {
        const largeList = Array(20)
          .fill(0)
          .map((_, i) => `Wella Product ${i + 1} Shade ${i % 10}`);
        return await BrandContextEnhancer.generateBrandContext(largeList);
      },
      { target: 500, category: 'brand_intelligence' }
    );

    console.log('   ✅ Brand intelligence benchmarked\n');
  }

  private async benchmarkAIEnhancements(): Promise<void> {
    console.log('📝 Benchmarking AI Enhancements...');

    const testDiagnosis = {
      natural_level: 6,
      desired_level: 8,
      porosity: 'medium',
      texture: 'medium',
    };

    const testContext = {
      detectedBrands: ['Wella Professionals'],
      recommendations: ['Use 20 vol developer', 'Apply heat for 30 min'],
      terminology: ['Wella /0 natural', 'Wella /1 ash'],
    };

    // Simple prompt generation
    await this.measureOperation(
      'ai_simple_prompt',
      async () => {
        return EnhancedPrompts.generateFormulaPrompt(
          {
            hairDiagnosis: testDiagnosis,
            brands: ['Wella Professionals'],
            context: testContext,
          },
          'simple'
        );
      },
      { target: 50, category: 'ai_enhancement' }
    );

    // Standard prompt generation
    await this.measureOperation(
      'ai_standard_prompt',
      async () => {
        return EnhancedPrompts.generateFormulaPrompt(
          {
            hairDiagnosis: testDiagnosis,
            brands: ['Wella Professionals'],
            context: testContext,
          },
          'standard'
        );
      },
      { target: 75, category: 'ai_enhancement' }
    );

    // Complex prompt generation
    await this.measureOperation(
      'ai_complex_prompt',
      async () => {
        return EnhancedPrompts.generateFormulaPrompt(
          {
            hairDiagnosis: testDiagnosis,
            brands: ['Wella Professionals', "L'Oréal Professionnel"],
            context: testContext,
          },
          'complex'
        );
      },
      { target: 100, category: 'ai_enhancement' }
    );

    // Prompt generation with large context
    await this.measureOperation(
      'ai_large_context_prompt',
      async () => {
        const largeContext = {
          detectedBrands: ['Wella Professionals', "L'Oréal Professionnel", 'Matrix'],
          recommendations: Array(10)
            .fill(0)
            .map((_, i) => `Recommendation ${i + 1}`),
          terminology: Array(15)
            .fill(0)
            .map((_, i) => `Term ${i + 1}`),
        };
        return EnhancedPrompts.generateFormulaPrompt(
          {
            hairDiagnosis: testDiagnosis,
            brands: largeContext.detectedBrands,
            context: largeContext,
          },
          'complex'
        );
      },
      { target: 150, category: 'ai_enhancement' }
    );

    console.log('   ✅ AI enhancements benchmarked\n');
  }

  private async benchmarkIntegrationLayer(): Promise<void> {
    console.log('🔗 Benchmarking Integration Layer...');

    // Product validation
    await this.measureOperation(
      'integration_product_validation',
      async () => {
        return await brandInventoryIntegration.validateProductBrand({
          brand: 'Wella',
          line: 'Koleston Perfect',
          type: 'Tinte',
          shade: '7.0',
        });
      },
      { target: 150, category: 'integration' }
    );

    // Brand autocomplete
    await this.measureOperation(
      'integration_brand_autocomplete',
      async () => {
        return await brandInventoryIntegration.getBrandAutocomplete('Well');
      },
      { target: 100, category: 'integration' }
    );

    // Line autocomplete
    await this.measureOperation(
      'integration_line_autocomplete',
      async () => {
        return await brandInventoryIntegration.getLineAutocomplete('Wella Professionals', 'Kol');
      },
      { target: 150, category: 'integration' }
    );

    // Multiple product validation
    await this.measureOperation(
      'integration_multiple_validation',
      async () => {
        const products = [
          { brand: 'Wella', line: 'Koleston', type: 'Tinte', shade: '7.0' },
          { brand: "L'Oreal", line: 'Majirel', type: 'Tinte', shade: '6.35' },
          { brand: 'Matrix', line: 'SoColor', type: 'Tinte', shade: '6N' },
        ];

        const results = [];
        for (const product of products) {
          results.push(await brandInventoryIntegration.validateProductBrand(product));
        }
        return results;
      },
      { target: 400, category: 'integration' }
    );

    console.log('   ✅ Integration layer benchmarked\n');
  }

  private async benchmarkResourceUsage(): Promise<void> {
    console.log('💾 Benchmarking Resource Usage...');

    const initialMemory = process.memoryUsage();

    // Memory usage during brand loading
    await this.measureOperation(
      'memory_brand_loading',
      async () => {
        const brands = await brandService.getAllBrands();
        const allLines = [];

        for (const brand of brands.slice(0, 20)) {
          const lines = await brandService.getLinesByBrandId(brand.id);
          allLines.push(...lines);
        }

        return { brands: brands.length, lines: allLines.length };
      },
      { target: 2000, category: 'resource_usage' }
    );

    const afterBrandMemory = process.memoryUsage();

    // Memory usage during context generation
    await this.measureOperation(
      'memory_context_generation',
      async () => {
        const contexts = [];
        for (let i = 0; i < 50; i++) {
          const context = await BrandContextEnhancer.generateBrandContext([
            `Test Product ${i} Shade ${i % 10}`,
          ]);
          contexts.push(context);
        }
        return contexts.length;
      },
      { target: 1000, category: 'resource_usage' }
    );

    const finalMemory = process.memoryUsage();

    // Calculate memory usage
    const brandLoadingMemory = afterBrandMemory.heapUsed - initialMemory.heapUsed;
    const contextGenerationMemory = finalMemory.heapUsed - afterBrandMemory.heapUsed;

    this.results.push({
      operation: 'memory_analysis',
      times: [0],
      avgTime: 0,
      minTime: 0,
      maxTime: 0,
      target: 0,
      passed: true,
      category: 'resource_usage',
      metadata: {
        initial_memory_mb: Math.round((initialMemory.heapUsed / 1024 / 1024) * 100) / 100,
        brand_loading_increase_mb: Math.round((brandLoadingMemory / 1024 / 1024) * 100) / 100,
        context_generation_increase_mb:
          Math.round((contextGenerationMemory / 1024 / 1024) * 100) / 100,
        total_memory_mb: Math.round((finalMemory.heapUsed / 1024 / 1024) * 100) / 100,
      },
    });

    console.log('   ✅ Resource usage benchmarked\n');
  }

  private async benchmarkConcurrentLoad(): Promise<void> {
    console.log('⚡ Benchmarking Concurrent Load...');

    // Concurrent brand lookups
    await this.measureOperation(
      'concurrent_brand_lookups',
      async () => {
        const brands = [
          'Wella Professionals',
          "L'Oréal Professionnel",
          'Matrix',
          'Redken',
          'Schwarzkopf Professional',
        ];
        const promises = brands.map(brand => brandService.getBrandByName(brand));
        return await Promise.all(promises);
      },
      { target: 500, category: 'concurrency' }
    );

    // Concurrent context generation
    await this.measureOperation(
      'concurrent_context_generation',
      async () => {
        const products = [
          ['Wella Koleston Perfect 7.0'],
          ["L'Oréal Majirel 6.35"],
          ['Matrix SoColor 6N'],
          ['Redken Shades EQ 09V'],
          ['Schwarzkopf Igora Royal 8-00'],
        ];

        const promises = products.map(productList =>
          BrandContextEnhancer.generateBrandContext(productList)
        );
        return await Promise.all(promises);
      },
      { target: 800, category: 'concurrency' }
    );

    // Concurrent validation
    await this.measureOperation(
      'concurrent_validation',
      async () => {
        const products = [
          { brand: 'Wella', line: 'Koleston', type: 'Tinte', shade: '7.0' },
          { brand: "L'Oreal", line: 'Majirel', type: 'Tinte', shade: '6.35' },
          { brand: 'Matrix', line: 'SoColor', type: 'Tinte', shade: '6N' },
          { brand: 'Redken', line: 'Shades EQ', type: 'Tinte', shade: '09V' },
          { brand: 'Schwarzkopf', line: 'Igora', type: 'Tinte', shade: '8-00' },
        ];

        const promises = products.map(product =>
          brandInventoryIntegration.validateProductBrand(product)
        );
        return await Promise.all(promises);
      },
      { target: 600, category: 'concurrency' }
    );

    console.log('   ✅ Concurrent load benchmarked\n');
  }

  private async measureOperation(
    name: string,
    operation: () => Promise<any>,
    config: { target: number; category: string; iterations?: number }
  ): Promise<void> {
    const iterations = config.iterations || 5;
    const times: number[] = [];

    console.log(`   ⏱️  ${name}...`);

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        await operation();
        const duration = Date.now() - startTime;
        times.push(duration);

        // Small delay between iterations to avoid overwhelming the system
        if (i < iterations - 1) {
          await new Promise(resolve => globalThis.setTimeout(resolve, 10));
        }
      } catch (error) {
        console.log(`     ❌ Iteration ${i + 1} failed: ${error.message}`);
        times.push(config.target * 2); // Penalty for failure
      }
    }

    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const passed = avgTime < config.target;

    this.results.push({
      operation: name,
      times,
      avgTime: Math.round(avgTime),
      minTime,
      maxTime,
      target: config.target,
      passed,
      category: config.category,
    });

    const status = passed ? '✅' : '❌';
    console.log(`     ${status} Avg: ${Math.round(avgTime)}ms (target: ${config.target}ms)`);
  }

  private generateReport(totalTime: number): BenchmarkReport {
    const categories = [...new Set(this.results.map(r => r.category))];
    const categoryResults: Record<string, CategorySummary> = {};

    categories.forEach(category => {
      const categoryTests = this.results.filter(r => r.category === category);
      const passed = categoryTests.filter(t => t.passed).length;
      const avgTime = categoryTests.reduce((sum, t) => sum + t.avgTime, 0) / categoryTests.length;

      categoryResults[category] = {
        total_tests: categoryTests.length,
        passed_tests: passed,
        pass_rate: Math.round((passed / categoryTests.length) * 100),
        avg_response_time: Math.round(avgTime),
        fastest_operation: categoryTests.reduce((min, t) => (t.avgTime < min.avgTime ? t : min)),
        slowest_operation: categoryTests.reduce((max, t) => (t.avgTime > max.avgTime ? t : max)),
      };
    });

    const totalPassed = this.results.filter(r => r.passed).length;
    const overallAvgTime =
      this.results.reduce((sum, r) => sum + r.avgTime, 0) / this.results.length;

    const report: BenchmarkReport = {
      timestamp: new Date().toISOString(),
      total_duration_ms: totalTime,
      summary: {
        total_operations: this.results.length,
        passed_operations: totalPassed,
        overall_pass_rate: Math.round((totalPassed / this.results.length) * 100),
        overall_avg_response_time: Math.round(overallAvgTime),
      },
      categories: categoryResults,
      detailed_results: this.results,
      recommendations: this.generatePerformanceRecommendations(),
    };

    this.printReport(report);
    return report;
  }

  private generatePerformanceRecommendations(): string[] {
    const recommendations: string[] = [];

    // Check for failed tests
    const failedTests = this.results.filter(r => !r.passed);
    if (failedTests.length > 0) {
      recommendations.push(`🚨 ${failedTests.length} operations failed performance targets`);

      const slowestFailed = failedTests.reduce((max, t) => (t.avgTime > max.avgTime ? t : max));
      recommendations.push(
        `⚡ Optimize ${slowestFailed.operation} (${slowestFailed.avgTime}ms vs ${slowestFailed.target}ms target)`
      );
    }

    // Check memory usage
    const memoryResult = this.results.find(r => r.operation === 'memory_analysis');
    if (memoryResult?.metadata?.total_memory_mb > 50) {
      recommendations.push(
        `💾 High memory usage detected (${memoryResult.metadata.total_memory_mb}MB)`
      );
      recommendations.push(`🔧 Consider implementing memory optimization strategies`);
    }

    // Check database performance
    const dbResults = this.results.filter(r => r.category === 'database');
    const slowDbOps = dbResults.filter(r => r.avgTime > r.target);
    if (slowDbOps.length > 0) {
      recommendations.push(`🗄️  Add database indexes for slow queries`);
    }

    // Check concurrency performance
    const concurrentResults = this.results.filter(r => r.category === 'concurrency');
    const slowConcurrentOps = concurrentResults.filter(r => r.avgTime > r.target);
    if (slowConcurrentOps.length > 0) {
      recommendations.push(`⚡ Implement connection pooling for concurrent operations`);
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ All performance targets met - system optimally configured');
    }

    return recommendations;
  }

  private printReport(report: BenchmarkReport): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 PERFORMANCE BENCHMARK REPORT');
    console.log('='.repeat(80));

    console.log(`\n⏱️  SUMMARY:`);
    console.log(`   Total Duration: ${report.total_duration_ms}ms`);
    console.log(`   Operations Tested: ${report.summary.total_operations}`);
    console.log(`   Performance Pass Rate: ${report.summary.overall_pass_rate}%`);
    console.log(`   Average Response Time: ${report.summary.overall_avg_response_time}ms`);

    console.log(`\n📈 CATEGORY BREAKDOWN:`);
    Object.entries(report.categories).forEach(([category, summary]) => {
      console.log(`   ${category.toUpperCase()}:`);
      console.log(
        `     Pass Rate: ${summary.pass_rate}% (${summary.passed_tests}/${summary.total_tests})`
      );
      console.log(`     Avg Time: ${summary.avg_response_time}ms`);
      console.log(
        `     Fastest: ${summary.fastest_operation.operation} (${summary.fastest_operation.avgTime}ms)`
      );
      console.log(
        `     Slowest: ${summary.slowest_operation.operation} (${summary.slowest_operation.avgTime}ms)`
      );
    });

    console.log(`\n🔍 DETAILED RESULTS:`);
    report.detailed_results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      const variance = result.maxTime - result.minTime;
      console.log(`   ${status} ${result.operation}`);
      console.log(`      Avg: ${result.avgTime}ms (target: ${result.target}ms)`);
      console.log(
        `      Range: ${result.minTime}ms - ${result.maxTime}ms (variance: ${variance}ms)`
      );
    });

    if (report.recommendations.length > 0) {
      console.log(`\n💡 RECOMMENDATIONS:`);
      report.recommendations.forEach(rec => {
        console.log(`   ${rec}`);
      });
    }

    console.log('\n' + '='.repeat(80));
  }
}

// Interfaces
interface BenchmarkResult {
  operation: string;
  times: number[];
  avgTime: number;
  minTime: number;
  maxTime: number;
  target: number;
  passed: boolean;
  category: string;
  metadata?: Record<string, any>;
}

interface CategorySummary {
  total_tests: number;
  passed_tests: number;
  pass_rate: number;
  avg_response_time: number;
  fastest_operation: BenchmarkResult;
  slowest_operation: BenchmarkResult;
}

interface BenchmarkReport {
  timestamp: string;
  total_duration_ms: number;
  summary: {
    total_operations: number;
    passed_operations: number;
    overall_pass_rate: number;
    overall_avg_response_time: number;
  };
  categories: Record<string, CategorySummary>;
  detailed_results: BenchmarkResult[];
  recommendations: string[];
}

// Export for CLI usage
export const runPerformanceBenchmark = async (): Promise<BenchmarkReport> => {
  const suite = new PerformanceBenchmarkSuite();
  return await suite.runComprehensiveBenchmark();
};
