import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Alert } from 'react-native';
import { Play, Pause, RotateCcw } from 'lucide-react-native';
import { TimelineBlock } from '@/types/visual-formulation';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import Colors from '@/constants/colors';

interface InteractiveTimelineProps {
  timeline: TimelineBlock[];
  totalTime: number;
}

export default function InteractiveTimeline({ timeline, totalTime }: InteractiveTimelineProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [activeBlocks, setActiveBlocks] = useState<string[]>([]);
  const animatedProgress = useRef(new Animated.Value(0)).current;
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [_elapsedSeconds, _setElapsedSeconds] = useState(0);

  const updateActiveBlocks = useCallback(() => {
    const active = timeline
      .filter(block => currentTime >= block.startTime && currentTime < block.endTime)
      .map(block => block.id);
    setActiveBlocks(active);
  }, [timeline, currentTime]);

  const animateProgress = useCallback(() => {
    Animated.timing(animatedProgress, {
      toValue: currentTime / totalTime,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [animatedProgress, currentTime, totalTime]);

  useEffect(() => {
    updateActiveBlocks();
    animateProgress();
  }, [updateActiveBlocks, animateProgress]);

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const startTimer = () => {
    if (currentTime >= totalTime) {
      setCurrentTime(0);
    }

    setIsRunning(true);
    intervalRef.current = setInterval(() => {
      _setElapsedSeconds(prev => prev + 1);
      setCurrentTime(prev => {
        const newTime = prev + 1 / 60; // Convert seconds to minutes
        if (newTime >= totalTime) {
          stopTimer();
          Alert.alert('Proceso Completado', 'El tiempo de procesamiento ha finalizado');
          return totalTime;
        }
        // Check for zone changes and alert
        const prevActiveBlocks = timeline
          .filter(block => prev >= block.startTime && prev < block.endTime)
          .map(block => block.id);
        const newActiveBlocks = timeline
          .filter(block => newTime >= block.startTime && newTime < block.endTime)
          .map(block => block.id);

        if (
          JSON.stringify(prevActiveBlocks) !== JSON.stringify(newActiveBlocks) &&
          newActiveBlocks.length > 0
        ) {
          const newBlock = timeline.find(b => b.id === newActiveBlocks[0]);
          if (newBlock) {
            Alert.alert('Cambio de Zona', `Es momento de: ${newBlock.label}`);
          }
        }
        return newTime;
      });
    }, 1000); // Update every second
  };

  const stopTimer = () => {
    setIsRunning(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const resetTimer = () => {
    stopTimer();
    setCurrentTime(0);
    _setElapsedSeconds(0);
    setActiveBlocks([]);
  };

  const toggleTimer = () => {
    if (isRunning) {
      stopTimer();
    } else {
      startTimer();
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    const secs = Math.floor((minutes * 60) % 60);
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTimeRemaining = (minutes: number) => {
    const remaining = Math.max(0, totalTime - minutes);
    return formatTime(remaining);
  };

  const getBlockColor = (block: TimelineBlock) => {
    if (activeBlocks.includes(block.id)) {
      return BeautyMinimalTheme.beautyColors.amethyst[500];
    }
    if (currentTime > block.endTime) {
      return BeautyMinimalTheme.beautyColors.mint;
    }
    return BeautyMinimalTheme.neutrals.mist;
  };

  const getBlockTextStyle = (block: TimelineBlock) => {
    if (activeBlocks.includes(block.id) || currentTime > block.endTime) {
      return styles.blockLabelActive;
    }
    return styles.blockLabelDefault;
  };

  const getBlockTimeStyle = (block: TimelineBlock) => {
    if (activeBlocks.includes(block.id) || currentTime > block.endTime) {
      return styles.blockTimeActive;
    }
    return styles.blockTimeDefault;
  };

  const handleBlockPress = (block: TimelineBlock) => {
    Alert.alert(
      block.label,
      `${block.description}\n\nTiempo: ${formatTime(block.startTime)} - ${formatTime(block.endTime)}`,
      [{ text: 'OK' }]
    );
  };

  // Pre-calculate progress width to avoid inline interpolation
  const progressWidth = animatedProgress.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={styles.container}>
      {/* Timer Display */}
      <View style={styles.timerSection}>
        <View style={styles.mainTimerContainer}>
          <Text style={styles.mainTimerLabel}>Tiempo Transcurrido</Text>
          <Text style={styles.mainTimerText}>{formatTime(currentTime)}</Text>
          <Text style={styles.remainingTimeLabel}>Tiempo Restante</Text>
          <Text style={styles.remainingTimeText}>{formatTimeRemaining(currentTime)}</Text>
        </View>

        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusIndicator,
              isRunning ? styles.statusRunning : styles.statusStopped,
            ]}
          />
          <Text style={styles.statusText}>{isRunning ? '▶️ Procesando' : '⏸ En espera'}</Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <Animated.View style={[styles.progressFill, { width: progressWidth }]} />
        </View>
      </View>

      {/* Timeline Blocks */}
      <View style={styles.blocksContainer}>
        {timeline.map(block => (
          <TouchableOpacity
            key={block.id}
            style={[
              styles.timeBlock,
              {
                backgroundColor: getBlockColor(block),
                flex: block.endTime - block.startTime,
              },
            ]}
            onPress={() => handleBlockPress(block)}
            activeOpacity={0.7}
          >
            <Text style={[styles.blockLabel, getBlockTextStyle(block)]}>{block.label}</Text>
            <Text style={[styles.blockTime, getBlockTimeStyle(block)]}>
              {formatTime(block.endTime - block.startTime)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Control Buttons */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, styles.resetButton]}
          onPress={resetTimer}
          disabled={currentTime === 0}
        >
          <RotateCcw
            size={20}
            color={
              currentTime === 0
                ? BeautyMinimalTheme.neutrals.whisper
                : BeautyMinimalTheme.semantic.text.secondary
            }
          />
          <Text
            style={[
              styles.controlButtonText,
              currentTime === 0 && styles.controlButtonTextDisabled,
            ]}
          >
            Reiniciar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, styles.playButton, isRunning && styles.pauseButton]}
          onPress={toggleTimer}
        >
          {isRunning ? (
            <>
              <Pause size={24} color="white" />
              <Text style={styles.playButtonText}>PAUSAR</Text>
            </>
          ) : (
            <>
              <Play size={24} color="white" />
              <Text style={styles.playButtonText}>INICIAR PROCESO</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Current Stage Info */}
      {activeBlocks.length > 0 && (
        <View style={styles.currentStageContainer}>
          <Text style={styles.currentStageLabel}>Etapa Actual:</Text>
          {activeBlocks.map(blockId => {
            const block = timeline.find(b => b.id === blockId);
            return block ? (
              <Text key={blockId} style={styles.currentStageText}>
                {block.label} - {block.description}
              </Text>
            ) : null;
          })}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 15,
    padding: 20,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  timerSection: {
    marginBottom: 20,
  },
  mainTimerContainer: {
    alignItems: 'center',
    backgroundColor: Colors.light.timelineBackground,
    borderRadius: 15,
    padding: 20,
    marginBottom: 10,
  },
  mainTimerLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 5,
  },
  mainTimerText: {
    fontSize: 48,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 10,
  },
  remainingTimeLabel: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 5,
  },
  remainingTimeText: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.light.text,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusRunning: {
    backgroundColor: Colors.light.success,
  },
  statusStopped: {
    backgroundColor: Colors.light.gray,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.light.progressBarBackground,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.progressBarFill,
    borderRadius: 4,
  },
  blocksContainer: {
    flexDirection: 'row',
    gap: 5,
    marginBottom: 20,
  },
  timeBlock: {
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
  },
  blockLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  blockLabelDefault: {
    color: Colors.light.text,
  },
  blockLabelActive: {
    color: Colors.light.textLight,
  },
  blockTime: {
    fontSize: 10,
    marginTop: 2,
  },
  blockTimeDefault: {
    color: Colors.light.gray,
  },
  blockTimeActive: {
    color: Colors.light.whiteTransparent80,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    minWidth: 120,
  },
  controlButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.gray,
    marginLeft: 8,
  },
  controlButtonTextDisabled: {
    color: Colors.light.disabledText,
  },
  playButton: {
    backgroundColor: Colors.light.primary,
    minWidth: 180,
  },
  pauseButton: {
    backgroundColor: Colors.light.pauseButton,
  },
  playButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
    marginLeft: 8,
  },
  resetButton: {
    backgroundColor: Colors.light.timelineBackground,
    borderWidth: 1,
    borderColor: Colors.light.progressBarBackground,
  },
  currentStageContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: Colors.light.timelineBackground,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  currentStageLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 5,
  },
  currentStageText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
});
