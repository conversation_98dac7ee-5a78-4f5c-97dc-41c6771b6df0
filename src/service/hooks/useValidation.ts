import { useMemo } from 'react';
import { Alert } from 'react-native';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { HairZone } from '@/types/hair-diagnosis';
import { HairAnalysisResult, isValidHairAnalysisResult } from '@/types/ai-analysis';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface UseValidationProps {
  data: ServiceData;
  analysisResult: HairAnalysisResult | null;
}

export const useValidation = ({ data, analysisResult }: UseValidationProps) => {
  // Memoize AI result validation
  const aiValidation = useMemo(() => {
    if (!analysisResult) return { isValid: true, errors: [], warnings: [] };

    return {
      isValid: isValidHairAnalysisResult(analysisResult),
      errors: isValidHairAnalysisResult(analysisResult) ? [] : ['Invalid AI analysis result'],
      warnings: [],
    };
  }, [analysisResult]);

  // Memoize diagnosis step validation
  const diagnosisValidation = useMemo((): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Essential measurements
    if (!data.hairLength || data.hairLength <= 0) {
      errors.push('Longitud del cabello requerida');
    }

    if (!data.hairDensity) {
      errors.push('Densidad del cabello requerida');
    }

    if (!data.hairThickness) {
      errors.push('Grosor del cabello requerido');
    }

    // Zone analysis validation
    const rootsState = data.zoneColorAnalysis?.[HairZone.ROOTS]?.state;
    if (!rootsState) {
      errors.push('Estado de raíces requerido');
    }

    // AI confirmation check
    if (analysisResult && !data.aiDiagnosisConfirmed) {
      warnings.push('Confirma los valores analizados por IA');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, [data, analysisResult]);

  // Memoize overall validation
  const overallValidation = useMemo(
    () => ({
      isValid: aiValidation.isValid && diagnosisValidation.isValid,
      errors: [...aiValidation.errors, ...diagnosisValidation.errors],
      warnings: [...aiValidation.warnings, ...diagnosisValidation.warnings],
    }),
    [aiValidation, diagnosisValidation]
  );

  // Validation action handlers
  const validateAndProceed = (onSuccess: () => void) => {
    // Check AI confirmation first
    if (analysisResult && !data.aiDiagnosisConfirmed) {
      Alert.alert(
        'Confirma los valores IA',
        'Por favor, revisa y confirma los valores analizados por IA antes de continuar.'
      );
      return;
    }

    // Check essential measurements
    if (!data.hairLength || data.hairLength <= 0) {
      Alert.alert(
        'Longitud requerida',
        'Indica la longitud total (cm). Afecta directamente a las cantidades.',
        [{ text: 'Entendido' }]
      );
      return;
    }

    if (!data.hairDensity) {
      Alert.alert('Densidad requerida', 'Selecciona la densidad del cabello.', [
        { text: 'Entendido' },
      ]);
      return;
    }

    if (!data.hairThickness) {
      Alert.alert('Grosor requerido', 'Selecciona el grosor del cabello.', [{ text: 'Entendido' }]);
      return;
    }

    const rootsState = data.zoneColorAnalysis?.[HairZone.ROOTS]?.state;
    if (!rootsState) {
      Alert.alert('Estado de raíces requerido', 'Indica si las raíces están Naturales o Teñidas.', [
        { text: 'Entendido' },
      ]);
      return;
    }

    // All validations passed
    onSuccess();
  };

  const validateAIResult = (result: unknown): result is HairAnalysisResult => {
    return isValidHairAnalysisResult(result);
  };

  return {
    // Validation results
    aiValidation,
    diagnosisValidation,
    overallValidation,

    // Action handlers
    validateAndProceed,
    validateAIResult,

    // Quick checks
    canProceed: overallValidation.isValid,
    hasErrors: overallValidation.errors.length > 0,
    hasWarnings: overallValidation.warnings.length > 0,
  };
};
