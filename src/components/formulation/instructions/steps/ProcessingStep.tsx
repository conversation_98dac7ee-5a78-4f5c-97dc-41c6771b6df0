import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { Calendar, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ColorConstants } from '@/styles/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';
import { TimerCard } from '../components/TimerCard';
import { StepCard } from '../components/StepCard';
import { instructionStyles as styles } from '../styles';

interface ProcessingStepProps {
  formulaData: VisualFormulationData;
  onProcessingComplete?: () => void;
  testID?: string;
}

interface TimelinePhase {
  id: string;
  label: string;
  startTime: number;
  endTime: number;
  description: string;
  zones: string[];
}

export const ProcessingStep: React.FC<ProcessingStepProps> = ({
  formulaData,
  onProcessingComplete,
  testID = 'processing-step',
}) => {
  const [currentPhase, setCurrentPhase] = useState(0);

  // Fallback data if timeline is missing
  const timeline: TimelinePhase[] = formulaData.timeline || [
    {
      id: 'application',
      label: 'Aplicación',
      startTime: 0,
      endTime: 15,
      description: 'Aplicar la fórmula según la técnica elegida',
      zones: ['roots', 'mids', 'ends'],
    },
    {
      id: 'processing',
      label: 'Procesamiento',
      startTime: 15,
      endTime: 35,
      description: 'Tiempo de procesamiento para el desarrollo del color',
      zones: ['roots', 'mids', 'ends'],
    },
    {
      id: 'checking',
      label: 'Control',
      startTime: 35,
      endTime: 40,
      description: 'Verificar el desarrollo del color',
      zones: ['roots'],
    },
    {
      id: 'rinsing',
      label: 'Enjuague',
      startTime: 40,
      endTime: 50,
      description: 'Enjuagar completamente con agua tibia',
      zones: ['roots', 'mids', 'ends'],
    },
  ];

  const totalTime =
    formulaData.applicationGuide?.totalTime || timeline[timeline.length - 1]?.endTime || 50;

  const handlePhaseComplete = () => {
    if (currentPhase < timeline.length - 1) {
      setCurrentPhase(currentPhase + 1);
    } else {
      onProcessingComplete?.();
    }
  };

  const getCurrentPhaseDuration = () => {
    const phase = timeline[currentPhase];
    if (!phase) return 0;
    return (phase.endTime - phase.startTime) * 60; // Convert to seconds
  };

  const getZoneDisplayName = (zone: string) => {
    const zoneMap = {
      [HairZone.ROOTS]: HairZoneDisplay[HairZone.ROOTS],
      [HairZone.MIDS]: HairZoneDisplay[HairZone.MIDS],
      [HairZone.ENDS]: HairZoneDisplay[HairZone.ENDS],
    };
    return zoneMap[zone as keyof typeof zoneMap] || 'Todo';
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false} testID={testID}>
      {/* Header */}
      <StepCard
        title="Cronograma del Proceso"
        subtitle={`Tiempo Total: ${totalTime} minutos`}
        icon={<Calendar size={48} color={Colors.light.accent} />}
        testID={`${testID}-header`}
      />

      {/* Current Phase Timer */}
      {timeline[currentPhase] && (
        <View style={localStyles.currentPhaseSection}>
          <TimerCard
            title={`Fase Actual: ${timeline[currentPhase].label}`}
            subtitle={timeline[currentPhase].description}
            initialTime={getCurrentPhaseDuration()}
            onComplete={handlePhaseComplete}
            autoStart={false}
            showControls={true}
            testID={`${testID}-timer`}
          />
        </View>
      )}

      {/* Timeline Overview */}
      <View style={localStyles.timelineContainer}>
        {timeline.map((phase, index) => (
          <View key={phase.id} style={localStyles.timelineItem}>
            <View style={localStyles.timelineMarker}>
              <View
                style={[
                  localStyles.timelineDot,
                  index <= currentPhase && localStyles.timelineDotActive,
                ]}
              />
              {index < timeline.length - 1 && (
                <View
                  style={[
                    localStyles.timelineLine,
                    index < currentPhase && localStyles.timelineLineActive,
                  ]}
                />
              )}
            </View>

            <View style={localStyles.timelineContent}>
              <View
                style={[
                  localStyles.timelineCard,
                  index === currentPhase && localStyles.timelineCardActive,
                ]}
              >
                <View style={localStyles.timelineCardHeader}>
                  <Text
                    style={[
                      localStyles.phaseTitle,
                      index === currentPhase && localStyles.phaseTitleActive,
                    ]}
                  >
                    {phase.label}
                  </Text>
                  <Text style={localStyles.phaseDuration}>
                    {phase.startTime}-{phase.endTime} min
                  </Text>
                </View>

                <Text style={localStyles.phaseDescription}>{phase.description}</Text>

                <View style={localStyles.phaseZones}>
                  {phase.zones.map((zone: string) => (
                    <View key={zone} style={localStyles.zoneChip}>
                      <Text style={localStyles.zoneChipText}>{getZoneDisplayName(zone)}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>

      {/* Important Note */}
      <View style={localStyles.timelineNote}>
        <AlertCircle size={20} color={Colors.light.secondary} />
        <Text style={localStyles.timelineNoteText}>
          Los tiempos son aproximados. Ajusta según las condiciones del cabello.
        </Text>
      </View>
    </ScrollView>
  );
};

const localStyles = StyleSheet.create({
  currentPhaseSection: {
    marginVertical: 16,
  },
  timelineContainer: {
    paddingHorizontal: 16,
    marginTop: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineMarker: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.surface,
    borderWidth: 2,
    borderColor: Colors.light.textTertiary,
  },
  timelineDotActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  timelineLine: {
    width: 2,
    height: 40,
    backgroundColor: Colors.light.textTertiary,
    marginTop: 8,
  },
  timelineLineActive: {
    backgroundColor: Colors.light.primary,
  },
  timelineContent: {
    flex: 1,
  },
  timelineCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    elevation: 1,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  timelineCardActive: {
    backgroundColor: Colors.light.surface,
    elevation: 3,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  timelineCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  phaseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  phaseTitleActive: {
    color: Colors.light.primary,
  },
  phaseDuration: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  phaseDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  phaseZones: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  zoneChip: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  zoneChipText: {
    color: ColorConstants.WHITE,
    fontSize: 12,
    fontWeight: '500',
  },
  timelineNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    margin: 16,
  },
  timelineNoteText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginLeft: 12,
  },
});
