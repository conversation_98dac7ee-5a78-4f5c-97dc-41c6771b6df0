import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Animated,
  PanResponder,
  Platform,
} from 'react-native';
import { ChevronLeft, ChevronRight, Check, Camera, Zap } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Thumb zone calculations for optimal reachability
const _THUMB_ZONE = {
  easy: SCREEN_HEIGHT * 0.66, // Bottom 1/3 of screen
  medium: SCREEN_HEIGHT * 0.5, // Middle area
  hard: SCREEN_HEIGHT * 0.33, // Top 1/3
};

interface MobileOptimizedServiceProps {
  steps: string[];
  currentStep: number;
  onStepChange: (step: number) => void;
  children: React.ReactNode;
}

export const MobileOptimizedService: React.FC<MobileOptimizedServiceProps> = ({
  steps,
  currentStep,
  onStepChange,
  children,
}) => {
  const [showQuickActions, setShowQuickActions] = useState(false);
  const swipeAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Swipe gesture handler
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Activate on horizontal swipes > 10px
        return Math.abs(gestureState.dx) > 10;
      },
      onPanResponderGrant: () => {
        // Haptic feedback on gesture start
        if (Platform.OS === 'ios') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      },
      onPanResponderMove: (_, gestureState) => {
        // Animate based on swipe distance
        swipeAnim.setValue(gestureState.dx);
      },
      onPanResponderRelease: (_, gestureState) => {
        const swipeThreshold = SCREEN_WIDTH * 0.25;

        if (gestureState.dx > swipeThreshold && currentStep > 0) {
          // Swipe right - go back
          animateTransition(() => onStepChange(currentStep - 1));
        } else if (gestureState.dx < -swipeThreshold && currentStep < steps.length - 1) {
          // Swipe left - go forward
          animateTransition(() => onStepChange(currentStep + 1));
        } else {
          // Snap back
          Animated.spring(swipeAnim, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const animateTransition = useCallback(
    (callback: () => void) => {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(swipeAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ]).start(() => {
        callback();
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      });
    },
    [fadeAnim, swipeAnim]
  );

  return (
    <View style={styles.container}>
      {/* Progress indicator - always visible at top */}
      <View style={styles.progressContainer}>
        <View style={styles.progressTrack}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressDot,
                index === currentStep && styles.progressDotActive,
                index < currentStep && styles.progressDotCompleted,
              ]}
              onPress={() => {
                if (index <= currentStep) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  onStepChange(index);
                }
              }}
              disabled={index > currentStep}
            >
              {index < currentStep && <Check size={12} color={Colors.light.textLight} />}
            </TouchableOpacity>
          ))}
        </View>
        <Text style={styles.stepLabel}>{steps[currentStep]}</Text>
      </View>

      {/* Main content area with swipe gestures */}
      <Animated.View
        style={[
          styles.contentArea,
          {
            transform: [
              { translateX: swipeAnim },
              {
                scale: swipeAnim.interpolate({
                  inputRange: [-SCREEN_WIDTH, 0, SCREEN_WIDTH],
                  outputRange: [0.95, 1, 0.95],
                }),
              },
            ],
            opacity: fadeAnim,
          },
        ]}
        {...panResponder.panHandlers}
      >
        <ScrollView
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {children}
        </ScrollView>
      </Animated.View>

      {/* Floating action button in thumb zone */}
      <TouchableOpacity
        style={styles.fabButton}
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          setShowQuickActions(!showQuickActions);
        }}
      >
        <Zap size={24} color={Colors.light.textLight} />
      </TouchableOpacity>

      {/* Quick actions menu */}
      {showQuickActions && (
        <Animated.View style={styles.quickActionsMenu}>
          <QuickActionButton icon={<Camera size={20} />} label="Foto" onPress={() => {}} />
          <QuickActionButton icon={<Zap size={20} />} label="IA" onPress={() => {}} />
          <QuickActionButton icon={<Check size={20} />} label="Guardar" onPress={() => {}} />
        </Animated.View>
      )}

      {/* Bottom navigation in easy thumb zone */}
      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navButton, currentStep === 0 && styles.navButtonDisabled]}
          onPress={() => {
            if (currentStep > 0) {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onStepChange(currentStep - 1);
            }
          }}
          disabled={currentStep === 0}
        >
          <ChevronLeft size={24} color={currentStep === 0 ? Colors.textLight : Colors.primary} />
          <Text style={[styles.navButtonText, currentStep === 0 && styles.navButtonTextDisabled]}>
            Anterior
          </Text>
        </TouchableOpacity>

        <View style={styles.stepIndicator}>
          <Text style={styles.stepIndicatorText}>
            {currentStep + 1} / {steps.length}
          </Text>
        </View>

        <TouchableOpacity
          style={[
            styles.navButton,
            styles.navButtonPrimary,
            currentStep === steps.length - 1 && styles.navButtonSuccess,
          ]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            if (currentStep < steps.length - 1) {
              onStepChange(currentStep + 1);
            }
          }}
        >
          <Text style={styles.navButtonTextPrimary}>
            {currentStep === steps.length - 1 ? 'Finalizar' : 'Siguiente'}
          </Text>
          <ChevronRight size={24} color={Colors.light.textLight} />
        </TouchableOpacity>
      </View>

      {/* Visual swipe hint */}
      {currentStep === 0 && (
        <View style={styles.swipeHint}>
          <Text style={styles.swipeHintText}>← Desliza para navegar →</Text>
        </View>
      )}
    </View>
  );
};

// Quick action button component
const QuickActionButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  onPress: () => void;
}> = ({ icon, label, onPress }) => (
  <TouchableOpacity
    style={styles.quickActionButton}
    onPress={() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }}
  >
    {icon}
    <Text style={styles.quickActionLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  progressContainer: {
    paddingTop: 16,
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.backgroundLight,
    // Keep in hard zone but minimal interaction needed
    maxHeight: SCREEN_HEIGHT * 0.12,
  },
  progressTrack: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressDotActive: {
    backgroundColor: Colors.primary,
    transform: [{ scale: 1.2 }],
  },
  progressDotCompleted: {
    backgroundColor: Colors.success,
  },
  stepLabel: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '600',
    textAlign: 'center',
  },
  contentArea: {
    flex: 1,
    marginBottom: 80, // Space for bottom nav
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: 40,
  },
  fabButton: {
    position: 'absolute',
    right: 20,
    bottom: 100, // In easy thumb zone
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  quickActionsMenu: {
    position: 'absolute',
    right: 20,
    bottom: 170,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 8,
    elevation: 8,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    minWidth: 120,
  },
  quickActionLabel: {
    marginLeft: 12,
    fontSize: 16,
    color: Colors.text,
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.backgroundLight,
    // Entirely in easy thumb zone
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: Colors.backgroundLight,
    minWidth: 100,
  },
  navButtonPrimary: {
    backgroundColor: Colors.primary,
  },
  navButtonSuccess: {
    backgroundColor: Colors.success,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 4,
  },
  navButtonTextPrimary: {
    fontSize: 16,
    color: Colors.light.textLight,
    fontWeight: '600',
    marginRight: 4,
  },
  navButtonTextDisabled: {
    color: Colors.textLight,
  },
  stepIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.backgroundLight,
    borderRadius: 20,
  },
  stepIndicatorText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  swipeHint: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    opacity: 0.6,
  },
  swipeHintText: {
    fontSize: 14,
    color: Colors.textLight,
    fontStyle: 'italic',
  },
});
