/**
 * Types for Hair Analysis AI Results
 *
 * These types define the structure of AI analysis responses
 * from the hair diagnosis system, ensuring type safety across components.
 */

// Zone-specific analysis data structure
export interface ZoneAnalysisData {
  // Color properties
  level?: number;
  tone?: string;
  tono?: string; // Spanish alias
  reflect?: string;
  reflejo?: string; // Spanish alias
  undertone?: string;

  // Physical properties
  condition?: string;
  damage?: string;
  daño?: string; // Spanish alias
  porosity?: string;
  porosidad?: string; // Spanish alias
  elasticity?: string;
  elasticidad?: string; // Spanish alias
  resistance?: string;
  resistencia?: string; // Spanish alias

  // Pigment analysis
  pigmentAccumulation?: string;
  acumulacionDePigmentos?: string; // Spanish alias
  'acumulación de pigmentos'?: string; // Spanish with spaces
  pigmentLoad?: string;
  colorAccumulation?: string;

  // Gray hair analysis
  grayPercentage?: number;
  canasPercentage?: number; // Spanish alias
  porcentajeCanas?: number; // Spanish alias
  grayType?: string;
  tiposCanas?: string; // Spanish alias
  grayPattern?: string;
  patronCanas?: string; // Spanish alias

  // Hair state
  state?: string;
  estado?: string; // Spanish alias
  unwantedTone?: string;
  tonoIndeseado?: string; // Spanish alias
  cuticleState?: string;
  estadoCuticula?: string; // Spanish alias

  // Quality metrics
  confidence?: number;

  // Additional properties for extensibility
  [key: string]: unknown;
}

// Chemical process detection
export interface DetectedProcesses {
  chemicalProcess?: string;
  lastProcessDate?: string;
}

// Risk detection analysis
export interface DetectedRisks {
  extremeDamage?: {
    detected?: boolean;
  };
}

// Hair analysis metadata
export interface HairAnalysis {
  overallCondition?: string;
}

// Complete AI analysis result structure
export interface HairAnalysisResult {
  // Overall hair characteristics
  hairThickness?: string;
  hairDensity?: string;
  hairLength?: number;

  // Color analysis
  overallTone?: string;
  overallReflect?: string;
  overallUndertone?: string; // Alternative field name
  averageLevel?: number;
  averageDepthLevel?: number; // Legacy field name

  // Chemical history detection
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedProcesses?: DetectedProcesses;

  // Risk and condition analysis
  detectedRisks?: DetectedRisks;
  hairAnalysis?: HairAnalysis;
  overallCondition?: string;

  // Zone-specific analysis
  zoneAnalysis?: {
    roots?: ZoneAnalysisData;
    ROOTS?: ZoneAnalysisData; // Uppercase variant
    mids?: ZoneAnalysisData;
    MIDS?: ZoneAnalysisData; // Uppercase variant
    ends?: ZoneAnalysisData;
    ENDS?: ZoneAnalysisData; // Uppercase variant
    [key: string]: unknown; // For dynamic zone names
  };

  // Analysis metadata
  overallConfidence?: number;
  condition?: Record<string, unknown>; // Legacy field
  texture?: Record<string, unknown>; // Legacy field

  // Extensibility for future AI features
  [key: string]: unknown;
}

// Type guards for runtime validation
export const isValidHairAnalysisResult = (obj: unknown): obj is HairAnalysisResult => {
  if (!obj || typeof obj !== 'object') return false;

  const result = obj as HairAnalysisResult;

  // Check for at least one expected field
  return !!(
    result.hairThickness ||
    result.hairDensity ||
    result.overallTone ||
    result.overallConfidence ||
    result.zoneAnalysis
  );
};

// Helper type for zone analysis transformation
export interface TransformedZoneData {
  level: number;
  tone?: string;
  reflect?: string;
  state?: string;
  grayPercentage: number;
  grayType?: string;
  grayPattern?: string;
  unwantedTone?: string;
  cuticleState?: string;
  damage?: string;
  porosity?: string;
  elasticity?: string;
  resistance?: string;
  pigmentAccumulation?: string;
}

// Export type alias for backward compatibility
export type AnalysisResult = HairAnalysisResult;
