import { logger } from '@/utils/logger';
import { supabase } from '@/lib/supabase';

export interface ProductFingerprint {
  brand: string;
  productType: string;
  level: number | null;
  tone: string | null;
  volume: number | null;
}

export interface DatabaseBrand {
  id: string;
  name: string;
  aliases?: string[];
}

export interface ProductMapping {
  id: string;
  ai_product_name: string;
  inventory_product_id: string;
  confidence: number;
  usage_count: number;
}

interface BrandPattern {
  keywords: string[];
  officialName: string;
  dbName?: string; // Nombre exacto en la BD para matching preciso
}

interface ProductTypePattern {
  keywords: string[];
  type: string;
}

export class ProductMatcherService {
  // Cache para marcas de la base de datos
  private static brandCache: Map<string, DatabaseBrand> = new Map();
  private static cacheExpiry = 0;
  private static readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutos

  // Patrones de marcas conocidas - FALLBACK si falla la BD
  private static brandPatterns: BrandPattern[] = [
    {
      keywords: ['wella professionals', 'wella', 'wel', 'wellaton'],
      officialName: 'wella professionals',
      dbName: 'Wella Professionals',
    },
    {
      keywords: ["l'oréal professionnel", "l'oreal professionnel", 'loreal', "l'oreal", 'oreal'],
      officialName: 'loreal professionnel',
      dbName: "L'Oréal Professionnel",
    },
    {
      keywords: ['schwarzkopf professional', 'schwarzkopf', 'schwarz'],
      officialName: 'schwarzkopf professional',
      dbName: 'Schwarzkopf Professional',
    },
    {
      keywords: ['redken professional', 'redken'],
      officialName: 'redken',
      dbName: 'Redken Professional',
    },
    {
      keywords: ['matrix professional', 'matrix'],
      officialName: 'matrix',
      dbName: 'Matrix Professional',
    },
    { keywords: ['joico'], officialName: 'joico', dbName: 'Joico' },
    {
      keywords: ['goldwell', 'gold'],
      officialName: 'goldwell',
      dbName: 'Goldwell',
    },
    {
      keywords: ['alfaparf milano', 'alfaparf', 'alfa'],
      officialName: 'alfaparf',
      dbName: 'Alfaparf Milano',
    },
    { keywords: ['indola'], officialName: 'indola', dbName: 'Indola' },
    {
      keywords: ['revlon professional', 'revlon'],
      officialName: 'revlon',
      dbName: 'Revlon Professional',
    },
    {
      keywords: ['salerm cosmetics', 'salerm', 'saler', 'salem'],
      officialName: 'salerm cosmetics',
      dbName: 'Salerm Cosmetics',
    },
    { keywords: ['olaplex'], officialName: 'olaplex', dbName: 'Olaplex' },
    {
      keywords: ['genérico', 'generico', 'generic'],
      officialName: 'generico',
      dbName: 'Genérico',
    },
  ];

  // Patrones de tipos de producto - ORDEN IMPORTA: más específicos primero
  private static productTypePatterns: ProductTypePattern[] = [
    {
      keywords: [
        'oxidante',
        'oxidant',
        'developer',
        'peróxido',
        'peroxide',
        'oxigenada',
        'activador',
        'oxydant',
        'welloxon',
      ],
      type: 'developer',
    },
    {
      keywords: [
        'decolorante',
        'bleach',
        'polvo',
        'lightener',
        'blanqueador',
        'blondor',
        'platinium',
      ],
      type: 'bleach',
    },
    {
      keywords: ['matizador', 'toner', 'tonalizador', 'silver', 'violet'],
      type: 'toner',
    },
    {
      // PRIMERO: Tratamientos que pueden contener color (Salermvision, etc)
      keywords: [
        'tratamiento',
        'treatment',
        'mascarilla',
        'mask',
        'acondicionador',
        'conditioner',
        'salermvision', // CRÍTICO: Evita que se clasifique como color
        'plex',
        'bond',
        'protector',
        'olaplex',
        'wellaplex',
      ],
      type: 'treatment',
    },
    {
      keywords: ['champú', 'shampoo', 'champu'],
      type: 'shampoo',
    },
    {
      // DESPUÉS: Tintes y coloraciones (más genérico)
      keywords: [
        'tinte',
        'color',
        'coloración',
        'dye',
        'koleston',
        'majirel',
        'igora',
        'illumina',
        'inoa',
        // IMPORTANTE: 'salermvision' se clasifica como tratamiento (regla especial arriba)
        // Aquí añadimos variantes de la línea de color 'Salermvison' y el término 'vison'
        'salermvison',
        'salerm vison',
        'vison',
      ],
      type: 'color',
    },
  ];

  /**
   * Genera una huella digital (fingerprint) de un producto
   */
  static async generateFingerprint(productName: string): Promise<ProductFingerprint> {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final antes de procesar
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return {
      brand: await this.detectBrand(normalized),
      productType: this.detectType(normalized),
      level: this.extractLevel(normalized),
      tone: this.extractTone(normalized),
      volume: this.extractVolume(normalized),
    };
  }

  /**
   * Carga marcas desde la base de datos con cache
   */
  private static async loadBrandsFromDatabase(): Promise<void> {
    try {
      if (Date.now() < this.cacheExpiry && this.brandCache.size > 0) {
        return; // Cache válido
      }

      const { data: brands, error } = await supabase
        .from('brands')
        .select('id, name')
        .eq('is_active', true);

      if (error) {
        logger.error('Failed to load brands from database', 'ProductMatcherService', { error });
        return;
      }

      this.brandCache.clear();
      brands?.forEach(brand => {
        // Normalizar nombre para búsqueda
        const normalizedName = brand.name.toLowerCase();

        // Generar aliases automáticos
        const aliases = this.generateBrandAliases(brand.name);

        this.brandCache.set(normalizedName, {
          id: brand.id,
          name: brand.name,
          aliases,
        });
      });

      this.cacheExpiry = Date.now() + this.CACHE_TTL;

      logger.info('Loaded brands from database', 'ProductMatcherService', {
        brandsCount: this.brandCache.size,
        cacheExpiry: new Date(this.cacheExpiry).toISOString(),
      });
    } catch (error) {
      logger.error('Error loading brands from database', 'ProductMatcherService', { error });
    }
  }

  /**
   * Genera aliases automáticos para una marca
   */
  private static generateBrandAliases(brandName: string): string[] {
    const aliases: string[] = [];
    const name = brandName.toLowerCase();

    // Alias básicos
    aliases.push(name);

    // Quitar "professional"
    if (name.includes('professional')) {
      aliases.push(name.replace(/\s*professional\s*/g, '').trim());
    }

    // Quitar acentos y caracteres especiales
    const withoutAccents = name
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ñ]/g, 'n')
      .replace(/[ç]/g, 'c')
      .replace(/[''']/g, '')
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (withoutAccents !== name) {
      aliases.push(withoutAccents);
    }

    // Abreviaciones comunes
    if (name.includes('loreal') || name.includes("l'oreal")) {
      aliases.push('loreal', "l'oreal", 'oreal');
    }

    if (name.includes('schwarzkopf')) {
      aliases.push('schwarz', 'schwarzkopf');
    }

    // Primera palabra si es multi-palabra
    const firstWord = name.split(' ')[0];
    if (firstWord.length > 3 && !aliases.includes(firstWord)) {
      aliases.push(firstWord);
    }

    return Array.from(new Set(aliases)); // Eliminar duplicados
  }

  /**
   * Detecta la marca del producto usando BD + fallback mejorado
   */
  private static async detectBrand(text: string): Promise<string> {
    // 1. Intentar con base de datos
    await this.loadBrandsFromDatabase();

    const normalizedText = text.toLowerCase();

    // Buscar coincidencia exacta o por aliases
    for (const [normalizedName, brand] of Array.from(this.brandCache.entries())) {
      if (normalizedText.includes(normalizedName)) {
        logger.info('Brand detected from database', 'ProductMatcherService', {
          detectedBrand: brand.name,
          inputText: text,
          matchedAlias: normalizedName,
        });
        return brand.name;
      }

      // Buscar en aliases
      if (brand.aliases) {
        for (const alias of brand.aliases) {
          if (normalizedText.includes(alias)) {
            logger.info('Brand detected via alias', 'ProductMatcherService', {
              detectedBrand: brand.name,
              inputText: text,
              matchedAlias: alias,
            });
            return brand.name;
          }
        }
      }
    }

    // 2. Fallback a patrones estáticos
    for (const pattern of this.brandPatterns) {
      if (pattern.keywords.some(keyword => normalizedText.includes(keyword))) {
        logger.info('Brand detected from static patterns (fallback)', 'ProductMatcherService', {
          detectedBrand: pattern.officialName,
          inputText: text,
        });
        return pattern.officialName;
      }
    }

    // 3. NUEVO: Intelligent fallback basado en el tipo de producto
    const productType = this.detectType(normalizedText);
    if (productType !== 'other') {
      // Si tenemos un tipo de producto válido pero no marca, usar una marca por defecto lógica
      const defaultBrand = this.getDefaultBrandForType(productType);
      if (defaultBrand) {
        logger.info(
          'Brand inferred from product type (intelligent fallback)',
          'ProductMatcherService',
          {
            detectedBrand: defaultBrand,
            inputText: text,
            productType,
          }
        );
        return defaultBrand;
      }
    }

    logger.warn('Brand not detected', 'ProductMatcherService', { inputText: text });
    return 'unknown';
  }

  /**
   * NUEVO: Obtiene una marca por defecto lógica según el tipo de producto
   */
  private static getDefaultBrandForType(productType: string): string | null {
    // Mapeo inteligente de tipos de producto a marcas comunes
    const typeToDefaultBrand: Record<string, string> = {
      color: 'wella professionals', // Wella es muy común para tintes
      developer: 'wella professionals', // Wella Welloxon es estándar
      bleach: 'wella professionals', // Blondor es popular
      toner: 'wella professionals', // Color Fresh muy usado
      treatment: 'olaplex', // Olaplex es líder en tratamientos
    };

    return typeToDefaultBrand[productType] || null;
  }

  /**
   * Detecta el tipo de producto con prioridad y validación mejorada
   */
  private static detectType(text: string): string {
    const normalizedText = text.toLowerCase();

    // REGLA ESPECIAL: "Salermvision" es tratamiento, no color
    if (normalizedText.includes('salermvision') || normalizedText.includes('salerm vision')) {
      return 'treatment';
    }

    // Buscar por patrones en orden de prioridad (más específicos primero)
    for (const pattern of this.productTypePatterns) {
      if (pattern.keywords.some(keyword => normalizedText.includes(keyword))) {
        return pattern.type;
      }
    }

    return 'other';
  }

  /**
   * Extrae el nivel/altura de tono (número antes del separador)
   */
  private static extractLevel(text: string): number | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const levelMatch = text.match(/\b(\d{1,2})\s*[\/\-\.,]\s*\d{1,2}\b/);
    if (levelMatch) {
      return parseInt(levelMatch[1]);
    }

    // Buscar nivel solo (ej: "nivel 7")
    const singleLevelMatch = text.match(/\b(?:nivel|level|altura)\s*(\d{1,2})\b/);
    if (singleLevelMatch) {
      return parseInt(singleLevelMatch[1]);
    }

    return null;
  }

  /**
   * Normaliza un tono para comparación (convierte todos los separadores a punto)
   */
  static normalizeShade(shade: string | null): string | null {
    if (!shade) return null;
    // Convertir todos los separadores (coma, barra, guión) a punto
    return shade.replace(/[,\/\-]/g, '.');
  }

  /**
   * Extrae el tono/reflejo (números después del separador)
   */
  private static extractTone(text: string): string | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const toneMatch = text.match(/\b\d{1,2}\s*[\/\-\.,]\s*(\d{1,2})\b/);
    if (toneMatch) {
      return toneMatch[1];
    }

    // Para productos con formato especial (ej: "7/43" o "9/60")
    const fullMatch = text.match(/\b(\d{1,2}[\/\-\.,]\d{1,2})\b/);
    if (fullMatch) {
      return fullMatch[1].replace(/[\/\-\.,]/, '');
    }

    // Si encontramos un nivel sin tono (ej: "Color 8" o "Tinte 8")
    // y es un producto de coloración, marcarlo como "NATURAL"
    const productType = this.detectType(text);
    if (productType === 'color') {
      const levelOnly = text.match(
        /\b(?:color|tinte|illumina|koleston|majirel|igora|vison)\s+(\d{1,2})\b/i
      );
      if (levelOnly) {
        return 'NATURAL'; // Indicador especial para tonos naturales sin reflejo
      }
    }

    return null;
  }

  /**
   * Extrae el volumen para oxidantes
   */
  private static extractVolume(text: string): number | null {
    // Buscar patrones como "20 vol", "20vol", "20 volúmenes", "6%"
    const volumeMatch = text.match(/\b(\d{1,2})\s*(?:vol(?:umen|úmenes)?|%)\b/);
    if (volumeMatch) {
      const value = parseInt(volumeMatch[1]);
      // Si es porcentaje, convertir a volúmenes
      if (text.includes('%')) {
        const volumeMap: { [key: number]: number } = {
          3: 10,
          6: 20,
          9: 30,
          12: 40,
        };
        return volumeMap[value] || value;
      }
      return value;
    }
    return null;
  }

  /**
   * Verifica si dos marcas son la misma (considerando variaciones)
   */
  static isSameBrand(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    if (fp1.brand === fp2.brand && fp1.brand !== 'unknown') {
      return true;
    }

    // Buscar si ambas marcas pertenecen al mismo patrón
    for (const pattern of this.brandPatterns) {
      const fp1Matches = pattern.keywords.some(k => fp1.brand?.includes(k));
      const fp2Matches = pattern.keywords.some(k => fp2.brand?.includes(k));
      if (fp1Matches && fp2Matches) {
        return true;
      }
    }

    return false;
  }

  /**
   * Verifica si alguna de las marcas es "unknown" o similar
   */
  static hasUnknownBrand(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    const unknownPatterns = ['unknown', 'genérico', 'generic', '', null, undefined];

    const brand1 = fp1.brand?.toLowerCase() || '';
    const brand2 = fp2.brand?.toLowerCase() || '';

    return unknownPatterns.some(
      pattern =>
        pattern === brand1 ||
        pattern === brand2 ||
        brand1.includes('unknown') ||
        brand2.includes('unknown')
    );
  }

  /**
   * Verifica si dos productos de coloración coinciden exactamente
   */
  static isExactColorMatch(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    // Ambos deben ser productos de coloración
    if (fp1.productType !== 'color' || fp2.productType !== 'color') {
      return false;
    }

    // La marca debe coincidir (usando comparación flexible)
    if (!this.isSameBrand(fp1, fp2)) {
      return false;
    }

    // El nivel debe coincidir exactamente
    if (fp1.level === null || fp2.level === null || fp1.level !== fp2.level) {
      return false;
    }

    // El tono debe coincidir (normalizado)
    const tone1 = this.normalizeShade(fp1.tone);
    const tone2 = this.normalizeShade(fp2.tone);

    if (tone1 !== tone2) {
      return false;
    }

    return true;
  }

  /**
   * Compara dos fingerprints y devuelve un score de similitud (0-100) - Versión mejorada
   */
  static compareFingerprints(fp1: ProductFingerprint, fp2: ProductFingerprint): number {
    // LOGGING DETALLADO para debugging
    logger.info('compareFingerprints DEBUG', 'ProductMatcherService', {
      fp1: { brand: fp1.brand, type: fp1.productType, level: fp1.level, tone: fp1.tone },
      fp2: { brand: fp2.brand, type: fp2.productType, level: fp2.level, tone: fp2.tone },
      typesMatch: fp1.productType === fp2.productType,
      brandsMatch: this.isSameBrand(fp1, fp2),
    });

    // Para productos de coloración, aplicar reglas estrictas con niveles
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      // Nivel 1: Match exacto (100%)
      if (this.isExactColorMatch(fp1, fp2)) {
        return 100;
      }

      // Nivel 2: Marca + Nivel + Tono normalizado (90%)
      const tone1 = this.normalizeShade(fp1.tone);
      const tone2 = this.normalizeShade(fp2.tone);

      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level && tone1 === tone2) {
        return 90;
      }

      // Nivel 3: Marca + Nivel (70%)
      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level) {
        return 70;
      }

      // NUEVO: Nivel 4: Nivel + Tono sin marca (60%) - Para casos de "unknown" brand
      if (fp1.level === fp2.level && tone1 === tone2 && fp1.level !== null && tone1 !== null) {
        return 60; // Buen match técnico aunque falte marca
      }

      // Nivel 5: Solo nivel (40%) - Para casos de "unknown" brand
      if (fp1.level === fp2.level && fp1.level !== null) {
        return 40; // Match parcial útil
      }

      // EMERGENCY FALLBACK: Si una marca es "unknown", buscar coincidencias técnicas flexibles
      if (this.hasUnknownBrand(fp1, fp2)) {
        // Solo tono match (35%) - Último recurso
        if (tone1 === tone2 && tone1 !== null && tone1 !== '') {
          return 35;
        }

        // Similarity by level difference (25% - 10%) - Colores cercanos
        const levelDiff = fp1.level && fp2.level ? Math.abs(fp1.level - fp2.level) : 999;
        if (levelDiff <= 2) {
          return Math.max(25 - levelDiff * 5, 10); // 25% si diff=0, 20% si diff=1, 15% si diff=2
        }

        // Al menos 10% para productos de color con brand unknown (permite mostrar sugerencias)
        return 10;
      }

      // Nivel 6: Solo marca (35%)
      if (this.isSameBrand(fp1, fp2)) {
        return 35;
      }

      // NUEVO: Nivel 7: Solo tono/shade (30%) - Útil para matching cuando falta otra info
      if (tone1 === tone2 && tone1 !== null) {
        return 30;
      }

      return 15; // Productos de color pero sin match específico
    }

    // Para otros productos, mantener lógica flexible pero mejorada
    let score = 0;

    // NUEVO FALLBACK CRÍTICO: Si los tipos no coinciden exactamente, buscar compatibilidad
    const typesCompatible = this.areTypesCompatible(fp1.productType, fp2.productType);

    if (typesCompatible.compatible) {
      logger.info('Types are compatible', 'ProductMatcherService', {
        type1: fp1.productType,
        type2: fp2.productType,
        reason: typesCompatible.reason,
        baseScore: typesCompatible.score,
      });
      score += typesCompatible.score; // Score basado en compatibilidad
    } else if (fp1.productType === fp2.productType) {
      // Tipo exacto (25 puntos) - importante
      score += 25;
    }

    // Marca (35 puntos) - importante pero no crítico si tenemos otro match
    if (this.isSameBrand(fp1, fp2)) {
      score += 35;
    } else if (
      (fp1.brand === 'unknown' || fp2.brand === 'unknown') &&
      (fp1.productType === fp2.productType || typesCompatible.compatible) &&
      fp1.productType !== 'other'
    ) {
      // NUEVO: Bonus por tipo matching cuando una marca es unknown
      score += 15;
    }

    // Para oxidantes: volumen es crítico
    if (fp1.productType === 'developer' && fp2.productType === 'developer') {
      if (fp1.volume !== null && fp2.volume !== null && fp1.volume === fp2.volume) {
        score += 40; // Muy importante para oxidantes
      } else if (fp1.volume === null || fp2.volume === null) {
        // NUEVO: Bonus parcial si uno no tiene volumen especificado
        score += 10;
      }
    }

    // EMERGENCY FALLBACK: Si el score sigue siendo 0, intentar matching por texto
    if (score === 0) {
      const textSimilarity = this.calculateTextSimilarity(fp1, fp2);
      if (textSimilarity > 0) {
        score = Math.max(10, textSimilarity); // Mínimo 10% si hay alguna similitud
        logger.info('Using text similarity fallback', 'ProductMatcherService', {
          textSimilarity,
          finalScore: score,
        });
      }
    }

    logger.info('compareFingerprints RESULT', 'ProductMatcherService', {
      finalScore: score,
      breakdown: {
        typeCompatible: typesCompatible.compatible,
        brandMatch: this.isSameBrand(fp1, fp2),
        type1: fp1.productType,
        type2: fp2.productType,
      },
    });

    return Math.min(100, score); // Cap at 100
  }

  /**
   * Normaliza un nombre de producto para comparación flexible
   */
  static normalizeForMatching(productName: string): string {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return (
      normalized
        // Normalizar separadores de tono (mantener consistencia)
        .replace(/(\d+)\s*[,\/\-]\s*(\d+)/g, '$1.$2') // 7/43, 7-43, 7,43 → 7.43
        // Normalizar volúmenes
        .replace(/(\d+)\s*vol(?:umen|úmenes)?/gi, '$1vol')
        // Normalizar espacios
        .replace(/\s+/g, ' ')
        // Quitar caracteres especiales excepto puntos en números
        .replace(/[^\w\s.]/g, '')
    );
  }

  /**
   * Busca mapping existente en la base de datos
   */
  private static async findExistingMapping(
    iaProduct: string,
    salonId: string
  ): Promise<ProductMapping | null> {
    try {
      const { data, error } = await supabase
        .from('product_mappings')
        .select('*')
        .eq('salon_id', salonId)
        .eq('ai_product_name', iaProduct)
        .order('confidence', { ascending: false })
        .order('usage_count', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 = no rows
        logger.error('Error finding existing mapping', 'ProductMatcherService', { error });
        return null;
      }

      return data as ProductMapping | null;
    } catch (error) {
      logger.error('Error in findExistingMapping', 'ProductMatcherService', { error });
      return null;
    }
  }

  /**
   * Guarda nuevo mapping en la base de datos
   */
  private static async saveMappingToDatabase(
    iaProduct: string,
    inventoryProductId: string,
    confidence: number,
    salonId: string
  ): Promise<void> {
    try {
      const { error } = await supabase.from('product_mappings').upsert(
        {
          salon_id: salonId,
          ai_product_name: iaProduct,
          inventory_product_id: inventoryProductId,
          confidence,
          usage_count: 1,
        },
        {
          onConflict: 'salon_id,ai_product_name,inventory_product_id',
        }
      );

      if (error) {
        logger.error('Error saving mapping to database', 'ProductMatcherService', { error });
      } else {
        logger.info('Mapping saved to database', 'ProductMatcherService', {
          iaProduct,
          inventoryProductId,
          confidence,
        });
      }
    } catch (error) {
      logger.error('Error in saveMappingToDatabase', 'ProductMatcherService', { error });
    }
  }

  /**
   * Incrementa uso de mapping existente
   */
  private static async incrementMappingUsage(mappingId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('product_mappings')
        .update({
          usage_count: supabase.raw('usage_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', mappingId);

      if (error) {
        logger.error('Error incrementing mapping usage', 'ProductMatcherService', { error });
      }
    } catch (error) {
      logger.error('Error in incrementMappingUsage', 'ProductMatcherService', { error });
    }
  }

  /**
   * Calcula similitud entre dos nombres de productos usando múltiples estrategias
   */
  static async calculateSmartSimilarity(
    iaProduct: string,
    inventoryProduct: string,
    salonId?: string
  ): Promise<number> {
    // 1. Verificar mapping existente si tenemos salonId
    if (salonId) {
      const existingMapping = await this.findExistingMapping(iaProduct, salonId);
      if (existingMapping) {
        await this.incrementMappingUsage(existingMapping.id);

        logger.info('Using existing mapping', 'ProductMatcherService', {
          iaProduct,
          inventoryProduct,
          confidence: existingMapping.confidence,
          usageCount: existingMapping.usage_count + 1,
        });

        return existingMapping.confidence;
      }
    }

    // 2. Comparación por fingerprint (más confiable)
    const fp1 = await this.generateFingerprint(iaProduct);
    const fp2 = await this.generateFingerprint(inventoryProduct);
    const fingerprintScore = this.compareFingerprints(fp1, fp2);

    // Para productos de coloración, SOLO usar fingerprint score
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      logger.info('ProductMatcher comparison (COLOR PRODUCT)', 'ProductMatcherService', {
        iaProduct,
        inventoryProduct,
        fingerprint1: fp1,
        fingerprint2: fp2,
        fingerprintScore,
        isExactMatch: this.isExactColorMatch(fp1, fp2),
        finalScore: fingerprintScore,
      });

      return fingerprintScore; // Para tintes, no usar otros métodos
    }

    // NUEVO: Para casos mixtos (treatment vs color), usar compatibilidad
    const areCompatible = this.areTypesCompatible(fp1.productType, fp2.productType);
    if (areCompatible.compatible) {
      logger.info('ProductMatcher comparison (COMPATIBLE TYPES)', 'ProductMatcherService', {
        iaProduct,
        inventoryProduct,
        fingerprint1: fp1,
        fingerprint2: fp2,
        fingerprintScore,
        compatibilityScore: areCompatible.score,
        compatibilityReason: areCompatible.reason,
        finalScore: Math.max(fingerprintScore, areCompatible.score),
      });

      return Math.max(fingerprintScore, areCompatible.score);
    }

    // 2. Para otros productos, usar comparación por normalización como fallback
    const normalized1 = this.normalizeForMatching(iaProduct);
    const normalized2 = this.normalizeForMatching(inventoryProduct);
    let stringScore = 0;

    if (normalized1 === normalized2) {
      stringScore = 100;
    } else if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
      stringScore = 70;
    }

    // 3. Para productos no-coloración, usar el mejor score
    const finalScore = Math.max(fingerprintScore, stringScore);

    logger.info('ProductMatcher comparison', 'ProductMatcherService', {
      iaProduct,
      inventoryProduct,
      fingerprint1: fp1,
      fingerprint2: fp2,
      fingerprintScore,
      stringScore,
      finalScore,
    });

    // 4. Guardar mapping si es bueno y tenemos salonId
    if (salonId && finalScore >= 70) {
      // TODO: Necesitamos el inventoryProductId real, por ahora solo loggear
      logger.info('Would save mapping to database', 'ProductMatcherService', {
        iaProduct,
        confidence: finalScore,
        note: 'Need inventoryProductId to save mapping',
      });
    }

    return finalScore;
  }

  /**
   * NUEVO: Verifica si dos tipos de producto son compatibles para matching
   */
  private static areTypesCompatible(
    type1: string,
    type2: string
  ): { compatible: boolean; score: number; reason?: string } {
    // Mappings de compatibilidad entre tipos
    const compatibilityMap: Record<
      string,
      { compatibleWith: string[]; score: number; reason: string }
    > = {
      treatment: {
        compatibleWith: ['color', 'toner'], // Tratamientos pueden ser de color
        score: 20,
        reason: 'Treatment products can include coloration',
      },
      color: {
        compatibleWith: ['treatment', 'toner'],
        score: 20,
        reason: 'Color products can be in treatment form',
      },
      toner: {
        compatibleWith: ['color', 'treatment'],
        score: 15,
        reason: 'Toners are specialized color products',
      },
    };

    if (type1 === type2) {
      return { compatible: true, score: 25, reason: 'Exact type match' };
    }

    const type1Config = compatibilityMap[type1];
    if (type1Config && type1Config.compatibleWith.includes(type2)) {
      return {
        compatible: true,
        score: type1Config.score,
        reason: type1Config.reason,
      };
    }

    const type2Config = compatibilityMap[type2];
    if (type2Config && type2Config.compatibleWith.includes(type1)) {
      return {
        compatible: true,
        score: type2Config.score,
        reason: type2Config.reason,
      };
    }

    return { compatible: false, score: 0 };
  }

  /**
   * NUEVO: Calcula similitud por texto como último recurso
   */
  private static calculateTextSimilarity(fp1: ProductFingerprint, fp2: ProductFingerprint): number {
    // Si tenemos números similares (nivel, volumen) dar algún score
    if (fp1.level && fp2.level) {
      const levelDiff = Math.abs(fp1.level - fp2.level);
      if (levelDiff <= 2) {
        return Math.max(15 - levelDiff * 2, 5); // 15% si iguales, 13% si diff=1, etc.
      }
    }

    if (fp1.volume && fp2.volume && fp1.volume === fp2.volume) {
      return 15;
    }

    if (fp1.tone && fp2.tone && fp1.tone === fp2.tone) {
      return 10;
    }

    return 0;
  }

  /**
   * Busca equivalencias conocidas entre diferentes nomenclaturas
   */
  static areProductsEquivalent(name1: string, name2: string): boolean {
    const equivalences = [
      // Oxidantes
      [
        'oxidante',
        'developer',
        'oxydant',
        'oxidant',
        'peróxido',
        'peroxide',
        'oxigenada',
        'welloxon',
      ],
      // Decolorantes
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener', 'blanqueador', 'blondor'],
      // Protectores
      ['olaplex', 'wellaplex', 'fibreplex', 'smartbond', 'bond'],
      // Volúmenes
      ['10 vol', '3%', '10v', '10 volúmenes'],
      ['20 vol', '6%', '20v', '20 volúmenes'],
      ['30 vol', '9%', '30v', '30 volúmenes'],
      ['40 vol', '12%', '40v', '40 volúmenes'],
    ];

    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    for (const group of equivalences) {
      const has1 = group.some(term => lower1.includes(term));
      const has2 = group.some(term => lower2.includes(term));
      if (has1 && has2) {
        return true;
      }
    }

    return false;
  }
}
