import React, { useState, useEffect, useRef } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';
import {
  Camera,
  Zap,
  Check,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Target,
  Palette,
  Microscope,
  Sparkles,
  FlaskConical,
} from 'lucide-react-native';
// Gesture handling temporarily simplified for compatibility
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/service/hooks/usePhotoAnalysis';
import { LoadingState } from '@/components/base/LoadingState';
import { CameraCapture } from '@/components/camera/CameraCapture';
import { ConfidenceIndicator } from '@/components/ai/ConfidenceIndicator';
import { spacing } from '@/constants/theme';

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

interface SimpleDiagnosisStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
}

type DiagnosisPhase = 'capture' | 'validation' | 'refinement';

export const SimpleDiagnosisStep: React.FC<SimpleDiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave: _onSave,
}) => {
  const [currentPhase, setCurrentPhase] = useState<DiagnosisPhase>('capture');
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [aiConfidence, setAiConfidence] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showCamera, setShowCamera] = useState(false);

  // Legacy Animation values (maintaining compatibility)
  const progressAnim = useRef(new Animated.Value(0)).current;
  const _fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;

  // Gesture handling disabled temporarily

  const phases: DiagnosisPhase[] = ['capture', 'validation', 'refinement'];
  const currentPhaseIndex = phases.indexOf(currentPhase);

  const {
    isAnalyzing,
    analysisResult,
    performAnalysis,
    takePhoto,
    pickMultipleImages: _pickMultipleImages,
  } = usePhotoAnalysis();

  // Progressive loading messages
  useEffect(() => {
    if (isAnalyzing) {
      const messages = [
        'Detectando color de cabello...',
        'Analizando estructura capilar...',
        'Calculando niveles por zona...',
        'Generando recomendaciones...',
      ];

      let index = 0;
      const interval = setInterval(() => {
        if (index < messages.length) {
          setLoadingMessage(messages[index]);
          Animated.timing(progressAnim, {
            toValue: (index + 1) / messages.length,
            duration: 500,
            useNativeDriver: false,
          }).start();
          index++;
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isAnalyzing, progressAnim]);

  // Handle AI analysis completion
  useEffect(() => {
    if (analysisResult && !isAnalyzing) {
      setAiConfidence(analysisResult.overallConfidence || 85);

      // Animate success
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto-progress to validation phase
      setTimeout(() => {
        setCurrentPhase('validation');
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }, 500);
    }
  }, [analysisResult, isAnalyzing, scaleAnim]);

  const handlePhotoCapture = async (photoUri?: string) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      let photo = photoUri;
      if (!photo) {
        photo = await takePhoto();
      }

      if (photo) {
        setCapturedPhotos(prev => [...prev, photo]);
        setShowCamera(false);

        // Auto-analyze after 3 photos or immediately if single photo
        if (capturedPhotos.length === 2 || capturedPhotos.length === 0) {
          handleAnalysis([...capturedPhotos, photo]);
        }
      }
    } catch (error) {
      logger.error('Error capturing photo:', error);
    }
  };

  const handleAnalysis = async (photos: string[]) => {
    if (photos.length === 0) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    await performAnalysis(photos[0], data.diagnosisData || {});
  };

  const handleValidation = (field: string, isCorrect: boolean) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (!isCorrect) {
      setValidationErrors(prev => [...prev, field]);
    } else {
      setValidationErrors(prev => prev.filter(f => f !== field));
    }
  };

  // Gesture handlers (simplified for now)

  const _goToNextPhase = () => {
    const nextIndex = Math.min(currentPhaseIndex + 1, phases.length - 1);
    setCurrentPhase(phases[nextIndex]);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const _goToPreviousPhase = () => {
    const prevIndex = Math.max(currentPhaseIndex - 1, 0);
    setCurrentPhase(phases[prevIndex]);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  // Animation disabled temporarily

  interface AIOverrideData {
    level?: number;
    tone?: string;
    condition?: string;
    grayPercentage?: number;
    [key: string]: unknown;
  }

  const handleAIOverride = (overrideData: AIOverrideData, justification: string) => {
    // Implement AI override logic
    // Debug logging removed for production
    onUpdate({
      diagnosisData: {
        ...data.diagnosisData,
        aiOverride: {
          data: overrideData,
          justification,
          timestamp: new Date().toISOString(),
        },
      },
    });
  };

  const renderCapturePhase = () => (
    <View style={styles.phaseContainer}>
      <View style={styles.header}>
        <View style={styles.titleWithIcon}>
          <Camera size={24} color={Colors.primary} />
          <Text style={styles.title}>Captura Rápida</Text>
        </View>
        <Text style={styles.subtitle}>Cliente: {data.client?.name || 'Sin seleccionar'}</Text>
      </View>

      <View style={styles.photoGuide}>
        <View style={styles.guideHeader}>
          <Target size={18} color={Colors.primary} />
          <Text style={styles.guideTitle}>TRUCO PRO: Captura en esta secuencia</Text>
        </View>

        {['Vista frontal (raíces)', 'Vista lateral (medios)', 'Vista puntas'].map(
          (label, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.photoButton, capturedPhotos[index] && styles.photoButtonCompleted]}
              onPress={() => setShowCamera(true)}
              disabled={isAnalyzing}
            >
              <View style={styles.photoButtonContent}>
                <View style={styles.photoNumberCircle}>
                  <Text style={styles.photoNumber}>{index + 1}</Text>
                </View>
                <Text style={styles.photoLabel}>{label}</Text>
                {capturedPhotos[index] ? (
                  <Check size={20} color={Colors.success} />
                ) : (
                  <Camera size={20} color={Colors.primary} />
                )}
              </View>
            </TouchableOpacity>
          )
        )}
      </View>

      {capturedPhotos.length >= 2 && !isAnalyzing && !analysisResult && (
        <Animated.View style={[styles.analyzeButton, { transform: [{ scale: scaleAnim }] }]}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => handleAnalysis(capturedPhotos)}
          >
            <Zap size={20} color={Colors.common.white} />
            <Text style={styles.primaryButtonText}>ANÁLISIS PROFESIONAL</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {isAnalyzing && (
        <LoadingState
          variant="contextual"
          context="photo-analysis"
          estimatedDuration={12}
          showProgress
          progress={(loadingMessage.length / 4) * 100}
          fullScreen={false}
        />
      )}
    </View>
  );

  const renderValidationPhase = () => (
    <ScrollView style={styles.phaseContainer}>
      <View style={styles.header}>
        <View style={styles.titleWithIcon}>
          <Sparkles size={24} color={Colors.primary} />
          <Text style={styles.title}>IA Analizó tu Cabello</Text>
        </View>
        <ConfidenceIndicator
          confidence={aiConfidence}
          reasoning={[
            'Imagen clara con buena iluminación',
            'Patrón de color bien definido',
            'Estructura capilar visible',
            'Coincidencia con base de datos',
          ]}
          analysisData={analysisResult}
          onOverride={handleAIOverride}
          context="diagnosis"
          showDetailed
        />
      </View>

      <View style={styles.validationSection}>
        <View style={styles.sectionHeader}>
          <Palette size={16} color={Colors.primary} />
          <Text style={styles.sectionTitle}>CARACTERÍSTICAS GENERALES</Text>
        </View>

        {analysisResult && (
          <>
            <ValidationRow
              label="Nivel"
              value={`${analysisResult.averageDepthLevel || 5} (${analysisResult.overallTone})`}
              onValidate={correct => handleValidation('level', correct)}
              isValid={!validationErrors.includes('level')}
            />

            <ValidationRow
              label="Reflejos"
              value={analysisResult.overallUndertone || 'Natural'}
              onValidate={correct => handleValidation('reflect', correct)}
              isValid={!validationErrors.includes('reflect')}
            />

            <ValidationRow
              label="Grosor"
              value={analysisResult.hairThickness || 'Medio'}
              onValidate={correct => handleValidation('thickness', correct)}
              isValid={!validationErrors.includes('thickness')}
            />
          </>
        )}
      </View>

      <View style={styles.validationSection}>
        <View style={styles.sectionHeader}>
          <Microscope size={16} color={Colors.primary} />
          <Text style={styles.sectionTitle}>ANÁLISIS POR ZONAS</Text>
        </View>

        {analysisResult?.zoneAnalysis && (
          <>
            <ZoneValidationRow
              zone="Raíces"
              data={analysisResult.zoneAnalysis.roots}
              hasWarning={false}
            />

            <ZoneValidationRow
              zone="Medios"
              data={analysisResult.zoneAnalysis.mids}
              hasWarning={analysisResult.zoneAnalysis.mids?.pigmentAccumulation}
            />

            <ZoneValidationRow
              zone="Puntas"
              data={analysisResult.zoneAnalysis.ends}
              hasWarning={analysisResult.zoneAnalysis.ends?.damage === 'Severo'}
            />
          </>
        )}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.primaryButton, styles.successButton]}
          onPress={onNext}
          disabled={validationErrors.length > 0}
        >
          <Check size={20} color={Colors.common.white} />
          <Text style={styles.primaryButtonText}>TODO CORRECTO - CONTINUAR</Text>
        </TouchableOpacity>

        {validationErrors.length > 0 && (
          <TouchableOpacity
            style={[styles.secondaryButton]}
            onPress={() => setCurrentPhase('refinement')}
          >
            <Text style={styles.secondaryButtonText}>AJUSTAR ZONAS</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );

  const renderRefinementPhase = () => (
    <View style={styles.phaseContainer}>
      <View style={styles.titleWithIcon}>
        <FlaskConical size={24} color={Colors.primary} />
        <Text style={styles.title}>Ajustes Finales</Text>
      </View>
      <Text style={styles.subtitle}>Solo los campos que necesitan corrección</Text>
      {/* Aquí iría el formulario de refinamiento simplificado */}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Phase Navigation */}
      <View style={styles.phaseNavigation}>
        {phases.map((phase, index) => (
          <TouchableOpacity
            key={phase}
            style={[
              styles.phaseIndicator,
              index === currentPhaseIndex && styles.phaseIndicatorActive,
              index < currentPhaseIndex && styles.phaseIndicatorCompleted,
            ]}
            onPress={() => {
              if (index <= currentPhaseIndex) {
                setCurrentPhase(phase);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
            }}
          >
            <Text
              style={[
                styles.phaseNumber,
                index === currentPhaseIndex && styles.phaseNumberActive,
                index < currentPhaseIndex && styles.phaseNumberCompleted,
              ]}
            >
              {index < currentPhaseIndex ? '✓' : index + 1}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Swipe hint */}
      <View style={styles.swipeHint}>
        {currentPhaseIndex > 0 && (
          <View style={styles.swipeDirection}>
            <ArrowLeft size={16} color={Colors.textSecondary} />
            <Text style={styles.swipeText}>Desliza para volver</Text>
          </View>
        )}
        {currentPhaseIndex < phases.length - 1 && (
          <View style={styles.swipeDirection}>
            <Text style={styles.swipeText}>Desliza para continuar</Text>
            <ArrowRight size={16} color={Colors.textSecondary} />
          </View>
        )}
      </View>

      <View style={styles.contentContainer}>
        {currentPhase === 'capture' && renderCapturePhase()}
        {currentPhase === 'validation' && renderValidationPhase()}
        {currentPhase === 'refinement' && renderRefinementPhase()}
      </View>

      {/* Camera Modal */}
      {showCamera && (
        <View style={styles.cameraModal}>
          <CameraCapture
            onCapture={handlePhotoCapture}
            onCancel={() => setShowCamera(false)}
            captureMode="single"
            analysisContext="hair-analysis"
            showGuides
          />
        </View>
      )}
    </View>
  );
};

// Validation Row Component
const ValidationRow: React.FC<{
  label: string;
  value: string;
  onValidate: (correct: boolean) => void;
  isValid: boolean;
}> = ({ label, value, onValidate, isValid }) => (
  <View style={[styles.validationRow, !isValid && styles.validationRowError]}>
    <View style={styles.validationLabel}>
      <Text style={styles.labelText}>{label}</Text>
      <Text style={styles.valueText}>{value}</Text>
    </View>
    <View style={styles.validationActions}>
      <TouchableOpacity
        style={[styles.validationButton, isValid && styles.validationButtonActive]}
        onPress={() => onValidate(true)}
      >
        <Check size={16} color={isValid ? Colors.success : Colors.textLight} />
        <Text style={styles.validationButtonText}>Correcto</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.validationButton} onPress={() => onValidate(false)}>
        <AlertCircle size={16} color={Colors.warning} />
        <Text style={styles.validationButtonText}>Cambiar</Text>
      </TouchableOpacity>
    </View>
  </View>
);

// Zone Validation Row Component
interface ZoneData {
  level?: number;
  tone?: string;
  condition?: string;
  confidence?: number;
  [key: string]: unknown;
}

const ZoneValidationRow: React.FC<{
  zone: string;
  data: ZoneData;
  hasWarning: boolean;
}> = ({ zone, data, hasWarning }) => (
  <View style={styles.zoneRow}>
    <View style={styles.zoneHeader}>
      {hasWarning ? (
        <AlertCircle size={16} color={Colors.warning} />
      ) : (
        <Check size={16} color={Colors.success} />
      )}
      <Text style={styles.zoneTitle}>{zone}</Text>
    </View>
    <Text style={styles.zoneDescription}>
      Nivel {data?.level || data?.depth || 5}
      {data?.grayPercentage > 0 && `, ${data.grayPercentage}% canas`}
      {hasWarning && ' - Requiere atención'}
    </Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  phaseContainer: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 24,
  },
  titleWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textLight,
  },
  photoGuide: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  guideHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  guideTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  photoButton: {
    backgroundColor: Colors.backgroundLight,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
  },
  photoButtonCompleted: {
    borderColor: Colors.success,
    backgroundColor: Colors.success + '10',
  },
  photoButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  photoNumberCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
  photoLabel: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.text,
  },
  analyzeButton: {
    marginTop: 16,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  successButton: {
    backgroundColor: Colors.success,
  },
  primaryButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    borderWidth: 2,
    borderColor: Colors.primary,
    padding: 14,
    borderRadius: 12,
    marginTop: 12,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  // loadingContainer: {
  //   alignItems: 'center',
  //   padding: 32,
  // },
  // loadingText: {
  //   marginTop: 16,
  //   fontSize: 16,
  //   color: Colors.text,
  // },
  // progressBar: {
  //   width: '100%',
  //   height: 4,
  //   backgroundColor: Colors.backgroundLight,
  //   borderRadius: 2,
  //   marginTop: 16,
  //   overflow: 'hidden',
  // },
  // progressFill: {
  //   height: '100%',
  //   backgroundColor: Colors.primary,
  //   borderRadius: 2,
  // },
  // confidenceBadge: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: Colors.success + '20',
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 20,
  //   marginTop: 8,
  //   alignSelf: 'flex-start',
  // },
  // confidenceText: {
  //   marginLeft: 6,
  //   color: Colors.success,
  //   fontWeight: '600',
  // },
  validationSection: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textLight,
  },
  validationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.backgroundLight,
  },
  validationRowError: {
    backgroundColor: Colors.error + '10',
  },
  validationLabel: {
    flex: 1,
  },
  labelText: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 4,
  },
  valueText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  validationActions: {
    flexDirection: 'row',
    gap: 8,
  },
  validationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.backgroundLight,
    gap: 4,
  },
  validationButtonActive: {
    backgroundColor: Colors.success + '20',
  },
  validationButtonText: {
    fontSize: 14,
    color: Colors.text,
  },
  zoneRow: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.backgroundLight,
  },
  zoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  zoneTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  zoneDescription: {
    fontSize: 14,
    color: Colors.textLight,
    marginLeft: 24,
  },
  actionButtons: {
    padding: 20,
  },

  // Phase navigation styles
  phaseNavigation: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.backgroundLight,
    gap: spacing.lg,
  },
  phaseIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.common.transparent,
  },
  phaseIndicatorActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  phaseIndicatorCompleted: {
    backgroundColor: Colors.success,
    borderColor: Colors.success,
  },
  phaseNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  phaseNumberActive: {
    color: Colors.light.textLight,
  },
  phaseNumberCompleted: {
    color: Colors.light.textLight,
  },

  // Swipe hint styles
  swipeHint: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.backgroundLight,
    minHeight: 32,
  },
  swipeDirection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  swipeText: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },

  // Content container
  contentContainer: {
    flex: 1,
  },

  // Camera modal
  cameraModal: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
    backgroundColor: Colors.light.text,
  },
});
