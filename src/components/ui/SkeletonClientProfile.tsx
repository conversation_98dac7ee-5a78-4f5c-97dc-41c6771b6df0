import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useAnimationsEnabled } from '@/stores/whimsy-store';
import { spacing, radius } from '@/constants/theme';
import Colors from '@/constants/colors';

interface SkeletonClientProfileProps {
  testID?: string;
}

export function SkeletonClientProfile({
  testID = 'skeleton-client-profile',
}: SkeletonClientProfileProps) {
  const animationsEnabled = useAnimationsEnabled();
  const shimmerValue = useSharedValue(0);

  React.useEffect(() => {
    if (animationsEnabled) {
      shimmerValue.value = withRepeat(
        withSequence(withTiming(1, { duration: 1000 }), withTiming(0, { duration: 1000 })),
        -1,
        false
      );
    } else {
      shimmerValue.value = 0.6;
    }
  }, [shimmerValue, animationsEnabled]);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(shimmerValue.value, [0, 1], [0.3, 0.6]);
    return {
      opacity,
    };
  });

  const createSkeletonElement = (width: number | string, height: number, style?: ViewStyle) => (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: Colors.light.border,
          borderRadius: radius.sm,
        },
        shimmerStyle,
        style,
      ]}
    />
  );

  return (
    <View style={styles.container} testID={testID}>
      {/* Header Section */}
      <View style={styles.header}>
        {/* Avatar skeleton */}
        <View style={styles.avatarSkeleton}>
          <Animated.View style={[styles.avatar, shimmerStyle]} />
        </View>

        {/* Name skeleton */}
        {createSkeletonElement(150, 24, { marginBottom: spacing.xs })}

        {/* Since date skeleton */}
        {createSkeletonElement(120, 16, { marginBottom: spacing.sm })}

        {/* Risk badge skeleton */}
        {createSkeletonElement(100, 26, { borderRadius: radius.lg })}
      </View>

      {/* Info Card Skeleton */}
      <View style={styles.infoCard}>
        {/* Contact info skeletons */}
        <View style={styles.infoItem}>
          {createSkeletonElement(16, 16, { borderRadius: 8, marginRight: spacing.sm })}
          {createSkeletonElement(180, 16)}
        </View>

        <View style={styles.infoItem}>
          {createSkeletonElement(16, 16, { borderRadius: 8, marginRight: spacing.sm })}
          {createSkeletonElement(140, 16)}
        </View>

        <View style={styles.infoItem}>{createSkeletonElement(160, 16)}</View>

        {/* Stats skeleton */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            {createSkeletonElement(30, 20, { marginBottom: spacing.xs })}
            {createSkeletonElement(60, 14)}
          </View>
          <View style={styles.statItem}>
            {createSkeletonElement(30, 20, { marginBottom: spacing.xs })}
            {createSkeletonElement(70, 14)}
          </View>
          <View style={styles.statItem}>
            {createSkeletonElement(35, 20, { marginBottom: spacing.xs })}
            {createSkeletonElement(50, 14)}
          </View>
        </View>
      </View>

      {/* Action Buttons Skeleton */}
      <View style={styles.actionButtons}>
        {createSkeletonElement('48%', 44, { borderRadius: radius.lg })}
        {createSkeletonElement('48%', 44, { borderRadius: radius.lg })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
  },
  header: {
    backgroundColor: Colors.light.primary,
    paddingTop: 60,
    paddingBottom: spacing.xl,
    alignItems: 'center',
  },
  avatarSkeleton: {
    marginBottom: spacing.md,
    marginTop: spacing.lg,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.primaryTransparent20,
  },
  infoCard: {
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    padding: spacing.lg,
    margin: spacing.md,
    marginTop: -spacing.lg,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  statItem: {
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
});
