import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';
import { generateLocalId } from './sync-queue-store';
import { clientHistoryService } from '@/services/clientHistoryService';
import {
  checkAllergies,
  checkPatchTests,
  checkConsent,
  convertSupabaseConsentToLocal,
} from '@/utils/clientHistoryUtils';
import {
  ClientHistoryState,
  ClientHistoryProfile,
  ClientAllergy,
  ClientPreference,
  HairEvolution,
  PreviousFormula,
  PatchTest,
  ConsentRecord,
} from '@/types/client-history.types';

const historyLogger = logger.withContext('ClientHistoryStore');

export const useClientHistoryStore = create<ClientHistoryState>()(
  persist(
    (set, get) => ({
      clientProfiles: {},
      isLoading: false,
      isInitialized: false,

      async loadClientHistory(clientId: string) {
        historyLogger.info('Loading history', { clientId });
        set({ isLoading: true });
        try {
          const profileData = await clientHistoryService.loadClientHistory(clientId);
          if (profileData) {
            set(state => ({
              clientProfiles: {
                ...state.clientProfiles,
                [clientId]: { ...state.clientProfiles[clientId], ...profileData },
              },
            }));
          }
        } catch (error) {
          historyLogger.error('Failed to load client history', error);
          get().initializeClientProfile(clientId); // Initialize empty on error
        } finally {
          set({ isLoading: false, isInitialized: true });
        }
      },

      getClientProfile: (clientId: string) => {
        return get().clientProfiles[clientId] || null;
      },

      updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => {
        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: { ...state.clientProfiles[clientId], ...profile },
          },
        }));
      },

      addAllergy: (clientId: string, allergy: ClientAllergy) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                allergies: [...profile.allergies, allergy],
                riskLevel: get().calculateRiskLevel(clientId),
              },
            },
          };
        });
        historyLogger.info('Allergy added', { clientId, substance: allergy.substance });
      },

      addPreference: (clientId: string, preference: ClientPreference) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: { ...profile, preferences: [...profile.preferences, preference] },
            },
          };
        });
      },

      addHairEvolution: (clientId: string, evolution: HairEvolution) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: { ...profile, hairEvolution: [evolution, ...profile.hairEvolution] },
            },
          };
        });
      },

      async addPreviousFormula(clientId: string, formula: PreviousFormula) {
        // This action seems to be for manually adding formulas, which is not fully implemented in the service.
        // For now, we'll keep the local update logic.
        const tempId = generateLocalId('formula');
        const tempFormula = {
          ...formula,
          id: tempId,
          _syncStatus: 'pending' as const,
          _localId: tempId,
        };

        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                previousFormulas: [tempFormula, ...profile.previousFormulas],
              },
            },
          };
        });
        // Syncing logic would call the service here.
      },

      addPatchTest: (clientId: string, test: PatchTest) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                patchTests: [test, ...profile.patchTests],
                riskLevel: get().calculateRiskLevel(clientId),
              },
            },
          };
        });
        historyLogger.info('Patch test added', { clientId, result: test.result });
      },

      async addConsentRecord(clientId: string, consent: ConsentRecord) {
        const tempId = generateLocalId('consent');
        const tempConsent = {
          ...consent,
          id: tempId,
          _syncStatus: 'pending' as const,
          _localId: tempId,
        };

        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;
          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: { ...profile, consentRecords: [tempConsent, ...profile.consentRecords] },
            },
          };
        });

        const result = await clientHistoryService.addConsentRecord(clientId, consent);

        if (result) {
          set(state => {
            const profile = state.clientProfiles[clientId];
            if (!profile) return state;
            return {
              clientProfiles: {
                ...state.clientProfiles,
                [clientId]: {
                  ...profile,
                  consentRecords: profile.consentRecords.map(c =>
                    c.id === tempId ? convertSupabaseConsentToLocal(result) : c
                  ),
                },
              },
            };
          });
        }
      },

      getRecommendationsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];
        // This logic can also be moved to utils if it gets more complex
        const recommendations: string[] = [];
        const successfulFormulas = profile.previousFormulas.filter(f => f.satisfaction >= 4);
        if (successfulFormulas.length > 0) {
          const mostSuccessful = successfulFormulas.sort(
            (a, b) => b.satisfaction - a.satisfaction
          )[0];
          recommendations.push(
            `Fórmula exitosa anterior: ${mostSuccessful.brand} ${mostSuccessful.line}`
          );
        }
        return recommendations;
      },

      getWarningsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const warnings: string[] = [];
        warnings.push(...checkAllergies(profile.allergies));
        warnings.push(...checkPatchTests(profile.patchTests));
        const lastConsent = get().getLastConsent(clientId);
        warnings.push(...checkConsent(lastConsent));

        if (profile.riskLevel === 'alto') {
          warnings.push(`⚠️ Cliente de alto riesgo - Extremar precauciones`);
        }

        return warnings;
      },

      calculateRiskLevel: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return 'bajo';

        let riskScore = 0;
        riskScore += profile.allergies.filter(a => a.severity === 'severa').length * 3;
        riskScore += profile.allergies.filter(a => a.severity === 'moderada').length * 2;
        riskScore += profile.patchTests.filter(t => t.result === 'positivo').length * 4;
        riskScore += profile.previousFormulas.filter(f => f.satisfaction <= 2).length * 1;

        if (riskScore >= 6) return 'alto';
        if (riskScore >= 3) return 'medio';
        return 'bajo';
      },

      getCompatibleFormulas: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];
        return profile.previousFormulas
          .filter(f => f.satisfaction >= 3)
          .sort((a, b) => b.satisfaction - a.satisfaction);
      },

      getLastPatchTest: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        return profile?.patchTests?.[0] || null;
      },

      getLastConsent: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        return profile?.consentRecords?.[0] || null;
      },

      initializeClientProfile: (clientId: string) => {
        if (get().clientProfiles[clientId]) return;
        const newProfile: ClientHistoryProfile = {
          clientId,
          allergies: [],
          preferences: [],
          hairEvolution: [],
          previousFormulas: [],
          patchTests: [],
          consentRecords: [],
          riskLevel: 'bajo',
          totalServices: 0,
          averageSatisfaction: null,
          preferredBrands: [],
          avoidedIngredients: [],
          specialNotes: [],
        };
        set(state => ({ clientProfiles: { ...state.clientProfiles, [clientId]: newProfile } }));
        historyLogger.info('Client profile initialized', { clientId });
      },

      async saveCompletedService(serviceData: any) {
        historyLogger.info('Saving completed service via service', {
          clientId: serviceData.clientId,
        });
        try {
          const newServiceDetails = await clientHistoryService.saveCompletedService(serviceData);
          // Optionally, update local state after save
          await get().loadClientHistory(serviceData.clientId); // Reload history to get the new service
          return newServiceDetails;
        } catch (error) {
          historyLogger.error('Error saving service via service', error);
          throw error;
        }
      },

      async getServiceDetails(serviceId: string) {
        return clientHistoryService.getServiceDetails(serviceId);
      },

      async syncWithSupabase() {
        historyLogger.info('Syncing all clients via service');
        // The service layer will call loadClientHistory, which will trigger state updates
        await clientHistoryService.syncWithSupabase();
      },

      clearAllProfiles: () => {
        historyLogger.info('Clearing all client profiles');
        set({ clientProfiles: {}, isInitialized: false });
      },
    }),
    {
      name: 'client-history-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        clientProfiles: state.clientProfiles,
        isInitialized: state.isInitialized,
      }),
    }
  )
);
