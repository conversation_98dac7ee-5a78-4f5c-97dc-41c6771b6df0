import { useCallback } from 'react';
import { <PERSON><PERSON>, Animated, ScrollView } from 'react-native';
import * as Haptics from 'expo-haptics';
import { useSafeAsync, useTimer } from '@/utils/memory-cleanup';
import { useChatStore, ChatAttachment } from '@/stores/chat-store';
import { logger } from '@/utils/logger';

interface UseMessageSenderProps {
  activeConversationId: string | null;
  isSending: boolean;
  scrollViewRef: React.RefObject<ScrollView>;
  sendButtonScale: Animated.Value;
  animateButtonPress: (animValue: Animated.Value, callback?: () => void) => void;
}

interface UseMessageSenderReturn {
  handleSend: (messageToSend: string, attachmentsToSend: ChatAttachment[]) => Promise<void>;
}

export const useMessageSender = ({
  activeConversationId,
  isSending,
  scrollViewRef,
  sendButtonScale,
  animateButtonPress,
}: UseMessageSenderProps): UseMessageSenderReturn => {
  const { execute: safeExecute } = useSafeAsync();
  const { setTimeout: safeSetTimeout } = useTimer();

  const handleSend = useCallback(
    async (messageToSend: string, attachmentsToSend: ChatAttachment[]) => {
      if (!messageToSend.trim() || isSending) return;

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      animateButtonPress(sendButtonScale);

      try {
        const chatStore = useChatStore.getState();
        await safeExecute(() =>
          chatStore.sendMessage(
            messageToSend,
            activeConversationId || undefined,
            attachmentsToSend.length > 0 ? attachmentsToSend : undefined
          )
        );

        safeSetTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        logger.error('Error sending message', 'ChatGPTInterface', error);
        Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
      }
    },
    [
      isSending,
      activeConversationId,
      safeExecute,
      safeSetTimeout,
      scrollViewRef,
      sendButtonScale,
      animateButtonPress,
    ]
  );

  return {
    handleSend,
  };
};
