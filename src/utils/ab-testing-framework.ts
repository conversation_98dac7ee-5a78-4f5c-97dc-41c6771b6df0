/**
 * A/B Testing Framework with Feature Flags
 * Enables gradual rollout and automatic rollback on metric degradation
 */

import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Experiment {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'rolled_back';
  startDate: Date;
  endDate?: Date;
  variants: Variant[];
  metrics: MetricDefinition[];
  rolloutPercentage: number;
  successCriteria: SuccessCriteria;
}

export interface Variant {
  id: string;
  name: string;
  description: string;
  weight: number; // 0-100, sum must be 100
  config: Record<string, unknown>; // Feature configuration
  isControl: boolean;
}

export interface MetricDefinition {
  name: string;
  type: 'conversion' | 'engagement' | 'performance' | 'quality';
  target: number;
  threshold: number; // Minimum acceptable value
}

export interface SuccessCriteria {
  minSampleSize: number;
  confidenceLevel: number; // 90, 95, 99
  minImprovementPercentage: number;
}

export interface ExperimentResult {
  variantId: string;
  metrics: {
    [key: string]: {
      value: number;
      sampleSize: number;
      confidence: number;
      isSignificant: boolean;
    };
  };
  recommendation: 'continue' | 'stop' | 'rollback' | 'deploy';
}

interface VariantMetrics {
  [metricName: string]: {
    values: number[];
    sum: number;
    count: number;
  };
}

export class ABTestingFramework {
  private static experiments: Map<string, Experiment> = new Map();
  private static userVariants: Map<string, string> = new Map();
  private static metricsBuffer: Record<string, unknown>[] = [];
  private static flushInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize framework and load active experiments
   */
  static async initialize(): Promise<void> {
    // Load active experiments from database
    const { data: experiments } = await supabase
      .from('experiments')
      .select('*')
      .eq('status', 'running');

    for (const exp of experiments || []) {
      this.experiments.set(exp.id, exp);
    }

    // Load user's assigned variants from storage
    const storedVariants = await AsyncStorage.getItem('ab_test_variants');
    if (storedVariants) {
      const variants = JSON.parse(storedVariants);
      Object.entries(variants).forEach(([expId, variantId]) => {
        this.userVariants.set(expId, variantId as string);
      });
    }

    // Start metrics flush interval
    this.startMetricsFlush();
  }

  /**
   * Get variant for user in experiment
   */
  static getVariant(experimentId: string, userId: string): Variant | null {
    const experiment = this.experiments.get(experimentId);
    if (!experiment || experiment.status !== 'running') {
      return null;
    }

    // Check if user already assigned to variant
    const assignedVariantId = this.userVariants.get(`${experimentId}:${userId}`);
    if (assignedVariantId) {
      return experiment.variants.find(v => v.id === assignedVariantId) || null;
    }

    // Check rollout percentage
    if (Math.random() * 100 > experiment.rolloutPercentage) {
      // User not in experiment
      return null;
    }

    // Assign user to variant based on weights
    const variant = this.assignVariant(experiment, userId);
    if (variant) {
      this.userVariants.set(`${experimentId}:${userId}`, variant.id);
      this.persistUserVariants();
    }

    return variant;
  }

  /**
   * Check if feature flag is enabled
   */
  static isFeatureEnabled(featureKey: string, userId: string, defaultValue = false): boolean {
    // Check if feature is part of an experiment
    for (const [expId, experiment] of this.experiments) {
      if (experiment.status !== 'running') continue;

      const variant = this.getVariant(expId, userId);
      if (variant && variant.config[featureKey] !== undefined) {
        return variant.config[featureKey];
      }
    }

    // Check global feature flags
    return this.getGlobalFeatureFlag(featureKey, defaultValue);
  }

  /**
   * Track metric for experiment
   */
  static trackMetric(
    experimentId: string,
    userId: string,
    metricName: string,
    value: number
  ): void {
    const variant = this.getVariant(experimentId, userId);
    if (!variant) return;

    this.metricsBuffer.push({
      experiment_id: experimentId,
      variant_id: variant.id,
      user_id: userId,
      metric_name: metricName,
      value,
      timestamp: new Date().toISOString(),
    });

    // Check if we should flush
    if (this.metricsBuffer.length >= 100) {
      this.flushMetrics();
    }
  }

  /**
   * Analyze experiment results
   */
  static async analyzeExperiment(experimentId: string): Promise<ExperimentResult[]> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) throw new Error('Experiment not found');

    const results: ExperimentResult[] = [];

    // Process variants in parallel for better performance
    const variantAnalyses = await Promise.all(
      experiment.variants.map(async variant => {
        const metrics = await this.getVariantMetrics(experimentId, variant.id);
        return { variant, metrics };
      })
    );

    for (const { variant, metrics } of variantAnalyses) {
      const result: ExperimentResult = {
        variantId: variant.id,
        metrics: {},
        recommendation: 'continue',
      };

      // Analyze each metric
      for (const metricDef of experiment.metrics) {
        const metricData = metrics[metricDef.name];
        if (!metricData) continue;

        const analysis = this.analyzeMetric(metricData, metricDef, experiment.successCriteria);

        result.metrics[metricDef.name] = analysis;

        // Check if metric is below threshold
        if (analysis.value < metricDef.threshold) {
          result.recommendation = 'rollback';
        }
      }

      // Check if we have enough data
      const totalSamples = Object.values(result.metrics).reduce((sum, m) => sum + m.sampleSize, 0);

      if (totalSamples < experiment.successCriteria.minSampleSize) {
        result.recommendation = 'continue';
      } else if (result.recommendation !== 'rollback') {
        // Check if variant is winning
        if (!variant.isControl && this.isWinning(result, experiment)) {
          result.recommendation = 'deploy';
        }
      }

      results.push(result);
    }

    // Auto-rollback if needed
    const shouldRollback = results.some(r => r.recommendation === 'rollback');
    if (shouldRollback) {
      await this.rollbackExperiment(experimentId);
    }

    return results;
  }

  /**
   * Rollback experiment
   */
  static async rollbackExperiment(experimentId: string): Promise<void> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) return;

    // Update status
    experiment.status = 'rolled_back';
    experiment.endDate = new Date();

    // Update in database
    await supabase
      .from('experiments')
      .update({
        status: 'rolled_back',
        end_date: experiment.endDate,
        rollback_reason: 'Metrics below threshold',
      })
      .eq('id', experimentId);

    // Clear user assignments
    for (const key of this.userVariants.keys()) {
      if (key.startsWith(experimentId)) {
        this.userVariants.delete(key);
      }
    }

    this.persistUserVariants();

    // Send alert
    await this.sendRollbackAlert(experiment);
  }

  /**
   * Deploy winning variant
   */
  static async deployVariant(experimentId: string, variantId: string): Promise<void> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) return;

    const variant = experiment.variants.find(v => v.id === variantId);
    if (!variant) return;

    // Update feature flags with winning variant config in parallel
    const flagUpdatePromises = Object.entries(variant.config).map(([key, value]) =>
      this.setGlobalFeatureFlag(key, value)
    );
    await Promise.all(flagUpdatePromises);

    // Update experiment status
    experiment.status = 'completed';
    experiment.endDate = new Date();

    await supabase
      .from('experiments')
      .update({
        status: 'completed',
        end_date: experiment.endDate,
        winning_variant_id: variantId,
      })
      .eq('id', experimentId);

    // Clear experiment from active list
    this.experiments.delete(experimentId);
  }

  // Private helper methods

  private static assignVariant(experiment: Experiment, userId: string): Variant | null {
    const random = this.hashUserId(userId, experiment.id);
    let cumulative = 0;

    for (const variant of experiment.variants) {
      cumulative += variant.weight;
      if (random <= cumulative) {
        return variant;
      }
    }

    return experiment.variants[0]; // Fallback to first variant
  }

  private static hashUserId(userId: string, salt: string): number {
    const str = `${userId}:${salt}`;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash) % 100;
  }

  private static async getVariantMetrics(
    experimentId: string,
    variantId: string
  ): Promise<VariantMetrics> {
    const { data } = await supabase
      .from('experiment_metrics')
      .select('*')
      .eq('experiment_id', experimentId)
      .eq('variant_id', variantId);

    const metrics: Record<string, number> = {};
    for (const row of data || []) {
      if (!metrics[row.metric_name]) {
        metrics[row.metric_name] = {
          values: [],
          sum: 0,
          count: 0,
        };
      }
      metrics[row.metric_name].values.push(row.value);
      metrics[row.metric_name].sum += row.value;
      metrics[row.metric_name].count++;
    }

    return metrics;
  }

  private static analyzeMetric(
    data: Record<string, unknown>,
    definition: MetricDefinition,
    criteria: SuccessCriteria
  ): Record<string, unknown> {
    const avg = data.sum / data.count;
    const variance = this.calculateVariance(data.values, avg);
    const stdDev = Math.sqrt(variance);
    const standardError = stdDev / Math.sqrt(data.count);

    // Calculate confidence interval
    const zScore = this.getZScore(criteria.confidenceLevel);
    const marginOfError = zScore * standardError;

    return {
      value: avg,
      sampleSize: data.count,
      confidence: criteria.confidenceLevel,
      isSignificant: data.count >= criteria.minSampleSize && marginOfError < avg * 0.05, // 5% margin
    };
  }

  private static calculateVariance(values: number[], mean: number): number {
    const squaredDiffs = values.map(v => Math.pow(v - mean, 2));
    return squaredDiffs.reduce((sum, v) => sum + v, 0) / values.length;
  }

  private static getZScore(confidenceLevel: number): number {
    const zScores = {
      90: 1.645,
      95: 1.96,
      99: 2.576,
    };
    return zScores[confidenceLevel] || 1.96;
  }

  private static isWinning(result: ExperimentResult, experiment: Experiment): boolean {
    // Check if all metrics meet success criteria
    for (const [metricName, analysis] of Object.entries(result.metrics)) {
      const definition = experiment.metrics.find(m => m.name === metricName);
      if (!definition) continue;

      if (!analysis.isSignificant) return false;
      if (analysis.value < definition.target) return false;

      // Check improvement percentage for non-control variants
      const controlVariant = experiment.variants.find(v => v.isControl);
      if (controlVariant) {
        // Would need to compare with control variant metrics
        // Simplified for this implementation
        const improvement = ((analysis.value - definition.target) / definition.target) * 100;
        if (improvement < experiment.successCriteria.minImprovementPercentage) {
          return false;
        }
      }
    }

    return true;
  }

  private static async persistUserVariants(): Promise<void> {
    const variants: Record<string, Variant> = {};
    this.userVariants.forEach((value, key) => {
      variants[key] = value;
    });
    await AsyncStorage.setItem('ab_test_variants', JSON.stringify(variants));
  }

  private static async getGlobalFeatureFlag(key: string, defaultValue: boolean): Promise<boolean> {
    const { data } = await supabase.from('feature_flags').select('enabled').eq('key', key).single();

    return data?.enabled ?? defaultValue;
  }

  private static async setGlobalFeatureFlag(key: string, value: unknown): Promise<void> {
    await supabase.from('feature_flags').upsert({
      key,
      enabled: value,
      updated_at: new Date().toISOString(),
    });
  }

  private static startMetricsFlush(): void {
    this.flushInterval = setInterval(() => {
      if (this.metricsBuffer.length > 0) {
        this.flushMetrics();
      }
    }, 30000); // Flush every 30 seconds
  }

  private static async flushMetrics(): Promise<void> {
    if (this.metricsBuffer.length === 0) return;

    const metrics = [...this.metricsBuffer];
    this.metricsBuffer = [];

    try {
      await supabase.from('experiment_metrics').insert(metrics);
    } catch {
      // Metrics flush failed
      // Re-add metrics to buffer
      this.metricsBuffer.unshift(...metrics);
    }
  }

  private static async sendRollbackAlert(_experiment: Experiment): Promise<void> {
    // Send alert via your notification service
    // ALERT: Experiment rolled back due to metric degradation
    // Could integrate with Slack, email, etc.
    // TODO: Create alerts table or use external alerting system
    // ALERT: Should log to alerts table - experiment rolled back
    // await supabase
    //   .from('alerts')  // Table doesn't exist in current schema
    //   .insert({
    //     type: 'experiment_rollback',
    //     severity: 'high',
    //     message: `Experiment "${experiment.name}" automatically rolled back`,
    //     metadata: { experiment_id: experiment.id },
    //     created_at: new Date().toISOString()
    //   })
  }
}
