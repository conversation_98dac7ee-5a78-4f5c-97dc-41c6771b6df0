import React, { useState } from 'react';
import {
  Modal,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { X, Package, Plus, Minus, RefreshCw, type LucideIcon } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useInventoryStore } from '@/stores/inventory-store';
import { StockMovement, Product } from '@/types/inventory';

interface StockMovementsModalProps {
  visible: boolean;
  onClose: () => void;
  product: Product;
  onComplete?: () => void;
}

export default function StockMovementsModal({
  visible,
  onClose,
  product,
  onComplete,
}: StockMovementsModalProps) {
  const [movementType, setMovementType] = useState<StockMovement['type']>('entrada');
  const [quantity, setQuantity] = useState('');
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [inputMode, setInputMode] = useState<'units' | 'packages'>('units');
  const [packageCount, setPackageCount] = useState('');

  const { updateStock } = useInventoryStore();

  const movementTypes: Array<{
    type: StockMovement['type'];
    label: string;
    icon: LucideIcon;
    color: string;
  }> = [
    {
      type: 'entrada',
      label: 'Entrada',
      icon: Plus,
      color: Colors.light.success,
    },
    { type: 'salida', label: 'Salida', icon: Minus, color: Colors.light.error },
    {
      type: 'ajuste',
      label: 'Ajuste',
      icon: RefreshCw,
      color: Colors.light.primary,
    },
  ];

  const validateForm = () => {
    if (!quantity || parseFloat(quantity) <= 0) {
      Alert.alert('Error', 'La cantidad debe ser mayor a 0');
      return false;
    }

    if (!reason.trim()) {
      Alert.alert('Error', 'Debes especificar un motivo');
      return false;
    }

    const qty = parseFloat(quantity);
    if (movementType === 'salida' && qty > product.currentStock) {
      Alert.alert(
        'Stock Insuficiente',
        `Solo hay ${product.currentStock} ${product.unitType} disponibles`
      );
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Calculate final quantity based on input mode
      const finalQuantity =
        inputMode === 'packages' && packageCount && product.unitSize
          ? parseFloat(packageCount) * product.unitSize
          : parseFloat(quantity);

      await updateStock(product.id, finalQuantity, movementType, reason.trim());

      // Show specific success message based on movement type
      const displayQuantity =
        inputMode === 'packages' && packageCount
          ? `${packageCount} envases (${finalQuantity} ${product.unitType})`
          : `${finalQuantity} ${product.unitType}`;

      const successMessage =
        movementType === 'entrada'
          ? `Se agregaron ${displayQuantity} al inventario`
          : movementType === 'salida'
            ? `Se retiraron ${displayQuantity} del inventario`
            : `Stock ajustado en ${displayQuantity}`;

      Alert.alert('Éxito', successMessage);

      // Reset form
      setQuantity('');
      setPackageCount('');
      setReason('');
      setMovementType('entrada');
      setInputMode('units');

      onComplete?.();
      onClose();
    } catch {
      Alert.alert('Error', 'No se pudo registrar el movimiento');
    } finally {
      setIsLoading(false);
    }
  };

  const getDefaultReason = () => {
    switch (movementType) {
      case 'entrada':
        return 'Compra de inventario';
      case 'salida':
        return 'Uso manual';
      case 'ajuste':
        return 'Ajuste de inventario';
      default:
        return '';
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.backdrop}>
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <View style={styles.headerLeft}>
                <Package size={24} color={Colors.light.primary} />
                <Text style={styles.headerTitle}>Gestionar Stock</Text>
              </View>
              <TouchableOpacity onPress={onClose}>
                <X size={24} color={Colors.light.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.body}>
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productBrand}>{product.brand}</Text>
                <View style={styles.stockInfo}>
                  <Text style={styles.stockLabel}>Stock Actual:</Text>
                  <Text style={styles.stockValue}>
                    {product.currentStock} {product.unitType}
                  </Text>
                </View>
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Tipo de Movimiento</Text>
                <View style={styles.typeButtons}>
                  {movementTypes.map(type => {
                    const Icon = type.icon;
                    return (
                      <TouchableOpacity
                        key={type.type}
                        style={[
                          styles.typeButton,
                          movementType === type.type && {
                            backgroundColor: type.color + '20',
                            borderColor: type.color,
                          },
                        ]}
                        onPress={() => {
                          setMovementType(type.type);
                          setReason(getDefaultReason());
                        }}
                      >
                        <Icon
                          size={20}
                          color={movementType === type.type ? type.color : Colors.light.gray}
                        />
                        <Text
                          style={[
                            styles.typeButtonText,
                            movementType === type.type && { color: type.color },
                          ]}
                        >
                          {type.label}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Cantidad ({product.unitType})</Text>
                <TextInput
                  style={styles.input}
                  placeholder="0"
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="decimal-pad"
                />
                {quantity && (
                  <Text style={styles.helperText}>
                    Nuevo stock:{' '}
                    {movementType === 'salida'
                      ? product.currentStock - parseFloat(quantity || '0')
                      : movementType === 'entrada'
                        ? product.currentStock + parseFloat(quantity || '0')
                        : parseFloat(quantity || '0')}{' '}
                    {product.unitType}
                  </Text>
                )}
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Motivo</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  placeholder="Especifica el motivo del movimiento"
                  value={reason}
                  onChangeText={setReason}
                  multiline
                  numberOfLines={3}
                  onFocus={() => {
                    if (!reason) {
                      setReason(getDefaultReason());
                    }
                  }}
                />
              </View>
            </ScrollView>

            <View style={styles.footer}>
              <TouchableOpacity style={styles.cancelButton} onPress={onClose} disabled={isLoading}>
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
                onPress={handleSubmit}
                disabled={isLoading}
              >
                <Text style={styles.submitButtonText}>
                  {isLoading ? 'Guardando...' : 'Registrar Movimiento'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backdrop: {
    flex: 1,
    backgroundColor: Colors.light.modalOverlay,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  body: {
    padding: 20,
  },
  productInfo: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stockLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  stockValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.gray,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 5,
  },
  footer: {
    flexDirection: 'row',
    gap: 10,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  submitButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
});
