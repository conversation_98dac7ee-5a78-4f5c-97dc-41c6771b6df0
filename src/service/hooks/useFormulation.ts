import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { getDefaultBrandAndLine } from '@/utils/brand-preferences';
import { calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { ViabilityAnalysis, Formulation } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';

// Import specialized hooks
import { useFormulaCost } from './useFormulaCost';
import { useViabilityAnalysis } from './useViabilityAnalysis';
import { useStockValidation } from './useStockValidation';
import { useBrandConversion } from './useBrandConversion';
import { useFormulaGeneration } from './useFormulaGeneration';
import { useMultiSessionPlanning } from './useMultiSessionPlanning';

// Import utilities
import { stringifySteps } from '../utils/formulationHelpers';

export const useFormulation = () => {
  const { preferredBrandLines } = useAuthStore();
  const defaultBrand = getDefaultBrandAndLine(preferredBrandLines);

  // Core formulation state
  const [selectedBrand, setSelectedBrand] = useState(defaultBrand.brandName);
  const [selectedLine, setSelectedLine] = useState(defaultBrand.lineName);
  const [formula, setFormula] = useState('');
  const [formulationData, setFormulationData] = useState<Formulation | null>(null);
  const [isFormulaFromAI, setIsFormulaFromAI] = useState(true);

  // Use specialized hooks
  const { formulaCost, setFormulaCost, calculateFormulaCost } = useFormulaCost();
  const { viabilityAnalysis, setViabilityAnalysis, performViabilityAnalysis } =
    useViabilityAnalysis();
  const { stockValidation, setStockValidation, checkStockAvailability } = useStockValidation();
  const {
    showBrandModal,
    brandModalType,
    conversionMode,
    originalBrand,
    originalLine,
    originalFormula,
    setShowBrandModal,
    setBrandModalType,
    setConversionMode,
    setOriginalBrand,
    setOriginalLine,
    setOriginalFormula,
    getConversionContext,
  } = useBrandConversion();
  const {
    isGeneratingFormula,
    formulationError,
    setFormulationError,
    generateFormulaWithAI: generateFormulaAI,
    generateFallbackFormula,
  } = useFormulaGeneration();
  const { createMultiSessionPlan } = useMultiSessionPlanning();

  // Update brand and line when user preferences change
  useEffect(() => {
    const newDefault = getDefaultBrandAndLine(preferredBrandLines);
    setSelectedBrand(newDefault.brandName);
    setSelectedLine(newDefault.lineName);
    // Updated default brand preferences
  }, [preferredBrandLines]);

  // Legacy analyzeViability function for backward compatibility
  const analyzeViability = useCallback(
    (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
    ): ViabilityAnalysis => {
      return performViabilityAnalysis(analysisResult, desiredAnalysisResult, zoneColorAnalysis);
    },
    [performViabilityAnalysis]
  );

  // Main generateFormulaWithAI function that orchestrates all the specialized hooks
  const generateFormulaWithAI = useCallback(
    async (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
      clientId?: string,
      adjustmentContext?: string,
      clientContext?: { hairLengthCm?: number; hairDensity?: string; hairThickness?: string }
    ) => {
      try {
        // Generate formula using AI
        const { formula: generatedFormula, formulation: structuredFormulation } =
          await generateFormulaAI(
            analysisResult,
            desiredAnalysisResult,
            zoneColorAnalysis,
            selectedBrand,
            selectedLine,
            getConversionContext(),
            {
              clientId,
              adjustmentContext,
              clientContext,
            }
          );

        // Set the generated formula and formulation
        setFormula(generatedFormula);
        setIsFormulaFromAI(true);

        // Perform viability analysis
        const viability = performViabilityAnalysis(
          analysisResult,
          desiredAnalysisResult,
          zoneColorAnalysis
        );

        // Handle multi-session planning if needed
        let finalFormulation = structuredFormulation;
        if (viability && viability.factors.estimatedSessions > 1 && structuredFormulation) {
          finalFormulation = await createMultiSessionPlan(structuredFormulation, viability, {
            selectedBrand,
            selectedLine,
            zoneColorAnalysis,
            currentLevel: analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5,
            targetLevel:
              parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7,
            clientContext,
          });
        }

        setFormulationData(finalFormulation);

        // Calculate formula cost preferring structured steps
        let costSource = stringifySteps(finalFormulation?.steps);
        if (!costSource || costSource.trim() === '') costSource = generatedFormula || '';
        const cost = await calculateFormulaCost(costSource);
        setFormulaCost(cost);

        // Auto-validate stock if inventory control is enabled
        const salonConfig = useSalonConfigStore.getState();
        if (salonConfig.configuration.inventoryControlLevel === 'control-total') {
          const stockCheck = await checkStockAvailability(generatedFormula);
          if (stockCheck && !stockCheck.hasStock) {
            return `⚠️ Stock insuficiente: ${stockCheck.missingProducts.join(', ')}`;
          }
        }

        return '✅ Fórmula generada con IA';
      } catch {
        // Fallback to basic formula generation
        Alert.alert(
          '⚠️ Sin conexión con IA',
          `Error al generar fórmula con IA.\n\nGenerando fórmula de ejemplo. Por favor ajusta manualmente según tu criterio profesional.\n\nPara usar IA verifica:\n• Conexión a internet\n• Configuración de OpenAI en Supabase`,
          [{ text: 'Entendido', style: 'default' }]
        );

        // Generate fallback formula
        const fallbackFormula = await generateFallbackFormula(
          analysisResult,
          desiredAnalysisResult,
          selectedBrand,
          selectedLine
        );

        setFormula(fallbackFormula);
        setIsFormulaFromAI(false);

        const cost = await calculateFormulaCost(fallbackFormula);
        setFormulaCost(cost);

        return '⚠️ Fórmula de ejemplo generada. Ajusta manualmente.';
      }
    },
    [
      selectedBrand,
      selectedLine,
      getConversionContext,
      generateFormulaAI,
      performViabilityAnalysis,
      createMultiSessionPlan,
      calculateFormulaCost,
      checkStockAvailability,
      generateFallbackFormula,
      setFormulaCost,
    ]
  );

  // Effect to recalculate cost when formula changes
  useEffect(() => {
    if (!formula || formula.trim() === '') {
      setFormulaCost(null);
      return;
    }

    const timeoutId = setTimeout(() => {
      calculateFormulaCost(formula)
        .then(cost => {
          setFormulaCost(cost);
        })
        .catch(() => {
          // Cost calculation failed - using simple fallback
          const simpleCost = calculateSimpleFormulaCost(formula);
          setFormulaCost(simpleCost);
        });
    }, 500);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [formula, calculateFormulaCost, setFormulaCost]);

  return {
    // Brand and formula state
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,

    // Brand conversion state
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,

    // Analysis and cost state
    formulaCost,
    setFormulaCost,
    viabilityAnalysis,
    setViabilityAnalysis,
    stockValidation,
    setStockValidation,
    formulationError,
    setFormulationError,

    // Functions
    calculateFormulaCost,
    analyzeViability,
    checkStockAvailability,
    generateFormulaWithAI,
    formulationData,
    setFormulationData,
  };
};
