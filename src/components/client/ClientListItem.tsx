import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform } from 'react-native';
import { Link } from 'expo-router';
import { Eye, Edit, Trash2, AlertTriangle, Phone, Calendar } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { Client } from '@/stores/client-store';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface UserProfile {
  avatar_url?: string;
  [key: string]: unknown;
}

interface ClientListItemProps {
  client: Client;
  warnings: string[];
  recommendations: string[];
  profile: UserProfile | null;
  canDelete: boolean;
  onDelete: (id: string, name: string) => void;
  onView: (client: Client) => void;
}

const getRiskColor = (riskLevel: string) => {
  switch (riskLevel) {
    case 'alto':
      return BeautyMinimalTheme.semantic.status.error;
    case 'medio':
      return BeautyMinimalTheme.semantic.status.warning;
    default:
      return BeautyMinimalTheme.semantic.status.success;
  }
};

const linkStyle = Platform.select({
  web: { textDecoration: 'none' } as const,
  default: {},
}) as const;

const ClientListItem = React.memo<ClientListItemProps>(
  ({
    client,
    warnings,
    recommendations: _recommendations,
    profile,
    canDelete,
    onDelete,
    onView,
  }) => {
    return (
      <BeautyCard
        style={styles.clientCard}
        variant="default"
        testID={`client-item-${client.id}`}
        accessibilityLabel={`Cliente ${client.name}`}
        accessibilityHint="Ver detalles del cliente"
      >
        <View style={styles.cardContent}>
          <View style={styles.clientAvatar}>
            <Text style={styles.clientInitial}>{client.name.charAt(0)}</Text>
            {warnings.length > 0 && (
              <View style={styles.warningBadge}>
                <Text style={styles.warningBadgeText}>{warnings.length}</Text>
              </View>
            )}
          </View>

          <View style={styles.clientInfo}>
            <View style={styles.clientHeader}>
              <Text style={styles.clientName} numberOfLines={1}>
                {client.name}
              </Text>
              {profile && (
                <View
                  style={[
                    styles.riskBadge,
                    { backgroundColor: getRiskColor(profile.riskLevel) + '20' },
                  ]}
                >
                  <Text style={[styles.riskText, { color: getRiskColor(profile.riskLevel) }]}>
                    {profile.riskLevel.toUpperCase()}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.clientDetails}>
              <View style={styles.detailRow}>
                <Phone size={12} color={BeautyMinimalTheme.semantic.text.tertiary} />
                <Text style={styles.clientPhone} numberOfLines={1}>
                  {client.phone}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Calendar size={12} color={BeautyMinimalTheme.semantic.text.tertiary} />
                <Text style={styles.clientLastVisit} numberOfLines={1}>
                  {client.lastVisit || 'Sin visitas recientes'}
                </Text>
              </View>
            </View>

            {warnings.length > 0 && (
              <View style={styles.warningInfo}>
                <AlertTriangle size={12} color={BeautyMinimalTheme.semantic.status.error} />
                <Text style={styles.warningText} numberOfLines={1}>
                  {warnings[0]}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.viewButton]}
              onPress={() => onView(client)}
              accessibilityLabel="Ver perfil del cliente"
            >
              <Eye size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            </TouchableOpacity>
            <Link href={`/client/edit/${client.id}`} style={linkStyle}>
              <View
                style={[styles.actionButton, styles.editButton]}
                accessibilityLabel="Editar cliente"
              >
                <Edit size={16} color={BeautyMinimalTheme.semantic.text.secondary} />
              </View>
            </Link>
            {canDelete && (
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => onDelete(client.id, client.name)}
                accessibilityLabel="Eliminar cliente"
              >
                <Trash2 size={16} color={BeautyMinimalTheme.semantic.status.error} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </BeautyCard>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.client.id === nextProps.client.id &&
      prevProps.client.name === nextProps.client.name &&
      prevProps.client.phone === nextProps.client.phone &&
      prevProps.warnings.length === nextProps.warnings.length &&
      prevProps.recommendations.length === nextProps.recommendations.length &&
      prevProps.profile?.riskLevel === nextProps.profile?.riskLevel &&
      prevProps.canDelete === nextProps.canDelete
    );
  }
);

ClientListItem.displayName = 'ClientListItem';

const styles = StyleSheet.create({
  clientCard: {
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientAvatar: {
    width: 44,
    height: 44,
    borderRadius: BeautyMinimalTheme.radius.full,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: BeautyMinimalTheme.spacing.md,
    position: 'relative',
  },
  clientInitial: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  warningBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: BeautyMinimalTheme.semantic.status.error,
    borderRadius: BeautyMinimalTheme.radius.full,
    width: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  warningBadgeText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  clientInfo: {
    flex: 1,
    minWidth: 0, // Allow text to truncate
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  riskBadge: {
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
    paddingVertical: 2,
    borderRadius: BeautyMinimalTheme.radius.sm,
  },
  riskText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  clientDetails: {
    gap: BeautyMinimalTheme.spacing.xs / 2,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  clientPhone: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    flex: 1,
  },
  clientLastVisit: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    flex: 1,
  },
  warningInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '10',
    borderRadius: BeautyMinimalTheme.radius.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    gap: BeautyMinimalTheme.spacing.xs,
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  warningText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.status.error,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'column',
    gap: BeautyMinimalTheme.spacing.xs,
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },
  actionButton: {
    width: BeautyMinimalTheme.spacing.touchTarget.minimum,
    height: 32,
    borderRadius: BeautyMinimalTheme.radius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
  },
  editButton: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
  },
  deleteButton: {
    backgroundColor: BeautyMinimalTheme.semantic.status.error + '10',
  },
});

export default ClientListItem;
