import { useSalonConfigStore } from '@/stores/salon-config-store';

export function useRegionalUnits() {
  const {
    regionalConfig,
    formatVolume,
    formatWeight,
    formatCurrency,
    convertVolume,
    convertWeight,
    getUnitLabel,
    getTerminology,
  } = useSalonConfigStore();

  const isMetric = regionalConfig?.measurementSystem === 'metric';
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';

  // Helper to convert stored values (always in base units) to display values
  const toDisplayVolume = (mlValue: number): number => {
    if (volumeUnit === 'fl oz') {
      return convertVolume(mlValue, 'ml', 'fl oz');
    }
    return mlValue;
  };

  const toDisplayWeight = (gValue: number): number => {
    if (weightUnit === 'oz') {
      return convertWeight(gValue, 'g', 'oz');
    }
    return gValue;
  };

  // Helper to convert user input to base units for storage
  const toBaseVolume = (displayValue: number): number => {
    if (volumeUnit === 'fl oz') {
      return convertVolume(displayValue, 'fl oz', 'ml');
    }
    return displayValue;
  };

  const toBaseWeight = (displayValue: number): number => {
    if (weightUnit === 'oz') {
      return convertWeight(displayValue, 'oz', 'g');
    }
    return displayValue;
  };

  return {
    // Regional config
    isMetric,
    volumeUnit,
    weightUnit,

    // Formatting functions
    formatVolume,
    formatWeight,
    formatCurrency,

    // Conversion functions
    convertVolume,
    convertWeight,
    toDisplayVolume,
    toDisplayWeight,
    toBaseVolume,
    toBaseWeight,

    // Labels and terminology
    getUnitLabel,
    getTerminology,

    // Quick access to terminology
    developerTerm: regionalConfig?.developerTerminology || 'oxidante',
    colorTerm: regionalConfig?.colorTerminology || 'tinte',

    // Regional info
    currency: regionalConfig?.currency || 'EUR',
    currencySymbol: regionalConfig?.currencySymbol || '€',
    decimalSeparator: regionalConfig?.decimalSeparator || ',',
    thousandsSeparator: regionalConfig?.thousandsSeparator || '.',
  };
}
