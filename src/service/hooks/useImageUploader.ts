import { useCallback } from 'react';
import { <PERSON><PERSON>, Animated, ScrollView } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTimer } from '@/utils/memory-cleanup';
import { ChatAttachment } from '@/stores/chat-store';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';

interface UseImageUploaderProps {
  setPendingAttachments: React.Dispatch<React.SetStateAction<ChatAttachment[]>>;
  scrollViewRef: React.RefObject<ScrollView>;
  animateButtonPress: (animValue: Animated.Value, callback?: () => void) => void;
  attachButtonScale: Animated.Value;
  isSending: boolean;
  pendingAttachmentsLength: number;
}

interface UseImageUploaderReturn {
  handleImageUpload: (source: 'camera' | 'library') => Promise<void>;
}

export const useImageUploader = ({
  setPendingAttachments,
  scrollViewRef,
  animateButtonPress,
  attachButtonScale,
  isSending,
  pendingAttachmentsLength,
}: UseImageUploaderProps): UseImageUploaderReturn => {
  const { setTimeout: safeSetTimeout } = useTimer();

  const processImageDirectly = useCallback(
    async (asset: { uri: string; fileSize?: number }) => {
      try {
        const originalSizeMB = asset.fileSize
          ? (asset.fileSize / (1024 * 1024)).toFixed(1)
          : 'Desconocido';
        if (__DEV__) logger.info(`Processing image: ${originalSizeMB}MB original size`);

        const compressedBase64 = await ImageProcessor.compressForUpload(asset.uri, true, 'chat');
        const imageDataUrl = `data:image/jpeg;base64,${compressedBase64}`;

        const compressedSizeKB = Math.round(imageDataUrl.length / 1024);
        if (__DEV__) logger.info(`Compressed to: ${compressedSizeKB}KB`);

        const newAttachment: ChatAttachment = {
          type: 'image' as const,
          url: imageDataUrl,
          mimeType: 'image/jpeg',
        };

        setPendingAttachments(prev => {
          const combined = [...prev, newAttachment];
          if (combined.length > 3) {
            Alert.alert(
              'Límite de imágenes',
              'Máximo 3 imágenes por consulta. Se han seleccionado las primeras 3.',
              [{ text: 'OK' }]
            );
            return combined.slice(0, 3);
          }
          return combined;
        });

        safeSetTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        logger.error('Error processing image directly', 'useImageUploader', error);
        Alert.alert('Error', 'No se pudo procesar la imagen. Inténtalo de nuevo.');
      }
    },
    [setPendingAttachments, safeSetTimeout, scrollViewRef]
  );

  const handleImageUpload = useCallback(
    async (source: 'camera' | 'library') => {
      if (isSending || pendingAttachmentsLength >= 3) return;

      animateButtonPress(attachButtonScale);

      try {
        if (source === 'camera') {
          const { status } = await ImagePicker.requestCameraPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
            return;
          }

          const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ['images'],
            quality: 0.7,
            base64: false,
          });

          if (!result.canceled && result.assets && result.assets[0]) {
            await processImageDirectly(result.assets[0]);
          }
        } else {
          const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert(
              'Permisos necesarios',
              'Se necesitan permisos de galería para seleccionar fotos'
            );
            return;
          }

          const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            quality: 0.7,
            base64: false,
            allowsMultipleSelection: true,
            selectionLimit: 3,
          });

          if (!result.canceled && result.assets && result.assets.length > 0) {
            await Promise.all(result.assets.map(asset => processImageDirectly(asset)));
          }
        }
      } catch (error) {
        logger.error('Error in direct image upload', 'useImageUploader', error);
        Alert.alert('Error', 'No se pudo procesar la imagen. Inténtalo de nuevo.');
      }
    },
    [
      isSending,
      pendingAttachmentsLength,
      animateButtonPress,
      attachButtonScale,
      processImageDirectly,
    ]
  );

  return {
    handleImageUpload,
  };
};
