/**
 * Modern Chat Interface
 * Evolución del ChatInterface actual con mejoras en UX/UI
 * Microinteracciones fluidas y diseño conversacional moderno
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Image,
  Pressable,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  FadeIn,
  FadeOut,
  SlideInRight,
  SlideInLeft,
  Layout,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Send,
  MessageCircle,
  Camera,
  Image as ImageIcon,
  Mic,
  Plus,
  X,
  User,
  Bot,
  Check,
  CheckCheck,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

import DesignSystem from '@/constants/DesignSystem';
import { EnhancedButton } from './EnhancedButton';
import { EnhancedLoadingState } from './EnhancedLoadingStates';
import { shadows, radius, typography } from '@/constants/theme';

const { colors, typography, spacing, radius, shadows } = DesignSystem;
const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    name?: string;
  }>;
}

interface ModernChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (
    message: string,
    attachments?: Array<{
      type: 'image' | 'document';
      url: string;
      name?: string;
    }>
  ) => void;
  isTyping?: boolean;
  conversationTitle?: string;
  context?: string;
  onClose?: () => void;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const ModernChatInterface: React.FC<ModernChatInterfaceProps> = ({
  messages = [],
  onSendMessage,
  isTyping = false,
  conversationTitle = 'Asistente Salonier',
  context,
  onClose,
}) => {
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const textInputRef = useRef<TextInput>(null);

  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // Animation values
  const sendButtonScale = useSharedValue(1);
  const inputFocusScale = useSharedValue(1);
  const attachmentScale = useSharedValue(0);

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  useEffect(() => {
    // Animate attachment menu
    attachmentScale.value = withSpring(showAttachments ? 1 : 0);
  }, [showAttachments, attachmentScale]);

  const handleSendMessage = useCallback(() => {
    if (!inputText.trim() && selectedImages.length === 0) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Animate send button
    sendButtonScale.value = withSequence(withSpring(0.8), withSpring(1.1), withSpring(1));

    onSendMessage(inputText.trim(), selectedImages);
    setInputText('');
    setSelectedImages([]);
    setShowAttachments(false);
  }, [inputText, selectedImages, onSendMessage, sendButtonScale]);

  const handleInputFocus = () => {
    inputFocusScale.value = withSpring(1.02);
  };

  const handleInputBlur = () => {
    inputFocusScale.value = withSpring(1);
  };

  const toggleAttachments = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowAttachments(!showAttachments);
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isUser = message.role === 'user';
    const showAvatar = index === 0 || messages[index - 1]?.role !== message.role;

    const messageDelay = index * 50;

    return (
      <Animated.View
        key={message.id}
        entering={isUser ? SlideInRight.delay(messageDelay) : SlideInLeft.delay(messageDelay)}
        layout={Layout.springify()}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        {/* Avatar */}
        {showAvatar && !isUser && (
          <Animated.View entering={FadeIn.delay(messageDelay + 100)} style={styles.avatar}>
            <Bot size={18} color={colors.primary} />
          </Animated.View>
        )}

        <View style={styles.messageContent}>
          {/* Message bubble */}
          <Animated.View
            entering={FadeIn.delay(messageDelay + 150)}
            style={[styles.messageBubble, isUser ? styles.userBubble : styles.assistantBubble]}
          >
            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <View style={styles.attachmentsContainer}>
                {message.attachments.map((attachment, attachIndex) => (
                  <Image
                    key={attachIndex}
                    source={{ uri: attachment.url }}
                    style={styles.attachmentImage}
                  />
                ))}
              </View>
            )}

            {/* Message text */}
            <Text
              style={[
                styles.messageText,
                isUser ? styles.userMessageText : styles.assistantMessageText,
              ]}
            >
              {message.content}
            </Text>
          </Animated.View>

          {/* Message metadata */}
          <Animated.View
            entering={FadeIn.delay(messageDelay + 200)}
            style={[
              styles.messageMetadata,
              isUser ? styles.userMetadata : styles.assistantMetadata,
            ]}
          >
            <Text style={styles.timestampText}>
              {message.timestamp.toLocaleTimeString('es-ES', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>

            {isUser && (
              <View style={styles.messageStatus}>
                {message.status === 'sending' && (
                  <EnhancedLoadingState variant="spinner" size="sm" />
                )}
                {message.status === 'sent' && <Check size={14} color={colors.text.tertiary} />}
                {message.status === 'delivered' && (
                  <CheckCheck size={14} color={colors.text.tertiary} />
                )}
                {message.status === 'read' && <CheckCheck size={14} color={colors.primary} />}
              </View>
            )}
          </Animated.View>
        </View>

        {showAvatar && isUser && (
          <Animated.View
            entering={FadeIn.delay(messageDelay + 100)}
            style={[styles.avatar, styles.userAvatar]}
          >
            <User size={18} color={colors.text.inverse} />
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const renderTypingIndicator = () => (
    <Animated.View
      entering={SlideInLeft.delay(100)}
      exiting={FadeOut}
      style={[styles.messageContainer, styles.assistantMessageContainer]}
    >
      <View style={styles.avatar}>
        <Bot size={18} color={colors.primary} />
      </View>

      <View style={styles.messageContent}>
        <View style={[styles.messageBubble, styles.assistantBubble, styles.typingBubble]}>
          <EnhancedLoadingState
            variant="contextual"
            size="sm"
            context="ai-analysis"
            message="Escribiendo..."
          />
        </View>
      </View>
    </Animated.View>
  );

  const attachmentMenuAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: attachmentScale.value }],
    opacity: attachmentScale.value,
  }));

  const renderAttachmentMenu = () => {
    if (!showAttachments) return null;

    return (
      <Animated.View style={[styles.attachmentMenu, attachmentMenuAnimatedStyle]}>
        <AttachmentOption
          icon={Camera}
          label="Cámara"
          onPress={() => {
            // Handle camera
            setShowAttachments(false);
          }}
        />
        <AttachmentOption
          icon={ImageIcon}
          label="Galería"
          onPress={() => {
            // Handle gallery
            setShowAttachments(false);
          }}
        />
      </Animated.View>
    );
  };

  const inputAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: inputFocusScale.value }],
  }));

  const sendButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sendButtonScale.value }],
  }));

  const canSend = inputText.trim().length > 0 || selectedImages.length > 0;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={insets.top}
    >
      {/* Header */}
      <Animated.View
        entering={FadeIn}
        style={[styles.header, { paddingTop: insets.top + spacing['3'] }]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <View style={styles.headerAvatar}>
              <MessageCircle size={24} color={colors.primary} />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{conversationTitle}</Text>
              {context && <Text style={styles.headerSubtitle}>{context}</Text>}
            </View>
          </View>

          {onClose && (
            <EnhancedButton
              variant="ghost"
              size="sm"
              icon={X}
              iconPosition="only"
              onPress={onClose}
              hapticFeedback="light"
            />
          )}
        </View>
      </Animated.View>

      {/* Messages */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {messages.length === 0 ? (
          <Animated.View entering={FadeIn.delay(300)} style={styles.emptyState}>
            <View style={styles.emptyIcon}>
              <MessageCircle size={48} color={colors.text.tertiary} />
            </View>
            <Text style={styles.emptyTitle}>¡Hola! Soy tu asistente experto</Text>
            <Text style={styles.emptyDescription}>
              Puedo ayudarte con fórmulas de coloración, diagnósticos capilares, técnicas
              profesionales y cualquier consulta sobre colorimetría.
            </Text>
          </Animated.View>
        ) : (
          messages.map(renderMessage)
        )}

        {isTyping && renderTypingIndicator()}
      </ScrollView>

      {/* Input area */}
      <Animated.View
        style={[styles.inputContainer, { paddingBottom: insets.bottom + spacing['3'] }]}
      >
        {/* Selected images preview */}
        {selectedImages.length > 0 && (
          <Animated.ScrollView
            entering={FadeIn}
            horizontal
            style={styles.selectedImagesContainer}
            showsHorizontalScrollIndicator={false}
          >
            {selectedImages.map((uri, index) => (
              <View key={index} style={styles.selectedImage}>
                <Image source={{ uri }} style={styles.selectedImagePreview} />
                <Pressable
                  style={styles.removeSelectedImage}
                  onPress={() => {
                    setSelectedImages(prev => prev.filter((_, i) => i !== index));
                  }}
                >
                  <X size={12} color={colors.text.inverse} />
                </Pressable>
              </View>
            ))}
          </Animated.ScrollView>
        )}

        <View style={styles.inputWrapper}>
          {/* Attachment button */}
          <AnimatedPressable style={styles.attachmentButton} onPress={toggleAttachments}>
            <Plus
              size={24}
              color={colors.primary}
              style={[
                styles.plusIcon,
                { transform: [{ rotate: showAttachments ? '45deg' : '0deg' }] },
              ]}
            />
          </AnimatedPressable>

          {/* Text input */}
          <Animated.View style={[styles.inputField, inputAnimatedStyle]}>
            <TextInput
              ref={textInputRef}
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Escribe tu consulta profesional..."
              placeholderTextColor={colors.text.tertiary}
              multiline
              maxLength={1000}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              returnKeyType="default"
              blurOnSubmit={false}
            />
          </Animated.View>

          {/* Voice/Send button */}
          <Animated.View style={sendButtonAnimatedStyle}>
            <EnhancedButton
              variant={canSend ? 'primary' : 'ghost'}
              size="base"
              icon={canSend ? Send : Mic}
              iconPosition="only"
              onPress={canSend ? handleSendMessage : () => setIsRecording(!isRecording)}
              disabled={false}
              hapticFeedback="medium"
              accessibilityLabel={canSend ? 'Enviar mensaje' : 'Grabar audio'}
              style={styles.sendButton}
            />
          </Animated.View>
        </View>

        {renderAttachmentMenu()}
      </Animated.View>
    </KeyboardAvoidingView>
  );
};

// Attachment Option Component
const AttachmentOption: React.FC<{
  icon: React.ComponentType<{ size: number; color: string }>;
  label: string;
  onPress: () => void;
}> = ({ icon: Icon, label, onPress }) => {
  return (
    <Pressable style={styles.attachmentOption} onPress={onPress}>
      <View style={styles.attachmentOptionIcon}>
        <Icon size={20} color={colors.primary} />
      </View>
      <Text style={styles.attachmentOptionLabel}>{label}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },

  header: {
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
    ...shadows.sm,
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing['4'],
    paddingBottom: spacing['3'],
  },

  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing['3'],
  },

  headerText: {
    flex: 1,
  },

  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
  },

  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    marginTop: spacing['0.5'],
  },

  messagesContainer: {
    flex: 1,
  },

  messagesContent: {
    padding: spacing['4'],
    paddingBottom: spacing['6'],
  },

  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing['12'],
  },

  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: radius.full,
    backgroundColor: colors.neutral[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['4'],
  },

  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing['2'],
  },

  emptyDescription: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: typography.sizes.base * typography.lineHeight.relaxed,
    paddingHorizontal: spacing['6'],
  },

  messageContainer: {
    flexDirection: 'row',
    marginBottom: spacing['4'],
    alignItems: 'flex-end',
  },

  userMessageContainer: {
    justifyContent: 'flex-end',
  },

  assistantMessageContainer: {
    justifyContent: 'flex-start',
  },

  avatar: {
    width: 32,
    height: 32,
    borderRadius: radius.full,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing['2'],
  },

  userAvatar: {
    backgroundColor: colors.primary,
  },

  messageContent: {
    flex: 1,
    maxWidth: SCREEN_WIDTH * 0.75,
  },

  messageBubble: {
    borderRadius: radius.xl,
    paddingHorizontal: spacing['4'],
    paddingVertical: spacing['3'],
    marginBottom: spacing['1'],
  },

  userBubble: {
    backgroundColor: colors.primary,
    alignSelf: 'flex-end',
    borderBottomRightRadius: radius.sm,
  },

  assistantBubble: {
    backgroundColor: colors.surface,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: radius.sm,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },

  typingBubble: {
    paddingVertical: spacing['2'],
  },

  messageText: {
    fontSize: typography.sizes.base,
    lineHeight: typography.sizes.base * typography.lineHeight.normal,
  },

  userMessageText: {
    color: colors.text.inverse,
  },

  assistantMessageText: {
    color: colors.text.primary,
  },

  messageMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing['2'],
  },

  userMetadata: {
    justifyContent: 'flex-end',
  },

  assistantMetadata: {
    justifyContent: 'flex-start',
  },

  timestampText: {
    fontSize: typography.sizes.xs,
    color: colors.text.tertiary,
    marginRight: spacing['1'],
  },

  messageStatus: {
    marginLeft: spacing['1'],
  },

  attachmentsContainer: {
    marginBottom: spacing['2'],
  },

  attachmentImage: {
    width: 150,
    height: 150,
    borderRadius: radius.md,
    marginBottom: spacing['2'],
  },

  inputContainer: {
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
    paddingHorizontal: spacing['4'],
    paddingTop: spacing['3'],
  },

  selectedImagesContainer: {
    marginBottom: spacing['3'],
    maxHeight: 80,
  },

  selectedImage: {
    marginRight: spacing['2'],
    position: 'relative',
  },

  selectedImagePreview: {
    width: 70,
    height: 70,
    borderRadius: radius.md,
  },

  removeSelectedImage: {
    position: 'absolute',
    top: -spacing['1'],
    right: -spacing['1'],
    backgroundColor: colors.error,
    borderRadius: radius.full,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },

  attachmentButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing['2'],
  },

  inputField: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: radius.xl,
    borderWidth: 1,
    borderColor: colors.neutral[200],
    marginRight: spacing['2'],
  },

  textInput: {
    fontSize: typography.sizes.base,
    color: colors.text.primary,
    paddingHorizontal: spacing['4'],
    paddingVertical: spacing['3'],
    minHeight: 44,
    maxHeight: 100,
    textAlignVertical: 'center',
  },

  sendButton: {
    width: 44,
    height: 44,
  },

  attachmentMenu: {
    position: 'absolute',
    bottom: 80,
    left: spacing['4'],
    flexDirection: 'row',
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    paddingHorizontal: spacing['3'],
    paddingVertical: spacing['2'],
    ...shadows.lg,
  },

  attachmentOption: {
    alignItems: 'center',
    paddingHorizontal: spacing['3'],
    paddingVertical: spacing['2'],
    marginHorizontal: spacing['1'],
  },

  attachmentOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['1'],
  },

  attachmentOptionLabel: {
    fontSize: typography.sizes.xs,
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },
  plusIcon: {
    // Container for Plus icon transform - no additional styling needed
  },
});
