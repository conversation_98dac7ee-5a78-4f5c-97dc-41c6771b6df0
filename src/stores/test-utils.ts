/**
 * Test Utilities for Inventory Store Testing
 *
 * Provides mock factories, test helpers, custom matchers,
 * and performance benchmarking utilities.
 */

import { Product, StockMovement, InventoryAlert, ProductMapping } from '@/types/inventory';
import { ColorFormula } from '@/types/formulation';
import { faker } from '@faker-js/faker';

// Custom matchers for inventory testing
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R;
      toBeValidProduct(): R;
      toBeValidMovement(): R;
      toHaveStockStatus(status: 'ok' | 'low' | 'out' | 'overstock'): R;
      toBeRecentDate(withinDays?: number): R;
    }
  }
}

// Extend Jest matchers
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    return {
      message: () =>
        pass
          ? `expected ${received} not to be within range ${floor} - ${ceiling}`
          : `expected ${received} to be within range ${floor} - ${ceiling}`,
      pass,
    };
  },

  toBeValidProduct(received: any) {
    const requiredFields = ['id', 'brand', 'category', 'currentStock', 'minStock', 'isActive'];
    const missingFields = requiredFields.filter(field => !(field in received));

    const pass =
      missingFields.length === 0 &&
      typeof received.currentStock === 'number' &&
      received.currentStock >= 0 &&
      typeof received.minStock === 'number' &&
      received.minStock >= 0;

    return {
      message: () =>
        pass
          ? `expected object not to be a valid product`
          : `expected object to be a valid product. Missing fields: ${missingFields.join(', ')}`,
      pass,
    };
  },

  toBeValidMovement(received: any) {
    const validTypes = ['purchase', 'use', 'adjustment', 'return', 'waste'];
    const hasValidType = validTypes.includes(received.type);

    const pass =
      hasValidType && typeof received.quantity === 'number' && received.productId && received.date;

    return {
      message: () =>
        pass
          ? `expected object not to be a valid stock movement`
          : `expected object to be a valid stock movement`,
      pass,
    };
  },

  toHaveStockStatus(received: Product, expectedStatus: 'ok' | 'low' | 'out' | 'overstock') {
    let actualStatus: string;

    if (received.currentStock === 0) {
      actualStatus = 'out';
    } else if (received.currentStock <= received.minStock) {
      actualStatus = 'low';
    } else if (received.maxStock && received.currentStock > received.maxStock) {
      actualStatus = 'overstock';
    } else {
      actualStatus = 'ok';
    }

    const pass = actualStatus === expectedStatus;

    return {
      message: () =>
        pass
          ? `expected product not to have stock status ${expectedStatus}`
          : `expected product to have stock status ${expectedStatus}, but got ${actualStatus}`,
      pass,
    };
  },

  toBeRecentDate(received: string, withinDays = 7) {
    const receivedDate = new Date(received);
    const now = new Date();
    const diffInDays = Math.abs(now.getTime() - receivedDate.getTime()) / (1000 * 60 * 60 * 24);

    const pass = diffInDays <= withinDays;

    return {
      message: () =>
        pass
          ? `expected date not to be within ${withinDays} days`
          : `expected date to be within ${withinDays} days, but it was ${diffInDays.toFixed(1)} days ago`,
      pass,
    };
  },
});

/**
 * Mock Data Factories
 */
export class InventoryMockFactory {
  /**
   * Create a realistic mock product with optional overrides
   */
  static createProduct(overrides: Partial<Product> = {}): Product {
    const brands = ['Wella', "L'Oreal", 'Matrix', 'Schwarzkopf', 'Redken', 'Goldwell'];
    const categories: Product['category'][] = [
      'tinte',
      'oxidante',
      'decolorante',
      'matizador',
      'tratamiento',
      'aditivo',
    ];
    const types = [
      'Tinte',
      'Oxidante',
      'Decolorante',
      'Matizador',
      'Tratamiento',
      'Champú',
      'Acondicionador',
    ];

    const brand = faker.helpers.arrayElement(brands);
    const category = faker.helpers.arrayElement(categories);
    const type = faker.helpers.arrayElement(types);
    const shade =
      category === 'tinte'
        ? faker.helpers.arrayElement(['7.1', '8.0', '6.35', '9.1', '5.4'])
        : category === 'oxidante'
          ? faker.helpers.arrayElement(['10 Vol', '20 Vol', '30 Vol', '40 Vol'])
          : undefined;

    return {
      id: faker.string.uuid(),
      brand,
      line: faker.helpers.arrayElement([
        'Color Perfect',
        'Majirel',
        'SoColor',
        'Koleston',
        'Chromatics',
      ]),
      type,
      shade,
      displayName: `${brand} ${type} ${shade || ''}`.trim(),
      name: `${brand} ${type} ${shade || ''}`.trim(),
      category,
      currentStock: faker.number.int({ min: 0, max: 1000 }),
      minStock: faker.number.int({ min: 50, max: 200 }),
      maxStock: faker.number.int({ min: 500, max: 2000 }),
      unitType: faker.helpers.arrayElement(['ml', 'g']),
      unitSize: faker.helpers.arrayElement([50, 60, 100, 500, 1000]),
      purchasePrice: faker.number.float({ min: 5, max: 50, multipleOf: 0.01 }),
      costPerUnit: faker.number.float({ min: 0.1, max: 2.0, multipleOf: 0.01 }),
      barcode: faker.string.numeric(13),
      supplier: faker.company.name(),
      notes: faker.lorem.sentence(),
      colorCode: shade || faker.color.rgb(),
      isActive: faker.datatype.boolean(0.9), // 90% chance of being active
      lastUpdated: faker.date.recent().toISOString(),
      _syncStatus: faker.helpers.arrayElement(['synced', 'pending', 'error']),
      ...overrides,
    };
  }

  /**
   * Create multiple products with automatic relationships
   */
  static createProductSet(
    count: number,
    options: {
      sameBrand?: boolean;
      sameCategory?: boolean;
      diverseStock?: boolean;
    } = {}
  ): Product[] {
    const products: Product[] = [];

    const baseBrand = options.sameBrand
      ? faker.helpers.arrayElement(['Wella', "L'Oreal"])
      : undefined;
    const baseCategory = options.sameCategory
      ? (faker.helpers.arrayElement(['tinte', 'oxidante']) as Product['category'])
      : undefined;

    for (let i = 0; i < count; i++) {
      const overrides: Partial<Product> = {};

      if (baseBrand) overrides.brand = baseBrand;
      if (baseCategory) overrides.category = baseCategory;

      if (options.diverseStock) {
        // Create products with different stock statuses
        const stockType = i % 4;
        switch (stockType) {
          case 0: // Out of stock
            overrides.currentStock = 0;
            break;
          case 1: // Low stock
            overrides.currentStock = 25;
            overrides.minStock = 100;
            break;
          case 2: // Overstock
            overrides.currentStock = 800;
            overrides.maxStock = 500;
            break;
          default: // Good stock
            overrides.currentStock = 300;
            overrides.minStock = 100;
        }
      }

      products.push(this.createProduct(overrides));
    }

    return products;
  }

  /**
   * Create a realistic stock movement
   */
  static createMovement(overrides: Partial<StockMovement> = {}): StockMovement {
    const types: StockMovement['type'][] = ['purchase', 'use', 'adjustment', 'return', 'waste'];
    const type = faker.helpers.arrayElement(types);

    let quantity: number;
    switch (type) {
      case 'purchase':
        quantity = faker.number.int({ min: 50, max: 500 });
        break;
      case 'use':
        quantity = -faker.number.int({ min: 10, max: 100 });
        break;
      case 'return':
        quantity = faker.number.int({ min: 10, max: 50 });
        break;
      case 'waste':
        quantity = -faker.number.int({ min: 5, max: 25 });
        break;
      default: // adjustment
        quantity = faker.number.int({ min: -50, max: 50 });
    }

    return {
      id: faker.string.uuid(),
      productId: faker.string.uuid(),
      quantity,
      type,
      date: faker.date.recent({ days: 90 }).toISOString(),
      userId: faker.string.uuid(),
      referenceId: type === 'use' ? `service-${faker.string.alphanumeric(8)}` : undefined,
      notes: faker.lorem.sentence(),
      ...overrides,
    };
  }

  /**
   * Create movements for specific products with realistic patterns
   */
  static createMovementsForProducts(
    productIds: string[],
    options: {
      daysBack?: number;
      movementsPerProduct?: number;
      includeUsage?: boolean;
      includePurchases?: boolean;
    } = {}
  ): StockMovement[] {
    const {
      daysBack = 30,
      movementsPerProduct = 5,
      includeUsage = true,
      includePurchases = true,
    } = options;

    const movements: StockMovement[] = [];

    productIds.forEach(productId => {
      for (let i = 0; i < movementsPerProduct; i++) {
        const daysAgo = Math.floor(Math.random() * daysBack);
        const date = new Date();
        date.setDate(date.getDate() - daysAgo);

        let type: StockMovement['type'];

        if (includeUsage && includePurchases) {
          type = Math.random() > 0.7 ? 'use' : 'purchase';
        } else if (includeUsage) {
          type = 'use';
        } else {
          type = 'purchase';
        }

        const quantity =
          type === 'use'
            ? -faker.number.int({ min: 15, max: 60 })
            : faker.number.int({ min: 100, max: 1000 });

        movements.push(
          this.createMovement({
            productId,
            type,
            quantity,
            date: date.toISOString(),
          })
        );
      }
    });

    return movements.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  /**
   * Create a realistic product mapping
   */
  static createProductMapping(overrides: Partial<ProductMapping> = {}): ProductMapping {
    const aiProductNames = [
      'Rubio Medio Ceniza',
      'Castaño Claro Dorado',
      'Negro Natural',
      'Rubio Oscuro Cobrizo',
      'Castaño Medio Rojizo',
      'Oxidante 20 Volúmenes',
      'Decolorante Polvo',
    ];

    return {
      id: faker.string.uuid(),
      salonId: faker.string.uuid(),
      aiProductName: faker.helpers.arrayElement(aiProductNames),
      inventoryProductId: faker.string.uuid(),
      confidence: faker.number.float({ min: 0.7, max: 0.99, multipleOf: 0.01 }),
      usageCount: faker.number.int({ min: 1, max: 50 }),
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    };
  }

  /**
   * Create a realistic color formula
   */
  static createColorFormula(overrides: Partial<ColorFormula> = {}): ColorFormula {
    const colors = [
      { tone: '7.1 Rubio Medio Ceniza', amount: faker.number.int({ min: 20, max: 60 }) },
      { tone: '8.0 Rubio Claro Natural', amount: faker.number.int({ min: 10, max: 40 }) },
    ];

    return {
      colors,
      developerVolume: faker.helpers.arrayElement([10, 20, 30, 40]),
      developerRatio: faker.helpers.arrayElement(['1:1', '1:1.5', '1:2']),
      additives: faker.helpers.arrayElements(['Anti-Yellow', 'Intensifier', 'Bond Builder'], {
        min: 0,
        max: 2,
      }),
      processingTime: faker.number.int({ min: 20, max: 45 }),
      technique: faker.helpers.arrayElement(['Global', 'Highlights', 'Lowlights', 'Balayage']),
      mixingInstructions: faker.lorem.sentence(),
      applicationNotes: faker.lorem.paragraph(),
      ...overrides,
    };
  }

  /**
   * Create an inventory alert
   */
  static createAlert(overrides: Partial<InventoryAlert> = {}): InventoryAlert {
    const types: InventoryAlert['type'][] = ['low_stock', 'out_of_stock', 'expiring', 'overstock'];
    const severities: InventoryAlert['severity'][] = ['info', 'warning', 'error'];

    return {
      id: faker.string.uuid(),
      productId: faker.string.uuid(),
      type: faker.helpers.arrayElement(types),
      message: faker.lorem.sentence(),
      severity: faker.helpers.arrayElement(severities),
      createdAt: faker.date.recent().toISOString(),
      isActive: faker.datatype.boolean(0.8),
      acknowledgedBy: faker.datatype.boolean(0.3) ? faker.string.uuid() : undefined,
      acknowledgedAt: faker.datatype.boolean(0.3) ? faker.date.recent().toISOString() : undefined,
      ...overrides,
    };
  }
}

/**
 * Test Helpers
 */
export class InventoryTestHelpers {
  /**
   * Create a complete test scenario with related products, movements, and alerts
   */
  static createTestScenario(name: 'salon_startup' | 'busy_month' | 'low_stock_crisis' | 'custom') {
    switch (name) {
      case 'salon_startup':
        return this.createSalonStartupScenario();
      case 'busy_month':
        return this.createBusyMonthScenario();
      case 'low_stock_crisis':
        return this.createLowStockCrisisScenario();
      default:
        return this.createCustomScenario();
    }
  }

  private static createSalonStartupScenario() {
    const products = InventoryMockFactory.createProductSet(20, { diverseStock: false });

    // All products have good initial stock
    products.forEach(product => {
      product.currentStock = faker.number.int({ min: 200, max: 800 });
      product._syncStatus = 'synced';
    });

    // Minimal movements (just initial purchases)
    const movements = products.map(product =>
      InventoryMockFactory.createMovement({
        productId: product.id,
        type: 'purchase',
        quantity: product.currentStock,
        date: faker.date.recent({ days: 3 }).toISOString(),
        notes: 'Initial inventory purchase',
      })
    );

    return { products, movements, alerts: [] };
  }

  private static createBusyMonthScenario() {
    const products = InventoryMockFactory.createProductSet(15, { diverseStock: true });
    const productIds = products.map(p => p.id);

    // Lots of consumption movements
    const movements = InventoryMockFactory.createMovementsForProducts(productIds, {
      daysBack: 30,
      movementsPerProduct: 15,
      includeUsage: true,
      includePurchases: true,
    });

    // Some products might have alerts
    const alerts = products
      .filter(() => Math.random() > 0.7)
      .map(product =>
        InventoryMockFactory.createAlert({
          productId: product.id,
          type: product.currentStock === 0 ? 'out_of_stock' : 'low_stock',
          severity: product.currentStock === 0 ? 'error' : 'warning',
        })
      );

    return { products, movements, alerts };
  }

  private static createLowStockCrisisScenario() {
    const products = InventoryMockFactory.createProductSet(10);

    // Most products have low or no stock
    products.forEach((product, index) => {
      if (index < 3) {
        product.currentStock = 0;
      } else if (index < 7) {
        product.currentStock = Math.min(product.minStock * 0.5, 25);
      }
    });

    const movements = products.flatMap(product =>
      Array.from({ length: 10 }, () =>
        InventoryMockFactory.createMovement({
          productId: product.id,
          type: 'use',
          quantity: -faker.number.int({ min: 20, max: 50 }),
          date: faker.date.recent({ days: 14 }).toISOString(),
        })
      )
    );

    const alerts = products
      .filter(p => p.currentStock <= p.minStock)
      .map(product =>
        InventoryMockFactory.createAlert({
          productId: product.id,
          type: product.currentStock === 0 ? 'out_of_stock' : 'low_stock',
          severity: product.currentStock === 0 ? 'error' : 'warning',
          isActive: true,
        })
      );

    return { products, movements, alerts };
  }

  private static createCustomScenario() {
    const products = InventoryMockFactory.createProductSet(12);
    const productIds = products.map(p => p.id);
    const movements = InventoryMockFactory.createMovementsForProducts(productIds);
    const alerts = products
      .filter(() => Math.random() > 0.8)
      .map(product => InventoryMockFactory.createAlert({ productId: product.id }));

    return { products, movements, alerts };
  }

  /**
   * Validate that all products in an array are consistent and valid
   */
  static validateProductConsistency(products: Product[]): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    products.forEach((product, index) => {
      if (!product.id) errors.push(`Product ${index} missing id`);
      if (!product.brand) errors.push(`Product ${index} missing brand`);
      if (product.currentStock < 0) errors.push(`Product ${index} has negative stock`);
      if (product.minStock < 0) errors.push(`Product ${index} has negative minStock`);
      if (product.maxStock && product.maxStock < product.minStock) {
        errors.push(`Product ${index} has maxStock less than minStock`);
      }
      if (product.costPerUnit <= 0) errors.push(`Product ${index} has invalid cost per unit`);
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calculate expected metrics for test validation
   */
  static calculateExpectedMetrics(products: Product[], movements: StockMovement[]) {
    const activeProducts = products.filter(p => p.isActive);

    const totalValue = products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0);
    const lowStockCount = activeProducts.filter(
      p => p.currentStock > 0 && p.currentStock <= p.minStock
    ).length;
    const outOfStockCount = activeProducts.filter(p => p.currentStock === 0).length;
    const overstockCount = activeProducts.filter(
      p => p.maxStock && p.currentStock > p.maxStock
    ).length;

    // Calculate most used products from last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentUsage = movements
      .filter(m => m.type === 'use' && new Date(m.date) >= thirtyDaysAgo)
      .reduce(
        (acc, m) => {
          acc[m.productId] = (acc[m.productId] || 0) + Math.abs(m.quantity);
          return acc;
        },
        {} as Record<string, number>
      );

    const mostUsedProducts = Object.entries(recentUsage)
      .map(([productId, consumed]) => {
        const product = products.find(p => p.id === productId);
        return product ? { product, totalConsumed: consumed } : null;
      })
      .filter(Boolean)
      .sort((a, b) => b!.totalConsumed - a!.totalConsumed)
      .slice(0, 5);

    return {
      totalValue,
      lowStockCount,
      outOfStockCount,
      overstockCount,
      mostUsedProducts: mostUsedProducts.filter(Boolean),
      totalProducts: products.length,
      activeProducts: activeProducts.length,
    };
  }

  /**
   * Create performance benchmark data
   */
  static createPerformanceTestData(size: 'small' | 'medium' | 'large' | 'xlarge') {
    const sizes = {
      small: { products: 10, movements: 50 },
      medium: { products: 100, movements: 500 },
      large: { products: 1000, movements: 5000 },
      xlarge: { products: 10000, movements: 50000 },
    };

    const config = sizes[size];
    const products = InventoryMockFactory.createProductSet(config.products);
    const productIds = products.map(p => p.id);
    const movements = InventoryMockFactory.createMovementsForProducts(productIds, {
      movementsPerProduct: Math.ceil(config.movements / config.products),
    });

    return { products, movements };
  }
}

/**
 * Performance Testing Utilities
 */
export class PerformanceTestHelper {
  /**
   * Measure execution time of an async function
   */
  static async measureAsync<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();

    return {
      result,
      duration: end - start,
    };
  }

  /**
   * Measure execution time of a sync function
   */
  static measure<T>(fn: () => T): { result: T; duration: number } {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    return {
      result,
      duration: end - start,
    };
  }

  /**
   * Run performance benchmark suite
   */
  static async benchmarkOperations(operations: Record<string, () => Promise<any> | any>) {
    const results: Record<string, number> = {};

    // Process operations in parallel to avoid await-in-loop
    const entries = Object.entries(operations);
    const promises = entries.map(async ([name, operation]) => {
      const { duration } =
        typeof operation() === 'object' && 'then' in operation()
          ? await this.measureAsync(operation as () => Promise<any>)
          : this.measure(operation as () => any);

      return { name, duration };
    });

    const measurements = await Promise.all(promises);

    for (const { name, duration } of measurements) {
      results[name] = duration;
    }

    return results;
  }

  /**
   * Assert performance constraints
   */
  static assertPerformance(duration: number, maxMs: number, operation: string) {
    if (duration > maxMs) {
      throw new Error(
        `Performance assertion failed: ${operation} took ${duration.toFixed(2)}ms, expected < ${maxMs}ms`
      );
    }
  }
}

/**
 * Mock Store State Helper
 */
export class MockStoreHelper {
  /**
   * Create mock store state with realistic data
   */
  static createMockStoreState(
    scenario: ReturnType<typeof InventoryTestHelpers.createTestScenario>
  ) {
    return {
      products: scenario.products,
      movements: scenario.movements,
      alerts: scenario.alerts,
      productMappings: [],
      lowStockProducts: scenario.alerts
        .filter(a => a.type === 'low_stock' || a.type === 'out_of_stock')
        .map(a => ({
          product_id: a.productId,
          brand: 'Test Brand',
          name: 'Test Product',
          category: 'tinte',
          stock_ml: a.type === 'out_of_stock' ? 0 : 25,
          minimum_stock_ml: 100,
          percentage_remaining: a.type === 'out_of_stock' ? 0 : 25,
        })),
      isLoading: false,
      lastSync: new Date().toISOString(),
      currentStock: scenario.products.reduce(
        (acc, p) => {
          acc[p.id] = p.currentStock;
          return acc;
        },
        {} as Record<string, number>
      ),
      activeFilters: {
        stockStatus: 'all' as const,
        categories: [],
        brands: [],
        searchQuery: '',
      },
      sortBy: 'name' as const,
      groupBy: 'none' as const,
    };
  }
}

// Export all utilities
export { InventoryMockFactory, InventoryTestHelpers, PerformanceTestHelper, MockStoreHelper };
