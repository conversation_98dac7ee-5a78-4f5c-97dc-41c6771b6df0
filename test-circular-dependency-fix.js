#!/usr/bin/env node

/**
 * Test script to verify the circular dependency fix
 * This script simulates the exact scenario that was causing problems
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Circular Dependency Fix\n');

// Test 1: Verify auth-store no longer has dynamic import
console.log('📋 Test 1: Checking auth-store for dynamic imports...');

const authStorePath = path.join(__dirname, 'src/stores/auth-store.ts');
const authStoreContent = fs.readFileSync(authStorePath, 'utf8');

// Check if the problematic dynamic import is gone
const hasDynamicImport = authStoreContent.includes("await import('./salon-config-store')");
const hasEventBusImport = authStoreContent.includes("import { storeEventBus, STORE_EVENTS }");
const hasEventEmit = authStoreContent.includes("storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED");

console.log(`   ❌ Dynamic import found: ${hasDynamicImport ? 'YES (BAD)' : 'NO (GOOD)'}`);
console.log(`   ✅ Event bus import found: ${hasEventBusImport ? 'YES (GOOD)' : 'NO (BAD)'}`);
console.log(`   ✅ Event emit found: ${hasEventEmit ? 'YES (GOOD)' : 'NO (BAD)'}`);

// Test 2: Verify salon-config-store has event listener
console.log('\n📋 Test 2: Checking salon-config-store for event listener...');

const salonConfigStorePath = path.join(__dirname, 'src/stores/salon-config-store.ts');
const salonConfigStoreContent = fs.readFileSync(salonConfigStorePath, 'utf8');

const hasEventListener = salonConfigStoreContent.includes("storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED");
const hasEventBusImportSalon = salonConfigStoreContent.includes("import { storeEventBus, STORE_EVENTS }");

console.log(`   ✅ Event bus import found: ${hasEventBusImportSalon ? 'YES (GOOD)' : 'NO (BAD)'}`);
console.log(`   ✅ Event listener found: ${hasEventListener ? 'YES (GOOD)' : 'NO (BAD)'}`);

// Test 3: Verify event bus file exists and has correct structure
console.log('\n📋 Test 3: Checking event bus implementation...');

const eventBusPath = path.join(__dirname, 'src/utils/store-event-bus.ts');
const eventBusExists = fs.existsSync(eventBusPath);

console.log(`   ✅ Event bus file exists: ${eventBusExists ? 'YES (GOOD)' : 'NO (BAD)'}`);

if (eventBusExists) {
  const eventBusContent = fs.readFileSync(eventBusPath, 'utf8');
  
  const hasStoreEvents = eventBusContent.includes("export const STORE_EVENTS");
  const hasSyncRequired = eventBusContent.includes("SYNC_REQUIRED: 'sync:required'");
  const hasEventBusClass = eventBusContent.includes("class StoreEventBus");
  const hasExportInstance = eventBusContent.includes("export const storeEventBus");
  
  console.log(`   ✅ STORE_EVENTS constant: ${hasStoreEvents ? 'YES (GOOD)' : 'NO (BAD)'}`);
  console.log(`   ✅ SYNC_REQUIRED event: ${hasSyncRequired ? 'YES (GOOD)' : 'NO (BAD)'}`);
  console.log(`   ✅ StoreEventBus class: ${hasEventBusClass ? 'YES (GOOD)' : 'NO (BAD)'}`);
  console.log(`   ✅ Exported instance: ${hasExportInstance ? 'YES (GOOD)' : 'NO (BAD)'}`);
}

// Test 4: Check for any remaining circular dependencies in stores
console.log('\n📋 Test 4: Scanning for potential circular dependencies...');

const storesDir = path.join(__dirname, 'src/stores');
const storeFiles = fs.readdirSync(storesDir).filter(file => file.endsWith('.ts') && !file.includes('.test.'));

let circularDependencies = [];

storeFiles.forEach(file => {
  const filePath = path.join(storesDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for direct imports to other stores
  const storeImports = storeFiles
    .filter(otherFile => otherFile !== file)
    .filter(otherFile => {
      const storeName = otherFile.replace('.ts', '');
      return content.includes(`from './${storeName}'`) || content.includes(`from "./${storeName}"`);
    });
  
  if (storeImports.length > 0) {
    circularDependencies.push({
      file,
      imports: storeImports
    });
  }
});

console.log(`   Found ${circularDependencies.length} potential circular dependencies:`);
circularDependencies.forEach(dep => {
  console.log(`   ⚠️  ${dep.file} imports: ${dep.imports.join(', ')}`);
});

// Test 5: Verify test files exist
console.log('\n📋 Test 5: Checking test coverage...');

const eventBusTestPath = path.join(__dirname, 'src/utils/__tests__/store-event-bus.test.ts');
const eventBusTestExists = fs.existsSync(eventBusTestPath);

console.log(`   ✅ Event bus tests exist: ${eventBusTestExists ? 'YES (GOOD)' : 'NO (BAD)'}`);

// Summary
console.log('\n🎯 SUMMARY:');
console.log('='.repeat(50));

const allTestsPassed = 
  !hasDynamicImport && 
  hasEventBusImport && 
  hasEventEmit && 
  hasEventListener && 
  hasEventBusImportSalon && 
  eventBusExists && 
  eventBusTestExists;

if (allTestsPassed) {
  console.log('🎉 ALL TESTS PASSED! Circular dependency fix is working correctly.');
  console.log('✅ Dynamic imports removed');
  console.log('✅ Event bus implemented');
  console.log('✅ Event listeners configured');
  console.log('✅ Test coverage in place');
  
  if (circularDependencies.length === 0) {
    console.log('✅ No circular dependencies detected in stores');
  } else {
    console.log(`⚠️  ${circularDependencies.length} potential circular dependencies found (may need attention)`);
  }
  
  process.exit(0);
} else {
  console.log('❌ SOME TESTS FAILED! Please review the implementation.');
  process.exit(1);
}
