/**
 * Store Event Bus - Eliminates circular dependencies between stores
 * Allows stores to communicate without direct imports
 */

export interface StoreEvent {
  type: string;
  payload: unknown;
  timestamp: number;
  source: string;
}

export type EventHandler = (payload: unknown) => void | Promise<void>;

class StoreEventBus {
  private listeners = new Map<string, EventHandler[]>();
  private isDebugMode = process.env.NODE_ENV === 'development';

  /**
   * Subscribe to store events
   */
  on(eventType: string, handler: EventHandler): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }

    this.listeners.get(eventType)!.push(handler);

    if (this.isDebugMode) {
      console.info(`[StoreEventBus] Subscribed to: ${eventType}`);
    }

    // Return unsubscribe function
    return () => {
      const handlers = this.listeners.get(eventType);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Emit store event
   */
  async emit(eventType: string, payload: unknown, source = 'unknown'): Promise<void> {
    if (this.isDebugMode) {
      console.info(`[StoreEventBus] Emitting: ${eventType}`, { source, payload });
    }

    const handlers = this.listeners.get(eventType) || [];

    // Execute all handlers
    await Promise.all(
      handlers.map(async handler => {
        try {
          await handler(payload);
        } catch (error) {
          console.error(`[StoreEventBus] Error in handler for ${eventType}:`, error);
        }
      })
    );
  }

  /**
   * Remove all listeners for an event type
   */
  off(eventType: string): void {
    this.listeners.delete(eventType);

    if (this.isDebugMode) {
      console.info(`[StoreEventBus] Removed all listeners for: ${eventType}`);
    }
  }

  /**
   * Get current listener count for debugging
   */
  getListenerCount(eventType?: string): number | Record<string, number> {
    if (eventType) {
      return this.listeners.get(eventType)?.length || 0;
    }

    const counts: Record<string, number> = {};
    this.listeners.forEach((handlers, type) => {
      counts[type] = handlers.length;
    });
    return counts;
  }

  /**
   * Clear all listeners (useful for testing)
   */
  clear(): void {
    this.listeners.clear();

    if (this.isDebugMode) {
      console.info('[StoreEventBus] Cleared all listeners');
    }
  }
}

// Singleton instance
export const storeEventBus = new StoreEventBus();

// Common event types to prevent typos
export const STORE_EVENTS = {
  // Auth events
  USER_SIGNED_IN: 'user:signed_in',
  USER_SIGNED_OUT: 'user:signed_out',
  USER_PROFILE_UPDATED: 'user:profile_updated',

  // Salon config events
  SALON_CONFIG_UPDATED: 'salon:config_updated',
  REGIONAL_CONFIG_CHANGED: 'salon:regional_config_changed',

  // Sync events
  SYNC_REQUIRED: 'sync:required',
  SYNC_COMPLETED: 'sync:completed',
  SYNC_FAILED: 'sync:failed',

  // Inventory events
  INVENTORY_UPDATED: 'inventory:updated',
  PRODUCT_ADDED: 'inventory:product_added',
  STOCK_LOW: 'inventory:stock_low',
} as const;

export type StoreEventType = (typeof STORE_EVENTS)[keyof typeof STORE_EVENTS];
