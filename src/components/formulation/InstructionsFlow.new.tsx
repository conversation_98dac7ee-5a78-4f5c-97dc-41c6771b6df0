/**
 * InstructionsFlow - New Modular Implementation
 *
 * Professional-grade instructions flow using extracted modular components.
 * Built with offline-first architecture, haptic feedback, and smooth animations.
 *
 * PHASE 4: Final Integration
 * - Combines all extracted components and hooks
 * - Maintains exact same API as legacy version
 * - Improved performance through modular architecture
 * - Enhanced maintainability and testability
 */

import React, { useCallback, useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { ChevronRight, ChevronLeft, Check, Clock, Beaker, Palette } from 'lucide-react-native';

// Hooks
import { useInstructionFlow, FlowStep } from '@/hooks/useInstructionFlow';
import { useStepTimer } from '@/hooks/useStepTimer';
import { useStepValidation } from '@/hooks/useStepValidation';
import { useAnimations } from '@/hooks/useAnimations';

// Components
import { ProgressIndicator, StepCard } from './instructions/components';

import {
  PreparationStep,
  MixingStep,
  ApplicationStep,
  ProcessingStep,
  RinsingStep,
  CompletionStep,
} from './instructions/steps';

// Types and utilities
import { VisualFormulationData } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

// Props interface - maintains exact compatibility with legacy version
export interface InstructionsFlowProps {
  formulaData: VisualFormulationData;
  onClose?: () => void;
  onComplete?: () => void;
  onStepChange?: (step: number, stepData: FlowStep) => void;
  initialStep?: number;
  autoSave?: boolean;
  persistenceKey?: string;
}

/**
 * Step definitions for the instruction flow
 * Each step includes metadata for proper timing and validation
 */
const FLOW_STEPS: FlowStep[] = [
  {
    id: 'preparation',
    title: 'Preparación',
    icon: ({ size, color }) => <Check size={size} color={color} />,
    color: Colors.primary,
    estimatedDuration: 5,
    requirements: ['materials_ready', 'workspace_prepared'],
  },
  {
    id: 'mixing',
    title: 'Mezclado',
    icon: ({ size, color }) => <Beaker size={size} color={color} />,
    color: Colors.accent,
    estimatedDuration: 3,
    requirements: ['formula_mixed'],
  },
  {
    id: 'application',
    title: 'Aplicación',
    icon: ({ size, color }) => <Palette size={size} color={color} />,
    color: Colors.secondary,
    estimatedDuration: 15,
    requirements: ['zones_applied'],
  },
  {
    id: 'processing',
    title: 'Procesado',
    icon: ({ size, color }) => <Clock size={size} color={color} />,
    color: Colors.tertiary,
    estimatedDuration: 30,
    requirements: ['processing_complete'],
  },
  {
    id: 'rinsing',
    title: 'Enjuague',
    icon: ({ size, color }) => <ChevronRight size={size} color={color} />,
    color: Colors.info,
    estimatedDuration: 10,
    requirements: ['rinsing_complete'],
  },
  {
    id: 'completion',
    title: 'Finalización',
    icon: ({ size, color }) => <ChevronLeft size={size} color={color} />,
    color: Colors.success,
    estimatedDuration: 5,
    isOptional: false,
  },
];

/**
 * Main InstructionsFlow Component
 *
 * Uses composition pattern with specialized hooks for:
 * - Flow state management (useInstructionFlow)
 * - Timer functionality (useStepTimer)
 * - Validation logic (useStepValidation)
 * - Smooth animations (useAnimations)
 */
export function InstructionsFlow({
  formulaData,
  onClose,
  onComplete,
  onStepChange,
  _initialStep = 0,
  autoSave = true,
  persistenceKey,
}: InstructionsFlowProps) {
  // Main flow state management
  const flow = useInstructionFlow({
    steps: FLOW_STEPS,
    formulaData,
    autoSave,
    onStepChange: step => {
      const stepData = FLOW_STEPS[step];
      onStepChange?.(step, stepData);
    },
    onComplete,
    persistenceKey,
  });

  // Timer functionality for processing steps
  const timer = useStepTimer(
    {
      phases: [
        {
          id: 'processing',
          name: 'Procesado',
          duration: FLOW_STEPS[3].estimatedDuration! * 60, // Convert to seconds
          description: 'Tiempo de procesamiento del color',
          color: FLOW_STEPS[3].color,
        },
      ],
      autoAdvance: true,
      allowPause: true,
      backgroundTracking: true,
      persistState: true,
      onPhaseComplete: () => flow.advanceStep(),
      onTimerComplete: () => flow.advanceStep(),
    },
    `timer_${formulaData.id || 'unknown'}`
  );

  // Step validation logic
  const validation = useStepValidation({
    steps: FLOW_STEPS,
    formulaData,
    strictMode: false,
    enableProfessionalScoring: true,
  });

  // Animation system
  const animations = useAnimations(FLOW_STEPS.length);

  // Shared props for all step components
  const stepProps = useMemo(
    () => ({
      formulaData,
      flow,
      timer,
      validation,
      animations,
      onItemCheck: flow.toggleChecklistItem,
      onStepComplete: flow.markStepComplete,
    }),
    [formulaData, flow, timer, validation, animations]
  );

  // Validate current step
  const currentValidation = validation.validateStep(flow.currentStep, {
    checkedItems: flow.checkedItems,
    completedSteps: flow.completedSteps,
    elapsedTime: flow.getElapsedTime(),
  });

  // Navigation props
  const navigationProps = useMemo(
    () => ({
      canAdvance: flow.canAdvance && currentValidation.canProceed,
      canGoBack: flow.canGoBack,
      onAdvance: flow.advanceStep,
      onGoBack: flow.goToPreviousStep,
      onClose,
    }),
    [flow, currentValidation, onClose]
  );

  // Step component renderer
  const renderCurrentStep = useCallback(() => {
    switch (flow.currentStep) {
      case 0:
        return <PreparationStep {...stepProps} />;
      case 1:
        return <MixingStep {...stepProps} />;
      case 2:
        return <ApplicationStep {...stepProps} />;
      case 3:
        return <ProcessingStep {...stepProps} />;
      case 4:
        return <RinsingStep {...stepProps} />;
      case 5:
        return <CompletionStep {...stepProps} />;
      default:
        return <PreparationStep {...stepProps} />;
    }
  }, [flow.currentStep, stepProps]);

  return (
    <View style={styles.container} testID="instructions-flow-new">
      {/* Progress indicator */}
      <ProgressIndicator
        currentStep={flow.currentStep}
        totalSteps={flow.totalSteps}
        completedSteps={flow.completedSteps}
        progress={flow.progress}
        steps={FLOW_STEPS}
      />

      {/* Main content area */}
      <View style={styles.contentContainer}>{renderCurrentStep()}</View>

      {/* Navigation controls */}
      <StepNavigationComponent {...navigationProps} />
    </View>
  );
}

/**
 * Step Navigation Component
 * Handles navigation between steps with validation
 */
interface StepNavigationProps {
  canAdvance: boolean;
  canGoBack: boolean;
  onAdvance: () => void;
  onGoBack: () => void;
  onClose?: () => void;
}

function StepNavigationComponent({
  canAdvance,
  canGoBack,
  onAdvance,
  onGoBack,
  onClose,
}: StepNavigationProps) {
  return (
    <View style={styles.navigationContainer}>
      {/* Back button */}
      {canGoBack && (
        <StepCard
          title="Anterior"
          onPress={onGoBack}
          style={styles.navigationButton}
          icon={<ChevronLeft size={20} color={Colors.textSecondary} />}
        />
      )}

      {/* Spacer */}
      <View style={styles.navigationSpacer} />

      {/* Close button (if provided) */}
      {onClose && (
        <StepCard
          title="Cerrar"
          onPress={onClose}
          style={[styles.navigationButton, styles.closeButton]}
        />
      )}

      {/* Next button */}
      {canAdvance && (
        <StepCard
          title="Siguiente"
          onPress={onAdvance}
          style={[styles.navigationButton, styles.advanceButton]}
          icon={<ChevronRight size={20} color={Colors.white} />}
        />
      )}
    </View>
  );
}

/**
 * Styles
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navigationButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 100,
  },
  navigationSpacer: {
    flex: 1,
  },
  closeButton: {
    backgroundColor: Colors.backgroundSecondary,
    marginRight: 12,
  },
  advanceButton: {
    backgroundColor: Colors.primary,
  },
});

// Export for compatibility
export default InstructionsFlow;
