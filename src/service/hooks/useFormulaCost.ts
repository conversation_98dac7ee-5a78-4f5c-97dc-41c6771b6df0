import { useState, useCallback } from 'react';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { FormulaCost } from '@/types/formulation';

/**
 * Hook for managing formula cost calculations
 */
export const useFormulaCost = () => {
  const [formulaCost, setFormulaCost] = useState<FormulaCost | null>(null);

  const calculateFormulaCost = useCallback(async (formulaText: string): Promise<FormulaCost> => {
    // Starting cost calculation

    try {
      const salonConfig = useSalonConfigStore.getState();

      if (salonConfig.configuration.inventoryControlLevel === 'solo-formulas') {
        // Using simple cost calculation (solo-formulas mode)
        const simpleCost = calculateSimpleFormulaCost(formulaText);
        return simpleCost;
      } else {
        // Using inventory-based cost calculation

        // Use new text-based calculation that parses exact products
        const consumptionAnalysis =
          await InventoryConsumptionService.calculateFormulationCostFromText(formulaText);

        // Convert to FormulaCost format
        const items: FormulaCost['items'] = consumptionAnalysis.items.map(item => ({
          product: item.productName,
          amount: `${item.amount}${item.unit}`,
          unitCost: item.unitCost,
          totalCost: item.totalCost,
        }));

        const totalMaterialCost = consumptionAnalysis.totalCost;

        // Apply salon's configured markup
        const suggestedServicePrice = salonConfig.applyMarkup(totalMaterialCost);
        const profitMargin = suggestedServicePrice - totalMaterialCost;

        const finalCost = {
          items,
          totalMaterialCost: Math.round(totalMaterialCost * 100) / 100,
          suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
          profitMargin: Math.round(profitMargin * 100) / 100,
          hasAllRealCosts: consumptionAnalysis.hasAllRealCosts,
        };

        return finalCost;
      }
    } catch {
      // Formula cost calculation failed - using fallback
      const fallbackCost = calculateSimpleFormulaCost(formulaText);
      return fallbackCost;
    }
  }, []);

  return {
    formulaCost,
    setFormulaCost,
    calculateFormulaCost,
  };
};
