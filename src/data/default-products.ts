/**
 * Default products for Salonier inventory
 *
 * This module contains the predefined list of common products
 * that are offered when initializing a new salon's inventory.
 *
 * @module data/default-products
 */

import { Product } from '@/types';

/**
 * Predefined common products for salons
 * These are suggested when setting up a new salon
 */
export const defaultProducts: Omit<Product, 'id' | 'lastUpdated'>[] = [
  // Oxidantes
  {
    name: 'Oxidante 10 Vol (3%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 20 Vol (6%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1500,
    minStock: 300,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 30 Vol (9%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 1000,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  {
    name: 'Oxidante 40 Vol (12%)',
    brand: 'Genérico',
    category: 'oxidante',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 1000,
    purchasePrice: 5,
    costPerUnit: 0.005,
    isActive: true,
  },
  // Tintes
  {
    name: 'Koleston Perfect',
    brand: 'Wella',
    category: 'tinte',
    currentStock: 20,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7.5,
    costPerUnit: 0.125,
    isActive: true,
  },
  {
    name: 'Inoa',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 15,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 8,
    costPerUnit: 0.133,
    isActive: true,
  },
  {
    name: 'Igora Royal',
    brand: 'Schwarzkopf',
    category: 'tinte',
    currentStock: 18,
    minStock: 5,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 7,
    costPerUnit: 0.117,
    isActive: true,
  },
  {
    name: 'Majirel',
    brand: "L'Oréal",
    category: 'tinte',
    currentStock: 25,
    minStock: 8,
    unitType: 'ml',
    unitSize: 50,
    purchasePrice: 6.25,
    costPerUnit: 0.125,
    isActive: true,
  },
  // Decolorantes
  {
    name: 'Polvo Decolorante Azul',
    brand: 'Genérico',
    category: 'decolorante',
    currentStock: 500,
    minStock: 100,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 15,
    costPerUnit: 0.03,
    isActive: true,
  },
  {
    name: 'Blondor Multi Blonde',
    brand: 'Wella',
    category: 'decolorante',
    currentStock: 400,
    minStock: 100,
    unitType: 'g',
    unitSize: 800,
    purchasePrice: 28,
    costPerUnit: 0.035,
    isActive: true,
  },
  {
    name: 'Platinium Plus',
    brand: "L'Oréal",
    category: 'decolorante',
    currentStock: 300,
    minStock: 50,
    unitType: 'g',
    unitSize: 500,
    purchasePrice: 22,
    costPerUnit: 0.044,
    isActive: true,
  },
  // Tratamientos
  {
    name: 'Olaplex No.1',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 300,
    minStock: 50,
    unitType: 'ml',
    unitSize: 100,
    purchasePrice: 80,
    costPerUnit: 0.8,
    isActive: true,
  },
  {
    name: 'Olaplex No.2',
    brand: 'Olaplex',
    category: 'tratamiento',
    currentStock: 500,
    minStock: 100,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 120,
    costPerUnit: 0.24,
    isActive: true,
  },
  {
    name: 'Smartbond Step 1',
    brand: "L'Oréal",
    category: 'tratamiento',
    currentStock: 250,
    minStock: 50,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 45,
    costPerUnit: 0.09,
    isActive: true,
  },
  {
    name: 'Fiberplex No.1',
    brand: 'Schwarzkopf',
    category: 'tratamiento',
    currentStock: 200,
    minStock: 40,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 55,
    costPerUnit: 0.11,
    isActive: true,
  },
  {
    name: 'Wellaplex No.1',
    brand: 'Wella',
    category: 'tratamiento',
    currentStock: 180,
    minStock: 30,
    unitType: 'ml',
    unitSize: 500,
    purchasePrice: 50,
    costPerUnit: 0.1,
    isActive: true,
  },
  // Otros
  {
    name: 'Champú Silver',
    brand: "L'Oréal",
    category: 'otro',
    currentStock: 800,
    minStock: 200,
    unitType: 'ml',
    unitSize: 1500,
    purchasePrice: 18,
    costPerUnit: 0.012,
    isActive: true,
  },
  {
    name: 'Papel de Aluminio',
    brand: 'Genérico',
    category: 'otro',
    currentStock: 50,
    minStock: 10,
    maxStock: 100,
    unitType: 'unidad',
    unitSize: 1,
    purchasePrice: 12,
    costPerUnit: 12,
    isActive: true,
    notes: 'Rollos de 100 metros',
  },
];
