#!/usr/bin/env node
// Generate QA scenarios from Supabase brand/line catalog
// Inputs: SUPABASE_URL, SUPABASE_ANON_KEY

import process from 'node:process';
import { createClient } from '@supabase/supabase-js';
import fs from 'node:fs';
import path from 'node:path';

const SUPABASE_URL_RAW = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
const SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!SUPABASE_URL_RAW || (!SUPABASE_ANON_KEY && !SERVICE_ROLE)) {
  console.error('Missing SUPABASE_URL and at least one key (ANON or SERVICE_ROLE)');
  process.exit(2);
}
const SUPABASE_URL = SUPABASE_URL_RAW.replace(/\/(auth|rest)\/v1.*$/i, '');

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE || SUPABASE_ANON_KEY, {
  auth: { persistSession: false },
});

const TONER_KEYWORDS = ['color sync', 'colorance', 'shades eq', 'young color excel', 'shinefinity', 'color touch'];

function isLikelyColorLine(line) {
  const n = (line?.name || '').toLowerCase();
  const c = (line?.category || '').toLowerCase();
  if (/developer|oxidant|oxidan|activator|peroxide/.test(n)) return false;
  if (/bleach|decolor|lightener/.test(n)) return false;
  if (c.includes('developer') || c.includes('bleach')) return false;
  return c.includes('color') || /color|tinte|shade/.test(n);
}

function isLikelyToner(line) {
  const n = (line?.name || '').toLowerCase();
  if (TONER_KEYWORDS.some(k => n.includes(k))) return true;
  // Some DBs mark demi/toner in category
  const c = (line?.category || '').toLowerCase();
  return c.includes('toner') || c.includes('demi');
}

function pickDesiredTone(brandName, lineName) {
  // Light heuristic per family; QA does not require exact shade string
  return { level: 9, tone: 'ash/violet' };
}

async function main() {
  const { data: lines, error } = await supabase
    .from('product_lines')
    .select('id, name, brand_id, category, brands!inner(name)')
    .eq('is_active', true)
    .limit(2000);
  if (error) {
    console.error('Failed fetching product_lines:', error.message || error);
    process.exit(2);
  }
  const scenarios = [];
  for (const line of lines || []) {
    const brandName = line.brands?.name || 'Unknown';
    const lineName = line.name;
    if (!isLikelyColorLine(line)) continue;

    // Deposit scenario for color lines
    scenarios.push({
      name: `${brandName} ${lineName} — depósito nivel 6`,
      brand: brandName,
      line: lineName,
      diagnosis: { averageLevel: 6, state: 'natural' },
      desired: { desiredColor: { level: 6, tone: 'neutral/ash' } },
    });

    // Toner scenario for likely toner/demi lines
    if (isLikelyToner(line)) {
      const desired = pickDesiredTone(brandName, lineName);
      scenarios.push({
        name: `${brandName} ${lineName} — toner post-bleach`,
        brand: brandName,
        line: lineName,
        diagnosis: { averageLevel: 9, state: 'bleached' },
        desired: { desiredColor: desired },
      });
    }
  }

  // De-dup by name and cap to 50 to keep runtime short
  const dedup = new Map();
  for (const s of scenarios) {
    dedup.set(`${s.brand}|${s.line}|${s.name}`, s);
  }
  let final = Array.from(dedup.values()).slice(0, 50);

  // Fallback seeds if nothing could be generated (e.g., RLS restrictions)
  if (final.length === 0) {
    final = [
      { name: 'Redken Chromatics — depósito nivel 5', brand: 'Redken', line: 'Chromatics', diagnosis: { averageLevel: 5, state: 'natural' }, desired: { desiredColor: { level: 5, tone: 'warm natural' } } },
      { name: 'Redken — toner tras bleach', brand: 'Redken', line: 'Chromatics', diagnosis: { averageLevel: 9, state: 'bleached' }, desired: { desiredColor: { level: 9, tone: 'cool/violet' } } },
      { name: 'Revlon — bleach + toner demi', brand: 'Revlon Professional', line: 'Colorsmetique', diagnosis: { averageLevel: 9, state: 'bleached' }, desired: { desiredColor: { level: 10, tone: 'ash' } } },
      { name: 'Matrix — toner demi', brand: 'Matrix', line: 'Color Sync', diagnosis: { averageLevel: 9, state: 'bleached' }, desired: { desiredColor: { level: 9, tone: 'violet pearly' } } },
      { name: 'Goldwell — toner demi', brand: 'Goldwell', line: 'Colorance', diagnosis: { averageLevel: 8, state: 'bleached' }, desired: { desiredColor: { level: 8, tone: 'cool ash' } } },
      { name: 'Wella Illumina — depósito 8/1', brand: 'Wella Professionals', line: 'Illumina Color', diagnosis: { averageLevel: 8, state: 'natural' }, desired: { desiredColor: { level: 8, tone: 'ash' } } },
      { name: 'Wella Color Touch — toner post-bleach', brand: 'Wella Professionals', line: 'Color Touch', diagnosis: { averageLevel: 9, state: 'bleached' }, desired: { desiredColor: { level: 9, tone: 'ash/violet' } } },
      { name: 'Wella Shinefinity — toner post-bleach', brand: 'Wella Professionals', line: 'Shinefinity', diagnosis: { averageLevel: 9, state: 'bleached' }, desired: { desiredColor: { level: 9, tone: 'pearl/violet' } } },
    ];
  }

  const outPath = path.resolve('qa/formulation-scenarios.json');
  fs.writeFileSync(outPath, JSON.stringify(final, null, 2));
  console.log(`Generated ${final.length} QA scenarios to ${outPath}`);
}

main().catch(e => { console.error(e); process.exit(2); });
