import React, { useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Star, TrendingUp, CheckCircle2, Clock } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';

// Internal imports
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseCard } from '@/components/base/BaseCard';
import { useFormulaFeedbackStore, type FormulaFeedback } from '@/stores/formula-feedback-store';

interface FeedbackSummaryCardProps {
  formulaId?: string;
  serviceId?: string;
  title?: string;
  compact?: boolean;
  showTrends?: boolean;
}

interface FeedbackStats {
  total: number;
  averageRating: number;
  successRate: number;
  wouldUseAgainRate: number;
  recentCount: number;
}

export const FeedbackSummaryCard: React.FC<FeedbackSummaryCardProps> = ({
  formulaId,
  serviceId,
  title = 'Resumen de Feedback',
  compact = false,
  showTrends = false,
}) => {
  const { feedbacks, getFeedbackByFormula, getFeedbackByService } = useFormulaFeedbackStore();

  // Get relevant feedbacks
  const relevantFeedbacks = useMemo((): FormulaFeedback[] => {
    if (formulaId) {
      return getFeedbackByFormula(formulaId);
    }
    if (serviceId) {
      const feedback = getFeedbackByService(serviceId);
      return feedback ? [feedback] : [];
    }
    // Return all feedbacks if no specific filter
    return feedbacks;
  }, [feedbacks, formulaId, serviceId, getFeedbackByFormula, getFeedbackByService]);

  // Calculate statistics
  const stats = useMemo((): FeedbackStats => {
    if (relevantFeedbacks.length === 0) {
      return {
        total: 0,
        averageRating: 0,
        successRate: 0,
        wouldUseAgainRate: 0,
        recentCount: 0,
      };
    }

    const total = relevantFeedbacks.length;
    const totalRating = relevantFeedbacks.reduce((sum, f) => sum + f.rating, 0);
    const successCount = relevantFeedbacks.filter(f => f.worked_as_expected).length;
    const wouldUseAgainCount = relevantFeedbacks.filter(f => f.would_use_again).length;

    // Count recent feedback (last 7 days)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentCount = relevantFeedbacks.filter(f => new Date(f.created_at) >= weekAgo).length;

    return {
      total,
      averageRating: totalRating / total,
      successRate: (successCount / total) * 100,
      wouldUseAgainRate: (wouldUseAgainCount / total) * 100,
      recentCount,
    };
  }, [relevantFeedbacks]);

  // Get rating color based on average
  const getRatingColor = (rating: number): string => {
    if (rating >= 4.5) return Colors.light.success;
    if (rating >= 3.5) return Colors.light.warning;
    if (rating >= 2.5) return Colors.light.secondary;
    return Colors.light.error;
  };

  // Get success rate color
  const getSuccessColor = (rate: number): string => {
    if (rate >= 80) return Colors.light.success;
    if (rate >= 60) return Colors.light.warning;
    return Colors.light.error;
  };

  // Empty state
  if (stats.total === 0) {
    return (
      <Animated.View entering={FadeIn.duration(300)}>
        <BaseCard style={styles.card}>
          <View style={styles.emptyState}>
            <Star size={32} color={Colors.light.grayLight} />
            <Text style={styles.emptyTitle}>Sin feedback aún</Text>
            <Text style={styles.emptyDescription}>
              {formulaId || serviceId
                ? 'No hay feedback para este elemento'
                : 'Completa servicios para ver estadísticas'}
            </Text>
          </View>
        </BaseCard>
      </Animated.View>
    );
  }

  // Compact version
  if (compact) {
    return (
      <Animated.View entering={FadeIn.duration(300)}>
        <BaseCard style={[styles.card, styles.compactCard]}>
          <View style={styles.compactContent}>
            <View style={styles.compactStat}>
              <Star
                size={16}
                color={getRatingColor(stats.averageRating)}
                fill={getRatingColor(stats.averageRating)}
              />
              <Text style={[styles.compactValue, { color: getRatingColor(stats.averageRating) }]}>
                {stats.averageRating.toFixed(1)}
              </Text>
            </View>

            <View style={styles.compactStat}>
              <CheckCircle2 size={16} color={getSuccessColor(stats.successRate)} />
              <Text style={[styles.compactValue, { color: getSuccessColor(stats.successRate) }]}>
                {Math.round(stats.successRate)}%
              </Text>
            </View>

            <Text style={styles.compactTotal}>
              {stats.total} feedback{stats.total !== 1 ? 's' : ''}
            </Text>
          </View>
        </BaseCard>
      </Animated.View>
    );
  }

  // Full version
  return (
    <Animated.View entering={FadeIn.duration(300)}>
      <BaseCard style={styles.card}>
        <Text style={styles.title}>{title}</Text>

        <View style={styles.statsGrid}>
          {/* Average Rating */}
          <View style={styles.statItem}>
            <View style={styles.statHeader}>
              <Star
                size={20}
                color={getRatingColor(stats.averageRating)}
                fill={getRatingColor(stats.averageRating)}
              />
              <Text style={styles.statLabel}>Calificación</Text>
            </View>
            <Text style={[styles.statValue, { color: getRatingColor(stats.averageRating) }]}>
              {stats.averageRating.toFixed(1)}/5
            </Text>
            <View style={styles.starRow}>
              {[1, 2, 3, 4, 5].map(star => (
                <Star
                  key={star}
                  size={12}
                  color={
                    star <= Math.round(stats.averageRating)
                      ? Colors.light.warning
                      : Colors.light.grayLight
                  }
                  fill={
                    star <= Math.round(stats.averageRating) ? Colors.light.warning : 'transparent'
                  }
                />
              ))}
            </View>
          </View>

          {/* Success Rate */}
          <View style={styles.statItem}>
            <View style={styles.statHeader}>
              <CheckCircle2 size={20} color={getSuccessColor(stats.successRate)} />
              <Text style={styles.statLabel}>Éxito</Text>
            </View>
            <Text style={[styles.statValue, { color: getSuccessColor(stats.successRate) }]}>
              {Math.round(stats.successRate)}%
            </Text>
            <Text style={styles.statSubtext}>
              {relevantFeedbacks.filter(f => f.worked_as_expected).length} de {stats.total}
            </Text>
          </View>

          {/* Would Use Again */}
          <View style={styles.statItem}>
            <View style={styles.statHeader}>
              <TrendingUp size={20} color={Colors.light.primary} />
              <Text style={styles.statLabel}>Repetirían</Text>
            </View>
            <Text style={[styles.statValue, { color: Colors.light.primary }]}>
              {Math.round(stats.wouldUseAgainRate)}%
            </Text>
            <Text style={styles.statSubtext}>
              {relevantFeedbacks.filter(f => f.would_use_again).length} de {stats.total}
            </Text>
          </View>

          {/* Recent Activity */}
          {showTrends && (
            <View style={styles.statItem}>
              <View style={styles.statHeader}>
                <Clock size={20} color={Colors.light.textSecondary} />
                <Text style={styles.statLabel}>Esta semana</Text>
              </View>
              <Text style={[styles.statValue, { color: Colors.light.text }]}>
                {stats.recentCount}
              </Text>
              <Text style={styles.statSubtext}>
                {stats.recentCount === 0 ? 'Sin actividad' : 'nuevos feedback'}
              </Text>
            </View>
          )}
        </View>

        {/* Summary footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Basado en {stats.total} feedback{stats.total !== 1 ? 's' : ''}
          </Text>
          {stats.recentCount > 0 && !showTrends && (
            <Text style={styles.recentBadge}>+{stats.recentCount} esta semana</Text>
          )}
        </View>
      </BaseCard>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    ...shadows.sm,
  },

  // Title
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },

  // Stats grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  statItem: {
    width: '50%',
    paddingRight: spacing.sm,
    marginBottom: spacing.md,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.xs,
  },
  statValue: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    marginBottom: spacing.xs,
  },
  statSubtext: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  starRow: {
    flexDirection: 'row',
    gap: 2,
  },

  // Footer
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  footerText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  recentBadge: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    backgroundColor: Colors.light.primaryBackground,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },

  // Compact version
  compactCard: {
    paddingVertical: spacing.sm,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactStat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactValue: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.xs,
  },
  compactTotal: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },

  // Empty state
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  emptyTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.sm,
    marginBottom: spacing.xs,
  },
  emptyDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
});

export default FeedbackSummaryCard;
