#!/usr/bin/env npx tsx

/**
 * File Structure Validation Script
 * Validates that the project follows the established organizational patterns
 */

import { promises as fs } from 'fs';
import { join, extname, basename, dirname } from 'path';

interface ValidationRule {
  pattern: RegExp;
  expectedDirectory: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface ValidationIssue {
  type: 'misplaced' | 'naming' | 'deep_nesting' | 'circular_dependency';
  path: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestedFix: string;
}

const VALIDATION_RULES: ValidationRule[] = [
  // React Components
  {
    pattern: /\.tsx$/,
    expectedDirectory: 'src/components',
    category: 'component',
    severity: 'medium'
  },
  // Screens
  {
    pattern: /Screen\.tsx$/,
    expectedDirectory: 'src/screens',
    category: 'screen',
    severity: 'medium'
  },
  // Stores
  {
    pattern: /store\.ts$/,
    expectedDirectory: 'src/stores',
    category: 'store',
    severity: 'medium'
  },
  // Services
  {
    pattern: /service\.ts$/,
    expectedDirectory: 'src/services',
    category: 'service',
    severity: 'medium'
  },
  // Utils
  {
    pattern: /util\.ts$/,
    expectedDirectory: 'src/utils',
    category: 'utility',
    severity: 'low'
  },
  // Types
  {
    pattern: /types?\.ts$/,
    expectedDirectory: 'src/types',
    category: 'type',
    severity: 'low'
  },
  // Hooks
  {
    pattern: /^use[A-Z].+\.ts$/,
    expectedDirectory: 'src/hooks',
    category: 'hook',
    severity: 'medium'
  },
  // Constants
  {
    pattern: /constants?\.ts$/,
    expectedDirectory: 'src/constants',
    category: 'constant',
    severity: 'low'
  },
  // Tests
  {
    pattern: /\.test\.(ts|tsx)$/,
    expectedDirectory: '__tests__',
    category: 'test',
    severity: 'high'
  },
  // Assets
  {
    pattern: /\.(png|jpg|jpeg|gif|svg|ttf|otf|woff|woff2)$/,
    expectedDirectory: 'assets',
    category: 'asset',
    severity: 'medium'
  }
];

const MAX_DIRECTORY_DEPTH = 5;
const PROJECT_ROOT = process.cwd();

async function getAllFiles(dir: string, files: string[] = []): Promise<string[]> {
  const entries = await fs.readdir(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = join(dir, entry.name);

    // Skip node_modules and other excluded directories
    if (entry.isDirectory()) {
      if (!['node_modules', '.git', '.expo', 'dist', 'build', 'target-structure'].includes(entry.name)) {
        await getAllFiles(fullPath, files);
      }
    } else {
      files.push(fullPath);
    }
  }

  return files;
}

function getRelativePath(filePath: string): string {
  return filePath.replace(PROJECT_ROOT + '/', '');
}

function getDirectoryDepth(filePath: string): number {
  return filePath.split('/').length - 1;
}

function validateFileLocation(filePath: string): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const relativePath = getRelativePath(filePath);
  const fileName = basename(filePath);
  const fileDir = dirname(relativePath);

  // Check directory depth
  const depth = getDirectoryDepth(relativePath);
  if (depth > MAX_DIRECTORY_DEPTH) {
    issues.push({
      type: 'deep_nesting',
      path: relativePath,
      severity: 'medium',
      description: `File is nested too deeply (${depth} levels, max ${MAX_DIRECTORY_DEPTH})`,
      suggestedFix: 'Consider flattening the directory structure or organizing by feature'
    });
  }

  // Check against validation rules
  for (const rule of VALIDATION_RULES) {
    if (rule.pattern.test(fileName)) {
      if (!fileDir.includes(rule.expectedDirectory)) {
        issues.push({
          type: 'misplaced',
          path: relativePath,
          severity: rule.severity,
          description: `${rule.category} file should be in ${rule.expectedDirectory}`,
          suggestedFix: `Move to ${rule.expectedDirectory}/`
        });
      }
    }
  }

  // Check naming conventions
  if (fileName.includes(' ') || fileName.includes('#')) {
    issues.push({
      type: 'naming',
      path: relativePath,
      severity: 'medium',
      description: 'File name contains invalid characters (spaces or #)',
      suggestedFix: 'Use kebab-case or camelCase naming'
    });
  }

  return issues;
}

function validateNamingConventions(filePath: string): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const relativePath = getRelativePath(filePath);
  const fileName = basename(filePath, extname(filePath));

  // React components should be PascalCase
  if (filePath.endsWith('.tsx') && fileName !== fileName.charAt(0).toUpperCase() + fileName.slice(1)) {
    if (!fileName.startsWith('use') && !fileName.includes('Screen')) {
      issues.push({
        type: 'naming',
        path: relativePath,
        severity: 'low',
        description: 'React component should use PascalCase naming',
        suggestedFix: `Rename to ${fileName.charAt(0).toUpperCase() + fileName.slice(1)}.tsx`
      });
    }
  }

  // Hooks should start with 'use'
  if (filePath.includes('/hooks/') && !fileName.startsWith('use')) {
    issues.push({
      type: 'naming',
      path: relativePath,
      severity: 'medium',
      description: 'Hook files should start with "use"',
      suggestedFix: `Rename to use${fileName.charAt(0).toUpperCase() + fileName.slice(1)}.ts`
    });
  }

  return issues;
}

async function main() {
  console.log('🔍 Validating file structure...\n');

  try {
    const allFiles = await getAllFiles(PROJECT_ROOT);
    const issues: ValidationIssue[] = [];

    for (const file of allFiles) {
      issues.push(...validateFileLocation(file));
      issues.push(...validateNamingConventions(file));
    }

    // Group issues by severity
    const critical = issues.filter(i => i.severity === 'critical');
    const high = issues.filter(i => i.severity === 'high');
    const medium = issues.filter(i => i.severity === 'medium');
    const low = issues.filter(i => i.severity === 'low');

    console.log(`📊 Validation Results:`);
    console.log(`Total files scanned: ${allFiles.length}`);
    console.log(`Issues found: ${issues.length}`);
    console.log(`- Critical: ${critical.length}`);
    console.log(`- High: ${high.length}`);
    console.log(`- Medium: ${medium.length}`);
    console.log(`- Low: ${low.length}\n`);

    // Display issues by severity
    const displayIssues = (severity: string, issueList: ValidationIssue[]) => {
      if (issueList.length > 0) {
        console.log(`🚨 ${severity.toUpperCase()} Issues:`);
        issueList.forEach(issue => {
          console.log(`  ${issue.path}`);
          console.log(`    ${issue.description}`);
          console.log(`    💡 ${issue.suggestedFix}\n`);
        });
      }
    };

    displayIssues('critical', critical);
    displayIssues('high', high);
    displayIssues('medium', medium.slice(0, 10)); // Limit medium issues display

    if (medium.length > 10) {
      console.log(`... and ${medium.length - 10} more medium issues`);
    }

    if (low.length > 0) {
      console.log(`ℹ️  ${low.length} low priority issues (use --verbose to see all)`);
    }

    // Exit codes
    if (critical.length > 0) {
      console.log('\n❌ Critical issues found. Please fix before proceeding.');
      process.exit(1);
    } else if (high.length > 0) {
      console.log('\n⚠️  High priority issues found. Consider fixing soon.');
      process.exit(0);
    } else {
      console.log('\n✅ Structure validation passed!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Error validating structure:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { validateFileLocation, validateNamingConventions, ValidationIssue, ValidationRule };