# Sistema de Feedback de Fórmulas

Un sistema completo para recolectar, analizar y mostrar feedback sobre fórmulas de coloración. Diseñado para ser simple y no intrusivo, permitiendo a los estilistas evaluar rápidamente la efectividad de las fórmulas generadas por IA.

## 🏗️ Arquitectura del Sistema

### Componentes Principales

1. **FormulaFeedback** - Modal principal de feedback con opciones rápidas y detalladas
2. **FeedbackManager** - Gestor automático que programa y muestra feedback requests
3. **FeedbackStats** - Componente de visualización de estadísticas
4. **FormulaFeedbackIndicator** - Indicador compacto para mostrar en listas/cards
5. **useFormulaFeedback** - Hook personalizado con lógica de negocio
6. **formula-feedback-store** - Store Zustand con persistencia offline

### Base de Datos

Utiliza las tablas existentes del sistema `proven_formulas`:

- `proven_formulas` - Fórmulas exitosas con métricas agregadas
- `formula_feedback` - Feedback individual de cada uso

## 🚀 Integración Rápida

### 1. Configurar el Manager Global

En tu layout principal (ej: `app/_layout.tsx`):

```tsx
import FeedbackManager from '@/app/components/FeedbackManager';

export default function RootLayout() {
  return <FeedbackManager>{/* Tu app aquí */}</FeedbackManager>;
}
```

### 2. Programar Feedback Automáticamente

Cuando completes un servicio con fórmula AI:

```tsx
import { useFormulaFeedback } from '@/hooks/useFormulaFeedback';

const { scheduleServiceFeedback } = useFormulaFeedback();

// Al completar servicio
scheduleServiceFeedback({
  formulaId: 'formula-uuid',
  serviceId: 'service-uuid',
  clientName: 'María González',
  formulaSummary: 'Wella Blondor - De castaño nivel 4 a rubio ceniza',
  delayMinutes: 30, // Opcional, default 30 min
});
```

### 3. Mostrar Estadísticas

En dashboard o vista de fórmula:

```tsx
import FeedbackStats from '@/app/components/FeedbackStats';

// Stats generales
<FeedbackStats />

// Stats de fórmula específica
<FeedbackStats formulaId="formula-uuid" showTitle={false} />
```

### 4. Indicadores en Listas

En componentes de fórmula:

```tsx
import FormulaFeedbackIndicator from '@/app/components/FormulaFeedbackIndicator';

// Versión compacta para listas
<FormulaFeedbackIndicator
  formulaId="formula-uuid"
  compact
  onPress={() => navigate('/feedback-details')}
/>

// Versión completa para cards
<FormulaFeedbackIndicator formulaId="formula-uuid" />
```

## 🎯 Flujo de Usuario

### Experiencia del Estilista

1. **Completa un servicio** con fórmula generada por IA
2. **30 minutos después** aparece automáticamente el modal de feedback
3. **Evaluación rápida**: 3 botones grandes
   - 👍 Perfecto (5 estrellas, auto-envía)
   - ⚠️ Necesitó ajustes (modal de detalles)
   - ❌ No funcionó (modal de detalles)
4. **Opciones adicionales**:
   - Posponer 15min/30min/1hr/4hrs
   - Descartar (con confirmación)
   - Detalles para feedback detallado

### Feedback Detallado

- Calificación por estrellas (1-5)
- Campo de texto: "¿Qué resultado obtuviste?"
- Campo de texto: "¿Qué ajustes hiciste?" (si aplica)
- Checkbox: "¿Usarías esta fórmula otra vez?"

## 📊 Métricas Generadas

### Stats por Fórmula

- **Total de usos**: Cuántas veces se aplicó
- **Tasa de éxito**: % que funcionó como esperado
- **Calificación promedio**: 1-5 estrellas
- **Tasa de reutilización**: % que la usarían otra vez

### Clasificación de Calidad

- **Excelente**: ⭐ 4.5+ rating, 90%+ éxito
- **Buena**: ⭐ 4.0+ rating, 80%+ éxito
- **Regular**: ⭐ 3.0+ rating, 60%+ éxito
- **Pobre**: ❌ Por debajo de los anteriores

### Fórmulas "Probadas"

Una fórmula se marca como "PROBADA" cuando:

- 3+ usos registrados
- 80%+ tasa de éxito
- 4.0+ calificación promedio

## 🔧 API del Hook useFormulaFeedback

```tsx
const {
  // Estado
  isSubmitting,
  error,
  hasPendingRequests,

  // Acciones de feedback
  submitQuickFeedback,
  submitDetailedFeedback,
  scheduleServiceFeedback,

  // Datos
  getFormulaStats,
  getServiceFeedback,
  getAllFeedbacks,

  // Gestión de requests
  getActiveFeedbackRequests,
  dismissRequest,
  snoozeRequest,
} = useFormulaFeedback();
```

### Ejemplo de Uso Programático

```tsx
// Feedback rápido
await submitQuickFeedback({
  formulaId: 'uuid',
  serviceId: 'uuid',
  workedAsExpected: true,
  rating: 5,
  wouldUseAgain: true,
});

// Feedback detallado
await submitDetailedFeedback({
  formulaId: 'uuid',
  serviceId: 'uuid',
  workedAsExpected: false,
  rating: 3,
  wouldUseAgain: true,
  actualResult: 'Salió más rojizo de lo esperado',
  adjustmentsMade: 'Agregué toner ceniza T18 por 10 min adicionales',
});

// Obtener stats
const stats = getFormulaStats('formula-uuid');
console.log(`Rating: ${stats.avgRating}, Usos: ${stats.totalUses}`);
```

## 🎨 Personalización UI

### Temas y Colores

Los componentes usan el sistema de colores de Salonier:

- `Colors.light.success` - Feedback positivo
- `Colors.light.warning` - Necesita ajustes
- `Colors.light.error` - No funcionó
- `Colors.light.primary` - Elementos principales

### Variants de Componentes

```tsx
// Indicador compacto para listas
<FormulaFeedbackIndicator formulaId="uuid" compact />

// Stats sin título para integraciones
<FeedbackStats formulaId="uuid" showTitle={false} />

// Manager con configuración personalizada
<FeedbackManager
  checkInterval={60000}  // Check cada minuto
  defaultDelay={45}      // 45 min default delay
/>
```

## 🔍 Debugging y Logging

El sistema incluye logging completo para debugging:

```bash
# Filtrar logs de feedback
adb logcat | grep "FormulaFeedback"

# Logs incluyen:
- Feedback requests programados
- Submission exitosos/fallidos
- Stats calculados
- Operaciones de sync
```

### Estados de Debug

```tsx
// Check store state
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';

const { feedbacks, pendingRequests } = useFormulaFeedbackStore();
console.log('Feedbacks:', feedbacks.length);
console.log('Pending:', pendingRequests.length);
```

## 📱 Offline Support

El sistema funciona completamente offline:

- **Feedback se guarda localmente** usando AsyncStorage
- **Auto-sync** cuando recupera conectividad
- **Queue system** maneja reintento automático
- **Optimistic UI** actualiza inmediatamente

### Sync Management

```tsx
import { useSyncQueueStore } from '@/stores/sync-queue-store';

const { getQueueStatus } = useSyncQueueStore();
const status = getQueueStatus();
console.log(`Pending: ${status.pending}, Failed: ${status.failed}`);
```

## 🔐 Seguridad y Permisos

- **RLS (Row Level Security)** en Supabase
- **Isolation por salón** - no cross-contamination
- **User ownership** validation
- **Data anonymization** para analytics

## 🚨 Consideraciones Importantes

### Performance

- **Lazy loading** de componentes grandes
- **Virtualization** para listas de feedback
- **Debounced** auto-save operations
- **Memory management** para large datasets

### UX Guidelines

- **Non-intrusive** - nunca bloquear flujo principal
- **Quick actions** - máximo 2 taps para feedback básico
- **Progressive disclosure** - detalles opcionales
- **Smart defaults** - inferir rating de quick actions

### Analytics Privacy

- **Opt-out** disponible en settings
- **Anonymous aggregation** para insights generales
- **Local storage** de preferencias de usuario
- **GDPR compliance** para datos EU

## 🧪 Testing

```bash
# Unit tests
npm run test -- --testPathPattern=feedback

# E2E tests
npm run test:e2e -- --grep "feedback"

# Component tests
npm run storybook
# Navegar a Feedback stories
```

---

Este sistema está diseñado para ser **plug-and-play** con integración mínima requerida. Los componentes son independientes y pueden usarse según necesidad.

Para soporte adicional, revisar los logs o contactar al equipo de desarrollo.
