# Repository Guidelines

This app is an Expo/React Native project (Expo Router + TypeScript) integrated with Supabase and Zustand.

## Project Structure & Module Organization

- `app/` Expo Router routes and screens (e.g., `app/(tabs)`, `app/modal.tsx`).
- `components/` reusable UI components in `.tsx`.
- `stores/` Zustand state (e.g., `ai-analysis-store.ts`).
- `utils/` helpers (e.g., `image-processor.ts`, `logger.ts`).
- `services/` domain services (inventory, color, product).
- `constants/`, `types/`, `styles/`, `assets/` static assets.
- Tests: `__tests__/`, `e2e/`, `test-utils/`. Backend: `supabase/` (migrations, functions).

## Build, Test, and Development Commands

- `npm run dev` start Expo dev server.
- `npm run start-web` run web target.
- `npm run android` / `npm run ios` launch platforms.
- `npm test` unit tests; `npm run test:coverage` coverage.
- `npm run e2e:full` (or `npm run test:e2e`) end‑to‑end tests.
- `npm run lint` / `npm run lint:fix` ESLint; `npm run format` Prettier.
- `npm run code-quality` run lint + formatting checks bundle.

## Coding Style & Naming Conventions

- TypeScript, 2‑space indent, single quotes, semicolons, width 100, trailing commas `es5` (see `.prettierrc`).
- Components `PascalCase.tsx`; hooks `useThing.ts(x)`; stores `kebab-case-store.ts`.
- Utilities/services follow local pattern; prefer named exports.
- Avoid `console.*`; use `utils/logger.ts`. Style with `nativewind`/`StyleSheet`; avoid non‑trivial inline styles.

## Testing Guidelines

- Jest preset: `jest-expo` with `@testing-library/react-native` (`jest.setup.js`).
- Place tests in `__tests__/` or alongside as `*.test.ts(x)` / `*.spec.ts(x)`.
- Coverage thresholds: branches 70%, lines/functions 75%+. Keep tests deterministic; minimal snapshots.

## Commit & Pull Request Guidelines

- Conventional Commits: `feat(scope): …`, `fix(color): …`. Keep short, imperative, scoped; link issues.
- Before pushing: `npm run code-quality && npm test` (and E2E if relevant).
- PRs: clear description, rationale, test instructions, and screenshots/GIFs for UI changes. Prefer small, focused diffs.

## Security & Configuration Tips

- Use `.env.local` with `EXPO_PUBLIC_*` (e.g., `EXPO_PUBLIC_SUPABASE_URL`, `EXPO_PUBLIC_SUPABASE_ANON_KEY`). Never commit private/service‑role keys.
- Prefer private storage buckets; avoid logging sensitive data. Husky pre‑commit runs `lint:fix`, a targeted Jest subset, and blocks `console.*` in `src/`, `app/`, `stores/`, `utils/`.

## Agent‑Specific Notes

- Changes must honor this file’s conventions repo‑wide. Update docs/tests when adding features and keep coverage thresholds green.
