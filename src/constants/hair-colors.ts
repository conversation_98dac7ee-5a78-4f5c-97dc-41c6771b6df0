// Hair color palette - Comprehensive professional colors for UI display
export const hairColorMap: Record<string, string> = {
  // NEGROS (Niveles 1-2)
  Negro: '#1a1a1a',
  'Negro Castaño': '#2d2016',

  // CASTAÑOS OSCUROS (Niveles 3-4)
  'Castaño Oscuro': '#3d2314',
  'Castaño Oscuro Ceni<PERSON>': '#3a2820',
  'Castaño O<PERSON>curo <PERSON>': '#4a2a17',

  // CASTAÑOS MEDIOS (Nivel 5)
  'Castaño Medio': '#5c3825',
  'Castaño Medio Ceniza': '#5a3e33',
  'Castaño Medio Dorado': '#664029',
  'Castaño Medio Cobrizo': '#6b3a26',

  // CASTAÑOS CLAROS (Nivel 6)
  'Castaño Claro': '#8b6239',
  'Castaño Claro Ceniza': '#7d5a45',
  'Castaño Claro Dora<PERSON>': '#9a6b3e',
  'Castaño <PERSON>': '#a0613a',

  // RUBIOS OSCUROS (Nivel 7)
  'Rubio Oscuro': '#b08d57',
  'Rubio Oscuro Ceniza': '#9d8566',
  'Rubio Oscuro Dorado': '#c19560',
  'Rubio Oscuro Beige': '#b5916b',

  // RUBIOS MEDIOS (Nivel 8)
  'Rubio Medio': '#d4a574',
  'Rubio Medio Ceniza': '#c8a47a', // ← THE CRITICAL MISSING COLOR!
  'Rubio Ceniza': '#c8a47a', // ← Direct alias for AI results
  'Rubio Medio Dorado': '#e0b077',
  'Rubio Medio Beige': '#d2ab82',

  // RUBIOS CLAROS (Nivel 9)
  'Rubio Claro': '#e6c697',
  'Rubio Claro Ceniza': '#dcc49a',
  'Rubio Claro Dorado': '#f0d09b',
  'Rubio Claro Perla': '#e8caa5',

  // RUBIOS MUY CLAROS (Nivel 10)
  'Rubio Muy Claro': '#f4e0c1',
  'Rubio Muy Claro Ceniza': '#f0dcbf',
  'Rubio Muy Claro Dorado': '#f8e5c4',
  'Rubio Platino': '#faf0dc',

  // ROJOS NATURALES
  Rojo: '#8b3a26',
  Caoba: '#6f2c1f',
  'Rojo Cobrizo': '#b85742',
  'Rojo Caoba': '#7d2e20',

  // ENTRADAS GENÉRICAS (compatibilidad)
  Castaño: '#5c3825',
  Rubio: '#d4a574',

  // REFLEJOS/SUBTONOS TÉCNICOS
  Cenizo: '#8a8680', // .1 - Ash
  Violeta: '#8b7898', // .2 - Violet
  Natural: '#a0826d', // .0 - Natural
  Dorado: '#d4a574', // .3 - Golden
  Cobrizo: '#b85742', // .4 - Copper
  Caoba: '#6f2c1f', // .5 - Mahogany
  Rojizo: '#a0522d', // .6 - Red
  Mate: '#8a8068', // .7 - Matte/Green
  Perla: '#e8e3f5', // .8 - Pearl
  Irisado: '#e8e3f5', // Iridescent
  Beige: '#e6d7c3', // Beige naturals
  Chocolate: '#4a3529', // Chocolate browns
  Miel: '#f0d09b', // Honey golds
  Arena: '#e6d7c3', // Sand beiges

  // MATICES NO DESEADOS
  Naranja: '#ff8c42',
  Amarillo: '#ffd966',
  Verde: '#7cb342',

  // TEMPERATURAS DE COLOR
  Frío: '#6c9bd2',
  Neutro: '#a0a0a0',
  Cálido: '#d4a574',
};

// Temperature indicators
export const temperatureColors = {
  cold: '#6c9bd2',
  neutral: '#a0a0a0',
  warm: '#d4a574',
};

// Get temperature from reflect - Updated for comprehensive professional reflects
export const getTemperatureFromReflect = (reflect: string): 'cold' | 'neutral' | 'warm' => {
  // Cold reflects (.1, .2, .7, .8)
  const coldTones = [
    'Cenizo',
    'Violeta',
    'Perla',
    'Irisado',
    'Mate',
    'Frío',
    // Also handle compound terms
    'Ceniza',
    'Ash',
    'Pearl',
    'Violet',
    'Purple',
    'Matte',
  ];

  // Warm reflects (.3, .4, .5, .6)
  const warmTones = [
    'Dorado',
    'Cobrizo',
    'Rojizo',
    'Caoba',
    'Chocolate',
    'Miel',
    'Cálido',
    // Also handle compound terms
    'Golden',
    'Copper',
    'Red',
    'Mahogany',
    'Honey',
    'Warm',
  ];

  // Natural/Neutral reflects (.0)
  const neutralTones = ['Natural', 'Beige', 'Arena', 'Neutro', 'Sand', 'Neutral'];

  if (coldTones.includes(reflect)) return 'cold';
  if (warmTones.includes(reflect)) return 'warm';
  if (neutralTones.includes(reflect)) return 'neutral';

  // Handle compound terms (like "Rubio Ceniza")
  const lowerReflect = reflect.toLowerCase();
  if (coldTones.some(tone => lowerReflect.includes(tone.toLowerCase()))) return 'cold';
  if (warmTones.some(tone => lowerReflect.includes(tone.toLowerCase()))) return 'warm';

  return 'neutral';
};

// Legacy alias for backward compatibility
export const getTemperatureFromUndertone = getTemperatureFromReflect;

// Helper to get indicator type for a field
export const getIndicatorType = (
  label: string
): 'thickness' | 'density' | 'color' | 'state' | 'none' => {
  const lowerLabel = label.toLowerCase();

  if (lowerLabel.includes('grosor')) return 'thickness';
  if (lowerLabel.includes('densidad')) return 'density';
  if (lowerLabel.includes('tono') || lowerLabel.includes('color') || lowerLabel.includes('matiz'))
    return 'color';
  if (lowerLabel.includes('estado')) return 'state';

  return 'none';
};
