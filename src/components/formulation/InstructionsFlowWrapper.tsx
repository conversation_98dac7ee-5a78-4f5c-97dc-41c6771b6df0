/**
 * InstructionsFlow Wrapper Component
 *
 * Provides seamless A/B testing between legacy and new implementations.
 * Handles feature flag evaluation, error boundaries, and monitoring.
 *
 * SAFETY FEATURES:
 * - Instant rollback via feature flags
 * - Error boundaries with automatic fallback
 * - Performance monitoring and alerting
 * - User experience consistency
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import * as Haptics from 'expo-haptics';
import { logger } from '@/utils/logger';

// Feature flag system
import {
  shouldUseNewInstructionsFlow,
  isEmergencyRollbackActive,
  getFeatureFlagOverride,
} from '@/utils/featureFlags';

// Component implementations
import InstructionsFlowNew, { InstructionsFlowProps } from './InstructionsFlow.new';
import InstructionsFlowLegacy from './InstructionsFlow.legacy';
import Colors from '@/constants/colors';

/**
 * Error Boundary for New Implementation
 * Automatically falls back to legacy version on any error
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class InstructionsFlowErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: (error: Error) => void },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode; onError: (error: Error) => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error for development debugging
    if (__DEV__) {
      logger.error('InstructionsFlow New Implementation Error:', { error, errorInfo });
    }
    this.props.onError(error);

    // Log error for monitoring
    this.logError(error, errorInfo);
  }

  private logError(error: Error, errorInfo: React.ErrorInfo) {
    // In production, send to error tracking service
    if (!__DEV__) {
      // TODO: Integrate with error tracking service (Sentry, Bugsnag, etc.)
      logger.error('Production error in new InstructionsFlow:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error en la nueva implementación. Usando versión estable...
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

/**
 * Performance Monitor Component
 * Tracks rendering performance and user interactions
 */
interface PerformanceMetrics {
  renderTime: number;
  componentMountTime: number;
  userInteractions: number;
  errorCount: number;
}

function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: Date.now(),
    userInteractions: 0,
    errorCount: 0,
  });

  const trackInteraction = () => {
    setMetrics(prev => ({
      ...prev,
      userInteractions: prev.userInteractions + 1,
    }));
  };

  const trackError = () => {
    setMetrics(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1,
    }));
  };

  const getMetrics = () => ({
    ...metrics,
    sessionDuration: Date.now() - metrics.componentMountTime,
  });

  return {
    trackInteraction,
    trackError,
    getMetrics,
  };
}

/**
 * Main Wrapper Component
 */
export function InstructionsFlowWrapper(props: InstructionsFlowProps) {
  const [useNewImplementation, setUseNewImplementation] = useState<boolean | null>(null);
  const [emergencyRollback, setEmergencyRollback] = useState(false);
  const [hasError, setHasError] = useState(false);

  const performance = usePerformanceMonitoring();

  // Determine which implementation to use
  useEffect(() => {
    async function determineImplementation() {
      try {
        // Check for emergency rollback first
        const isEmergencyActive = await isEmergencyRollbackActive();
        if (isEmergencyActive) {
          setEmergencyRollback(true);
          setUseNewImplementation(false);
          return;
        }

        // Check for manual override
        const override = await getFeatureFlagOverride('NEW_INSTRUCTIONS_FLOW');
        if (override !== null) {
          setUseNewImplementation(override);
          return;
        }

        // Use feature flag logic
        const shouldUseNew = shouldUseNewInstructionsFlow();
        setUseNewImplementation(shouldUseNew);

        // Log A/B test assignment (for analytics)
        if (!__DEV__) {
          logger.info('A/B Test Assignment', 'InstructionsFlowWrapper', {
            version: shouldUseNew ? 'new' : 'legacy',
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        logger.warn('Error determining implementation', 'InstructionsFlowWrapper', error);
        // Fallback to legacy on any error
        setUseNewImplementation(false);
      }
    }

    determineImplementation();
  }, []);

  // Handle error from new implementation
  const handleNewImplementationError = (error: Error) => {
    setHasError(true);
    performance.trackError();

    // Provide haptic feedback for error
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

    // Log error occurrence
    logger.error(
      'New InstructionsFlow failed, falling back to legacy',
      'InstructionsFlowWrapper',
      error
    );
  };

  // Enhanced props with monitoring
  const enhancedProps = {
    ...props,
    onStepChange: (step: number, stepData: object) => {
      performance.trackInteraction();
      props.onStepChange?.(step, stepData);
    },
    onComplete: () => {
      performance.trackInteraction();

      // Log completion metrics
      const metrics = performance.getMetrics();
      logger.info('Completion Metrics', 'InstructionsFlowWrapper', {
        version: useNewImplementation ? 'new' : 'legacy',
        ...metrics,
      });

      props.onComplete?.();
    },
  };

  // Loading state while determining implementation
  if (useNewImplementation === null) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Cargando...</Text>
      </View>
    );
  }

  // Force legacy for emergency rollback or errors
  if (emergencyRollback || hasError || !useNewImplementation) {
    return <InstructionsFlowLegacy {...enhancedProps} />;
  }

  // New implementation with error boundary
  return (
    <InstructionsFlowErrorBoundary onError={handleNewImplementationError}>
      <InstructionsFlowNew {...enhancedProps} />
    </InstructionsFlowErrorBoundary>
  );
}

/**
 * Styles
 */
const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    fontFamily: 'System',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: Colors.warning,
    borderRadius: 8,
    margin: 16,
  },
  errorText: {
    fontSize: 14,
    color: Colors.warningText,
    textAlign: 'center',
  },
});

// Export as default for easy replacement
export default InstructionsFlowWrapper;
