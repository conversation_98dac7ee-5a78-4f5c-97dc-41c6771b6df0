# Feedback UI Components

This document explains the new feedback UI components that make formula feedback requests more visible and accessible to users.

## Overview

The feedback system now includes three main UI components that work together:

1. **FeedbackFloatingButton** - Prominent floating action button for pending feedbacks
2. **FeedbackBadgeIndicator** - Discrete badge for headers and navigation areas
3. **Enhanced FeedbackManager** - Improved management with user control options

## Components

### FeedbackFloatingButton

A prominent floating action button that appears when there are pending feedback requests.

**Features:**

- Shows pending feedback count with animated badge
- Bouncing animations to draw attention
- Configurable positioning (bottom-right/bottom-left)
- Subtle pulse animation for visibility
- Quality star indicator for visual appeal
- Haptic feedback on interactions

**Usage:**

```tsx
import { FeedbackFloatingButton } from '@/components/FeedbackFloatingButton';

<FeedbackFloatingButton
  onRequestFeedback={handleFeedbackRequest}
  position="bottom-right"
  bottomOffset={100}
/>;
```

**Props:**

- `onRequestFeedback`: Callback when user taps the button
- `position`: 'bottom-right' | 'bottom-left' (default: 'bottom-right')
- `bottomOffset`: Distance from bottom of screen (default: 100)

### FeedbackBadgeIndicator

A discrete badge indicator perfect for headers, navigation bars, or anywhere space is limited.

**Features:**

- Two variants: 'compact' and 'full'
- Configurable icon and count display
- Subtle pulse animation for new requests
- Professional minimal design
- Accessible with proper contrast ratios

**Usage:**

```tsx
import { FeedbackBadgeIndicator } from '@/components/FeedbackBadgeIndicator';

// Compact version for tight spaces
<FeedbackBadgeIndicator
  onRequestFeedback={handleFeedbackRequest}
  variant="compact"
  showIcon={true}
  showCount={true}
/>

// Full version with text
<FeedbackBadgeIndicator
  onRequestFeedback={handleFeedbackRequest}
  variant="full"
/>
```

**Props:**

- `onRequestFeedback`: Callback when user taps the badge
- `variant`: 'compact' | 'full' (default: 'full')
- `showIcon`: Whether to show the message icon (default: true)
- `showCount`: Whether to show the count badge (default: true)
- `testID`: Test identifier for testing

### Enhanced FeedbackManager

The main manager now provides better control over feedback display behavior.

**New Features:**

- Option to disable automatic feedback modals
- Configurable floating button position
- Better integration with user-initiated feedback

**Usage:**

```tsx
import { FeedbackManager } from '@/components/FeedbackManager';

<FeedbackManager
  autoShowFeedback={true} // Set to false for manual-only mode
  floatingButtonPosition="bottom-right"
  floatingButtonBottomOffset={100}
>
  {/* Your app content */}
</FeedbackManager>;
```

**Props:**

- `children`: App content to wrap
- `autoShowFeedback`: Whether to auto-show feedback modals (default: true)
- `floatingButtonPosition`: Position of floating button (default: 'bottom-right')
- `floatingButtonBottomOffset`: Bottom offset for floating button (default: 100)

## Hook: useFeedbackManager

A custom hook for managing feedback requests in any component.

**Features:**

- Reactive access to pending feedback requests
- Utilities for managing feedback state
- Automatic cleanup of old requests
- Configurable refresh intervals

**Usage:**

```tsx
import { useFeedbackManager } from '@/hooks/useFeedbackManager';

function MyComponent() {
  const {
    pendingRequests,
    pendingCount,
    hasPendingRequests,
    getNextRequest,
    requestFeedback,
    currentRequest,
    closeCurrentRequest,
    isModalVisible,
  } = useFeedbackManager({
    checkInterval: 30000, // Check every 30 seconds
    autoCleanup: true,
  });

  // Your component logic...
}
```

**Return Values:**

- `pendingRequests`: Array of pending feedback requests
- `pendingCount`: Number of pending requests
- `hasPendingRequests`: Boolean indicating if there are pending requests
- `getNextRequest()`: Get the oldest pending request
- `refreshRequests()`: Manually refresh the request list
- `requestFeedback(request)`: Show feedback for a specific request
- `currentRequest`: Currently displayed request (if any)
- `closeCurrentRequest()`: Close the current feedback modal
- `isModalVisible`: Whether feedback modal is currently shown

## Integration Patterns

### Pattern 1: Full Auto Mode (Default)

Perfect for most use cases - automatic feedback with floating button backup:

```tsx
<FeedbackManager autoShowFeedback={true}>
  <YourApp />
</FeedbackManager>
```

### Pattern 2: Manual Mode Only

User has full control, feedback only shown when requested:

```tsx
<FeedbackManager autoShowFeedback={false}>
  <YourApp />
</FeedbackManager>
```

### Pattern 3: Header Integration

Add feedback badge to your headers:

```tsx
import { HeaderWithFeedbackBadge } from '@/components/examples/HeaderWithFeedbackBadge';

<HeaderWithFeedbackBadge title="My Screen" onBack={handleBack} onMenu={handleMenu} />;
```

### Pattern 4: Custom Integration

Use the hook for complete custom control:

```tsx
function CustomScreen() {
  const { hasPendingRequests, requestFeedback, getNextRequest } = useFeedbackManager();

  const handleCustomFeedback = () => {
    const next = getNextRequest();
    if (next) {
      requestFeedback(next);
    }
  };

  return (
    <View>
      {hasPendingRequests && <Button onPress={handleCustomFeedback}>Hay feedback pendiente</Button>}
    </View>
  );
}
```

## Design Principles

### Professional Minimalism

- Follows Salonier's 90/10 beauty minimalism strategy
- Uses brand colors strategically (10% usage rule)
- Maintains professional trust while being approachable

### Accessibility

- WCAG 2.1 AA compliant contrast ratios
- Proper semantic roles and labels
- Haptic feedback for better user experience
- Keyboard navigation support

### Performance

- Efficient re-rendering with proper memoization
- Optimized animations using native driver
- Smart polling intervals to minimize battery impact
- Automatic cleanup of stale data

### User Experience

- Non-intrusive design that doesn't block workflows
- Progressive disclosure (compact → full information)
- Consistent with platform conventions
- Clear visual hierarchy and call-to-action

## Testing

All components include testID props for automated testing:

```tsx
// Testing floating button
const floatingButton = await screen.findByTestId('feedback-floating-button');
fireEvent.press(floatingButton);

// Testing badge indicator
const badgeIndicator = await screen.findByTestId('feedback-badge-indicator');
expect(badgeIndicator).toBeVisible();

// Testing header integration
const headerBadge = await screen.findByTestId('header-feedback-badge');
fireEvent.press(headerBadge);
```

## Migration Guide

### From Existing FeedbackManager

No breaking changes - existing usage continues to work:

```tsx
// Before (still works)
<FeedbackManager>
  <YourApp />
</FeedbackManager>

// After (enhanced with new features)
<FeedbackManager autoShowFeedback={true} floatingButtonPosition="bottom-right">
  <YourApp />
</FeedbackManager>
```

### Adding Badge to Existing Headers

Replace your current header with the feedback-enabled version:

```tsx
// Before
<Header title="My Screen" onBack={handleBack} />

// After
<HeaderWithFeedbackBadge
  title="My Screen"
  onBack={handleBack}
  onMenu={handleMenu}
/>
```

## Troubleshooting

### Floating Button Not Appearing

- Check that FeedbackManager is wrapping your app
- Verify there are actually pending feedback requests
- Check `bottomOffset` doesn't place button off-screen

### Badge Not Updating

- Ensure `useFeedbackManager` hook is being used correctly
- Check that `autoCleanup` is enabled if using stale data
- Verify the check interval is reasonable (not too long)

### Performance Issues

- Reduce `checkInterval` if checking too frequently
- Use `variant="compact"` for better performance in lists
- Consider disabling `autoShowFeedback` if not needed

### Animations Laggy

- Ensure `useNativeDriver: true` is used in custom animations
- Check that the device isn't in low-power mode
- Verify proper cleanup of animation references
