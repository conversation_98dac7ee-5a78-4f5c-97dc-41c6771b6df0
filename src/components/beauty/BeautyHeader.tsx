/**
 * BEAUTY HEADER COMPONENT
 *
 * Claude-style compact header (48px height) with beauty-minimalist styling.
 * Consistent across all screens with proper safe area handling.
 *
 * Features:
 * - Claude-inspired compact density
 * - Professional neutral colors (90% strategy)
 * - Strategic beauty accent colors (10% strategy)
 * - Safe area compatibility
 * - Flexible action button support
 */

import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { ArrowLeft, LucideIcon } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BeautyHeaderProps {
  title: string;
  subtitle?: string;
  onBack?: () => void;
  showBackButton?: boolean;
  rightIcon?: LucideIcon;
  onRightPress?: () => void;
  rightButtonTitle?: string;
  backgroundColor?: string;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  testID?: string;
  accessibilityLabel?: string;
}

/**
 * BeautyHeader - Consistent header following <PERSON>'s compact design
 *
 * DESIGN PRINCIPLES:
 * - 48px height (Claude-style compact vs 64px traditional)
 * - Warm papaya-whip pearl background (90% strategy)
 * - Strategic barn-red color for actions (10% strategy)
 * - Proper typography hierarchy
 * - Safe area handling for all devices
 */
export const BeautyHeader = memo<BeautyHeaderProps>(
  ({
    title,
    subtitle,
    onBack,
    showBackButton = true,
    rightIcon: RightIcon,
    onRightPress,
    rightButtonTitle,
    backgroundColor,
    style,
    titleStyle,
    testID = 'beauty-header',
    accessibilityLabel,
  }) => {
    const handleBackPress = useCallback(() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      if (onBack) {
        onBack();
      } else {
        // Default behavior: go back in router
        if (router.canGoBack()) {
          router.back();
        }
      }
    }, [onBack]);

    const handleRightPress = useCallback(() => {
      if (onRightPress) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onRightPress();
      }
    }, [onRightPress]);

    const headerStyle = [styles.container, backgroundColor && { backgroundColor }, style];

    return (
      <SafeAreaView style={styles.safeArea} testID={testID}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={backgroundColor || BeautyMinimalTheme.semantic.background.primary}
        />
        <View style={headerStyle}>
          {/* Left side - Back button */}
          <View style={styles.leftSection}>
            {showBackButton && (
              <TouchableOpacity
                onPress={handleBackPress}
                style={styles.backButton}
                hitSlop={styles.hitSlop}
                testID={`${testID}-back`}
                accessibilityRole="button"
                accessibilityLabel="Volver"
                accessibilityHint="Navega a la pantalla anterior"
              >
                <ArrowLeft size={24} color={BeautyMinimalTheme.semantic.text.primary} />
              </TouchableOpacity>
            )}
          </View>

          {/* Center - Title and subtitle */}
          <View style={styles.centerSection}>
            <Text
              style={[styles.title, titleStyle]}
              numberOfLines={1}
              ellipsizeMode="tail"
              testID={`${testID}-title`}
              accessibilityLabel={accessibilityLabel || title}
            >
              {title}
            </Text>
            {subtitle && (
              <Text
                style={styles.subtitle}
                numberOfLines={1}
                ellipsizeMode="tail"
                testID={`${testID}-subtitle`}
              >
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right side - Action button */}
          <View style={styles.rightSection}>
            {(RightIcon || rightButtonTitle) && onRightPress && (
              <TouchableOpacity
                onPress={handleRightPress}
                style={styles.rightButton}
                hitSlop={styles.hitSlop}
                testID={`${testID}-right`}
                accessibilityRole="button"
                accessibilityLabel={rightButtonTitle || 'Acción'}
              >
                {RightIcon && (
                  <RightIcon
                    size={24}
                    color={BeautyMinimalTheme.semantic.interactive.primary.default} // 10% barn-red color
                  />
                )}
                {rightButtonTitle && <Text style={styles.rightButtonText}>{rightButtonTitle}</Text>}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </SafeAreaView>
    );
  }
);

BeautyHeader.displayName = 'BeautyHeader';

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary, // Warm papaya-whip pearl background
  },

  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 48, // Claude-style compact height
    paddingHorizontal: BeautyMinimalTheme.spacing.component.screenMargin,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle, // Subtle warm border
  },

  leftSection: {
    flex: 0,
    flexDirection: 'row',
    alignItems: 'center',
    width: 40, // Fixed width for consistent alignment
  },

  centerSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
  },

  rightSection: {
    flex: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 80, // Fixed width for consistent alignment
  },

  // Back button styling
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BeautyMinimalTheme.radius.sm,
  },

  // Title styling
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading, // 18px
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary, // Prussian-blue charcoal (90% warm)
    lineHeight:
      BeautyMinimalTheme.typography.sizes.heading * BeautyMinimalTheme.typography.lineHeights.tight,
    textAlign: 'center',
  },

  subtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption, // 11px
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.semantic.text.tertiary, // Warm silver (90% warm)
    lineHeight:
      BeautyMinimalTheme.typography.sizes.caption *
      BeautyMinimalTheme.typography.lineHeights.normal,
    textAlign: 'center',
    marginTop: 1,
  },

  // Right button styling
  rightButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.minimum, // 44px minimum touch target
  },

  rightButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body, // 14px
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.interactive.primary.default, // 10% barn-red
    marginLeft: BeautyMinimalTheme.spacing.xs,
  },

  // Touch targets
  hitSlop: {
    top: 8,
    bottom: 8,
    left: 8,
    right: 8,
  },
});
