import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import Svg, { Path, Text as SvgText } from 'react-native-svg';
import { ApplicationGuide } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

interface ApplicationDiagramProps {
  guide: ApplicationGuide;
}

const { width: screenWidth } = Dimensions.get('window');
const diagramSize = Math.min(screenWidth - 80, 250);

export default function ApplicationDiagram({ guide }: ApplicationDiagramProps) {
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  const getHeadPath = () => {
    // SVG path for a simplified head shape
    const scale = diagramSize / 250;
    return `
      M ${125 * scale} ${30 * scale}
      C ${180 * scale} ${30 * scale}, ${220 * scale} ${70 * scale}, ${220 * scale} ${125 * scale}
      L ${220 * scale} ${180 * scale}
      C ${220 * scale} ${220 * scale}, ${190 * scale} ${240 * scale}, ${125 * scale} ${240 * scale}
      C ${60 * scale} ${240 * scale}, ${30 * scale} ${220 * scale}, ${30 * scale} ${180 * scale}
      L ${30 * scale} ${125 * scale}
      C ${30 * scale} ${70 * scale}, ${70 * scale} ${30 * scale}, ${125 * scale} ${30 * scale}
      Z
    `;
  };

  const getZonePath = (zoneId: string) => {
    const scale = diagramSize / 250;

    switch (zoneId) {
      // For zonal coloring (roots/mids/ends)
      case 'roots':
        return `
          M ${125 * scale} ${30 * scale}
          C ${180 * scale} ${30 * scale}, ${220 * scale} ${70 * scale}, ${220 * scale} ${90 * scale}
          L ${30 * scale} ${90 * scale}
          C ${30 * scale} ${70 * scale}, ${70 * scale} ${30 * scale}, ${125 * scale} ${30 * scale}
          Z
        `;
      case 'mids':
        return `
          M ${30 * scale} ${90 * scale}
          L ${220 * scale} ${90 * scale}
          L ${220 * scale} ${170 * scale}
          L ${30 * scale} ${170 * scale}
          Z
        `;
      case 'ends':
        return `
          M ${30 * scale} ${170 * scale}
          L ${220 * scale} ${170 * scale}
          C ${220 * scale} ${220 * scale}, ${190 * scale} ${240 * scale}, ${125 * scale} ${240 * scale}
          C ${60 * scale} ${240 * scale}, ${30 * scale} ${220 * scale}, ${30 * scale} ${170 * scale}
          Z
        `;
      // For anatomical zones (full color)
      case 'crown':
        return `
          M ${125 * scale} ${30 * scale}
          C ${180 * scale} ${30 * scale}, ${220 * scale} ${70 * scale}, ${220 * scale} ${100 * scale}
          L ${30 * scale} ${100 * scale}
          C ${30 * scale} ${70 * scale}, ${70 * scale} ${30 * scale}, ${125 * scale} ${30 * scale}
          Z
        `;
      case 'nape':
        return `
          M ${30 * scale} ${180 * scale}
          L ${220 * scale} ${180 * scale}
          C ${220 * scale} ${220 * scale}, ${190 * scale} ${240 * scale}, ${125 * scale} ${240 * scale}
          C ${60 * scale} ${240 * scale}, ${30 * scale} ${220 * scale}, ${30 * scale} ${180 * scale}
          Z
        `;
      case 'sides':
        return `
          M ${30 * scale} ${100 * scale}
          L ${30 * scale} ${180 * scale}
          L ${70 * scale} ${180 * scale}
          L ${70 * scale} ${100 * scale}
          Z
          M ${180 * scale} ${100 * scale}
          L ${220 * scale} ${100 * scale}
          L ${220 * scale} ${180 * scale}
          L ${180 * scale} ${180 * scale}
          Z
        `;
      // For techniques
      case 'v-section':
        return `
          M ${125 * scale} ${50 * scale}
          L ${60 * scale} ${200 * scale}
          L ${80 * scale} ${210 * scale}
          L ${125 * scale} ${100 * scale}
          L ${170 * scale} ${210 * scale}
          L ${190 * scale} ${200 * scale}
          Z
        `;
      case 'foils':
        // Stripes pattern for highlights
        return `
          M ${50 * scale} ${50 * scale}
          L ${60 * scale} ${50 * scale}
          L ${60 * scale} ${200 * scale}
          L ${50 * scale} ${200 * scale}
          Z
          M ${95 * scale} ${50 * scale}
          L ${105 * scale} ${50 * scale}
          L ${105 * scale} ${200 * scale}
          L ${95 * scale} ${200 * scale}
          Z
          M ${145 * scale} ${50 * scale}
          L ${155 * scale} ${50 * scale}
          L ${155 * scale} ${200 * scale}
          L ${145 * scale} ${200 * scale}
          Z
          M ${190 * scale} ${50 * scale}
          L ${200 * scale} ${50 * scale}
          L ${200 * scale} ${200 * scale}
          L ${190 * scale} ${200 * scale}
          Z
        `;
      default:
        return '';
    }
  };

  const handleZonePress = (zoneId: string) => {
    setSelectedZone(zoneId === selectedZone ? null : zoneId);
  };

  const renderSteps = () => {
    return guide.steps.map((step, index) => (
      <View key={index} style={styles.stepItem}>
        <View style={styles.stepNumber}>
          <Text style={styles.stepNumberText}>{step.order}</Text>
        </View>
        <Text style={styles.stepDescription}>{step.description}</Text>
        {step.duration && <Text style={styles.stepDuration}>{step.duration} min</Text>}
      </View>
    ));
  };

  return (
    <View style={styles.container}>
      {/* Technique Title */}
      <View style={styles.techniqueHeader}>
        <Text style={styles.techniqueTitle}>
          {guide.technique === 'balayage'
            ? 'Técnica Balayage'
            : guide.technique === 'highlights'
              ? 'Técnica de Mechas'
              : guide.technique === 'zonal'
                ? 'Aplicación por Zonas'
                : 'Aplicación Completa'}
        </Text>
        <Text style={styles.totalTime}>Tiempo total: {guide.totalTime} min</Text>
      </View>

      {/* Head Diagram */}
      <View style={styles.diagramContainer}>
        <Svg width={diagramSize} height={diagramSize} viewBox={`0 0 ${diagramSize} ${diagramSize}`}>
          {/* Base head outline */}
          <Path
            d={getHeadPath()}
            fill={Colors.light.backgroundSecondary}
            stroke={Colors.light.border}
            strokeWidth="3"
          />

          {/* Zones */}
          {guide.zones.map(zone => (
            <TouchableOpacity
              key={zone.id}
              onPress={() => handleZonePress(zone.id)}
              activeOpacity={0.7}
            >
              <Path
                d={getZonePath(zone.id)}
                fill={selectedZone === zone.id ? zone.color : `${zone.color}88`}
                stroke={zone.color}
                strokeWidth="2"
                opacity={selectedZone && selectedZone !== zone.id ? 0.3 : 1}
              />
              {/* Zone numbers for different techniques */}
              {/* Roots/Mids/Ends zones */}
              {zone.id === 'roots' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.24}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  {zone.order}
                </SvgText>
              )}
              {zone.id === 'mids' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.52}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  {zone.order}
                </SvgText>
              )}
              {zone.id === 'ends' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.82}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  {zone.order}
                </SvgText>
              )}
              {/* Anatomical zones */}
              {zone.id === 'crown' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.25}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  {zone.order}
                </SvgText>
              )}
              {zone.id === 'nape' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.85}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  {zone.order}
                </SvgText>
              )}
              {zone.id === 'sides' && (
                <>
                  <SvgText
                    x={diagramSize * 0.2}
                    y={diagramSize * 0.55}
                    fill="white"
                    fontSize="24"
                    fontWeight="bold"
                    textAnchor="middle"
                  >
                    3
                  </SvgText>
                  <SvgText
                    x={diagramSize * 0.8}
                    y={diagramSize * 0.55}
                    fill="white"
                    fontSize="24"
                    fontWeight="bold"
                    textAnchor="middle"
                  >
                    4
                  </SvgText>
                </>
              )}
              {/* Special technique zones */}
              {zone.id === 'v-section' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.5}
                  fill="white"
                  fontSize="24"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  V
                </SvgText>
              )}
              {zone.id === 'foils' && (
                <SvgText
                  x={diagramSize / 2}
                  y={diagramSize * 0.5}
                  fill={Colors.light.primary}
                  fontSize="18"
                  fontWeight="bold"
                  textAnchor="middle"
                >
                  MECHAS
                </SvgText>
              )}
            </TouchableOpacity>
          ))}
        </Svg>
      </View>

      {/* Zone Legend */}
      <View style={styles.legendContainer}>
        {guide.zones.map(zone => (
          <TouchableOpacity
            key={zone.id}
            style={[styles.legendItem, selectedZone === zone.id && styles.legendItemActive]}
            onPress={() => handleZonePress(zone.id)}
          >
            <View style={[styles.legendColor, { backgroundColor: zone.color }]} />
            <View style={styles.legendTextContainer}>
              <Text style={styles.legendName}>{zone.name}</Text>
              {zone.description && <Text style={styles.legendDescription}>{zone.description}</Text>}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Application Steps */}
      {guide.steps.length > 0 && (
        <View style={styles.stepsContainer}>
          <Text style={styles.stepsTitle}>Pasos de Aplicación</Text>
          {renderSteps()}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 15,
    padding: 20,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  techniqueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  techniqueTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  totalTime: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  diagramContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.transparent,
  },
  legendItemActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primaryTransparent10,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendTextContainer: {
    flex: 1,
  },
  legendName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  legendDescription: {
    fontSize: 11,
    color: Colors.light.gray,
  },
  stepsContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingTop: 15,
  },
  stepsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 10,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  stepNumberText: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
  },
  stepDescription: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  stepDuration: {
    fontSize: 12,
    color: Colors.light.gray,
    marginLeft: 10,
  },
});
