import { Database } from '@/types/database';

// Tipos de datos para el historial del cliente

export interface FormulaData {
  selectedBrand?: string;
  selectedLine?: string;
  formula?: string;
  cost?: number;
  products?: Array<{
    name: string;
    quantity: number;
    unit: string;
  }>;
}

export interface AIAnalysis {
  detectedLevel?: number;
  detectedTone?: string;
  viabilityScore?: number;
  confidence?: number;
  recommendations?: string[];
  warnings?: string[];
}

export interface ServiceDetails {
  id: string;
  clientId: string;
  clientName: string;
  serviceType: string;
  formula?: string;
  formulaData?: FormulaData;
  technique?: string;
  processingTime?: number;
  developerVolume?: number;
  satisfaction?: number;
  notes?: string;
  beforePhotos?: string[];
  afterPhotos?: string[];
  aiAnalysis?: AIAnalysis;
  createdAt: string;
  completedAt?: string;
}

export interface ClientAllergy {
  substance: string;
  severity: 'leve' | 'moderada' | 'severa';
  dateDetected: string;
  notes?: string;
}

export interface ClientPreference {
  category: 'tono' | 'marca' | 'tecnica' | 'tiempo';
  value: string;
  priority: 'alta' | 'media' | 'baja';
}

export interface HairEvolution {
  date: string;
  level: string;
  porosity: string;
  damage: string;
  resistance: string;
  notes: string;
}

export interface PreviousFormula {
  id: string; // formula_id cuando existe; si no, service_id
  serviceId?: string; // siempre que sea posible, el id del servicio asociado
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface PatchTest {
  id: string;
  date: string;
  products: string[];
  result: 'negativo' | 'positivo' | 'pendiente';
  notes?: string;
  reminderSent: boolean;
}

export interface ConsentRecord {
  id: string;
  date: string;
  consentItems: Array<{
    id: string;
    title: string;
    description: string;
    accepted: boolean;
  }>;
  signature: string;
  safetyChecklist: Array<{
    id: string;
    title: string;
    checked: boolean;
  }>;
  ipAddress: string;
  userAgent: string;
  skipSafetyVerification?: boolean;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface ClientHistoryProfile {
  clientId: string;
  allergies: ClientAllergy[];
  preferences: ClientPreference[];
  hairEvolution: HairEvolution[];
  previousFormulas: PreviousFormula[];
  patchTests: PatchTest[];
  consentRecords: ConsentRecord[];
  riskLevel: 'bajo' | 'medio' | 'alto';
  lastAnalysisDate?: string;
  totalServices: number;
  averageSatisfaction: number | null;
  preferredBrands: string[];
  avoidedIngredients: string[];
  specialNotes: string[];
}

// Tipos de Supabase para referencia
export type SupabaseService = Database['public']['Tables']['services']['Row'];
export type SupabaseFormula = Database['public']['Tables']['formulas']['Row'];
export type SupabaseConsent = Database['public']['Tables']['client_consents']['Row'];

// Estado del Store de Zustand
export interface ClientHistoryState {
  clientProfiles: Record<string, ClientHistoryProfile>;
  isLoading: boolean;
  isInitialized: boolean;

  // Acciones
  loadClientHistory: (clientId: string) => Promise<void>;
  getClientProfile: (clientId: string) => ClientHistoryProfile | null;
  updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => void;
  addAllergy: (clientId: string, allergy: ClientAllergy) => void;
  addPreference: (clientId: string, preference: ClientPreference) => void;
  addHairEvolution: (clientId: string, evolution: HairEvolution) => void;
  addPreviousFormula: (clientId: string, formula: PreviousFormula) => Promise<void>;
  addPatchTest: (clientId: string, test: PatchTest) => void;
  addConsentRecord: (clientId: string, consent: ConsentRecord) => Promise<void>;
  getRecommendationsForClient: (clientId: string) => string[];
  getWarningsForClient: (clientId: string) => string[];
  calculateRiskLevel: (clientId: string) => 'bajo' | 'medio' | 'alto';
  getCompatibleFormulas: (clientId: string) => PreviousFormula[];
  getLastPatchTest: (clientId: string) => PatchTest | null;
  getLastConsent: (clientId: string) => ConsentRecord | null;
  initializeClientProfile: (clientId: string) => void;
  saveCompletedService: (serviceData: {
    clientId: string;
    clientName: string;
    serviceType: string;
    formula?: string;
    formulaData?: FormulaData;
    technique?: string;
    processingTime?: number;
    developerVolume?: number;
    satisfaction?: number;
    notes?: string;
    beforePhotos?: string[];
    afterPhotos?: string[];
    aiAnalysis?: AIAnalysis;
  }) => Promise<ServiceDetails>;
  getServiceDetails: (serviceId: string) => Promise<ServiceDetails | null>;
  syncWithSupabase: () => Promise<void>;
  clearAllProfiles: () => void;
}
