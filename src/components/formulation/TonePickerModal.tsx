import React, { useEffect, useState } from 'react';
import { Modal, View, Text, TouchableOpacity, ActivityIndicator, FlatList } from 'react-native';
import { supabase } from '@/lib/supabase';

type Props = {
  visible: boolean;
  brand: string | null | undefined;
  line: string | null | undefined;
  onClose: () => void;
  onSelect: (shadeCode: string) => void;
};

export const TonePickerModal: React.FC<Props> = ({ visible, brand, line, onClose, onSelect }) => {
  const [loading, setLoading] = useState(false);
  const [shades, setShades] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchShades = async () => {
      if (!visible || !brand || !line) return;
      setLoading(true);
      setError(null);
      try {
        const { data: b } = await supabase
          .from('brands')
          .select('id')
          .ilike('name', brand)
          .maybeSingle();
        if (!b?.id) {
          setShades([]);
          setError('Marca no encontrada');
          setLoading(false);
          return;
        }
        const { data: l } = await supabase
          .from('product_lines')
          .select('id')
          .eq('brand_id', b.id)
          .ilike('name', line)
          .maybeSingle();
        if (!l?.id) {
          setShades([]);
          setError('Línea no encontrada');
          setLoading(false);
          return;
        }
        const { data: s } = await supabase
          .from('brand_line_shades')
          .select('shade_code')
          .eq('brand_id', b.id)
          .eq('line_id', l.id)
          .eq('is_active', true)
          .order('shade_code');
        setShades((s || []).map((r: any) => r.shade_code));
      } catch (e: any) {
        setError(e?.message || 'Error al cargar tonos');
      } finally {
        setLoading(false);
      }
    };
    fetchShades();
  }, [visible, brand, line]);

  return (
    <Modal visible={visible} animationType="slide" transparent onRequestClose={onClose}>
      <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.4)', justifyContent: 'flex-end' }}>
        <View
          style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            maxHeight: '70%',
          }}
        >
          <View style={{ padding: 16, borderBottomWidth: 1, borderBottomColor: '#eee' }}>
            <Text style={{ fontWeight: '600', fontSize: 16 }}>Selecciona un tono</Text>
            {!!brand && !!line && (
              <Text style={{ color: '#666', marginTop: 4 }}>
                {brand} — {line}
              </Text>
            )}
          </View>

          {loading && (
            <View style={{ padding: 24, alignItems: 'center' }}>
              <ActivityIndicator />
            </View>
          )}
          {!loading && error && (
            <View style={{ padding: 16 }}>
              <Text style={{ color: '#b91c1c' }}>{error}</Text>
            </View>
          )}
          {!loading && !error && (
            <FlatList
              data={shades}
              keyExtractor={item => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => onSelect(item)}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 14,
                    borderBottomWidth: 1,
                    borderBottomColor: '#f3f4f6',
                  }}
                >
                  <Text style={{ fontSize: 15 }}>{item}</Text>
                </TouchableOpacity>
              )}
            />
          )}

          <View style={{ padding: 12, alignItems: 'flex-end' }}>
            <TouchableOpacity
              onPress={onClose}
              style={{ paddingHorizontal: 12, paddingVertical: 8 }}
            >
              <Text style={{ color: '#2563eb', fontWeight: '600' }}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default TonePickerModal;
