import * as React from 'react';
import { useEffect, useRef, useCallback } from 'react';
import { InteractionManager, AppState, AppStateStatus } from 'react-native';
import { logger } from '@/utils/logger';

// Memory leak prevention utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private cleanup: (() => void)[] = [];
  private intervals: NodeJS.Timeout[] = [];
  private timeouts: NodeJS.Timeout[] = [];
  private listeners: { remove: () => void }[] = [];

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  addCleanup(fn: () => void): void {
    this.cleanup.push(fn);
  }

  addInterval(interval: NodeJS.Timeout): void {
    this.intervals.push(interval);
  }

  addTimeout(timeout: NodeJS.Timeout): void {
    this.timeouts.push(timeout);
  }

  addListener(listener: { remove: () => void }): void {
    this.listeners.push(listener);
  }

  cleanup(): void {
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];

    // Clear all timeouts
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts = [];

    // Remove all listeners
    this.listeners.forEach(listener => listener.remove());
    this.listeners = [];

    // Run custom cleanup functions
    this.cleanup.forEach(fn => {
      try {
        fn();
      } catch (error) {
        logger.warn('Cleanup function failed:', error);
      }
    });
    this.cleanup = [];
  }
}

// Hook for automatic cleanup on unmount
export function useCleanup(cleanupFn: () => void): void {
  useEffect(() => {
    return cleanupFn;
  }, [cleanupFn]);
}

// Hook for safe async operations
export function useSafeAsync<T>(): {
  execute: (asyncFn: () => Promise<T>) => Promise<T | undefined>;
  cancel: () => void;
} {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const execute = useCallback(async (asyncFn: () => Promise<T>) => {
    if (!isMountedRef.current) return undefined;

    try {
      const result = await asyncFn();
      return isMountedRef.current ? result : undefined;
    } catch (error) {
      if (isMountedRef.current) {
        throw error;
      }
      return undefined;
    }
  }, []);

  const cancel = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  return { execute, cancel };
}

// Hook for managing timers with automatic cleanup
export function useTimer(): {
  setTimeout: (callback: () => void, delay: number) => NodeJS.Timeout;
  setInterval: (callback: () => void, delay: number) => NodeJS.Timeout;
  clearAll: () => void;
} {
  const timersRef = useRef<{
    timeouts: NodeJS.Timeout[];
    intervals: NodeJS.Timeout[];
  }>({ timeouts: [], intervals: [] });

  const clearAll = useCallback(() => {
    timersRef.current.timeouts.forEach(clearTimeout);
    timersRef.current.intervals.forEach(clearInterval);
    timersRef.current = { timeouts: [], intervals: [] };
  }, []);

  useEffect(() => {
    return clearAll;
  }, [clearAll]);

  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    const timeout = setTimeout(callback, delay);
    timersRef.current.timeouts.push(timeout);
    return timeout;
  }, []);

  const safeSetInterval = useCallback((callback: () => void, delay: number) => {
    const interval = setInterval(callback, delay);
    timersRef.current.intervals.push(interval);
    return interval;
  }, []);

  return {
    setTimeout: safeSetTimeout,
    setInterval: safeSetInterval,
    clearAll,
  };
}

// Hook for managing event listeners
export function useEventListeners(): {
  addListener: (listener: { remove: () => void }) => void;
  removeAll: () => void;
} {
  const listenersRef = useRef<{ remove: () => void }[]>([]);

  const addListener = useCallback((listener: { remove: () => void }) => {
    listenersRef.current.push(listener);
  }, []);

  const removeAll = useCallback(() => {
    listenersRef.current.forEach(listener => {
      try {
        listener.remove();
      } catch (error) {
        logger.warn('Failed to remove listener:', error);
      }
    });
    listenersRef.current = [];
  }, []);

  useEffect(() => {
    return removeAll;
  }, [removeAll]);

  return { addListener, removeAll };
}

// Hook for background task cleanup
export function useBackgroundTaskCleanup(): {
  scheduleCleanup: (task: () => void) => void;
  runCleanup: () => void;
} {
  const cleanupTasksRef = useRef<(() => void)[]>([]);

  const scheduleCleanup = useCallback((task: () => void) => {
    cleanupTasksRef.current.push(task);
  }, []);

  const runCleanup = useCallback(() => {
    // Use InteractionManager to run cleanup after interactions
    InteractionManager.runAfterInteractions(() => {
      cleanupTasksRef.current.forEach(task => {
        try {
          task();
        } catch (error) {
          logger.warn('Background cleanup task failed:', error);
        }
      });
      cleanupTasksRef.current = [];
    });
  }, []);

  // Run cleanup when app goes to background
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background') {
        runCleanup();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Cleanup on unmount
    return () => {
      subscription?.remove();
      runCleanup();
    };
  }, [runCleanup]);

  return { scheduleCleanup, runCleanup };
}

// HOC for automatic memory management
export function withMemoryManagement<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>
) {
  const _displayName = Component.displayName || Component.name || 'Component';

  return function MemoryManagedComponent(props: P) {
    const memoryManager = MemoryManager.getInstance();

    useEffect(() => {
      return () => {
        // Cleanup on unmount
        memoryManager.cleanup();
      };
    }, [memoryManager]);

    return <Component {...props} />;
  };
}

// Cache cleanup utilities
export function createCacheCleanupStrategy(
  maxSize = 100,
  ttl = 300000 // 5 minutes
) {
  return {
    shouldCleanup: (cacheSize: number, lastCleanup: number) => {
      return cacheSize > maxSize || Date.now() - lastCleanup > ttl;
    },

    cleanup: <T,>(cache: Map<string, T>, isExpired: (item: T) => boolean) => {
      const entriesToDelete: string[] = [];

      cache.forEach((value, key) => {
        if (isExpired(value)) {
          entriesToDelete.push(key);
        }
      });

      // If still too large after removing expired items, remove oldest
      if (cache.size - entriesToDelete.length > maxSize) {
        const allKeys = Array.from(cache.keys());
        const oldestKeys = allKeys.slice(0, allKeys.length - maxSize);
        entriesToDelete.push(...oldestKeys);
      }

      entriesToDelete.forEach(key => cache.delete(key));

      return entriesToDelete.length;
    },
  };
}

// Performance monitoring for memory leaks
export function useMemoryMonitor(componentName: string): void {
  useEffect(() => {
    const startTime = Date.now();
    const startMemory =
      (performance as PerformanceNavigator & { memory?: { usedJSHeapSize: number } }).memory
        ?.usedJSHeapSize || 0;

    return () => {
      const endTime = Date.now();
      const endMemory =
        (performance as PerformanceNavigator & { memory?: { usedJSHeapSize: number } }).memory
          ?.usedJSHeapSize || 0;
      const duration = endTime - startTime;
      const memoryDelta = endMemory - startMemory;

      // Log potential memory leaks
      if (memoryDelta > 5 * 1024 * 1024) {
        // 5MB increase
        logger.warn(`Potential memory leak detected in ${componentName}:`, {
          duration: `${duration}ms`,
          memoryIncrease: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`,
        });
      }
    };
  }, [componentName]);
}
