import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { supabase } from '@/lib/supabase';

type Props = {
  brand?: string | null;
  line?: string | null;
  formulationData: any;
  onApply: (next: any) => void;
};

async function loadLineRules(brand?: string | null, line?: string | null) {
  if (!brand || !line) return null;
  const { data: b } = await supabase.from('brands').select('id').ilike('name', brand).maybeSingle();
  if (!b?.id) return null;
  const { data: l } = await supabase
    .from('product_lines')
    .select('id')
    .eq('brand_id', b.id)
    .ilike('name', line)
    .maybeSingle();
  if (!l?.id) return null;
  const { data: r } = await supabase
    .from('brand_line_rules')
    .select('ratio_exact, ratio_min, ratio_max, allowed_vols, is_active')
    .eq('brand_id', b.id)
    .eq('line_id', l.id)
    .maybeSingle();
  return r || null;
}

export const QuickFixActions: React.FC<Props> = ({ brand, line, formulationData, onApply }) => {
  const fixVolumes = async () => {
    const rules = await loadLineRules(brand || null, line || null);
    if (!rules || !rules.allowed_vols || rules.allowed_vols.length === 0) return;
    const allowed: number[] = rules.allowed_vols as number[];
    const clone = JSON.parse(JSON.stringify(formulationData));
    clone.steps = (clone.steps || []).map((s: any) => ({
      ...s,
      mix: (s.mix || []).map((p: any) => {
        const name = String(p?.productName || '');
        const type = String(p?.type || '').toLowerCase();
        if (!/developer|oxidante|activador/i.test(name) && type !== 'developer') return p;
        const current = (() => {
          const m = name.match(/(\d+)\s*vol/i) || String(p?.volume || '')?.match(/(\d+)/);
          return m ? parseInt(m[1]) : null;
        })();
        if (!current || allowed.includes(current)) return p;
        // choose nearest allowed
        let nearest = allowed[0];
        for (const v of allowed)
          if (Math.abs(v - current) < Math.abs(nearest - current)) nearest = v;
        return {
          ...p,
          productName: name.replace(/(\d+)\s*vol/i, `${nearest} vol`),
          volume: nearest,
        };
      }),
    }));
    onApply(clone);
  };

  const fixRatio = async () => {
    const rules = await loadLineRules(brand || null, line || null);
    if (!rules) return;
    const target =
      rules.ratio_exact ??
      (rules.ratio_min && rules.ratio_max
        ? (Number(rules.ratio_min) + Number(rules.ratio_max)) / 2
        : null);
    if (!target) return;
    const clone = JSON.parse(JSON.stringify(formulationData));
    // compute totals
    let totalColor = 0;
    const devItems: { idxS: number; idxP: number; amount: number }[] = [];
    (clone.steps || []).forEach((s: any, i: number) => {
      (s.mix || []).forEach((p: any, j: number) => {
        const amt = Number(p?.quantity) || 0;
        const type = String(p?.type || '').toLowerCase();
        if (['color', 'tinte', 'coloración', 'toner', 'matizador'].includes(type))
          totalColor += amt;
        if (['developer', 'oxidante', 'activador'].includes(type))
          devItems.push({ idxS: i, idxP: j, amount: amt });
      });
    });
    if (totalColor <= 0 || devItems.length === 0) return;
    const neededDev = totalColor * Number(target);
    const per = neededDev / devItems.length;
    devItems.forEach(ref => {
      clone.steps[ref.idxS].mix[ref.idxP].quantity = Math.round(per);
    });
    onApply(clone);
  };

  const addPrepigment = () => {
    const clone = JSON.parse(JSON.stringify(formulationData));
    const pre = {
      stepNumber: 1,
      stepTitle: 'Pre‑pigmentación cálida',
      instructions:
        'Aplicar relleno cálido en medios/puntas según objetivo. No aclarar: secar a toalla y proceder con depósito.',
      mix: [
        {
          productId: `prep-${Date.now()}`,
          productName: `${brand || 'Marca'} ${line || ''} 7/3 (relleno sugerido)`,
          quantity: 30,
          unit: 'ml',
          type: 'toner',
          shade: '7/3',
        },
        {
          productId: `dev-${Date.now()}`,
          productName: `${brand || 'Marca'} Oxidante 10 vol`,
          quantity: 30,
          unit: 'ml',
          type: 'developer',
          volume: 10,
        },
      ],
      processingTime: 10,
    };
    clone.steps = [pre, ...(clone.steps || [])].map((s: any, i: number) => ({
      ...s,
      stepNumber: i + 1,
    }));
    onApply(clone);
  };

  return (
    <View style={{ marginTop: 8 }}>
      <Text style={{ fontWeight: '600', marginBottom: 6 }}>Reparos rápidos</Text>
      <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
        <TouchableOpacity
          onPress={fixVolumes}
          style={{
            backgroundColor: '#eef2ff',
            paddingHorizontal: 10,
            paddingVertical: 8,
            borderRadius: 8,
            marginRight: 8,
            marginBottom: 8,
          }}
        >
          <Text style={{ color: '#3730a3' }}>Ajustar volúmenes</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={fixRatio}
          style={{
            backgroundColor: '#ecfeff',
            paddingHorizontal: 10,
            paddingVertical: 8,
            borderRadius: 8,
            marginRight: 8,
            marginBottom: 8,
          }}
        >
          <Text style={{ color: '#155e75' }}>Ajustar ratio</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={addPrepigment}
          style={{
            backgroundColor: '#fef3c7',
            paddingHorizontal: 10,
            paddingVertical: 8,
            borderRadius: 8,
            marginRight: 8,
            marginBottom: 8,
          }}
        >
          <Text style={{ color: '#92400e' }}>Añadir pre‑pigmentación</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default QuickFixActions;
