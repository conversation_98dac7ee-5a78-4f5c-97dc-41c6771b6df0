import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, TextInput, Animated } from 'react-native';
import { Check } from 'lucide-react-native';
import Colors from '@/constants/colors';
import DiagnosisSelector from './DiagnosisSelector';
import { ZoneAnalysisDisplay } from '@/components/base';
import { HairZone, getNaturalToneOptions, getUndertoneOptions } from '@/types/hair-diagnosis';
import { COLOR_TECHNIQUES } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import {
  MaintenanceLevel,
  AvoidableTone,
  getMaintenanceLevelLabel,
  getAvoidableToneLabel,
  getAvoidableToneOptions,
} from '@/types/lifestyle-preferences';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { commonStyles } from '@/styles/commonStyles';
import {
  getRecommendedTechniques,
  getTechniqueMismatchWarning,
} from '@/utils/technique-recommendations';

interface DesiredColorAnalysisFormProps {
  analysisResult: DesiredColorAnalysisResult | null;
  onAnalysisChange: (analysis: DesiredColorAnalysisResult) => void;
  isFromAI?: boolean;
}

export default function DesiredColorAnalysisForm({
  analysisResult,
  onAnalysisChange,
  isFromAI = false,
}: DesiredColorAnalysisFormProps) {
  const [currentZone, setCurrentZone] = useState<HairZone>(HairZone.ROOTS);
  const [showCustomTechnique, setShowCustomTechnique] = useState(false);
  const [_techniqueWarning, _setTechniqueWarning] = useState<string | null>(null);

  // Get salon configuration
  const { configuration: _configuration } = useSalonConfigStore();

  // Animations
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (analysisResult && isFromAI) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [analysisResult, isFromAI, fadeAnim]);

  const updateGeneral = (field: string, value: string | number) => {
    if (!analysisResult) return;

    // Check for technique compatibility when technique changes
    if (field === 'technique' && analysisResult.lifestyle?.maintenanceLevel) {
      const warning = getTechniqueMismatchWarning(value, analysisResult.lifestyle.maintenanceLevel);
      setTechniqueWarning(warning);
    }

    onAnalysisChange({
      ...analysisResult,
      general: { ...analysisResult.general, [field]: value },
      isFromAI: false,
    });
  };

  const updateZone = (zone: HairZone, field: string, value: string | number) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      zones: {
        ...analysisResult.zones,
        [zone]: { ...analysisResult.zones[zone], [field]: value },
      },
      isFromAI: false,
    });
  };

  const updateAdvanced = (field: string, value: string) => {
    if (!analysisResult) return;
    onAnalysisChange({
      ...analysisResult,
      advanced: { ...analysisResult.advanced, [field]: value },
      isFromAI: false,
    });
  };

  const updateLifestyle = (field: string, value: string | AvoidableTone[]) => {
    if (!analysisResult) return;

    // Update technique warning when maintenance level changes
    if (field === 'maintenanceLevel' && analysisResult.general.technique) {
      const warning = getTechniqueMismatchWarning(analysisResult.general.technique, value);
      setTechniqueWarning(warning);
    }

    onAnalysisChange({
      ...analysisResult,
      lifestyle: { ...analysisResult.lifestyle, [field]: value },
      isFromAI: false,
    });
  };

  const toggleAvoidTone = (tone: AvoidableTone) => {
    if (!analysisResult) return;
    const currentTones = analysisResult.lifestyle?.avoidTones || [];
    const newTones = currentTones.includes(tone)
      ? currentTones.filter(t => t !== tone)
      : [...currentTones, tone];

    updateLifestyle('avoidTones', newTones);
  };

  if (!analysisResult) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {isFromAI
            ? 'Captura o sube fotos del color deseado para comenzar el análisis'
            : 'Selecciona el modo Manual para introducir los valores deseados'}
        </Text>
      </View>
    );
  }

  // Get recommended techniques based on maintenance level
  const _recommendedTechniques = analysisResult?.lifestyle?.maintenanceLevel
    ? getRecommendedTechniques(analysisResult.lifestyle.maintenanceLevel)
    : [];

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Main Analysis Section */}
      <View style={styles.mainSection}>
        <Text style={styles.mainTitle}>
          Color Deseado
          {isFromAI && <Text style={styles.aiIndicator}> • AI</Text>}
        </Text>

        {/* General fields in vertical layout */}
        <DiagnosisSelector
          label="Nivel deseado"
          value={analysisResult.general.overallLevel}
          options={['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '8/9', '9/10']}
          onValueChange={value => updateGeneral('overallLevel', value)}
          placeholder="Seleccionar nivel"
          required
          isFromAI={analysisResult.isFromAI}
        />

        <DiagnosisSelector
          label="Tono"
          value={analysisResult.general.overallTone}
          options={[
            'Castaño oscuro',
            'Castaño medio',
            'Castaño claro',
            'Rubio oscuro',
            'Rubio medio',
            'Rubio claro',
            'Rubio muy claro',
            'Rubio ceniza',
            'Rubio dorado',
            'Cobrizo',
            'Rojizo',
            'Negro',
            'Chocolate',
            'Miel',
            'Arena',
          ]}
          onValueChange={value => updateGeneral('overallTone', value)}
          placeholder="Seleccionar tono"
          required
          isFromAI={analysisResult.isFromAI}
        />

        {/* Technique Selection */}
        <View style={styles.techniqueSection}>
          <Text style={styles.label}>Técnica de aplicación</Text>
          <View style={styles.techniqueRow}>
            {COLOR_TECHNIQUES.slice(0, 4).map(technique => (
              <TouchableOpacity
                key={technique.id}
                style={[
                  styles.techniqueButton,
                  analysisResult.general.technique === technique.id && styles.techniqueButtonActive,
                ]}
                onPress={() => {
                  updateGeneral('technique', technique.id);
                  setShowCustomTechnique(false);
                }}
              >
                <Text style={styles.techniqueIcon}>{technique.icon}</Text>
                <Text
                  style={[
                    styles.techniqueText,
                    analysisResult.general.technique === technique.id && styles.techniqueTextActive,
                  ]}
                >
                  {technique.name.split(' ')[0]}
                </Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[styles.techniqueButton, showCustomTechnique && styles.techniqueButtonActive]}
              onPress={() => {
                setShowCustomTechnique(true);
                updateGeneral('technique', 'custom');
              }}
            >
              <Text style={styles.techniqueIcon}>➕</Text>
              <Text
                style={[styles.techniqueText, showCustomTechnique && styles.techniqueTextActive]}
              >
                Custom
              </Text>
            </TouchableOpacity>
          </View>

          {showCustomTechnique && (
            <TextInput
              style={styles.customTechniqueInput}
              value={analysisResult.general.customTechnique || ''}
              onChangeText={value => updateGeneral('customTechnique', value)}
              placeholder="Ej: Balayage invertido con babylights"
              multiline
            />
          )}
        </View>
      </View>

      {/* Zone Analysis Section */}
      <View style={styles.zoneSection}>
        <Text style={styles.sectionTitle}>Análisis por Zonas</Text>

        {/* Zone Tabs */}
        <ZoneAnalysisDisplay
          key="desired-zone-tabs"
          currentZone={currentZone}
          onZoneChange={setCurrentZone}
          showCompletionIndicators={false}
        />

        {/* Zone Fields in vertical layout */}
        <View style={styles.zoneFieldsContainer}>
          <DiagnosisSelector
            label="Nivel"
            value={analysisResult.zones[currentZone]?.desiredLevel?.toString() || ''}
            options={Array.from({ length: 10 }, (_, i) => i + 1)}
            onValueChange={value => updateZone(currentZone, 'desiredLevel', parseInt(value))}
            required
            isFromAI={analysisResult.isFromAI}
          />

          <DiagnosisSelector
            label="Tono"
            value={analysisResult.zones[currentZone]?.desiredTone || ''}
            options={getNaturalToneOptions()}
            onValueChange={value => updateZone(currentZone, 'desiredTone', value)}
            required
            isFromAI={analysisResult.isFromAI}
          />

          <DiagnosisSelector
            label="Reflejo"
            value={analysisResult.zones[currentZone]?.desiredReflect || ''}
            options={getUndertoneOptions()}
            onValueChange={value => updateZone(currentZone, 'desiredReflect', value)}
            required
            isFromAI={analysisResult.isFromAI}
          />

          <View>
            <Text style={styles.label}>Cobertura (%)</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.input,
                  analysisResult.zones[currentZone]?.coverage && styles.inputWithValue,
                ]}
                value={analysisResult.zones[currentZone]?.coverage?.toString() || '100'}
                onChangeText={value => updateZone(currentZone, 'coverage', parseInt(value) || 100)}
                keyboardType="numeric"
                placeholder="100"
                maxLength={3}
              />
              {analysisResult.zones[currentZone]?.coverage && (
                <Check size={20} color={Colors.light.success} style={styles.checkIcon} />
              )}
            </View>
          </View>
        </View>
      </View>

      {/* Lifestyle Preferences - Simplified */}
      <View style={styles.lifestyleSection}>
        <Text style={styles.sectionTitle}>Preferencias</Text>

        {/* Maintenance Level */}
        <View style={styles.preferenceGroup}>
          <Text style={[styles.label, commonStyles.marginBottom16]}>
            Frecuencia de mantenimiento
          </Text>
          <View style={styles.optionsRow}>
            {[MaintenanceLevel.LOW, MaintenanceLevel.MEDIUM, MaintenanceLevel.HIGH].map(level => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.optionButton,
                  analysisResult.lifestyle?.maintenanceLevel === level && styles.optionButtonActive,
                ]}
                onPress={() => updateLifestyle('maintenanceLevel', level)}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    analysisResult.lifestyle?.maintenanceLevel === level &&
                      styles.optionButtonTextActive,
                  ]}
                >
                  {getMaintenanceLevelLabel(level)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Contrast */}
        <View style={styles.preferenceGroup}>
          <Text style={[styles.label, commonStyles.marginBottom16]}>Contraste</Text>
          <View style={styles.optionsRow}>
            {(['subtle', 'medium', 'high'] as const).map(level => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.optionButton,
                  analysisResult.advanced.contrast === level && styles.optionButtonActive,
                ]}
                onPress={() => updateAdvanced('contrast', level)}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    analysisResult.advanced.contrast === level && styles.optionButtonTextActive,
                  ]}
                >
                  {level === 'subtle' ? 'Sutil' : level === 'medium' ? 'Medio' : 'Alto'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Direction */}
        <View style={styles.preferenceGroup}>
          <Text style={[styles.label, commonStyles.marginBottom16]}>Dirección</Text>
          <View style={styles.optionsRow}>
            {(['warmer', 'neutral', 'cooler'] as const).map(dir => (
              <TouchableOpacity
                key={dir}
                style={[
                  styles.optionButton,
                  analysisResult.advanced.direction === dir && styles.optionButtonActive,
                ]}
                onPress={() => updateAdvanced('direction', dir)}
              >
                <Text
                  style={[
                    styles.optionButtonText,
                    analysisResult.advanced.direction === dir && styles.optionButtonTextActive,
                  ]}
                >
                  {dir === 'warmer' ? 'Cálido' : dir === 'neutral' ? 'Neutro' : 'Frío'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Tones to Avoid - Compact */}
        <View style={styles.preferenceGroup}>
          <Text style={[styles.label, commonStyles.marginBottom16]}>Evitar tonos</Text>
          <View style={styles.tonesGrid}>
            {getAvoidableToneOptions().map(tone => (
              <TouchableOpacity
                key={tone}
                style={[
                  styles.toneChip,
                  analysisResult.lifestyle?.avoidTones?.includes(tone) && styles.toneChipActive,
                ]}
                onPress={() => toggleAvoidTone(tone)}
              >
                <Text
                  style={[
                    styles.toneChipText,
                    analysisResult.lifestyle?.avoidTones?.includes(tone) &&
                      styles.toneChipTextActive,
                  ]}
                >
                  {getAvoidableToneLabel(tone)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: 'center',
  },
  mainSection: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  mainTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 20,
  },
  aiIndicator: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: 'normal',
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    paddingRight: 44,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  inputWithValue: {
    borderColor: Colors.light.success,
    backgroundColor: Colors.light.success + '08',
  },
  inputContainer: {
    position: 'relative',
  },
  checkIcon: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -10,
  },
  techniqueSection: {
    marginBottom: 16,
  },
  techniqueRow: {
    flexDirection: 'row',
    gap: 8,
  },
  techniqueButton: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  techniqueButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  techniqueIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  techniqueText: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.light.gray,
  },
  techniqueTextActive: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  customTechniqueInput: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginTop: 12,
    minHeight: 60,
  },
  zoneSection: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 16,
  },
  zoneFieldsContainer: {},
  lifestyleSection: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  preferenceGroup: {
    marginBottom: 24,
    paddingBottom: 8,
  },
  optionsRow: {
    flexDirection: 'row',
    marginTop: 10,
  },
  optionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  optionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  optionButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.text,
  },
  optionButtonTextActive: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  tonesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  toneChip: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
    marginRight: 12,
    marginBottom: 12,
    minWidth: 90,
  },
  toneChipActive: {
    backgroundColor: Colors.light.error + '10',
    borderColor: Colors.light.error,
  },
  toneChipText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  toneChipTextActive: {
    color: Colors.light.error,
    fontWeight: '600',
  },
});
