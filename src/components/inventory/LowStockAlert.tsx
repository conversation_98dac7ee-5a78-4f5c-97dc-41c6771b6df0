import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { AlertTriangle, Package } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useAuthStore } from '@/stores/auth-store';
import { BeautyCard } from '@/components/beauty/BeautyCard';

interface LowStockAlertProps {
  onProductPress?: (productId: string) => void;
  compact?: boolean;
}

export function LowStockAlert({ onProductPress, compact = false }: LowStockAlertProps) {
  const { getLowStockProducts, loadLowStockProducts, isLoadingLowStock } = useInventoryStore();
  const { user } = useAuthStore();

  const lowStockProducts = getLowStockProducts();

  useEffect(() => {
    if (user?.salonId) {
      loadLowStockProducts();
    }
  }, [user?.salonId, loadLowStockProducts]);

  if (isLoadingLowStock || lowStockProducts.length === 0) {
    return null;
  }

  const getSeverityColor = (percentage: number) => {
    if (percentage <= 10) return BeautyMinimalTheme.semantic.status.error;
    if (percentage <= 25) return BeautyMinimalTheme.semantic.status.warning;
    return BeautyMinimalTheme.semantic.status.success;
  };

  const renderProduct = ({ item }: { item: (typeof lowStockProducts)[0] }) => {
    const severityColor = getSeverityColor(item.percentage_remaining);

    return (
      <BeautyCard
        variant="default"
        style={[styles.productCard, { borderLeftColor: severityColor }]}
        onPress={() => onProductPress?.(item.product_id)}
      >
        <View style={styles.productHeader}>
          <View style={styles.productInfo}>
            <Text style={styles.productName}>
              {item.brand} {item.name}
              {item.color_code && <Text style={styles.colorCode}> ({item.color_code})</Text>}
            </Text>
            <Text style={styles.productCategory}>{item.category}</Text>
          </View>
          <View style={[styles.percentageBadge, { backgroundColor: severityColor + '20' }]}>
            <Text style={[styles.percentageText, { color: severityColor }]}>
              {Math.round(item.percentage_remaining)}%
            </Text>
          </View>
        </View>
        <View style={styles.stockInfo}>
          <Text style={styles.stockText}>
            Stock: {item.stock_ml}ml / Mínimo: {item.minimum_stock_ml}ml
          </Text>
        </View>
      </BeautyCard>
    );
  };

  if (compact) {
    return (
      <BeautyCard
        variant="outlined"
        style={styles.compactAlert}
        onPress={() => onProductPress?.('inventory')}
      >
        <View style={styles.compactHeader}>
          <AlertTriangle size={16} color={BeautyMinimalTheme.semantic.status.warning} />
          <Text style={styles.compactTitle}>
            {lowStockProducts.length} producto
            {lowStockProducts.length !== 1 ? 's' : ''} con stock bajo
          </Text>
        </View>
      </BeautyCard>
    );
  }

  return (
    <BeautyCard variant="outlined" style={styles.container}>
      <View style={styles.header}>
        <Package size={20} color={BeautyMinimalTheme.semantic.status.warning} />
        <Text style={styles.title}>Productos con Stock Bajo</Text>
      </View>

      <FlatList
        data={lowStockProducts}
        keyExtractor={item => item.product_id}
        renderItem={renderProduct}
        scrollEnabled={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </BeautyCard>
  );
}

const styles = StyleSheet.create({
  container: {
    // BeautyCard provides the base styling, we only add specific overrides
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },
  productCard: {
    // BeautyCard provides the base styling, we only add specific overrides
    borderLeftWidth: 4,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  productInfo: {
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  productName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  colorCode: {
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
  productCategory: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
  percentageBadge: {
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
  },
  percentageText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  stockInfo: {
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  stockText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  separator: {
    height: BeautyMinimalTheme.spacing.sm,
  },
  compactAlert: {
    // BeautyCard provides base styling, we add warning-specific background
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '15',
    marginBottom: BeautyMinimalTheme.spacing.sm,
    borderColor: BeautyMinimalTheme.semantic.status.warning + '40',
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.warning,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    marginLeft: BeautyMinimalTheme.spacing.xs,
  },
});
