import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import {
  Droplets,
  Clock,
  CheckCircle,
  AlertCircle,
  Thermometer,
  LucideIcon,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
import { StepCard } from '../components/StepCard';
import { TimerCard } from '../components/TimerCard';
import { MaterialsList } from '../components/MaterialsList';
import { instructionStyles as styles } from '../styles';

interface RinsingStepProps {
  formulaData: VisualFormulationData;
  onRinsingComplete?: () => void;
  testID?: string;
}

interface RinsePhase {
  id: string;
  title: string;
  description: string;
  duration: number; // in seconds
  temperature: string;
  icon: LucideIcon;
  completed: boolean;
}

export const RinsingStep: React.FC<RinsingStepProps> = ({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  formulaData,
  onRinsingComplete,
  testID = 'rinsing-step',
}) => {
  const [currentPhase, setCurrentPhase] = useState(0);
  const [phases, setPhases] = useState<RinsePhase[]>([
    {
      id: 'initial-rinse',
      title: 'Enjuague Inicial',
      description: 'Enjuaga suavemente para eliminar el exceso de producto. No uses shampoo aún.',
      duration: 120, // 2 minutes
      temperature: 'Tibia (35-37°C)',
      icon: Droplets,
      completed: false,
    },
    {
      id: 'deep-rinse',
      title: 'Enjuague Profundo',
      description: 'Continúa enjuagando hasta que el agua salga completamente clara.',
      duration: 180, // 3 minutes
      temperature: 'Tibia (35-37°C)',
      icon: Droplets,
      completed: false,
    },
    {
      id: 'shampoo',
      title: 'Lavado con Shampoo',
      description: 'Aplica shampoo suave para cabello teñido. Masajea delicadamente.',
      duration: 60, // 1 minute
      temperature: 'Tibia (35-37°C)',
      icon: Droplets,
      completed: false,
    },
    {
      id: 'final-rinse',
      title: 'Enjuague Final',
      description: 'Enjuague final con agua fría para cerrar las cutículas y fijar el color.',
      duration: 60, // 1 minute
      temperature: 'Fría (20-25°C)',
      icon: Droplets,
      completed: false,
    },
  ]);

  const rinsingMaterials = [
    { id: 'towels', name: 'Toallas suaves', type: 'material' },
    { id: 'shampoo', name: 'Shampoo para cabello teñido', type: 'material' },
    { id: 'conditioner', name: 'Acondicionador protector', type: 'material' },
    { id: 'cape', name: 'Capa impermeable', type: 'material' },
  ];

  const [checkedMaterials, setCheckedMaterials] = useState<string[]>([]);

  const handlePhaseComplete = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Mark current phase as completed
    const updatedPhases = [...phases];
    updatedPhases[currentPhase].completed = true;
    setPhases(updatedPhases);

    if (currentPhase < phases.length - 1) {
      setCurrentPhase(currentPhase + 1);
    } else {
      // All phases completed
      onRinsingComplete?.();
    }
  };

  const handleManualComplete = async (phaseIndex: number) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const updatedPhases = [...phases];
    updatedPhases[phaseIndex].completed = !updatedPhases[phaseIndex].completed;
    setPhases(updatedPhases);

    // If all phases are completed, call the completion callback
    const allCompleted = updatedPhases.every(phase => phase.completed);
    if (allCompleted) {
      onRinsingComplete?.();
    }
  };

  const getTemperatureColor = (temp: string) => {
    return temp.toLowerCase().includes('fría') ? Colors.light.accent : Colors.light.warning;
  };

  const allPhasesCompleted = phases.every(phase => phase.completed);

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false} testID={testID}>
      {/* Header */}
      <StepCard
        title="Proceso de Enjuague"
        subtitle="Sigue cada fase para preservar el color y la salud del cabello"
        icon={<Droplets size={48} color={Colors.light.accent} />}
        testID={`${testID}-header`}
      />

      {/* Materials Checklist */}
      <View style={localStyles.materialsSection}>
        <MaterialsList
          items={rinsingMaterials}
          checkedItems={checkedMaterials}
          onToggleItem={id => {
            const isChecked = checkedMaterials.includes(id);
            setCheckedMaterials(
              isChecked ? checkedMaterials.filter(item => item !== id) : [...checkedMaterials, id]
            );
          }}
          title="Materiales Necesarios"
          testID={`${testID}-materials`}
        />
      </View>

      {/* Current Phase Timer */}
      {currentPhase < phases.length && !allPhasesCompleted && (
        <View style={localStyles.currentPhaseSection}>
          <TimerCard
            title={`Fase ${currentPhase + 1}: ${phases[currentPhase].title}`}
            subtitle={phases[currentPhase].description}
            initialTime={phases[currentPhase].duration}
            onComplete={handlePhaseComplete}
            autoStart={false}
            showControls={true}
            testID={`${testID}-timer`}
          />
        </View>
      )}

      {/* Phase Overview */}
      <View style={localStyles.phasesContainer}>
        <Text style={localStyles.phasesTitle}>Fases del Enjuague</Text>

        {phases.map((phase, index) => {
          const Icon = phase.icon;
          const isActive = index === currentPhase;
          const isCompleted = phase.completed;

          return (
            <TouchableOpacity
              key={phase.id}
              style={[
                localStyles.phaseCard,
                isActive && localStyles.phaseCardActive,
                isCompleted && localStyles.phaseCardCompleted,
              ]}
              onPress={() => handleManualComplete(index)}
              testID={`${testID}-phase-${index}`}
            >
              <View style={localStyles.phaseHeader}>
                <View style={localStyles.phaseIconContainer}>
                  {isCompleted ? (
                    <CheckCircle size={24} color={Colors.light.success} />
                  ) : (
                    <Icon
                      size={24}
                      color={isActive ? Colors.light.primary : Colors.light.textSecondary}
                    />
                  )}
                </View>

                <View style={localStyles.phaseInfo}>
                  <Text
                    style={[localStyles.phaseTitle, isCompleted && localStyles.phaseTitleCompleted]}
                  >
                    {phase.title}
                  </Text>
                  <Text style={localStyles.phaseDescription}>{phase.description}</Text>
                </View>

                <View style={localStyles.phaseDetails}>
                  <View style={localStyles.temperatureIndicator}>
                    <Thermometer size={16} color={getTemperatureColor(phase.temperature)} />
                    <Text style={localStyles.temperatureText}>{phase.temperature}</Text>
                  </View>

                  <View style={localStyles.durationIndicator}>
                    <Clock size={16} color={Colors.light.textSecondary} />
                    <Text style={localStyles.durationText}>
                      {Math.floor(phase.duration / 60)} min
                    </Text>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Important Tips */}
      <StepCard
        title="Consejos Importantes"
        icon={<AlertCircle size={24} color={Colors.light.warning} />}
        testID={`${testID}-tips`}
      >
        <View style={localStyles.tipsList}>
          <Text style={localStyles.tipItem}>
            • Mantén la temperatura del agua constante para evitar daños
          </Text>
          <Text style={localStyles.tipItem}>
            • El agua debe salir completamente clara antes de aplicar shampoo
          </Text>
          <Text style={localStyles.tipItem}>
            • No frotes vigorosamente, el cabello recién teñido es más frágil
          </Text>
          <Text style={localStyles.tipItem}>
            • El enjuague final con agua fría ayuda a fijar el color
          </Text>
        </View>
      </StepCard>

      {/* Completion Status */}
      {allPhasesCompleted && (
        <StepCard
          title="¡Enjuague Completado!"
          subtitle="Todas las fases han sido completadas correctamente"
          icon={<CheckCircle size={24} color={Colors.light.success} />}
          style={localStyles.completionCard}
          testID={`${testID}-completion`}
        />
      )}
    </ScrollView>
  );
};

const localStyles = StyleSheet.create({
  materialsSection: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  currentPhaseSection: {
    marginVertical: 16,
  },
  phasesContainer: {
    paddingHorizontal: 16,
    marginTop: 20,
  },
  phasesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  phaseCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  phaseCardActive: {
    borderWidth: 2,
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.surface,
  },
  phaseCardCompleted: {
    backgroundColor: Colors.light.surface,
    borderColor: Colors.light.success,
  },
  phaseHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  phaseIconContainer: {
    marginRight: 12,
    paddingTop: 2,
  },
  phaseInfo: {
    flex: 1,
    marginRight: 12,
  },
  phaseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  phaseTitleCompleted: {
    textDecorationLine: 'line-through',
    color: Colors.light.textSecondary,
  },
  phaseDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  phaseDetails: {
    alignItems: 'flex-end',
  },
  temperatureIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  temperatureText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  durationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  tipsList: {
    gap: 8,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  completionCard: {
    backgroundColor: Colors.light.success,
    opacity: 0.1,
  },
});
