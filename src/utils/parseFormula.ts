import { ColorFormula } from '@/types/formulation';

/**
 * Parse formula text to extract structured data
 * Handles various formula formats from different brands
 */
export function parseFormulaText(formulaText: string): ColorFormula {
  // Extract brand and line from first line
  const lines = formulaText.split('\n');
  const firstLine = lines[0] || '';

  // Try to extract brand and line
  const brandLineMatch = firstLine.match(/^([^:]+):\s*$/);
  const [brand, line] = brandLineMatch
    ? brandLineMatch[1].split(' ').filter(Boolean)
    : ['Unknown', 'Unknown'];

  // Extract colors/tones with enhanced regex patterns
  const colors: ColorFormula['colors'] = [];

  // Multiple patterns to catch different formats
  const colorPatterns = [
    /[-–]\s*([\w\s]+)\s+(\d+[/.,-]?\d*)\s*\((\d+)g\)/g, // Original pattern
    /[-–]\s*(\d+[/.,-]?\d*)\s*([\w\s]*?)\s*\((\d+)g\)/g, // Tone first
    /([\w\s]*?)\s+(\d+[/.,-]?\d*)\s*[-–:]\s*(\d+)\s*g/g, // Colon/dash separator
    /(\d+[/.,-]?\d*)\s*[\w\s]*?\s*(\d+)\s*gr?/g, // Simple tone amount
  ];

  for (const pattern of colorPatterns) {
    let colorMatch;
    while ((colorMatch = pattern.exec(formulaText)) !== null) {
      // Handle different capture groups based on pattern
      let tone, amount;

      if (pattern.source.includes('Tone first')) {
        tone = colorMatch[1]; // First capture is tone
        amount = parseInt(colorMatch[3]);
      } else {
        tone = colorMatch[2] || colorMatch[1]; // Second or first capture
        amount = parseInt(colorMatch[3] || colorMatch[2]);
      }

      // Only add if we have valid tone and amount
      if (tone && amount && !isNaN(amount)) {
        colors.push({
          tone: tone.trim(),
          amount: amount,
        });
      }
    }
  }

  // Enhanced developer/oxidant detection
  const developerPatterns = [
    /[Oo]xidante\s+(\d+)\s*vol/,
    /[Dd]eveloper\s+(\d+)\s*vol/,
    /[Rr]evelador\s+(\d+)\s*vol/,
    /(\d+)\s*vol/,
  ];

  let developerVolume = 20; // Default
  for (const pattern of developerPatterns) {
    const match = formulaText.match(pattern);
    if (match) {
      developerVolume = parseInt(match[1]);
      break;
    }
  }

  // Enhanced ratio detection including 2:1, 1:2, etc.
  const ratioPatterns = [
    /\(?([1-9]:[1-9](?:\.\d+)?)\)?/,
    /ratio\s*([1-9]:[1-9](?:\.\d+)?)/i,
    /proporción\s*([1-9]:[1-9](?:\.\d+)?)/i,
  ];

  let developerRatio = '1:1.5'; // Default
  for (const pattern of ratioPatterns) {
    const match = formulaText.match(pattern);
    if (match) {
      developerRatio = match[1];
      break;
    }
  }

  // Enhanced processing time extraction
  const timePatterns = [
    /(\d+)\s*min/,
    /(\d+)\s*minutos/,
    /time[:\s]*(\d+)/i,
    /tiempo[:\s]*(\d+)/i,
    /process[:\s]*(\d+)/i,
  ];

  let processingTime = 35; // Default
  for (const pattern of timePatterns) {
    const match = formulaText.match(pattern);
    if (match) {
      processingTime = parseInt(match[1]);
      break;
    }
  }

  // Enhanced additive detection
  const additives: string[] = [];
  const additiveKeywords = [
    { keyword: 'olaplex', name: 'Olaplex' },
    { keyword: 'protector', name: 'Protector' },
    { keyword: 'bond', name: 'Bond Builder' },
    { keyword: 'plex', name: 'Plex Treatment' },
    { keyword: 'tratamiento', name: 'Tratamiento' },
    { keyword: 'neutralizant', name: 'Neutralizante' },
    { keyword: 'matizador', name: 'Matizador' },
    { keyword: 'toner', name: 'Toner' },
  ];

  const textLower = formulaText.toLowerCase();
  for (const { keyword, name } of additiveKeywords) {
    if (textLower.includes(keyword)) {
      additives.push(name);
    }
  }

  // Default values if nothing found
  if (colors.length === 0) {
    colors.push({
      tone: '7/1',
      amount: 60,
    });
  }

  return {
    brand: brand || 'Unknown',
    line: line || 'Unknown',
    colors,
    developerVolume,
    developerRatio,
    additives,
    processingTime,
    formulaText,
  };
}

/**
 * Parse structured formula data from JSON to extract products
 */
export function parseStructuredFormulaToProducts(formulationData: unknown): Array<{
  name: string;
  brand?: string;
  line?: string;
  type?: string;
  shade?: string;
  amount: number;
  unit: string;
}> {
  const products: Array<{
    name: string;
    brand?: string;
    line?: string;
    type?: string;
    shade?: string;
    amount: number;
    unit: string;
  }> = [];

  if (!formulationData || !formulationData.steps) {
    return products;
  }

  // Extract products from all steps
  if (
    formulationData &&
    typeof formulationData === 'object' &&
    'steps' in formulationData &&
    Array.isArray(formulationData.steps)
  ) {
    formulationData.steps.forEach((step: unknown) => {
      if (step && typeof step === 'object' && 'mix' in step && Array.isArray(step.mix)) {
        step.mix.forEach((product: unknown) => {
          if (product && typeof product === 'object') {
            const prod = product as Record<string, unknown>;
            products.push({
              name: (prod.productName as string) || '',
              brand: prod.brand as string,
              line: prod.line as string,
              type: prod.type as string,
              shade: prod.shade as string,
              amount: (prod.quantity as number) || 0,
              unit: (prod.unit as string) || 'g',
            });
          }
        });
      }
    });
  }

  return products;
}

/**
 * Parse formula text to extract product list as shown in shopping list
 * Enhanced to capture more formats including ratios like "2:1"
 */
export function parseFormulaTextToProducts(formulaText: string): Array<{
  name: string;
  amount: number;
  unit: string;
}> {
  const products: Array<{ name: string; amount: number; unit: string }> = [];

  // Split by lines and process each line
  const lines = formulaText
    .split('\n')
    .map(line => line.trim())
    .filter(Boolean);

  for (const line of lines) {
    // Pattern 1: "60gr Illumina Color 9/03" or "160ml Oxidante 30 vol"
    const match1 = line.match(/(\d+)\s*(gr?|ml|g)\s+(.+?)(?:\s*\(|$)/);
    if (match1) {
      products.push({
        name: match1[3].trim(),
        amount: parseInt(match1[1]),
        unit: match1[2] === 'gr' ? 'g' : match1[2],
      });
      continue;
    }

    // Pattern 2: "- Illumina Color 9/03 (60g)" or "- Oxidante 30 vol (160ml)"
    const match2 = line.match(/^[-–•]\s*(.+?)\s*\((\d+)\s*(g|ml|gr)\)/);
    if (match2) {
      products.push({
        name: match2[1].trim(),
        amount: parseInt(match2[2]),
        unit: match2[3] === 'gr' ? 'g' : match2[3],
      });
      continue;
    }

    // Pattern 3: "Illumina Color 9/03: 60g" or "Oxidante 30 vol: 160ml"
    const match3 = line.match(/^(.+?):\s*(\d+)\s*(g|ml|gr)/);
    if (match3) {
      products.push({
        name: match3[1].trim(),
        amount: parseInt(match3[2]),
        unit: match3[3] === 'gr' ? 'g' : match3[3],
      });
      continue;
    }

    // Pattern 4: "Product Name - 60g" or "Product Name — 160ml"
    const match4 = line.match(/^(.+?)\s*[-–—]\s*(\d+)\s*(g|ml|gr)/);
    if (match4) {
      products.push({
        name: match4[1].trim(),
        amount: parseInt(match4[2]),
        unit: match4[3] === 'gr' ? 'g' : match4[3],
      });
      continue;
    }

    // Pattern 5: Ratio format "Tinte 60g + Oxidante 90ml (2:3 ratio)"
    if (line.includes('ratio') && (line.includes('tinte') || line.includes('oxidante'))) {
      const tinteMatch = line.match(/tinte.*?(\d+)\s*(g|ml)/i);
      const oxidanteMatch = line.match(/oxidante.*?(\d+)\s*(g|ml)/i);

      if (tinteMatch && oxidanteMatch) {
        products.push({
          name: 'Tinte',
          amount: parseInt(tinteMatch[1]),
          unit: tinteMatch[2],
        });
        products.push({
          name: 'Oxidante',
          amount: parseInt(oxidanteMatch[1]),
          unit: oxidanteMatch[2],
        });
        continue;
      }
    }

    // Pattern 6: Enhanced oxidant/developer detection
    if (
      line.toLowerCase().includes('oxidante') ||
      line.toLowerCase().includes('developer') ||
      line.toLowerCase().includes('revelador')
    ) {
      const volMatch = line.match(/(\d+)\s*vol/);
      const amountMatch = line.match(/(\d+)\s*(ml|g)/);
      if (volMatch && amountMatch) {
        products.push({
          name: `Oxidante ${volMatch[1]} vol`,
          amount: parseInt(amountMatch[1]),
          unit: amountMatch[2],
        });
        continue;
      }
    }

    // Pattern 7: Additive detection (Olaplex, Bond Builder, etc.)
    const additiveKeywords = [
      'olaplex',
      'bond',
      'protector',
      'neutralizant',
      'matizador',
      'treatment',
    ];
    const hasAdditive = additiveKeywords.some(keyword => line.toLowerCase().includes(keyword));

    if (hasAdditive) {
      const amountMatch = line.match(/(\d+)\s*(ml|g|gotas|drops)/);
      if (amountMatch) {
        const productName = line.replace(/\d+\s*(ml|g|gotas|drops).*/, '').trim();
        products.push({
          name: productName,
          amount: parseInt(amountMatch[1]),
          unit: amountMatch[2] === 'gotas' ? 'ml' : amountMatch[2], // Convert drops to ml
        });
        continue;
      }
    }
  }

  // If no products found AND there's actual content, try to extract from parsed formula
  if (products.length === 0 && formulaText.trim().length > 10) {
    const formula = parseFormulaText(formulaText);

    // Only add products if we found valid colors
    if (formula.colors.length > 0) {
      // Add color products
      formula.colors.forEach(color => {
        products.push({
          name: `${formula.brand} ${formula.line} ${color.tone}`.trim(),
          amount: color.amount || 0,
          unit: 'g',
        });
      });

      // Add developer
      if (formula.developerVolume) {
        const totalColorAmount = formula.colors.reduce((sum, c) => sum + (c.amount || 0), 0);
        const ratioMatch = formula.developerRatio?.match(/1:(\d+\.?\d*)/);
        const multiplier = ratioMatch ? parseFloat(ratioMatch[1]) : 1.5;
        const developerAmount = Math.round(totalColorAmount * multiplier);

        products.push({
          name: `Oxidante ${formula.developerVolume} vol`,
          amount: developerAmount,
          unit: 'ml',
        });
      }

      // Add additives
      formula.additives.forEach(additive => {
        products.push({
          name: additive,
          amount: 10,
          unit: 'ml',
        });
      });
    }
  }

  return products;
}

/**
 * Calculate simple formula cost without inventory
 * Enhanced with better product type detection and cost modeling
 */
export function calculateSimpleFormulaCost(formulaText: string) {
  // Use the enhanced parser to extract products
  const products = parseFormulaTextToProducts(formulaText);

  // Enhanced cost estimates based on professional salon pricing
  const costEstimates = {
    tintPerGram: 0.18, // €0.18 per gram for quality tints
    developerPerMl: 0.006, // €0.006 per ml for developer/oxidant
    additiveBase: 3.0, // €3.00 base cost for additives
    treatmentPerMl: 0.25, // €0.25 per ml for treatments
    neutralizerPerMl: 0.15, // €0.15 per ml for neutralizers
  };

  let totalCost = 0;
  const items = [];

  for (const product of products) {
    let unitCost = 0;
    let cost = 0;
    const productLower = product.name.toLowerCase();

    // Enhanced product type detection
    if (
      productLower.includes('oxidante') ||
      productLower.includes('developer') ||
      productLower.includes('revelador')
    ) {
      unitCost = costEstimates.developerPerMl;
      cost = product.amount * unitCost;
    } else if (
      productLower.includes('olaplex') ||
      productLower.includes('bond') ||
      productLower.includes('protector')
    ) {
      unitCost = costEstimates.additiveBase;
      cost = costEstimates.additiveBase; // Fixed cost for additives
    } else if (
      productLower.includes('treatment') ||
      productLower.includes('tratamiento') ||
      productLower.includes('mask')
    ) {
      unitCost = costEstimates.treatmentPerMl;
      cost = product.amount * unitCost;
    } else if (
      productLower.includes('matizador') ||
      productLower.includes('neutralizant') ||
      productLower.includes('toner')
    ) {
      unitCost = costEstimates.neutralizerPerMl;
      cost = product.amount * unitCost;
    } else {
      // Default to tint cost - consider unit type
      if (product.unit === 'ml') {
        unitCost = costEstimates.developerPerMl;
        cost = product.amount * unitCost;
      } else {
        unitCost = costEstimates.tintPerGram;
        cost = product.amount * unitCost;
      }
    }

    totalCost += cost;
    items.push({
      product: product.name,
      amount: `${product.amount}${product.unit}`,
      unitCost: 0, // Mark as estimated (0 means no real inventory cost)
      totalCost: Math.round(cost * 100) / 100,
    });
  }

  // Apply professional markup (2.8x-3.2x material cost, average 3x)
  const markupMultiplier = 3.0;
  const suggestedServicePrice = totalCost * markupMultiplier;
  const profitMargin = suggestedServicePrice - totalCost;

  return {
    items,
    totalMaterialCost: Math.round(totalCost * 100) / 100,
    suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
    profitMargin: Math.round(profitMargin * 100) / 100,
    hasAllRealCosts: false, // Always false for estimated costs
  };
}
