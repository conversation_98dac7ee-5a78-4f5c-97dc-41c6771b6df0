/**
 * Accessibility utilities for Salonier
 * Ensures WCAG 2.1 AA compliance
 */

/**
 * Calculate contrast ratio between two colors
 * @param color1 - First color in hex format
 * @param color2 - Second color in hex format
 * @returns Contrast ratio (1-21)
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (hex: string): number => {
    const rgb = parseInt(hex.slice(1), 16);
    const r = (rgb >> 16) & 0xff;
    const g = (rgb >> 8) & 0xff;
    const b = (rgb >> 0) & 0xff;

    const sRGB = [r, g, b].map(value => {
      value = value / 255;
      return value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Check if contrast ratio meets WCAG AA standards
 * @param ratio - Contrast ratio
 * @param isLargeText - Whether the text is large (18pt+ or 14pt+ bold)
 * @returns Whether the contrast meets AA standards
 */
export const meetsWCAGAA = (ratio: number, isLargeText = false): boolean => {
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
};

/**
 * Check if contrast ratio meets WCAG AAA standards
 * @param ratio - Contrast ratio
 * @param isLargeText - Whether the text is large (18pt+ or 14pt+ bold)
 * @returns Whether the contrast meets AAA standards
 */
export const meetsWCAGAAA = (ratio: number, isLargeText = false): boolean => {
  return isLargeText ? ratio >= 4.5 : ratio >= 7;
};

/**
 * Get readable contrast ratio label
 * @param ratio - Contrast ratio
 * @returns Human-readable label
 */
export const getContrastLabel = (ratio: number): string => {
  if (ratio >= 7) return 'Excelente (AAA)';
  if (ratio >= 4.5) return 'Bueno (AA)';
  if (ratio >= 3) return 'Aceptable para texto grande';
  return 'Insuficiente';
};

/**
 * Suggest a better color if contrast is insufficient
 * @param foreground - Foreground color in hex
 * @param background - Background color in hex
 * @param targetRatio - Target contrast ratio (default 4.5 for AA)
 * @returns Suggested color or null if current is sufficient
 */
export const suggestBetterColor = (
  foreground: string,
  background: string,
  targetRatio = 4.5
): string | null => {
  const currentRatio = getContrastRatio(foreground, background);
  if (currentRatio >= targetRatio) return null;

  // Simple approach: darken or lighten the foreground color
  const isDarkBackground = getLuminance(background) < 0.5;
  const step = isDarkBackground ? 10 : -10;

  let suggested = foreground;
  let attempts = 0;
  const maxAttempts = 20;

  while (getContrastRatio(suggested, background) < targetRatio && attempts < maxAttempts) {
    const rgb = parseInt(suggested.slice(1), 16);
    let r = (rgb >> 16) & 0xff;
    let g = (rgb >> 8) & 0xff;
    let b = (rgb >> 0) & 0xff;

    r = Math.max(0, Math.min(255, r + step));
    g = Math.max(0, Math.min(255, g + step));
    b = Math.max(0, Math.min(255, b + step));

    suggested = `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
    attempts++;
  }

  return suggested !== foreground ? suggested : null;
};

// Helper function for suggestBetterColor
const getLuminance = (hex: string): number => {
  const rgb = parseInt(hex.slice(1), 16);
  const r = (rgb >> 16) & 0xff;
  const g = (rgb >> 8) & 0xff;
  const b = (rgb >> 0) & 0xff;

  const sRGB = [r, g, b].map(value => {
    value = value / 255;
    return value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
};

/**
 * Accessibility helpers for React Native components
 */
export const a11y = {
  // Common button accessibility props
  button: (label: string, hint?: string) => ({
    accessible: true,
    accessibilityRole: 'button' as const,
    accessibilityLabel: label,
    accessibilityHint: hint,
  }),

  // Link accessibility props
  link: (label: string, hint?: string) => ({
    accessible: true,
    accessibilityRole: 'link' as const,
    accessibilityLabel: label,
    accessibilityHint: hint,
  }),

  // Image accessibility props
  image: (label: string) => ({
    accessible: true,
    accessibilityRole: 'image' as const,
    accessibilityLabel: label,
  }),

  // Text input accessibility props
  input: (label: string, hint?: string, value?: string) => ({
    accessible: true,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessibilityValue: value ? { text: value } : undefined,
  }),

  // Header accessibility props
  header: (label: string, level: 1 | 2 | 3 | 4 | 5 | 6 = 1) => ({
    accessible: true,
    accessibilityRole: 'header' as const,
    accessibilityLabel: label,
    // Additional prop for screen readers to understand hierarchy
    accessibilityLevel: level,
  }),

  // List item accessibility props
  listItem: (label: string, position: number, total: number) => ({
    accessible: true,
    accessibilityLabel: `${label}, ${position} de ${total}`,
  }),

  // Loading state accessibility props
  loading: (message = 'Cargando') => ({
    accessible: true,
    accessibilityLabel: message,
    accessibilityState: { busy: true },
  }),

  // Error state accessibility props
  error: (message: string) => ({
    accessible: true,
    accessibilityLabel: `Error: ${message}`,
    accessibilityRole: 'alert' as const,
  }),

  // Success state accessibility props
  success: (message: string) => ({
    accessible: true,
    accessibilityLabel: `Éxito: ${message}`,
    accessibilityRole: 'alert' as const,
  }),

  // Tab accessibility props
  tab: (label: string, selected: boolean, position: number, total: number) => ({
    accessible: true,
    accessibilityRole: 'tab' as const,
    accessibilityLabel: `${label}, pestaña ${position} de ${total}`,
    accessibilityState: { selected },
  }),

  // Switch accessibility props
  switch: (label: string, checked: boolean) => ({
    accessible: true,
    accessibilityRole: 'switch' as const,
    accessibilityLabel: label,
    accessibilityState: { checked },
  }),

  // Progress bar accessibility props
  progress: (label: string, value: number, max = 100) => ({
    accessible: true,
    accessibilityRole: 'progressbar' as const,
    accessibilityLabel: label,
    accessibilityValue: {
      min: 0,
      max,
      now: value,
      text: `${Math.round((value / max) * 100)}% completado`,
    },
  }),
};
