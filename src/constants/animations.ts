import { Animated, Easing } from 'react-native';

// Animation durations
export const durations = {
  fast: 200,
  normal: 300,
  slow: 500,
  verySlow: 800,
} as const;

// Common easing functions
export const easings = {
  easeIn: Easing.bezier(0.4, 0, 1, 1),
  easeOut: Easing.bezier(0, 0, 0.2, 1),
  easeInOut: Easing.bezier(0.4, 0, 0.2, 1),
  spring: Easing.bezier(0.175, 0.885, 0.32, 1.275),
  bounce: Easing.bounce,
} as const;

// Predefined animations
export const animations = {
  // Fade animations
  fadeIn: (animatedValue: Animated.Value, duration: number = durations.normal) => {
    return Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: easings.easeOut,
      useNativeDriver: true,
    });
  },

  fadeOut: (animatedValue: Animated.Value, duration: number = durations.normal) => {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: easings.easeIn,
      useNativeDriver: true,
    });
  },

  // Slide animations
  slideInFromRight: (animatedValue: Animated.Value, duration: number = durations.normal) => {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: easings.easeOut,
      useNativeDriver: true,
    });
  },

  slideInFromBottom: (animatedValue: Animated.Value, duration: number = durations.normal) => {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: easings.easeOut,
      useNativeDriver: true,
    });
  },

  // Scale animations
  scaleIn: (animatedValue: Animated.Value, _duration: number = durations.fast) => {
    return Animated.spring(animatedValue, {
      toValue: 1,
      tension: 50,
      friction: 7,
      useNativeDriver: true,
    });
  },

  scaleOut: (animatedValue: Animated.Value, duration: number = durations.fast) => {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: easings.easeIn,
      useNativeDriver: true,
    });
  },

  // Button press animation
  buttonPress: (animatedValue: Animated.Value) => {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 0.95,
        duration: 100,
        easing: easings.easeOut,
        useNativeDriver: true,
      }),
      Animated.spring(animatedValue, {
        toValue: 1,
        tension: 100,
        friction: 10,
        useNativeDriver: true,
      }),
    ]);
  },

  // Success checkmark animation
  successCheckmark: (animatedValue: Animated.Value) => {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1.2,
        duration: 200,
        easing: easings.easeOut,
        useNativeDriver: true,
      }),
      Animated.spring(animatedValue, {
        toValue: 1,
        tension: 100,
        friction: 5,
        useNativeDriver: true,
      }),
    ]);
  },

  // Pulse animation
  pulse: (animatedValue: Animated.Value) => {
    return Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1.1,
          duration: 1000,
          easing: easings.easeInOut,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: easings.easeInOut,
          useNativeDriver: true,
        }),
      ])
    );
  },
};

// Animation utilities
export const createAnimation = (
  from: number,
  to: number,
  duration: number = durations.normal,
  easing: (value: number) => number = easings.easeInOut
): Animated.Value => {
  const animatedValue = new Animated.Value(from);

  Animated.timing(animatedValue, {
    toValue: to,
    duration,
    easing,
    useNativeDriver: true,
  }).start();

  return animatedValue;
};

// Parallel animations helper
export const parallel = (animations: Animated.CompositeAnimation[]) => {
  return Animated.parallel(animations);
};

// Sequence animations helper
export const sequence = (animations: Animated.CompositeAnimation[]) => {
  return Animated.sequence(animations);
};

// Stagger animations helper
export const stagger = (delay: number, animations: Animated.CompositeAnimation[]) => {
  return Animated.stagger(delay, animations);
};
