import { logger } from '@/utils/logger';

export interface ProductMappingDebugInfo {
  aiProductName: string;
  inventoryProductName?: string;
  inventoryProductId?: string;
  matchConfidence?: number;
  matchType?: 'exact' | 'fuzzy' | 'partial' | 'none';
  alternativeMatches?: Array<{
    name: string;
    confidence: number;
  }>;
}

export interface FormulationDebugData {
  hasAnalysis: boolean;
  hasDesiredResult: boolean;
  selectedBrand: string;
  selectedLine: string;
  productMappings: ProductMappingDebugInfo[];
  formulationError?: {
    type: string;
    message: string;
    details?: any;
  };
  aiResponseTime?: number;
  edgeFunctionLogs?: string[];
}

export class FormulationDebugger {
  private static debugData: FormulationDebugData | null = null;

  static startDebugging(initialData: Partial<FormulationDebugData>) {
    if (__DEV__) {
      this.debugData = {
        hasAnalysis: false,
        hasDesiredResult: false,
        selectedBrand: '',
        selectedLine: '',
        productMappings: [],
        ...initialData,
      };
      logger.debug('🐛 Formulation debugging started', 'FormulationDebugger', initialData);
    }
  }

  static updateDebugData(updates: Partial<FormulationDebugData>) {
    if (__DEV__ && this.debugData) {
      this.debugData = { ...this.debugData, ...updates };
      logger.debug('🐛 Debug data updated', 'FormulationDebugger', updates);
    }
  }

  static logProductMappingIssues(products: any[]) {
    if (!__DEV__ || !products) return;

    const unmappedProducts = products.filter(p => !p.inventoryProductId);
    const lowConfidenceProducts = products.filter(
      p => p.matchConfidence && p.matchConfidence < 0.8
    );

    if (unmappedProducts.length > 0) {
      logger.warn('⚠️ Products without mapping found', 'FormulationDebugger', {
        count: unmappedProducts.length,
        products: unmappedProducts.map(p => p.name || p.productName),
      });
    }

    if (lowConfidenceProducts.length > 0) {
      logger.warn('⚠️ Low confidence product mappings found', 'FormulationDebugger', {
        count: lowConfidenceProducts.length,
        products: lowConfidenceProducts.map(p => ({
          name: p.name || p.productName,
          confidence: p.matchConfidence,
        })),
      });
    }
  }

  static logFormulationError(error: any) {
    if (__DEV__) {
      logger.error('🚨 Formulation error occurred', 'FormulationDebugger', {
        error,
        debugData: this.debugData,
        timestamp: new Date().toISOString(),
      });
    }
  }

  static getDebugSummary(): FormulationDebugData | null {
    return __DEV__ ? this.debugData : null;
  }

  static logSuccessfulFormulation(formulaLength: number, hasStructuredData: boolean) {
    if (__DEV__) {
      logger.debug('✅ Formula generation successful', 'FormulationDebugger', {
        formulaLength,
        hasStructuredData,
        debugData: this.debugData,
        timestamp: new Date().toISOString(),
      });
    }
  }

  static reset() {
    if (__DEV__) {
      logger.debug('🔄 Debug data reset', 'FormulationDebugger');
      this.debugData = null;
    }
  }
}

// Helper to generate debugging suggestions for developers
export function generateDebuggingSuggestions(
  error: any,
  debugData: FormulationDebugData | null
): string[] {
  const suggestions: string[] = [];

  if (!debugData) return ['Enable debugging to get detailed suggestions'];

  // Analysis-related suggestions
  if (!debugData.hasAnalysis) {
    suggestions.push('• Complete hair diagnosis first - analysis data is missing');
  }

  if (!debugData.hasDesiredResult) {
    suggestions.push('• Define desired color result - target analysis is missing');
  }

  // Brand/line suggestions
  if (!debugData.selectedBrand || debugData.selectedBrand === 'Genérico') {
    suggestions.push('• Select a specific brand - avoid generic brands for better results');
  }

  // Product mapping suggestions
  if (debugData.productMappings.length === 0) {
    suggestions.push('• Check product inventory - no products available for mapping');
  } else {
    const unmappedCount = debugData.productMappings.filter(p => !p.inventoryProductId).length;
    if (unmappedCount > 0) {
      suggestions.push(`• Map ${unmappedCount} products manually - AI couldn't find matches`);
    }
  }

  // Error-specific suggestions
  if (debugData.formulationError) {
    switch (debugData.formulationError.type) {
      case 'auth':
        suggestions.push('• Check Supabase authentication - token may be expired');
        break;
      case 'network':
        suggestions.push('• Check internet connection - API calls are failing');
        break;
      case 'ai':
        suggestions.push('• Check OpenAI API key configuration in Supabase Edge Functions');
        break;
      case 'validation':
        suggestions.push('• Verify all required data is provided before generating formula');
        break;
      default:
        suggestions.push('• Check browser console for detailed error information');
    }
  }

  return suggestions.length > 0 ? suggestions : ['No specific issues detected'];
}
