export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      ai_analysis_cache: {
        Row: {
          analysis_type: string;
          cost_usd: number | null;
          created_at: string | null;
          expires_at: string | null;
          id: string;
          input_data: Json | null;
          input_hash: string;
          model_used: string | null;
          prompt_version: string | null;
          result: Json;
          salon_id: string;
          tokens_used: number | null;
        };
        Insert: {
          analysis_type: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash: string;
          model_used?: string | null;
          prompt_version?: string | null;
          result: Json;
          salon_id: string;
          tokens_used?: number | null;
        };
        Update: {
          analysis_type?: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash?: string;
          model_used?: string | null;
          prompt_version?: string | null;
          result?: Json;
          salon_id?: string;
          tokens_used?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'ai_analysis_cache_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      ai_cache: {
        Row: {
          cache_key: string;
          created_at: string | null;
          expires_at: string;
          id: string;
          request_payload: Json;
          response_data: Json;
          task_type: string;
          updated_at: string | null;
        };
        Insert: {
          cache_key: string;
          created_at?: string | null;
          expires_at: string;
          id?: string;
          request_payload: Json;
          response_data: Json;
          task_type: string;
          updated_at?: string | null;
        };
        Update: {
          cache_key?: string;
          created_at?: string | null;
          expires_at?: string;
          id?: string;
          request_payload?: Json;
          response_data?: Json;
          task_type?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      ai_quality_metrics: {
        Row: {
          created_at: string | null;
          edge_function_version: number | null;
          id: string;
          metric_type: string;
          metrics: Json;
          salon_id: string;
          timestamp: string | null;
        };
        Insert: {
          created_at?: string | null;
          edge_function_version?: number | null;
          id?: string;
          metric_type: string;
          metrics: Json;
          salon_id: string;
          timestamp?: string | null;
        };
        Update: {
          created_at?: string | null;
          edge_function_version?: number | null;
          id?: string;
          metric_type?: string;
          metrics?: Json;
          salon_id?: string;
          timestamp?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'ai_quality_metrics_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      cache_metrics: {
        Row: {
          id: number;
          metrics: Json;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          metrics?: Json;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          metrics?: Json;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      chat_attachments: {
        Row: {
          attachment_type: string;
          created_at: string | null;
          file_name: string | null;
          file_size: number | null;
          file_url: string;
          height: number | null;
          id: string;
          message_id: string;
          metadata: Json | null;
          mime_type: string | null;
          thumbnail_url: string | null;
          width: number | null;
        };
        Insert: {
          attachment_type: string;
          created_at?: string | null;
          file_name?: string | null;
          file_size?: number | null;
          file_url: string;
          height?: number | null;
          id?: string;
          message_id: string;
          metadata?: Json | null;
          mime_type?: string | null;
          thumbnail_url?: string | null;
          width?: number | null;
        };
        Update: {
          attachment_type?: string;
          created_at?: string | null;
          file_name?: string | null;
          file_size?: number | null;
          file_url?: string;
          height?: number | null;
          id?: string;
          message_id?: string;
          metadata?: Json | null;
          mime_type?: string | null;
          thumbnail_url?: string | null;
          width?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_attachments_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_attachments_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages_with_attachments';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_context_references: {
        Row: {
          created_at: string | null;
          id: string;
          message_id: string;
          reference_data: Json | null;
          reference_id: string;
          reference_type: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          message_id: string;
          reference_data?: Json | null;
          reference_id: string;
          reference_type: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          message_id?: string;
          reference_data?: Json | null;
          reference_id?: string;
          reference_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_context_references_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_context_references_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages_with_attachments';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_conversations: {
        Row: {
          context_id: string | null;
          context_type: string | null;
          created_at: string | null;
          id: string;
          metadata: Json | null;
          salon_id: string;
          status: string;
          title: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          context_id?: string | null;
          context_type?: string | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          salon_id: string;
          status?: string;
          title?: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          context_id?: string | null;
          context_type?: string | null;
          created_at?: string | null;
          id?: string;
          metadata?: Json | null;
          salon_id?: string;
          status?: string;
          title?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_messages: {
        Row: {
          completion_tokens: number | null;
          content: string;
          conversation_id: string;
          cost_usd: number | null;
          created_at: string | null;
          has_attachments: boolean | null;
          id: string;
          metadata: Json | null;
          prompt_tokens: number | null;
          role: string;
          total_tokens: number | null;
        };
        Insert: {
          completion_tokens?: number | null;
          content: string;
          conversation_id: string;
          cost_usd?: number | null;
          created_at?: string | null;
          has_attachments?: boolean | null;
          id?: string;
          metadata?: Json | null;
          prompt_tokens?: number | null;
          role: string;
          total_tokens?: number | null;
        };
        Update: {
          completion_tokens?: number | null;
          content?: string;
          conversation_id?: string;
          cost_usd?: number | null;
          created_at?: string | null;
          has_attachments?: boolean | null;
          id?: string;
          metadata?: Json | null;
          prompt_tokens?: number | null;
          role?: string;
          total_tokens?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations_with_stats';
            referencedColumns: ['id'];
          },
        ];
      };
      client_consents: {
        Row: {
          client_id: string;
          completed_steps: Json | null;
          consent_data: Json | null;
          consent_text: string;
          consent_type: string;
          created_at: string | null;
          critical_verifications: Json | null;
          form_version: string | null;
          id: string;
          ip_address: unknown | null;
          language: string | null;
          patch_test_result: string | null;
          safety_checklist: Json | null;
          salon_id: string;
          service_id: string | null;
          signature_data: string | null;
          signature_url: string | null;
          signed_at: string | null;
          skip_safety: boolean | null;
          user_agent: string | null;
        };
        Insert: {
          client_id: string;
          completed_steps?: Json | null;
          consent_data?: Json | null;
          consent_text: string;
          consent_type: string;
          created_at?: string | null;
          critical_verifications?: Json | null;
          form_version?: string | null;
          id?: string;
          ip_address?: unknown | null;
          language?: string | null;
          patch_test_result?: string | null;
          safety_checklist?: Json | null;
          salon_id: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Update: {
          client_id?: string;
          completed_steps?: Json | null;
          consent_data?: Json | null;
          consent_text?: string;
          consent_type?: string;
          created_at?: string | null;
          critical_verifications?: Json | null;
          form_version?: string | null;
          id?: string;
          ip_address?: unknown | null;
          language?: string | null;
          patch_test_result?: string | null;
          safety_checklist?: Json | null;
          salon_id?: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'client_consents_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      clients: {
        Row: {
          allergies: string[] | null;
          birth_date: string | null;
          created_at: string | null;
          created_by: string | null;
          current_medications: string | null;
          email: string | null;
          id: string;
          is_vip: boolean | null;
          medical_conditions: string | null;
          name: string;
          notes: string | null;
          phone: string | null;
          salon_id: string;
          tags: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name: string;
          notes?: string | null;
          phone?: string | null;
          salon_id: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name?: string;
          notes?: string | null;
          phone?: string | null;
          salon_id?: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'clients_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'clients_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      formulas: {
        Row: {
          catalog_issues: Json | null;
          catalog_status: Database['public']['Enums']['formula_catalog_status'];
          colorimetry_flags: Json | null;
          content_hash: string | null;
          duplicate_of: string | null;
          brand: string | null;
          created_at: string | null;
          created_by: string | null;
          developer_volume: number | null;
          formula_data: Json;
          formula_text: string;
          id: string;
          line: string | null;
          name: string | null;
          personalization_summary: Json | null;
          processing_time: number | null;
          scenario_hash: string | null;
          salon_id: string;
          service_id: string | null;
          technique: string | null;
          total_cost: number | null;
          validation_errors: Json | null;
          validation_status: Database['public']['Enums']['formula_validation_status'];
          version: number;
        };
        Insert: {
          catalog_issues?: Json | null;
          catalog_status?: Database['public']['Enums']['formula_catalog_status'];
          colorimetry_flags?: Json | null;
          content_hash?: string | null;
          duplicate_of?: string | null;
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          developer_volume?: number | null;
          formula_data: Json;
          formula_text: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          personalization_summary?: Json | null;
          processing_time?: number | null;
          scenario_hash?: string | null;
          salon_id: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
          validation_errors?: Json | null;
          validation_status?: Database['public']['Enums']['formula_validation_status'];
          version?: number;
        };
        Update: {
          catalog_issues?: Json | null;
          catalog_status?: Database['public']['Enums']['formula_catalog_status'];
          colorimetry_flags?: Json | null;
          content_hash?: string | null;
          duplicate_of?: string | null;
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          developer_volume?: number | null;
          formula_data?: Json;
          formula_text?: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          personalization_summary?: Json | null;
          processing_time?: number | null;
          scenario_hash?: string | null;
          salon_id?: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
          validation_errors?: Json | null;
          validation_status?: Database['public']['Enums']['formula_validation_status'];
          version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'formulas_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      brand_line_shades: {
        Row: {
          brand_id: string;
          created_at: string;
          display_name: string | null;
          id: string;
          is_active: boolean;
          line_id: string;
          metadata: Json | null;
          shade_code: string;
          updated_at: string;
        };
        Insert: {
          brand_id: string;
          created_at?: string;
          display_name?: string | null;
          id?: string;
          is_active?: boolean;
          line_id: string;
          metadata?: Json | null;
          shade_code: string;
          updated_at?: string;
        };
        Update: {
          brand_id?: string;
          created_at?: string;
          display_name?: string | null;
          id?: string;
          is_active?: boolean;
          line_id?: string;
          metadata?: Json | null;
          shade_code?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'brand_line_shades_brand_id_fkey';
            columns: ['brand_id'];
            isOneToOne: false;
            referencedRelation: 'brands';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'brand_line_shades_line_id_fkey';
            columns: ['line_id'];
            isOneToOne: false;
            referencedRelation: 'product_lines';
            referencedColumns: ['id'];
          },
        ];
      };
      product_mappings: {
        Row: {
          ai_product_name: string;
          confidence: number;
          created_at: string | null;
          id: string;
          inventory_product_id: string;
          salon_id: string;
          updated_at: string | null;
          usage_count: number | null;
        };
        Insert: {
          ai_product_name: string;
          confidence: number;
          created_at?: string | null;
          id?: string;
          inventory_product_id: string;
          salon_id: string;
          updated_at?: string | null;
          usage_count?: number | null;
        };
        Update: {
          ai_product_name?: string;
          confidence?: number;
          created_at?: string | null;
          id?: string;
          inventory_product_id?: string;
          salon_id?: string;
          updated_at?: string | null;
          usage_count?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'product_mappings_inventory_product_id_fkey';
            columns: ['inventory_product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'product_mappings_inventory_product_id_fkey';
            columns: ['inventory_product_id'];
            isOneToOne: false;
            referencedRelation: 'v_salon_products_summary';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'product_mappings_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      products: {
        Row: {
          barcode: string | null;
          brand: string;
          category: string | null;
          color_code: string | null;
          cost_per_unit: number | null;
          created_at: string | null;
          display_name: string | null;
          id: string;
          is_active: boolean | null;
          last_purchase_date: string | null;
          line: string | null;
          max_stock: number | null;
          minimum_stock_ml: number | null;
          name: string;
          notes: string | null;
          sale_price: number | null;
          salon_id: string;
          shade: string | null;
          size_ml: number;
          stock_ml: number | null;
          supplier: string | null;
          type: string | null;
          updated_at: string | null;
        };
        Insert: {
          barcode?: string | null;
          brand: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          display_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id: string;
          shade?: string | null;
          size_ml: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Update: {
          barcode?: string | null;
          brand?: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          display_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name?: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id?: string;
          shade?: string | null;
          size_ml?: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'products_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string | null;
          email: string;
          full_name: string | null;
          id: string;
          is_active: boolean | null;
          license_number: string | null;
          permissions: string[] | null;
          phone: string | null;
          role: string | null;
          salon_id: string | null;
          specializations: string[] | null;
          updated_at: string | null;
          years_experience: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          is_active?: boolean | null;
          license_number?: string | null;
          permissions?: string[] | null;
          phone?: string | null;
          role?: string | null;
          salon_id?: string | null;
          specializations?: string[] | null;
          updated_at?: string | null;
          years_experience?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          license_number?: string | null;
          permissions?: string[] | null;
          phone?: string | null;
          role?: string | null;
          salon_id?: string | null;
          specializations?: string[] | null;
          updated_at?: string | null;
          years_experience?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      salons: {
        Row: {
          created_at: string | null;
          id: string;
          name: string;
          owner_id: string | null;
          settings: Json | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          name: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          name?: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      services: {
        Row: {
          after_photos: string[] | null;
          ai_analysis: Json | null;
          before_photos: string[] | null;
          client_id: string;
          created_at: string | null;
          duration_minutes: number | null;
          id: string;
          notes: string | null;
          price: number | null;
          salon_id: string;
          satisfaction_score: number | null;
          service_date: string | null;
          service_type: string;
          status: string | null;
          stylist_id: string;
          updated_at: string | null;
        };
        Insert: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          id?: string;
          notes?: string | null;
          price?: number | null;
          salon_id: string;
          satisfaction_score?: number | null;
          service_date?: string | null;
          service_type: string;
          status?: string | null;
          stylist_id: string;
          updated_at?: string | null;
        };
        Update: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id?: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          id?: string;
          notes?: string | null;
          price?: number | null;
          salon_id?: string;
          satisfaction_score?: number | null;
          service_date?: string | null;
          service_type?: string;
          status?: string | null;
          stylist_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'services_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_stylist_id_fkey';
            columns: ['stylist_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      stock_movements: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          id: string;
          notes: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id: string | null;
          reference_type: string | null;
          salon_id: string;
          type: string;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id: string;
          type: string;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id?: string;
          quantity_ml?: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id?: string;
          type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'stock_movements_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'v_salon_products_summary';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      ai_quality_metrics_summary: {
        Row: {
          avg_compatibility_score: number | null;
          avg_validation_errors: number | null;
          avg_validation_warnings: number | null;
          bleaching_required_count: number | null;
          date: string | null;
          formulas_passed_validation: number | null;
          formulas_with_errors: number | null;
          metric_type: string | null;
          most_used_technique: string | null;
          prepigmentation_required_count: number | null;
          salon_id: string | null;
          total_requests: number | null;
          unachievable_roots_count: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'ai_quality_metrics_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_conversations_with_stats: {
        Row: {
          context_id: string | null;
          context_type: string | null;
          created_at: string | null;
          id: string | null;
          last_message_at: string | null;
          message_count: number | null;
          metadata: Json | null;
          salon_id: string | null;
          status: string | null;
          title: string | null;
          total_cost_usd: number | null;
          total_tokens_used: number | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_messages_with_attachments: {
        Row: {
          attachments: Json | null;
          completion_tokens: number | null;
          content: string | null;
          conversation_id: string | null;
          cost_usd: number | null;
          created_at: string | null;
          has_attachments: boolean | null;
          id: string | null;
          metadata: Json | null;
          prompt_tokens: number | null;
          role: string | null;
          total_tokens: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations_with_stats';
            referencedColumns: ['id'];
          },
        ];
      };
      v_salon_products_summary: {
        Row: {
          brand: string | null;
          cost_per_unit: number | null;
          created_at: string | null;
          current_stock: number | null;
          id: string | null;
          is_active: boolean | null;
          is_low_stock: boolean | null;
          min_stock_level: number | null;
          name: string | null;
          product_line: string | null;
          product_type: string | null;
          recent_movements: Json | null;
          salon_id: string | null;
          shade: string | null;
          size: number | null;
          unit: string | null;
          updated_at: string | null;
        };
        Insert: {
          brand?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          current_stock?: number | null;
          id?: string | null;
          is_active?: boolean | null;
          is_low_stock?: never;
          min_stock_level?: number | null;
          name?: string | null;
          product_line?: string | null;
          product_type?: string | null;
          recent_movements?: never;
          salon_id?: string | null;
          shade?: string | null;
          size?: number | null;
          unit?: never;
          updated_at?: string | null;
        };
        Update: {
          brand?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          current_stock?: number | null;
          id?: string | null;
          is_active?: boolean | null;
          is_low_stock?: never;
          min_stock_level?: number | null;
          name?: string | null;
          product_line?: string | null;
          product_type?: string | null;
          recent_movements?: never;
          salon_id?: string | null;
          shade?: string | null;
          size?: number | null;
          unit?: never;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'products_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Functions: {
      clean_old_originals: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      clean_temp_photos: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_ai_cache: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_cache: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      delete_old_temp_photos: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      get_conversation_total_cost: {
        Args: { conversation_uuid: string };
        Returns: number;
      };
      get_low_stock_products: {
        Args: { p_salon_id: string };
        Returns: {
          product_id: string;
          brand: string;
          name: string;
          category: string;
          stock_ml: number;
          minimum_stock_ml: number;
          percentage_remaining: number;
          color_code: string;
        }[];
      };
      get_user_salon_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      increment_mapping_usage: {
        Args: { p_salon_id: string; p_ai_product_name: string };
        Returns: undefined;
      };
      manual_user_setup: {
        Args: { p_user_id: string } | { user_id: string; user_email: string; user_name: string };
        Returns: Json;
      };
      update_salon_settings: {
        Args: { p_salon_id: string; p_settings: Json };
        Returns: boolean;
      };
    };
    Enums: {
      formula_catalog_status: 'unverified' | 'matched' | 'mismatch' | 'partial' | 'unknown_brand';
      formula_validation_status:
        | 'pending'
        | 'auto_passed'
        | 'auto_corrected'
        | 'auto_rejected'
        | 'manual_review';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      formula_catalog_status: {
        UNVERIFIED: 'unverified',
        MATCHED: 'matched',
        MISMATCH: 'mismatch',
        PARTIAL: 'partial',
        UNKNOWN_BRAND: 'unknown_brand',
      },
      formula_validation_status: {
        PENDING: 'pending',
        AUTO_PASSED: 'auto_passed',
        AUTO_CORRECTED: 'auto_corrected',
        AUTO_REJECTED: 'auto_rejected',
        MANUAL_REVIEW: 'manual_review',
      },
    },
  },
} as const;
