// Complete helpers.ts for Salonier Assistant Edge Function
// Contains all utility functions needed by the main function

/**
 * Convert URL to base64 data URL for OpenAI
 */
export async function urlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Convert to base64
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);

    // Determine content type
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    return `data:${contentType};base64,${base64}`;
  } catch (error) {
    console.error('urlToBase64 error:', error);
    throw new Error(`Failed to convert URL to base64: ${error}`);
  }
}

/**
 * Determine complexity of the request
 */
export function determineComplexity(payload: any): 'simple' | 'medium' | 'complex' {
  if (!payload) return 'simple';

  const hasImage = !!(payload.imageUrl || payload.imageBase64);
  const hasDetailedAnalysis = !!(payload.detailedAnalysis || payload.zoneAnalysis);
  const hasBrandConstraints = !!(payload.selectedBrand || payload.availableProducts);

  if (hasImage && hasDetailedAnalysis && hasBrandConstraints) return 'complex';
  if ((hasImage && hasDetailedAnalysis) || hasBrandConstraints) return 'medium';
  return 'simple';
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts) {
        break;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * Calculate API call cost
 */
export function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  const pricing: Record<string, { input: number; output: number }> = {
    'gpt-4o': { input: 2.5, output: 10.0 },
    'gpt-4o-mini': { input: 0.15, output: 0.6 },
    'gpt-3.5-turbo': { input: 0.5, output: 1.5 },
  };

  const modelPricing = pricing[model] || pricing['gpt-4o'];
  const inputCost = (inputTokens / 1_000_000) * modelPricing.input;
  const outputCost = (outputTokens / 1_000_000) * modelPricing.output;

  return inputCost + outputCost;
}

/**
 * Extract JSON from string response
 */
export function extractJsonFromString(str: string): any {
  if (!str) throw new Error('Empty string provided');

  // Try to parse as direct JSON first
  try {
    return JSON.parse(str);
  } catch {
    // Look for JSON within the string
    const jsonMatch = str.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0]);
      } catch {
        throw new Error('Found JSON-like structure but failed to parse');
      }
    }

    throw new Error('No valid JSON found in response');
  }
}

/**
 * Extract bucket name from Supabase storage URL
 */
export function extractBucketFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');

    // Supabase storage URLs have format: /storage/v1/object/public/bucket-name/...
    // or /storage/v1/object/sign/bucket-name/...
    const bucketIndex = pathParts.findIndex(part => part === 'public' || part === 'sign') + 1;

    if (bucketIndex > 0 && bucketIndex < pathParts.length) {
      return pathParts[bucketIndex];
    }

    throw new Error('Could not extract bucket from URL');
  } catch (error) {
    console.error('extractBucketFromUrl error:', error);
    return 'photos'; // Default bucket
  }
}

/**
 * Ensure user has a salon_id association
 */
export async function ensureUserHasSalonId(supabase: any, userId: string): Promise<string | null> {
  try {
    // First, check if user already has a salon_id in profiles
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', userId)
      .single();

    if (profile?.salon_id) {
      return profile.salon_id;
    }

    // If no salon_id, try to find user's salon through other associations
    // This is a fallback mechanism

    // Check if user has any services or data that might link them to a salon
    const { data: userServices } = await supabase
      .from('services')
      .select('salon_id')
      .eq('created_by', userId)
      .limit(1)
      .single();

    if (userServices?.salon_id) {
      // Update the profile with the found salon_id
      await supabase
        .from('profiles')
        .update({ salon_id: userServices.salon_id })
        .eq('id', userId);

      return userServices.salon_id;
    }

    // If still no salon found, create a default salon for the user
    const { data: newSalon, error: salonError } = await supabase
      .from('salons')
      .insert({
        name: 'Mi Salón',
        created_by: userId,
        updated_at: new Date().toISOString(),
      })
      .select('id')
      .single();

    if (salonError || !newSalon) {
      console.error('Failed to create default salon:', salonError);
      return null;
    }

    // Update user profile with new salon_id
    const { error: profileError } = await supabase
      .from('profiles')
      .update({ salon_id: newSalon.id })
      .eq('id', userId);

    if (profileError) {
      console.error('Failed to update profile with salon_id:', profileError);
      return null;
    }

    return newSalon.id;
  } catch (error) {
    console.error('ensureUserHasSalonId error:', error);
    return null;
  }
}

/**
 * Validate base64 image format
 */
export function validateBase64Image(base64String: string): boolean {
  try {
    // Remove data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');

    // Check if it's valid base64
    const decoded = atob(base64Data);

    // Check reasonable size limits (between 100 bytes and 4MB)
    if (decoded.length < 100 || decoded.length > 4 * 1024 * 1024) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Generate cache key for caching system
 */
export function generateCacheKey(task: string, payload: any): string {
  try {
    // Create a stable key based on task and relevant payload data
    const relevantData = {
      task,
      imageUrl: payload.imageUrl ? 'has_url' : null,
      imageBase64: payload.imageBase64 ? `hash_${payload.imageBase64.length}` : null,
      selectedBrand: payload.selectedBrand || null,
      selectedLine: payload.selectedLine || null,
      desiredLevel: payload.desiredLevel || null,
      desiredTone: payload.desiredTone || null,
    };

    // Remove null values
    const cleanedData = Object.fromEntries(
      Object.entries(relevantData).filter(([_, value]) => value !== null)
    );

    // Create hash-like key
    const dataString = JSON.stringify(cleanedData);
    const hash = Array.from(dataString)
      .reduce((hash, char) => {
        const charCode = char.charCodeAt(0);
        hash = ((hash << 5) - hash) + charCode;
        return hash & hash; // Convert to 32-bit integer
      }, 0);

    return `${task}_${Math.abs(hash)}`;
  } catch (error) {
    console.error('generateCacheKey error:', error);
    return `${task}_${Date.now()}`;
  }
}

/**
 * Simple delay function
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Validate payload structure for specific tasks
 */
export function validatePayload(task: string, payload: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!payload || typeof payload !== 'object') {
    return { isValid: false, errors: ['Payload must be an object'] };
  }

  switch (task) {
    case 'diagnose_image':
      if (!payload.imageUrl && !payload.imageBase64) {
        errors.push('Either imageUrl or imageBase64 is required');
      }
      break;

    case 'analyze_desired_look':
      if (!payload.imageUrl && !payload.imageBase64) {
        errors.push('Either imageUrl or imageBase64 is required');
      }
      if (!payload.currentLevel) {
        errors.push('currentLevel is required');
      }
      break;

    case 'generate_formula':
      if (!payload.diagnosis) {
        errors.push('diagnosis is required');
      }
      if (!payload.desiredLook) {
        errors.push('desiredLook is required');
      }
      break;

    case 'convert_formula':
      if (!payload.originalFormula) {
        errors.push('originalFormula is required');
      }
      if (!payload.targetBrand) {
        errors.push('targetBrand is required');
      }
      break;

    case 'parse_product_text':
      if (!payload.productText) {
        errors.push('productText is required');
      }
      break;

    case 'chat_assistant':
      if (!payload.message) {
        errors.push('message is required');
      }
      if (!payload.conversationId) {
        errors.push('conversationId is required');
      }
      break;

    case 'upload_photo':
      if (!payload.imageBase64) {
        errors.push('imageBase64 is required');
      }
      if (!payload.clientId) {
        errors.push('clientId is required');
      }
      if (!payload.photoType || !['before', 'after', 'desired'].includes(payload.photoType)) {
        errors.push('photoType must be one of: before, after, desired');
      }
      break;
  }

  return { isValid: errors.length === 0, errors };
}