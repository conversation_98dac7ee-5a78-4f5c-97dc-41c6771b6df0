BEGIN;

-- Ensure required enum types exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_type
    WHERE typname = 'formula_validation_status'
  ) THEN
    CREATE TYPE public.formula_validation_status AS ENUM (
      'pending',
      'auto_passed',
      'auto_corrected',
      'auto_rejected',
      'manual_review'
    );
  END IF;
END
$$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_type
    WHERE typname = 'formula_catalog_status'
  ) THEN
    CREATE TYPE public.formula_catalog_status AS ENUM (
      'unverified',
      'matched',
      'mismatch',
      'partial',
      'unknown_brand'
    );
  END IF;
END
$$;

-- Extend formulas table with hashing, validation and catalog metadata
ALTER TABLE public.formulas
  ADD COLUMN IF NOT EXISTS scenario_hash text,
  ADD COLUMN IF NOT EXISTS content_hash text,
  ADD COLUMN IF NOT EXISTS version integer NOT NULL DEFAULT 1,
  ADD COLUMN IF NOT EXISTS duplicate_of uuid REFERENCES public.formulas(id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS validation_status public.formula_validation_status NOT NULL DEFAULT 'pending',
  ADD COLUMN IF NOT EXISTS validation_errors jsonb,
  ADD COLUMN IF NOT EXISTS catalog_status public.formula_catalog_status NOT NULL DEFAULT 'unverified',
  ADD COLUMN IF NOT EXISTS catalog_issues jsonb,
  ADD COLUMN IF NOT EXISTS personalization_summary jsonb,
  ADD COLUMN IF NOT EXISTS colorimetry_flags jsonb;

CREATE INDEX IF NOT EXISTS idx_formulas_scenario_hash
  ON public.formulas (scenario_hash);

CREATE INDEX IF NOT EXISTS idx_formulas_content_hash
  ON public.formulas (content_hash);

CREATE INDEX IF NOT EXISTS idx_formulas_duplicate_of
  ON public.formulas (duplicate_of);

-- Mapping table between brands, lines and shade codes
CREATE TABLE IF NOT EXISTS public.brand_line_shades (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  brand_id uuid NOT NULL REFERENCES public.brands(id) ON DELETE CASCADE,
  line_id uuid NOT NULL REFERENCES public.product_lines(id) ON DELETE CASCADE,
  shade_code text NOT NULL,
  display_name text,
  metadata jsonb DEFAULT '{}'::jsonb,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (brand_id, line_id, shade_code)
);

CREATE INDEX IF NOT EXISTS idx_brand_line_shades_brand
  ON public.brand_line_shades (brand_id);

CREATE INDEX IF NOT EXISTS idx_brand_line_shades_line
  ON public.brand_line_shades (line_id);

CREATE INDEX IF NOT EXISTS idx_brand_line_shades_active
  ON public.brand_line_shades (is_active)
  WHERE is_active = true;

-- Reuse generic updated_at trigger helper
CREATE OR REPLACE FUNCTION public.trigger_set_updated_at()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS set_updated_at_brand_line_shades ON public.brand_line_shades;

CREATE TRIGGER set_updated_at_brand_line_shades
  BEFORE UPDATE ON public.brand_line_shades
  FOR EACH ROW
  EXECUTE FUNCTION public.trigger_set_updated_at();

-- Utility function to calculate a deterministic hash of formula data
CREATE OR REPLACE FUNCTION public.calc_formula_content_hash(p_formula jsonb)
RETURNS text
LANGUAGE plpgsql
AS $$
BEGIN
  IF p_formula IS NULL THEN
    RETURN NULL;
  END IF;
  RETURN encode(digest(jsonb_pretty(p_formula)::text, 'sha256'), 'hex');
END;
$$;

COMMIT;
