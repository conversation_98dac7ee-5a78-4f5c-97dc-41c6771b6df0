import { PhotoAngle } from './photo-capture';
import { ImageSourcePropType } from 'react-native';

// Preferimos WebP (más ligero); los PNG siguen como fallback en el repo
// eslint-disable-next-line @typescript-eslint/no-var-requires
const HairGeneralView: ImageSourcePropType = require('../../assets/images/hair-general-view.webp');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const HairHighlights: ImageSourcePropType = require('../../assets/images/hair-highlights.webp');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const HairNaturalTone: ImageSourcePropType = require('../../assets/images/hair-natural-tone.webp');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const HairRootsContrast: ImageSourcePropType = require('../../assets/images/hair-roots-contrast.webp');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const HairDimensionMovement: ImageSourcePropType = require('../../assets/images/hair-dimension-movement.webp');

export enum DesiredPhotoType {
  MAIN = 'main',
  DETAILS = 'details',
  ALTERNATIVE = 'alternative',
  ROOTS_CONTRAST = 'roots_contrast',
  DIMENSION = 'dimension',
}

export interface DesiredPhotoGuide {
  type: DesiredPhotoType;
  label: string;
  description: string;
  icon: string | ImageSourcePropType;
  required: boolean;
  angle?: PhotoAngle;
  instructions?: string;
  helpText?: string; // Tooltip text for additional guidance
}

export interface DesiredPhoto {
  id: string;
  uri: string;
  type: DesiredPhotoType;
  timestamp: Date;
  analysis?: {
    level: number;
    tones: string[];
    technique: string;
    viability: 'easy' | 'moderate' | 'challenging';
  };
}

export interface ColorTechnique {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export const DESIRED_PHOTO_GUIDES: DesiredPhotoGuide[] = [
  {
    type: DesiredPhotoType.MAIN,
    label: 'Vista General',
    description: 'Referencia completa del look deseado',
    icon: HairGeneralView,
    required: true,
    angle: PhotoAngle.FRONT,
    instructions: 'Captura una vista frontal completa que muestre todo el estilo y color deseado.',
    helpText:
      'Esta foto es esencial para entender el look general que deseas. Asegúrate de que se vea la caída natural del cabello y la distribución del color.',
  },
  {
    type: DesiredPhotoType.DETAILS,
    label: 'Técnica/Mechas',
    description: 'Detalles de la técnica de aplicación',
    icon: HairHighlights,
    required: false,
    angle: PhotoAngle.LEFT_SIDE,
    instructions:
      'Muestra claramente las mechas, reflejos o la técnica específica (balayage, highlights, etc.).',
    helpText:
      'Enfócate en cómo están distribuidas las mechas o reflejos. Esto ayuda a determinar la técnica de aplicación más adecuada.',
  },
  {
    type: DesiredPhotoType.ALTERNATIVE,
    label: 'Tono Natural',
    description: 'Color bajo luz natural',
    icon: HairNaturalTone,
    required: false,
    angle: PhotoAngle.RIGHT_SIDE,
    instructions:
      'Foto con luz natural para ver el tono real sin filtros ni iluminación artificial.',
    helpText:
      'La luz natural muestra el verdadero tono del color. Evita filtros o luz artificial que puedan alterar la percepción del color.',
  },
  {
    type: DesiredPhotoType.ROOTS_CONTRAST,
    label: 'Contraste Raíces',
    description: 'Transición desde las raíces',
    icon: HairRootsContrast,
    required: false,
    angle: PhotoAngle.CROWN,
    instructions:
      'Muestra cómo deseas la transición del color desde las raíces hacia medios y puntas.',
    helpText:
      'Importante para técnicas como balayage o cuando se busca un crecimiento natural. Muestra qué tan marcado o sutil quieres el contraste.',
  },
  {
    type: DesiredPhotoType.DIMENSION,
    label: 'Dimensión/Movimiento',
    description: 'Color en movimiento',
    icon: HairDimensionMovement,
    required: false,
    angle: PhotoAngle.BACK,
    instructions:
      'Captura el cabello en movimiento o desde atrás para ver la dimensión y profundidad del color.',
    helpText:
      'El movimiento revela las diferentes capas de color y cómo interactúan entre sí. Ideal para ver efectos multidimensionales.',
  },
];

export const COLOR_TECHNIQUES: ColorTechnique[] = [
  {
    id: 'full_color',
    name: 'Tinte Completo',
    description: 'Coloración uniforme en todo el cabello',
    icon: '🎨',
  },
  {
    id: 'highlights',
    name: 'Mechas',
    description: 'Mechas tradicionales con papel aluminio',
    icon: '📋',
  },
  {
    id: 'balayage',
    name: 'Balayage',
    description: 'Técnica de barrido a mano alzada',
    icon: '🖌️',
  },
  {
    id: 'ombre',
    name: 'Ombré',
    description: 'Degradado de oscuro a claro',
    icon: '🌅',
  },
  {
    id: 'babylights',
    name: 'Babylights',
    description: 'Mechas muy finas y naturales',
    icon: '🌟',
  },
  {
    id: 'color_correction',
    name: 'Corrección de Color',
    description: 'Corrección de colores no deseados',
    icon: '🔧',
  },
  {
    id: 'foilyage',
    name: 'Foilyage',
    description: 'Combinación de foil y balayage',
    icon: '✨',
  },
  {
    id: 'money_piece',
    name: 'Money Piece',
    description: 'Mechones frontales contrastantes',
    icon: '💫',
  },
  {
    id: 'chunky_highlights',
    name: 'Chunky Highlights',
    description: 'Mechas gruesas y definidas',
    icon: '🎯',
  },
  {
    id: 'reverse_balayage',
    name: 'Reverse Balayage',
    description: 'Balayage oscuro sobre base clara',
    icon: '🔄',
  },
];

export const getDesiredPhotoGuide = (type: DesiredPhotoType): DesiredPhotoGuide | undefined => {
  return DESIRED_PHOTO_GUIDES.find(guide => guide.type === type);
};

export const getColorTechnique = (id: string): ColorTechnique | undefined => {
  return COLOR_TECHNIQUES.find(technique => technique.id === id);
};
