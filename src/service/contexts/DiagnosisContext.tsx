import React, { createContext, useContext, ReactNode } from 'react';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { HairAnalysisResult } from '@/types/ai-analysis';

interface DiagnosisContextType {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  analysisResult: HairAnalysisResult | null;
  isDataFromAI: boolean;
  diagnosisMethod: string;
}

const DiagnosisContext = createContext<DiagnosisContextType | undefined>(undefined);

interface DiagnosisProviderProps {
  children: ReactNode;
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  analysisResult: HairAnalysisResult | null;
  isDataFromAI: boolean;
  diagnosisMethod: string;
}

export const DiagnosisProvider: React.FC<DiagnosisProviderProps> = ({
  children,
  data,
  onUpdate,
  analysisResult,
  isDataFromAI,
  diagnosisMethod,
}) => {
  const contextValue: DiagnosisContextType = {
    data,
    onUpdate,
    analysisResult,
    isDataFromAI,
    diagnosisMethod,
  };

  return <DiagnosisContext.Provider value={contextValue}>{children}</DiagnosisContext.Provider>;
};

export const useDiagnosisContext = (): DiagnosisContextType => {
  const context = useContext(DiagnosisContext);
  if (context === undefined) {
    throw new Error('useDiagnosisContext must be used within a DiagnosisProvider');
  }
  return context;
};
