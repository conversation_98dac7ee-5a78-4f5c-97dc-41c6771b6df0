/**
 * <PERSON> Context Manager - Intelligent context loading for Claude Code sessions
 * Reduces initial context load from ~32k to ~15k tokens by loading only relevant tools and agents
 */

export interface ContextProfile {
  name: string;
  description: string;
  mcpTools: string[];
  agents: string[];
  claudeFiles: string[];
  tokenEstimate: number;
}

export const contextProfiles: Record<string, ContextProfile> = {
  minimal: {
    name: 'Minimal',
    description: 'Essential tools only - fastest startup',
    mcpTools: ['mcp__ide__getDiagnostics'],
    agents: [],
    claudeFiles: ['CLAUDE-core.md'],
    tokenEstimate: 8000,
  },

  frontend: {
    name: 'Frontend Development',
    description: 'React Native, UI, and mobile development',
    mcpTools: [
      'mcp__ide__getDiagnostics',
      'mcp__context7__resolve_library_id',
      'mcp__context7__get_library_docs',
    ],
    agents: [
      'frontend-developer',
      'ui-designer',
      'whimsy-injector',
      'ux-researcher',
      'performance-benchmarker',
    ],
    claudeFiles: ['CLAUDE-core.md', 'CLAUDE-agents.md'],
    tokenEstimate: 12000,
  },

  backend: {
    name: 'Backend & Database',
    description: 'Database, Edge Functions, and server-side work',
    mcpTools: [
      'mcp__supabase__list_tables',
      'mcp__supabase__execute_sql',
      'mcp__supabase__apply_migration',
      'mcp__supabase__get_logs',
      'mcp__supabase__generate_typescript_types',
      'mcp__supabase__get_advisors',
      'mcp__ide__getDiagnostics',
    ],
    agents: [
      'database-architect',
      'deployment-engineer',
      'data-migration-specialist',
      'security-privacy-auditor',
    ],
    claudeFiles: ['CLAUDE-core.md', 'CLAUDE-agents.md', 'CLAUDE-mcp.md'],
    tokenEstimate: 16000,
  },

  ai: {
    name: 'AI & Business Logic',
    description: 'OpenAI integration, formulation, and business logic',
    mcpTools: [
      'mcp__supabase__list_edge_functions',
      'mcp__supabase__deploy_edge_function',
      'mcp__supabase__get_logs',
      'mcp__context7__resolve_library_id',
      'mcp__context7__get_library_docs',
      'mcp__ide__getDiagnostics',
    ],
    agents: [
      'ai-integration-specialist',
      'colorimetry-expert',
      'product-ceo',
      'deployment-engineer',
    ],
    claudeFiles: ['CLAUDE-core.md', 'CLAUDE-agents.md'],
    tokenEstimate: 14000,
  },

  debug: {
    name: 'Debugging & Testing',
    description: 'Error investigation, testing, and performance analysis',
    mcpTools: [
      'mcp__supabase__get_logs',
      'mcp__supabase__execute_sql',
      'mcp__supabase__get_advisors',
      'mcp__ide__getDiagnostics',
      'mcp__context7__resolve_library_id',
    ],
    agents: [
      'debug-specialist',
      'test-runner',
      'performance-benchmarker',
      'security-privacy-auditor',
    ],
    claudeFiles: ['CLAUDE-core.md', 'CLAUDE-troubleshooting.md'],
    tokenEstimate: 13000,
  },

  deployment: {
    name: 'Deployment & DevOps',
    description: 'Production deployments, migrations, and CI/CD',
    mcpTools: [
      'mcp__supabase__deploy_edge_function',
      'mcp__supabase__list_edge_functions',
      'mcp__supabase__apply_migration',
      'mcp__supabase__create_branch',
      'mcp__supabase__merge_branch',
      'mcp__supabase__list_branches',
      'mcp__supabase__get_logs',
      'mcp__supabase__get_advisors',
    ],
    agents: [
      'deployment-engineer',
      'database-architect',
      'data-migration-specialist',
      'security-privacy-auditor',
    ],
    claudeFiles: ['CLAUDE-core.md', 'CLAUDE-mcp.md'],
    tokenEstimate: 18000,
  },

  full: {
    name: 'Full Context',
    description: 'All tools and agents - maximum capability',
    mcpTools: [
      'mcp__supabase__list_tables',
      'mcp__supabase__execute_sql',
      'mcp__supabase__apply_migration',
      'mcp__supabase__get_logs',
      'mcp__supabase__generate_typescript_types',
      'mcp__supabase__get_advisors',
      'mcp__supabase__list_edge_functions',
      'mcp__supabase__deploy_edge_function',
      'mcp__supabase__create_branch',
      'mcp__supabase__merge_branch',
      'mcp__supabase__list_branches',
      'mcp__context7__resolve_library_id',
      'mcp__context7__get_library_docs',
      'mcp__ide__getDiagnostics',
    ],
    agents: [
      'frontend-developer',
      'ui-designer',
      'whimsy-injector',
      'ux-researcher',
      'performance-benchmarker',
      'database-architect',
      'deployment-engineer',
      'data-migration-specialist',
      'security-privacy-auditor',
      'ai-integration-specialist',
      'colorimetry-expert',
      'product-ceo',
      'debug-specialist',
      'test-runner',
      'sprint-prioritizer',
      'offline-sync-specialist',
    ],
    claudeFiles: [
      'CLAUDE-core.md',
      'CLAUDE-agents.md',
      'CLAUDE-mcp.md',
      'CLAUDE-troubleshooting.md',
    ],
    tokenEstimate: 32000,
  },
};

/**
 * Detects the most appropriate context profile based on user input keywords
 */
export function detectContextProfile(userInput: string): ContextProfile {
  const input = userInput.toLowerCase();

  // Frontend keywords
  const frontendKeywords = [
    'react native',
    'expo',
    'component',
    'ui',
    'interface',
    'design',
    'animation',
    'style',
    'screen',
    'navigation',
    'mobile',
    'ios',
    'android',
  ];

  // Backend keywords
  const backendKeywords = [
    'database',
    'sql',
    'supabase',
    'migration',
    'table',
    'schema',
    'rls',
    'policy',
    'edge function',
    'api',
    'server',
  ];

  // AI keywords
  const aiKeywords = [
    'openai',
    'gpt',
    'ai',
    'formula',
    'colorimetry',
    'prompt',
    'vision',
    'chat',
    'assistant',
    'business',
    'strategy',
  ];

  // Debug keywords
  const debugKeywords = [
    'error',
    'bug',
    'test',
    'debug',
    'crash',
    'issue',
    'problem',
    'fix',
    'troubleshoot',
    'performance',
    'slow',
    'memory',
  ];

  // Deployment keywords
  const deploymentKeywords = [
    'deploy',
    'deployment',
    'production',
    'ci/cd',
    'release',
    'branch',
    'merge',
    'migration',
    'rollback',
  ];

  // Count keyword matches for each profile
  const scores = {
    frontend: frontendKeywords.filter(keyword => input.includes(keyword)).length,
    backend: backendKeywords.filter(keyword => input.includes(keyword)).length,
    ai: aiKeywords.filter(keyword => input.includes(keyword)).length,
    debug: debugKeywords.filter(keyword => input.includes(keyword)).length,
    deployment: deploymentKeywords.filter(keyword => input.includes(keyword)).length,
  };

  // Find the profile with the highest score
  const maxScore = Math.max(...Object.values(scores));

  if (maxScore === 0) {
    // No specific keywords found, use minimal profile
    return contextProfiles.minimal;
  }

  const bestProfile = Object.entries(scores).find(([_, score]) => score === maxScore)?.[0];

  return contextProfiles[bestProfile || 'minimal'];
}

/**
 * Generates context loading recommendations
 */
export function getContextRecommendations(profile: ContextProfile) {
  return {
    profile,
    tokenSavings: contextProfiles.full.tokenEstimate - profile.tokenEstimate,
    loadingStrategy:
      profile.name === 'Full Context'
        ? 'All tools loaded - no optimization'
        : `Load ${profile.name} profile first, expand as needed`,
    expansionOptions:
      profile.name !== 'Full Context'
        ? [
            'Add more MCP tools on-demand',
            'Load additional agents as required',
            'Expand to full context if needed',
          ]
        : [],
  };
}

/**
 * Example usage in Claude Code session initialization
 */
export function initializeContextSession(userQuery: string) {
  const detectedProfile = detectContextProfile(userQuery);
  const recommendations = getContextRecommendations(detectedProfile);

  return {
    selectedProfile: detectedProfile,
    recommendations,
    contextCommand: `/context-profile ${detectedProfile.name.toLowerCase().replace(' ', '-')}`,
    estimatedTokens: detectedProfile.tokenEstimate,
    description: detectedProfile.description,
  };
}
