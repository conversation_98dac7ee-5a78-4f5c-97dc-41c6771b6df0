import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Search, X } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface SearchableListProps<T> {
  data: T[];
  renderItem: (item: T, isSelected: boolean) => React.ReactNode;
  keyExtractor: (item: T) => string;
  searchKeys: (keyof T)[];
  placeholder?: string;
  selectedValue?: string;
  onSelect?: (item: T) => void;
  getItemValue?: (item: T) => string;
  ListHeaderComponent?: React.ReactNode;
  groupBy?: (item: T) => string;
  containerStyle?: ViewStyle;
  searchContainerStyle?: ViewStyle;
  inputStyle?: TextStyle;
}

export function SearchableList<T>({
  data,
  renderItem,
  keyExtractor,
  searchKeys,
  placeholder = 'Buscar...',
  selectedValue,
  onSelect,
  getItemValue,
  ListHeaderComponent,
  groupBy,
  containerStyle,
  searchContainerStyle,
  inputStyle,
}: SearchableListProps<T>) {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter and group data
  const { filteredData, groupedData } = useMemo(() => {
    let filtered = data;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = data.filter(item =>
        searchKeys.some(key => {
          const value = item[key];
          if (typeof value === 'string') {
            return value.toLowerCase().includes(query);
          }
          return false;
        })
      );
    }

    if (groupBy) {
      const grouped = filtered.reduce(
        (acc, item) => {
          const group = groupBy(item);
          if (!acc[group]) {
            acc[group] = [];
          }
          acc[group].push(item);
          return acc;
        },
        {} as Record<string, T[]>
      );

      return { filteredData: filtered, groupedData: grouped };
    }

    return { filteredData: filtered, groupedData: null };
  }, [data, searchQuery, searchKeys, groupBy]);

  const clearSearch = () => {
    setSearchQuery('');
  };

  const renderGroupedList = () => {
    if (!groupedData) return null;

    return (
      <View>
        {Object.entries(groupedData).map(([group, items]) => (
          <View key={group}>
            <Text style={styles.groupTitle}>{group}</Text>
            {items.map(item => {
              const itemKey = keyExtractor(item);
              const itemValue = getItemValue ? getItemValue(item) : itemKey;
              const isSelected = selectedValue === itemValue;

              return (
                <TouchableOpacity
                  key={itemKey}
                  onPress={() => onSelect?.(item)}
                  activeOpacity={0.7}
                >
                  {renderItem(item, isSelected)}
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  const renderFlatList = () => {
    return (
      <FlatList
        data={filteredData}
        keyExtractor={keyExtractor}
        renderItem={({ item }) => {
          const itemValue = getItemValue ? getItemValue(item) : keyExtractor(item);
          const isSelected = selectedValue === itemValue;

          return (
            <TouchableOpacity onPress={() => onSelect?.(item)} activeOpacity={0.7}>
              {renderItem(item, isSelected)}
            </TouchableOpacity>
          );
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No se encontraron resultados</Text>
          </View>
        }
      />
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, searchContainerStyle]}>
        <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, inputStyle]}
          placeholder={placeholder}
          placeholderTextColor={Colors.light.gray}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
            <X size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        )}
      </View>

      {/* List Header */}
      {ListHeaderComponent}

      {/* Results Count */}
      {searchQuery.trim() && (
        <View style={styles.resultsHeader}>
          <Text style={styles.resultsText}>
            {filteredData.length} resultado
            {filteredData.length !== 1 ? 's' : ''}
          </Text>
        </View>
      )}

      {/* List */}
      {groupBy ? renderGroupedList() : renderFlatList()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginHorizontal: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.light.text,
  },
  clearButton: {
    padding: 4,
  },
  resultsHeader: {
    paddingHorizontal: 20,
    paddingBottom: 12,
  },
  resultsText: {
    fontSize: 14,
    color: Colors.light.gray,
    fontWeight: '500',
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.gray,
    marginBottom: 12,
    paddingHorizontal: 20,
    marginTop: 16,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: 'center',
  },
});
