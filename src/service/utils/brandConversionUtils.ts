/**
 * Brand conversion utilities for formulation
 */

export interface BrandConversionState {
  showBrandModal: boolean;
  brandModalType: 'main' | 'conversion';
  conversionMode: boolean;
  originalBrand: string;
  originalLine: string;
  originalFormula: string;
}

export interface BrandConversionActions {
  setShowBrandModal: (show: boolean) => void;
  setBrandModalType: (type: 'main' | 'conversion') => void;
  setConversionMode: (mode: boolean) => void;
  setOriginalBrand: (brand: string) => void;
  setOriginalLine: (line: string) => void;
  setOriginalFormula: (formula: string) => void;
}

/**
 * Initialize brand conversion state
 */
export const createInitialBrandConversionState = (): BrandConversionState => ({
  showBrandModal: false,
  brandModalType: 'main',
  conversionMode: false,
  originalBrand: '',
  originalLine: '',
  originalFormula: '',
});

/**
 * Create brand conversion context for AI generation
 */
export const createConversionContext = (state: BrandConversionState) => {
  if (!state.conversionMode) return undefined;

  return {
    originalBrand: state.originalBrand || '',
    originalLine: state.originalLine || '',
    originalFormula: state.originalFormula || '',
  };
};

/**
 * Reset brand conversion state
 */
export const resetBrandConversionState = (): Partial<BrandConversionState> => ({
  showBrandModal: false,
  brandModalType: 'main',
  conversionMode: false,
  originalBrand: '',
  originalLine: '',
  originalFormula: '',
});

/**
 * Validate brand conversion data
 */
export const validateBrandConversion = (state: BrandConversionState): boolean => {
  if (!state.conversionMode) return true;

  return !!(state.originalBrand && state.originalLine && state.originalFormula);
};
