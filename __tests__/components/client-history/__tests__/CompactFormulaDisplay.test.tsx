import React from 'react';
import { render } from '@testing-library/react-native';
import { CompactFormulaDisplay } from '../../../../src/components/client-history/CompactFormulaDisplay';
import { PreviousFormula } from '@/stores/client-history-store';

// Mock PreviousFormula data for testing
const mockService: PreviousFormula = {
  id: '1',
  date: '2024-01-15T10:00:00Z',
  formula: "L'Oreal Inoa 6.35 + oxidante 20vol 1:1",
  brand: "L'Oreal",
  line: 'Inoa',
  result: 'excelente',
  satisfaction: 5,
  notes: 'Cliente muy satisfecha con el resultado',
  processingTime: 35,
  oxidantVolume: '20',
  _syncStatus: 'synced',
};

// Mock animations
jest.mock('react-native-reanimated', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const Reanimated = require('react-native-reanimated/mock');

  // Mock FadeIn animation
  Reanimated.FadeIn = {
    duration: () => ({ duration: jest.fn() }),
  };

  return Reanimated;
});

describe('CompactFormulaDisplay', () => {
  it('should render without crashing', () => {
    expect(() => render(<CompactFormulaDisplay service={mockService} />)).not.toThrow();
  });

  it('should display brand and line information', () => {
    const { getByText } = render(<CompactFormulaDisplay service={mockService} />);

    expect(getByText("L'Oreal")).toBeTruthy();
    expect(getByText('Inoa')).toBeTruthy();
  });

  it('should display formula text', () => {
    const { getByText } = render(<CompactFormulaDisplay service={mockService} />);

    expect(getByText("L'Oreal Inoa 6.35 + oxidante 20vol 1:1")).toBeTruthy();
  });

  it('should display processing information', () => {
    const { getByText } = render(<CompactFormulaDisplay service={mockService} />);

    expect(getByText('35min')).toBeTruthy();
    expect(getByText('20vol')).toBeTruthy();
    expect(getByText('5/5')).toBeTruthy();
  });

  it('should show confidence indicator by default', () => {
    const { getByText } = render(<CompactFormulaDisplay service={mockService} />);

    expect(getByText('Alta confianza')).toBeTruthy();
  });

  it('should hide confidence indicator when showConfidence is false', () => {
    const { queryByText } = render(
      <CompactFormulaDisplay service={mockService} showConfidence={false} />
    );

    expect(queryByText('Alta confianza')).toBeFalsy();
  });

  it('should display different confidence levels correctly', () => {
    // Test medium confidence (satisfaction 3)
    const mediumService: PreviousFormula = {
      ...mockService,
      satisfaction: 3,
    };

    const { rerender, getByText } = render(<CompactFormulaDisplay service={mediumService} />);

    expect(getByText('Confianza media')).toBeTruthy();

    // Test low confidence (satisfaction 2)
    const lowService: PreviousFormula = {
      ...mockService,
      satisfaction: 2,
    };

    rerender(<CompactFormulaDisplay service={lowService} />);

    expect(getByText('Baja confianza')).toBeTruthy();
  });

  it('should render in detailed variant', () => {
    const { getByText } = render(
      <CompactFormulaDisplay service={mockService} variant="detailed" />
    );

    // Should still render basic information
    expect(getByText("L'Oreal")).toBeTruthy();
    expect(getByText('Cliente muy satisfecha con el resultado')).toBeTruthy();
  });

  it('should handle missing notes in detailed variant', () => {
    const serviceWithoutNotes: PreviousFormula = {
      ...mockService,
      notes: '',
    };

    expect(() =>
      render(<CompactFormulaDisplay service={serviceWithoutNotes} variant="detailed" />)
    ).not.toThrow();
  });

  it('should render with custom styles', () => {
    const customStyle = {
      backgroundColor: 'red',
      margin: 10,
    };

    expect(() =>
      render(<CompactFormulaDisplay service={mockService} style={customStyle} />)
    ).not.toThrow();
  });

  it('should have proper accessibility labels', () => {
    const { getByLabelText } = render(<CompactFormulaDisplay service={mockService} />);

    expect(getByLabelText("Fórmula de L'Oreal Inoa")).toBeTruthy();
  });

  it('should handle edge cases gracefully', () => {
    const edgeCaseService: PreviousFormula = {
      id: '2',
      date: '2024-01-15T10:00:00Z',
      formula: '',
      brand: '',
      line: '',
      result: 'bueno',
      satisfaction: 0,
      notes: '',
      processingTime: 0,
      oxidantVolume: '',
    };

    expect(() => render(<CompactFormulaDisplay service={edgeCaseService} />)).not.toThrow();
  });

  it('should parse formula components correctly', () => {
    const complexFormula: PreviousFormula = {
      ...mockService,
      formula: 'Wella Koleston Perfect 7.3 + 9.03 + oxidante 30vol 2:1',
    };

    const { getByText } = render(<CompactFormulaDisplay service={complexFormula} />);

    // Should extract and display processing information
    expect(getByText('30vol')).toBeTruthy();
    expect(getByText('2:1')).toBeTruthy();
  });
});
