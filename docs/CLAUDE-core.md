# CLAUDE-core.md

Essential guidance for Claude Code in the Salonier project.

## 🚀 Essential Commands

### Development

```bash
# Mobile development (primary)
npm run mobile          # Expo with LAN (stable)
npm run mobile:tunnel   # Expo with tunnel (for remote testing)
npm run ios            # iOS Simulator
npm run android        # Android Emulator

# Testing & Quality
npm test               # Run all tests
npm run lint           # Check for linting errors
npm run lint:fix       # Auto-fix linting errors
npm run code-quality   # Full quality check (lint + format)
```

### Database & Edge Functions

```bash
# Supabase Edge Functions (use deployment-engineer agent for complex deployments)
npx supabase functions deploy [function-name]

# Database migrations (use database-architect agent for schema changes)
npx supabase db push        # Apply pending migrations
npx supabase db reset       # Reset database to initial state
```

## 🏗️ Architecture Overview

### Core Paradigm: 100% AI-Powered

- **NO traditional algorithms** - Every formula is uniquely generated by GPT-4o
- **Contextual reasoning** - AI analyzes each case individually
- **Continuous improvement** - Automatically benefits from OpenAI model updates

### Technical Architecture

```
Frontend (React Native + Expo)
    ↓
Zustand Stores (Offline-First State)
    ↓
Supabase Edge Functions (AI Processing)
    ↓
PostgreSQL with RLS (Multi-tenant Data)
    ↓
OpenAI GPT-4o (Vision + Text Generation)
```

### Key Design Patterns

#### Offline-First with Optimistic UI

- All operations work offline using Zustand stores
- UI updates immediately, syncs in background
- Queue system for pending operations
- Conflict resolution via last-write-wins

#### Multi-Tenant Architecture

- Row Level Security (RLS) ensures complete data isolation
- Each salon has dedicated `salon_id` in all tables
- No cross-salon data access possible

## 📁 Project Structure & Key Files

### Core Business Logic

- `stores/` - Zustand stores for offline-first state management
  - `auth-store.ts` - Authentication and user management
  - `inventory-store.ts` - Product inventory management
  - `salon-config-store.ts` - Salon configuration and settings
  - `service-store.ts` - Service workflow state

### Critical Flows

1. **Service Flow** (`app/service/`)
   - `DiagnosisStep` → `DesiredColorStep` → `FormulationStep` → `CompletionStep`
   - Each step works offline, syncs when possible

2. **Inventory System** (`components/inventory/`)
   - Structured products: brand → line → type → shade
   - Intelligent matching: AI output ↔ inventory
   - Consumption tracking with stock validation

## 🛠️ Development Workflow

### Before Starting Work

1. Read `planning.md` - Current project state and vision
2. Check `todo.md` - Active tasks organized by sprints
3. Review recent commits: `git log --oneline -10`

### Making Changes

1. **KISS Principle** - Keep changes minimal (<50 lines when possible)
2. **Test First** - Write/run tests before committing
3. **Offline First** - Ensure functionality works without internet
4. **Update todo.md** - Mark completed tasks immediately

### Before Committing

```bash
npm run lint:fix        # Fix linting issues
npm test               # Ensure tests pass
npm run code-quality   # Final quality check
```

## ⚠️ Security Checklist

For EVERY change involving:

- User data: Verify RLS policies
- API calls: Check HTTPS usage
- Secrets: Use environment variables
- AI responses: Validate and sanitize
- File uploads: Check size and type limits

## 📊 Current Priorities (v2.2.0)

1. **ESLint Cleanup** - Reduce from 607 errors to <100
2. **Test Coverage** - Expand from 33 to 100+ tests
3. **Performance** - Achieve <3s AI latency consistently
4. **UX Polish** - Complete micro-interactions implementation

Remember: This is a pre-launch product. Focus on stability, performance, and user experience over new features.
