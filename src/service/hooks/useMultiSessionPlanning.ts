import { useCallback } from 'react';
import { getBrandPack } from '@/data/brand-packs';
import { computeDeveloperAmount, updateProductNameRatio } from '@/services/mixing-engine';
import { Formulation, FormulationStep, ViabilityAnalysis } from '@/types/formulation';
import { HairZone, HairState, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import {
  computeIntervalDays,
  getTonerLineByBrand,
  getNeutralizationHints,
  getBrandTreatmentHints,
} from '../utils/formulationHelpers';

interface MultiSessionContext {
  selectedBrand: string;
  selectedLine: string;
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
  currentLevel: number;
  targetLevel: number;
  clientContext?: {
    hairLengthCm?: number;
    hairDensity?: string;
    hairThickness?: string;
  };
}

/**
 * Hook for managing multi-session formula planning
 */
export const useMultiSessionPlanning = () => {
  const createMultiSessionPlan = useCallback(
    async (
      structuredFormula: Formulation,
      viability: ViabilityAnalysis,
      context: MultiSessionContext
    ): Promise<Formulation> => {
      if (viability.factors.estimatedSessions <= 1) {
        return structuredFormula;
      }

      const {
        selectedBrand,
        selectedLine,
        zoneColorAnalysis,
        currentLevel,
        targetLevel,
        clientContext,
      } = context;

      const extraSteps = [] as FormulationStep[];
      const rootsZone = zoneColorAnalysis[HairZone.ROOTS] || {};
      const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as HairState | undefined;
      const damage = ((rootsZone as Partial<ZoneColorAnalysis>)?.damage || 'Bajo') as
        | 'Bajo'
        | 'Medio'
        | 'Alto';
      const goingLighter = targetLevel > currentLevel;

      const waitDays = computeIntervalDays(
        targetLevel,
        currentLevel,
        goingLighter,
        damage,
        rootsState
      );
      const nextDate = new Date();
      nextDate.setDate(nextDate.getDate() + waitDays);
      const nextDateStr = nextDate.toLocaleDateString();

      // Explain reasons for the interval (transparency)
      const reasons: string[] = [];
      if (goingLighter)
        reasons.push(`aclarado de ${Math.max(0, targetLevel - currentLevel)} niveles`);
      if (rootsState && rootsState !== HairState.NATURAL) reasons.push('raíces teñidas');
      if (damage === 'Medio' || damage === 'Alto') reasons.push(`daño ${damage.toLowerCase()}`);
      const reasonText = reasons.length > 0 ? ` Motivo: ${reasons.join(', ')}.` : '';

      extraSteps.push({
        stepNumber: (structuredFormula?.steps?.length || 0) + 1,
        stepTitle: 'Sesión 2: Intervalo recomendado',
        instructions: `Esperar ${waitDays} días antes de continuar. Fecha sugerida: ${nextDateStr}. No agendar antes de ${nextDateStr}. Mantener rutina de hidratación y evitar procesos químicos.${reasonText}`,
        processingTime: 0,
      });

      // Sesión 2: preparación antes del toner
      extraSteps.push({
        stepNumber: (structuredFormula?.steps?.length || 0) + 2,
        stepTitle: 'Sesión 2: Preparación para tonalizar',
        instructions:
          'Lavar con shampoo de pH balanceado. Secar a toalla al 70–80%. Seccionar en particiones finas.',
        processingTime: 5,
      });

      // Selección de línea demi/toner más adecuada por marca
      const tonerLine = getTonerLineByBrand(selectedBrand);
      const tonerPack = getBrandPack(selectedBrand, tonerLine);
      const { neutralHint, foaText } = getNeutralizationHints(targetLevel);

      const toneGuide =
        ' \n\nCómo elegir el tono (decidir en la cita):\n' +
        '• Si fondo 7 (naranja) → familias ceniza/azul (p. ej. 7/1, 7/11).\n' +
        '• Si fondo 8 (amarillo) → familias violeta/perla (p. ej. 9/2, 9/18).\n' +
        '• Si fondo 9 (amarillo pálido) → perla/silver suaves (p. ej. 10/2, 10/8).';

      // Estimate grams based on hair length/density/thickness when available
      const { estimateColorGrams } = await import('@/services/consumption-estimator');
      const estGr = estimateColorGrams({
        hairLengthCm: clientContext?.hairLengthCm,
        density: clientContext?.hairDensity,
        thickness: clientContext?.hairThickness,
        application: 'toner_global',
      });

      extraSteps.push({
        stepNumber: (structuredFormula?.steps?.length || 0) + 3,
        stepTitle: 'Sesión 2: Depositar/tonalizar',
        instructions: goingLighter
          ? `${foaText}Neutralizar el fondo de aclaración con reflejo frío.${neutralHint} Aplicar de raíz difuminada hacia puntas para efecto natural.${toneGuide}`
          : `Depositar tono objetivo controlando porosidad.${neutralHint} Saturación uniforme. Revisar cada 5–10 min.${toneGuide}`,
        mix: (() => {
          const colorGr = estGr || 30;
          const developerMl = computeDeveloperAmount(colorGr, tonerPack.defaultMixRatio || '1:2');
          const toneName = `${selectedBrand} ${tonerLine} [Seleccionar tono] (mezcla ${tonerPack.defaultMixRatio || '1:2'})`;
          return [
            {
              productId: `toner-${Date.now()}`,
              productName: updateProductNameRatio(
                toneName,
                `${Math.round(colorGr)}:${developerMl}`
              ),
              quantity: colorGr,
              unit: 'gr',
            },
            {
              productId: `dev10-${Date.now()}`,
              productName: updateProductNameRatio(
                `${selectedBrand} Oxidante 10 vol`,
                `${Math.round(colorGr)}:${developerMl}`
              ),
              quantity: developerMl,
              unit: 'ml',
            },
          ];
        })(),
        processingTime: tonerPack.techniqueTimes?.toner || 20,
      });

      // Post tratamiento de sesión 2
      extraSteps.push({
        stepNumber: (structuredFormula?.steps?.length || 0) + 4,
        stepTitle: 'Sesión 2: Post‑tratamiento',
        instructions:
          'Enjuagar delicadamente. Aplicar tratamiento reconstructor y sellador ácido. Secar y evaluar el resultado.',
        processingTime: 10,
      });

      // Sesión 3 si fuera necesaria
      if (viability.factors.estimatedSessions > 2) {
        const waitDays2 = Math.max(
          7,
          Math.min(
            28,
            computeIntervalDays(targetLevel, currentLevel, goingLighter, damage, rootsState)
          )
        );
        const nextDate2 = new Date(nextDate.getTime());
        nextDate2.setDate(nextDate2.getDate() + waitDays2);
        const nextDateStr2 = nextDate2.toLocaleDateString();
        const reasonText2 = reasons.length > 0 ? ` Motivo: ${reasons.join(', ')}.` : '';

        extraSteps.push({
          stepNumber: (structuredFormula?.steps?.length || 0) + 5,
          stepTitle: 'Sesión 3: Intervalo recomendado',
          instructions: `Esperar ${waitDays2} días antes de continuar. Fecha sugerida: ${nextDateStr2}. No agendar antes de ${nextDateStr2}. Mantener rutina de hidratación y evitar procesos químicos.${reasonText2}`,
          processingTime: 0,
        });

        const pack3 = getBrandPack(selectedBrand, selectedLine);
        extraSteps.push({
          stepNumber: (structuredFormula?.steps?.length || 0) + 6,
          stepTitle: 'Sesión 3: Ajustes finales',
          instructions:
            'Refrescar medios/puntas si hay desvanecimiento y ajustar tono según evolución. Finalizar con tratamiento protector.',
          mix: [
            {
              productId: `refresh-${Date.now()}`,
              productName: `${selectedBrand} ${selectedLine} [Seleccionar tono] (mezcla ${pack3.defaultMixRatio || '1:1'})`,
              quantity: 30,
              unit: 'gr',
            },
            {
              productId: `dev-${Date.now()}`,
              productName: `${selectedBrand} Oxidante ${pack3.defaultDeveloper || 20} vol`,
              quantity: 30,
              unit: 'ml',
            },
          ],
          processingTime: pack3.techniqueTimes?.refresh || 10,
        });
      }

      // Insert post-treatment in Session 1 if missing
      const brandHints = getBrandTreatmentHints(selectedBrand);
      const s1HasPost = (structuredFormula.steps || []).some(s =>
        `${s.stepTitle}`.toLowerCase().match(/post(\s|-)tratamiento|tratamiento final|sellado/)
      );
      const session1Post = s1HasPost
        ? []
        : [
            {
              stepNumber: 9999,
              stepTitle: 'Sesión 1: Post‑tratamiento',
              instructions: `Enjuagar delicadamente. Aplicar tratamiento reconstructor y sellador ácido. ${brandHints}`,
              processingTime: 10,
            } as FormulationStep,
          ];

      const session1Steps = structuredFormula.steps.map((s, idx) => ({
        ...s,
        stepTitle:
          s.stepTitle && s.stepTitle.toLowerCase().includes('sesión')
            ? s.stepTitle
            : `Sesión 1: ${s.stepTitle || `Paso ${idx + 1}`}`,
      }));

      const merged: Formulation = {
        ...structuredFormula,
        summary:
          structuredFormula.summary ||
          'Según las reglas de colorimetría, este objetivo puede requerir varias sesiones para proteger la fibra capilar.',
        steps: [...session1Steps, ...session1Post, ...extraSteps].map((st, i) => ({
          ...st,
          stepNumber: i + 1,
        })),
        totalTime:
          (structuredFormula.totalTime || 0) +
          extraSteps.reduce((a, b) => a + (b.processingTime || 0), 0),
        warnings: Array.from(
          new Set([...(structuredFormula.warnings || []), ...viability.warnings])
        ),
      };

      return merged;
    },
    []
  );

  const createSafePlan = useCallback(
    (viability: ViabilityAnalysis, context: MultiSessionContext): Formulation => {
      const extraSteps = [] as FormulationStep[];
      const { selectedBrand, selectedLine } = context;
      // Use selectedBrand and selectedLine for pack configuration
      const _unusedBrand = selectedBrand;
      const _unusedLine = selectedLine;

      // Create a basic safe plan when no detailed steps are available
      const safePlan: Formulation = {
        formulaTitle: 'Plan en múltiples sesiones (seguro y profesional)',
        summary:
          'Según las reglas de colorimetría, este objetivo requiere varias sesiones para proteger la fibra capilar y respetar límites técnicos.',
        steps: extraSteps,
        totalTime: extraSteps.reduce((a, b) => a + (b.processingTime || 0), 0),
        warnings: viability.warnings,
      };

      return safePlan;
    },
    []
  );

  return {
    createMultiSessionPlan,
    createSafePlan,
  };
};
