/**
 * Centralized mapping between Spanish UI categories and English database types
 * This ensures consistency across all product creation flows
 */

export const CATEGORY_TO_TYPE_MAPPING: Record<string, string> = {
  // Spanish categories (UI) → Database constraint values
  // Database constraint expects: 'permanent', 'demi-permanent', 'semi-permanent', 'lightener', 'developer', 'toner', 'direct-dye', 'additive', 'remover'

  tinte: 'permanent', // Most hair colors are permanent
  oxidante: 'developer',
  decolorante: 'lightener',
  tratamiento: 'additive', // Treatments are typically additives
  matizador: 'toner',
  aditivo: 'additive',
  'pre-pigmentacion': 'direct-dye', // Pre-pigmentation is similar to direct dye
  otro: 'additive', // Default to additive for unknown types

  // Additional Spanish variants that might come from AI
  colorante: 'permanent',
  'tinte/color': 'permanent',
  'tinte permanente': 'permanent',
  'tinte semi-permanente': 'semi-permanent',
  'tinte demi-permanente': 'demi-permanent',
  oxigenada: 'developer',
  'agua oxigenada': 'developer',
  'oxidante/revelador': 'developer',
  revelador: 'developer',
  'decolorante/blanqueador': 'lightener',
  blanqueador: 'lightener',
  'polvo decolorante': 'lightener',
  'matizador/toner': 'toner',
  'tratamiento reconstructor': 'additive',
  'mascarilla capilar': 'additive',
  'aceite capilar': 'additive',
  'serum/ampolla': 'additive',
  ampolla: 'additive',
  removedor: 'remover',
  'removedor de color': 'remover',
  'tinte directo': 'direct-dye',
  'coloración directa': 'direct-dye',

  // English types (for backwards compatibility)
  permanent: 'permanent',
  'permanent color': 'permanent',
  'demi-permanent': 'demi-permanent',
  'semi-permanent': 'semi-permanent',
  developer: 'developer',
  lightener: 'lightener',
  bleach: 'lightener', // Map old 'bleach' to 'lightener'
  toner: 'toner',
  'direct-dye': 'direct-dye',
  'direct dye': 'direct-dye',
  additive: 'additive',
  remover: 'remover',

  // Legacy mappings for backward compatibility
  color: 'permanent',
  'hair color': 'permanent',
  treatment: 'additive',
  shampoo: 'additive',
  conditioner: 'additive',
  mask: 'additive',
  oil: 'additive',
  serum: 'additive',
};

/**
 * Convert Spanish category or any type to valid English database type
 */
export function mapCategoryToType(categoryOrType: string): string {
  const normalized = categoryOrType?.toLowerCase().trim() || '';
  return CATEGORY_TO_TYPE_MAPPING[normalized] || 'additive';
}

/**
 * Validate that a type value is valid for the database constraint
 */
export function isValidDatabaseType(type: string): boolean {
  // These are the exact values from the database constraint: products_type_check
  const validTypes = [
    'permanent',
    'demi-permanent',
    'semi-permanent',
    'lightener',
    'developer',
    'toner',
    'direct-dye',
    'additive',
    'remover',
  ];
  return validTypes.includes(type?.toLowerCase());
}
