import React, { useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions, TouchableOpacity } from 'react-native';
import { <PERSON>, Sparkles } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface AIResultNotificationProps {
  visible: boolean;
  onDismiss: () => void;
  onViewResults?: () => void;
  message?: string;
  fieldsCount?: number;
  duration?: number;
}

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

export default function AIResultNotification({
  visible,
  onDismiss,
  onViewResults,
  message = 'Análisis completado',
  fieldsCount,
  duration = 5000,
}: AIResultNotificationProps) {
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const dismissTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleDismiss = useCallback(() => {
    if (dismissTimeoutRef.current) {
      clearTimeout(dismissTimeoutRef.current);
    }
    onDismiss();
  }, [onDismiss]);

  useEffect(() => {
    if (visible) {
      // Reset progress animation
      progressAnim.setValue(0);

      // Show animation
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 40,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start();

      // Start progress animation if duration is set
      if (duration > 0) {
        Animated.timing(progressAnim, {
          toValue: 1,
          duration: duration,
          useNativeDriver: true,
        }).start();

        dismissTimeoutRef.current = setTimeout(() => {
          handleDismiss();
        }, duration);
      }
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -100,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(progressAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }

    return () => {
      if (dismissTimeoutRef.current) {
        clearTimeout(dismissTimeoutRef.current);
      }
    };
  }, [visible, duration, handleDismiss, slideAnim, opacityAnim, scaleAnim, progressAnim]);

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.leftContent}>
          <View style={styles.iconContainer}>
            <Sparkles size={20} color={Colors.light.primary} />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.title}>{message}</Text>
            {fieldsCount && (
              <Text style={styles.subtitle}>{fieldsCount} campos actualizados automáticamente</Text>
            )}
          </View>
        </View>

        <View style={styles.actions}>
          {onViewResults && (
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => {
                handleDismiss();
                setTimeout(() => {
                  if (onViewResults) {
                    onViewResults();
                  }
                }, 150);
              }}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={styles.viewButtonText}>Ver</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleDismiss}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <X size={20} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress bar for auto-dismiss */}
      {duration > 0 && (
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                transform: [
                  {
                    scaleX: progressAnim,
                  },
                ],
              },
            ]}
          />
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 40,
    left: 16,
    right: 16,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 10,
    zIndex: 9999,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  leftContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.light.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  viewButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    marginRight: 8,
  },
  viewButtonText: {
    color: Colors.light.textLight,
    fontSize: 14,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  progressBar: {
    height: 3,
    backgroundColor: Colors.light.border,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  progressFill: {
    height: '100%',
    width: '100%',
    backgroundColor: Colors.light.primary + '40',
    transformOrigin: 'left center',
  },
});
