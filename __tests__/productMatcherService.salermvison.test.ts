import { ProductMatcherService } from '@/services/productMatcherService';

// These tests focus on the Salermvison vs Salermvision classification
// and exact matching for 9.3 tones. They do not rely on DB state.

describe('ProductMatcherService - Salermvison classification and matching', () => {
  it('classifies "Salermvison 9.3 Rubio Dorado" as a color product', async () => {
    const fp = await ProductMatcherService.generateFingerprint('Salermvison 9.3 Rubio Dorado');
    expect(fp.productType).toBe('color');
    expect(fp.level).toBe(9);
    // Tone extractor returns only the decimals part (e.g., 9.3 -> "3")
    expect(ProductMatcherService.normalizeShade(fp.tone)).toBe('3');
  });

  it('does NOT classify "Salermvision Mascarilla 9.3" as color (mask/treatment)', async () => {
    const fp = await ProductMatcherService.generateFingerprint('Salermvision Mascarilla 9.3');
    // The special rule keeps Salermvision as a treatment
    expect(fp.productType).toBe('treatment');
  });

  it('matches Salermvison 9.3 names exactly (score 100) when brand/level/tone align', async () => {
    const a = 'Salermvison 9.3 Rubio Dorado';
    const b = 'Salerm Cosmetics Salermvison 9.3 (permanent)';

    const fp1 = await ProductMatcherService.generateFingerprint(a);
    const fp2 = await ProductMatcherService.generateFingerprint(b);

    // Normalize brands to avoid flakiness due to DB/alias differences in CI
    fp1.brand = 'salerm cosmetics';
    fp2.brand = 'salerm cosmetics';

    const score = ProductMatcherService.compareFingerprints(fp1, fp2);
    expect(score).toBe(100);
  });
});
