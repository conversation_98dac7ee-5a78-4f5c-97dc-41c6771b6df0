/**
 * Comprehensive tests for useFormulation hook
 * Testing formula generation, cost calculation, and viability analysis
 */

import { renderHook, act, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useFormulation } from '../../../src/service/hooks/useFormulation';
import { supabase } from '@/lib/supabase';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useInventoryStore } from '@/stores/inventory-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useAuthStore } from '@/stores/auth-store';
import { ColorCorrectionService } from '@/services/colorCorrectionService';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText, calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { getDefaultBrandAndLine } from '@/utils/brand-preferences';
import { HairZone } from '@/types/hair-diagnosis';

// Mock dependencies
jest.mock('@/lib/supabase');
jest.mock('@/stores/salon-config-store');
jest.mock('@/stores/inventory-store');
jest.mock('@/stores/client-history-store');
jest.mock('@/stores/auth-store');
jest.mock('@/services/colorCorrectionService');
jest.mock('@/services/inventoryConsumptionService');
jest.mock('@/utils/parseFormula');
jest.mock('@/utils/brand-preferences');

jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockUseSalonConfigStore = useSalonConfigStore as jest.MockedFunction<
  typeof useSalonConfigStore
>;
const _mockUseInventoryStore = useInventoryStore as jest.MockedFunction<typeof useInventoryStore>;
const mockUseClientHistoryStore = useClientHistoryStore as jest.MockedFunction<
  typeof useClientHistoryStore
>;
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;
const mockColorCorrectionService = ColorCorrectionService as jest.Mocked<
  typeof ColorCorrectionService
>;
const mockInventoryConsumptionService = InventoryConsumptionService as jest.Mocked<
  typeof InventoryConsumptionService
>;
const mockParseFormulaText = parseFormulaText as jest.MockedFunction<typeof parseFormulaText>;
const mockCalculateSimpleFormulaCost = calculateSimpleFormulaCost as jest.MockedFunction<
  typeof calculateSimpleFormulaCost
>;
const mockGetDefaultBrandAndLine = getDefaultBrandAndLine as jest.MockedFunction<
  typeof getDefaultBrandAndLine
>;
const mockAlert = Alert.alert as jest.MockedFunction<typeof Alert.alert>;

// Mock data
const mockAnalysisResult = {
  averageLevel: 5,
  hairThickness: 'Medio',
  hairDensity: 'Alta',
  overallTone: 'Castaño',
  overallReflect: 'Dorado',
  zoneAnalysis: {
    roots: { level: 4, tone: 'Castaño', confidence: 0.9 },
    mids: { level: 5, tone: 'Castaño', confidence: 0.85 },
    ends: { level: 6, tone: 'Castaño', confidence: 0.8 },
  },
};

const mockDesiredAnalysisResult = {
  general: {
    overallLevel: '8/1',
    technique: 'balayage',
  },
  confidence: 0.85,
};

const mockZoneColorAnalysis = {
  [HairZone.ROOTS]: {
    zone: HairZone.ROOTS,
    level: 4,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
    damage: 'Bajo',
  },
  [HairZone.MIDS]: {
    zone: HairZone.MIDS,
    level: 5,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
    damage: 'Bajo',
  },
  [HairZone.ENDS]: {
    zone: HairZone.ENDS,
    level: 6,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
    damage: 'Medio',
  },
};

const mockFormulaCost = {
  items: [
    { product: 'Color 8/1', amount: '30g', unitCost: 0.5, totalCost: 15 },
    { product: 'Oxidante 30vol', amount: '60g', unitCost: 0.2, totalCost: 12 },
  ],
  totalMaterialCost: 27,
  suggestedServicePrice: 54,
  profitMargin: 27,
  hasAllRealCosts: true,
};

const mockConsumptionAnalysis = {
  items: [
    { productName: 'Color 8/1', amount: 30, unit: 'g', unitCost: 0.5, totalCost: 15 },
    { productName: 'Oxidante 30vol', amount: 60, unit: 'g', unitCost: 0.2, totalCost: 12 },
  ],
  totalCost: 27,
  hasAllRealCosts: true,
};

const mockGeneratedFormula = `Illumina Color:
- 8/1 Rubio Claro Cenizo (30g)
- 8/69 Rubio Claro Rojo Violáceo (10g)
- Welloxon Perfect 30 vol (60g)

Aplicación:
1. Dividir el cabello en secciones
2. Aplicar técnica balayage
3. Procesar 35 minutos
4. Enjuagar y acondicionar`;

describe('useFormulation', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    mockGetDefaultBrandAndLine.mockReturnValue({
      brandName: 'Wella Professionals',
      lineName: 'Illumina Color',
    });

    mockUseAuthStore.mockReturnValue({
      preferredBrandLines: ['Wella Professionals:Illumina Color'],
    } as any);

    mockUseSalonConfigStore.mockReturnValue({
      getState: () => ({
        configuration: {
          inventoryControlLevel: 'control-total',
        },
        regionalConfig: {
          country: 'ES',
          currency: 'EUR',
        },
        applyMarkup: (cost: number) => cost * 2,
      }),
    } as any);

    mockUseClientHistoryStore.mockReturnValue({
      getCompatibleFormulas: jest.fn().mockReturnValue([]),
      getRecommendationsForClient: jest.fn().mockReturnValue([]),
    } as any);

    mockInventoryConsumptionService.calculateFormulationCostFromText.mockResolvedValue(
      mockConsumptionAnalysis
    );
    mockInventoryConsumptionService.checkStock.mockResolvedValue({
      hasStock: true,
      missingProducts: [],
    });

    mockCalculateSimpleFormulaCost.mockReturnValue(mockFormulaCost);
    mockParseFormulaText.mockReturnValue({
      colorProducts: [{ shade: '8/1', amount: 30, unit: 'g' }],
      developer: { volume: 30, amount: 60, unit: 'g' },
      additives: [],
    });

    mockColorCorrectionService.analyzeColorCorrection.mockReturnValue({
      needsCorrection: false,
      corrections: [],
    });
  });

  describe('Initialization', () => {
    it('should initialize with default brand and line', () => {
      const { result } = renderHook(() => useFormulation());

      expect(result.current.selectedBrand).toBe('Wella Professionals');
      expect(result.current.selectedLine).toBe('Illumina Color');
      expect(result.current.isFormulaFromAI).toBe(true);
      expect(result.current.formula).toBe('');
      expect(result.current.isGeneratingFormula).toBe(false);
    });

    it('should update brand preferences when auth store changes', () => {
      const { result, rerender } = renderHook(() => useFormulation());

      mockGetDefaultBrandAndLine.mockReturnValue({
        brandName: "L'Oreal Professional",
        lineName: 'Majirel',
      });

      mockUseAuthStore.mockReturnValue({
        preferredBrandLines: ["L'Oreal Professional:Majirel"],
      } as any);

      rerender();

      expect(result.current.selectedBrand).toBe("L'Oreal Professional");
      expect(result.current.selectedLine).toBe('Majirel');
    });
  });

  describe('Formula Cost Calculation', () => {
    it('should calculate cost with inventory control enabled', async () => {
      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        const cost = await result.current.calculateFormulaCost(mockGeneratedFormula);
        expect(cost).toEqual(mockFormulaCost);
      });

      expect(mockInventoryConsumptionService.calculateFormulationCostFromText).toHaveBeenCalledWith(
        mockGeneratedFormula
      );
    });

    it('should use simple calculation for formula-only mode', async () => {
      mockUseSalonConfigStore.mockReturnValue({
        getState: () => ({
          configuration: {
            inventoryControlLevel: 'solo-formulas',
          },
        }),
      } as any);

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        const cost = await result.current.calculateFormulaCost(mockGeneratedFormula);
        expect(cost).toEqual(mockFormulaCost);
      });

      expect(mockCalculateSimpleFormulaCost).toHaveBeenCalledWith(mockGeneratedFormula);
      expect(
        mockInventoryConsumptionService.calculateFormulationCostFromText
      ).not.toHaveBeenCalled();
    });

    it('should fallback to simple calculation on error', async () => {
      mockInventoryConsumptionService.calculateFormulationCostFromText.mockRejectedValue(
        new Error('Calculation failed')
      );

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        const cost = await result.current.calculateFormulaCost(mockGeneratedFormula);
        expect(cost).toEqual(mockFormulaCost);
      });

      expect(mockCalculateSimpleFormulaCost).toHaveBeenCalledWith(mockGeneratedFormula);
    });
  });

  describe('Viability Analysis', () => {
    it('should analyze service viability correctly', () => {
      const { result } = renderHook(() => useFormulation());

      const viability = result.current.analyzeViability(
        mockAnalysisResult,
        mockDesiredAnalysisResult,
        mockZoneColorAnalysis
      );

      expect(viability).toEqual(
        expect.objectContaining({
          score: expect.any(String),
          factors: expect.objectContaining({
            levelDifference: expect.any(Number),
            hairHealth: expect.any(String),
            estimatedSessions: expect.any(Number),
          }),
          recommendations: expect.any(Array),
          warnings: expect.any(Array),
        })
      );
    });

    it('should identify high risk scenarios', () => {
      const { result } = renderHook(() => useFormulation());

      const riskAnalysis = {
        ...mockAnalysisResult,
        averageLevel: 2,
      };

      const riskDesired = {
        ...mockDesiredAnalysisResult,
        general: {
          overallLevel: '10/1',
          technique: 'full_color',
        },
      };

      const riskZoneAnalysis = {
        ...mockZoneColorAnalysis,
        [HairZone.ROOTS]: {
          ...mockZoneColorAnalysis[HairZone.ROOTS],
          damage: 'Alto',
        },
      };

      const viability = result.current.analyzeViability(
        riskAnalysis,
        riskDesired,
        riskZoneAnalysis
      );

      expect(viability.score).toBe('risky');
      expect(viability.factors.estimatedSessions).toBeGreaterThan(1);
      expect(viability.warnings.length).toBeGreaterThan(0);
    });

    it('should handle missing data gracefully', () => {
      const { result } = renderHook(() => useFormulation());

      const viability = result.current.analyzeViability(null, null, {});

      expect(viability).toEqual(
        expect.objectContaining({
          score: 'caution',
          factors: expect.objectContaining({
            levelDifference: 0,
            hairHealth: 'good',
            estimatedSessions: 1,
          }),
        })
      );
    });
  });

  describe('Stock Validation', () => {
    it('should check stock availability successfully', async () => {
      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      await act(async () => {
        const stockCheck = await result.current.checkStockAvailability();
        expect(stockCheck).toEqual({
          hasStock: true,
          missingProducts: [],
        });
      });

      expect(result.current.stockValidation.hasStock).toBe(true);
      expect(result.current.stockValidation.checked).toBe(true);
    });

    it('should handle stock shortage', async () => {
      mockInventoryConsumptionService.checkStock.mockResolvedValue({
        hasStock: false,
        missingProducts: ['Color 8/1', 'Oxidante 30vol'],
      });

      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      await act(async () => {
        await result.current.checkStockAvailability();
      });

      expect(result.current.stockValidation.hasStock).toBe(false);
      expect(result.current.stockValidation.missingProducts).toEqual([
        'Color 8/1',
        'Oxidante 30vol',
      ]);
    });

    it('should handle stock check errors', async () => {
      mockInventoryConsumptionService.checkStock.mockRejectedValue(new Error('Stock check failed'));

      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      await act(async () => {
        await result.current.checkStockAvailability();
      });

      expect(result.current.stockValidation.hasStock).toBe(false);
      expect(result.current.stockValidation.missingProducts).toEqual(['Error al verificar stock']);
    });
  });

  describe('AI Formula Generation', () => {
    it('should generate formula with AI successfully', async () => {
      const mockEdgeFunctionResponse = {
        data: {
          success: true,
          data: {
            formulaText: mockGeneratedFormula,
            formulationData: {
              brand: 'Wella Professionals',
              line: 'Illumina Color',
              technique: 'balayage',
            },
          },
        },
        error: null,
      };

      mockSupabase.functions.invoke.mockResolvedValue(mockEdgeFunctionResponse);

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        const response = await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis,
          'client-123'
        );

        expect(response).toBe('✅ Fórmula generada con IA');
      });

      expect(result.current.formula).toBe(mockGeneratedFormula);
      expect(result.current.isFormulaFromAI).toBe(true);
      expect(result.current.formulationData).toBeTruthy();
    });

    it('should handle missing diagnosis data', async () => {
      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        await result.current.generateFormulaWithAI(null, null, {});
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'Error',
        'Necesitas completar el diagnóstico y el análisis del color deseado antes de generar la fórmula'
      );
    });

    it('should generate formula with client history context', async () => {
      const mockCompatibleFormulas = [{ formula: 'Previous formula', satisfaction: 5 }];
      const mockRecommendations = ['Use pre-lightening for better results'];

      const mockClientHistoryStore = {
        getCompatibleFormulas: jest.fn().mockReturnValue(mockCompatibleFormulas),
        getRecommendationsForClient: jest.fn().mockReturnValue(mockRecommendations),
      };

      mockUseClientHistoryStore.mockReturnValue(mockClientHistoryStore as any);

      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          data: {
            formulaText: mockGeneratedFormula,
          },
        },
        error: null,
      });

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis,
          'client-123'
        );
      });

      expect(mockClientHistoryStore.getCompatibleFormulas).toHaveBeenCalledWith('client-123');
      expect(mockClientHistoryStore.getRecommendationsForClient).toHaveBeenCalledWith('client-123');
    });

    it('should fallback to basic formula on AI error', async () => {
      mockSupabase.functions.invoke.mockRejectedValue(new Error('AI service unavailable'));

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        const response = await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis
        );

        expect(response).toBe('⚠️ Fórmula de ejemplo generada. Ajusta manualmente.');
      });

      expect(mockAlert).toHaveBeenCalledWith(
        expect.stringContaining('Sin conexión con IA'),
        expect.stringContaining('Generando fórmula de ejemplo'),
        expect.any(Array)
      );

      expect(result.current.formula).toContain('Fórmula Base:');
      expect(result.current.isFormulaFromAI).toBe(false);
    });

    it('should handle different Edge Function response formats', async () => {
      // Test different response structures for backward compatibility
      const responses = [
        { data: { success: true, data: { formulaText: mockGeneratedFormula } } },
        { data: { formulaText: mockGeneratedFormula } },
        { data: { result: { formulaText: mockGeneratedFormula } } },
        { data: mockGeneratedFormula },
      ];

      const { result } = renderHook(() => useFormulation());

      for (const response of responses) {
        mockSupabase.functions.invoke.mockResolvedValue({ ...response, error: null });

        // eslint-disable-next-line no-await-in-loop -- Sequential testing required to verify each response individually
        await act(async () => {
          await result.current.generateFormulaWithAI(
            mockAnalysisResult,
            mockDesiredAnalysisResult,
            mockZoneColorAnalysis
          );
        });

        expect(result.current.formula).toBe(mockGeneratedFormula);
      }
    });
  });

  describe('Brand Conversion', () => {
    it('should handle brand conversion mode', () => {
      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setConversionMode(true);
        result.current.setOriginalBrand("L'Oreal Professional");
        result.current.setOriginalLine('Majirel');
        result.current.setOriginalFormula('Original formula');
      });

      expect(result.current.conversionMode).toBe(true);
      expect(result.current.originalBrand).toBe("L'Oreal Professional");
      expect(result.current.originalLine).toBe('Majirel');
      expect(result.current.originalFormula).toBe('Original formula');
    });

    it('should include conversion context in AI generation', async () => {
      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          data: { formulaText: mockGeneratedFormula },
        },
        error: null,
      });

      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setConversionMode(true);
        result.current.setOriginalBrand("L'Oreal Professional");
        result.current.setOriginalLine('Majirel');
        result.current.setOriginalFormula('6/1 + 20vol');
      });

      await act(async () => {
        await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis
        );
      });

      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith(
        'salonier-assistant',
        expect.objectContaining({
          body: expect.objectContaining({
            payload: expect.objectContaining({
              conversionMode: {
                originalBrand: "L'Oreal Professional",
                originalLine: 'Majirel',
                originalFormula: '6/1 + 20vol',
              },
            }),
          }),
        })
      );
    });
  });

  describe('Auto Formula Cost Calculation', () => {
    it('should recalculate cost when formula changes', async () => {
      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      // Wait for debounced calculation
      await waitFor(() => {
        expect(result.current.formulaCost).toEqual(mockFormulaCost);
      });
    });

    it('should clear cost when formula is empty', () => {
      const { result } = renderHook(() => useFormulation());

      // Set a formula first
      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      // Clear the formula
      act(() => {
        result.current.setFormula('');
      });

      expect(result.current.formulaCost).toBeNull();
    });

    it('should fallback to simple calculation on error', async () => {
      mockInventoryConsumptionService.calculateFormulationCostFromText.mockRejectedValue(
        new Error('Service error')
      );

      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setFormula(mockGeneratedFormula);
      });

      await waitFor(() => {
        expect(result.current.formulaCost).toEqual(mockFormulaCost);
      });

      expect(mockCalculateSimpleFormulaCost).toHaveBeenCalledWith(mockGeneratedFormula);
    });
  });

  describe('State Management', () => {
    it('should manage brand modal states', () => {
      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setShowBrandModal(true);
        result.current.setBrandModalType('conversion');
      });

      expect(result.current.showBrandModal).toBe(true);
      expect(result.current.brandModalType).toBe('conversion');
    });

    it('should update selected brand and line', () => {
      const { result } = renderHook(() => useFormulation());

      act(() => {
        result.current.setSelectedBrand("L'Oreal Professional");
        result.current.setSelectedLine('Inoa');
      });

      expect(result.current.selectedBrand).toBe("L'Oreal Professional");
      expect(result.current.selectedLine).toBe('Inoa');
    });

    it('should update viability analysis and stock validation', () => {
      const { result } = renderHook(() => useFormulation());

      const mockViability = {
        score: 'safe' as const,
        factors: {
          levelDifference: 1,
          hairHealth: 'good' as const,
          chemicalHistory: [],
          estimatedSessions: 1,
        },
        recommendations: [],
        warnings: [],
      };

      const mockStockValidation = {
        isChecking: false,
        hasStock: false,
        missingProducts: ['Product A'],
        checked: true,
      };

      act(() => {
        result.current.setViabilityAnalysis(mockViability);
        result.current.setStockValidation(mockStockValidation);
      });

      expect(result.current.viabilityAnalysis).toEqual(mockViability);
      expect(result.current.stockValidation).toEqual(mockStockValidation);
    });
  });

  describe('Error Handling', () => {
    it('should handle Edge Function errors gracefully', async () => {
      mockSupabase.functions.invoke.mockResolvedValue({
        data: null,
        error: new Error('Edge Function error'),
      });

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis
        );
      });

      expect(result.current.isFormulaFromAI).toBe(false);
      expect(mockAlert).toHaveBeenCalled();
    });

    it('should handle invalid AI response gracefully', async () => {
      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: false, error: 'Invalid response' },
        error: null,
      });

      const { result } = renderHook(() => useFormulation());

      await act(async () => {
        await result.current.generateFormulaWithAI(
          mockAnalysisResult,
          mockDesiredAnalysisResult,
          mockZoneColorAnalysis
        );
      });

      expect(result.current.isFormulaFromAI).toBe(false);
      expect(mockAlert).toHaveBeenCalled();
    });
  });
});
