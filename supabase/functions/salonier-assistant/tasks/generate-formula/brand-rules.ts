// Brand-specific logic and rules.
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { extractHairLevel, extractDesiredLevel } from './mappers.ts';

// ------------------------------
// Tone Autoselection Helpers
// ------------------------------

export type ToneGrammar = {
  notation?: 'slash' | 'dot' | 'letter' | 'mixed';
  levels?: number[];
  families?: string[];
  family_map?: Record<string, string>; // letter -> numeric (e.g., V->2)
};

export async function fetchLineRulesWithGrammar(supabase: SupabaseClient, brand?: string, line?: string): Promise<{ grammar: ToneGrammar | null; compat: any | null } | null> {
  try {
    if (!brand || !line) return null;
    const { data: b } = await supabase.from('brands').select('id').ilike('name', brand).maybeSingle();
    if (!b?.id) return { grammar: null, compat: null };
    const { data: l } = await supabase
      .from('product_lines')
      .select('id')
      .eq('brand_id', b.id)
      .ilike('name', line)
      .maybeSingle();
    if (!l?.id) return { grammar: null, compat: null };
    const { data: r } = await supabase
      .from('brand_line_rules')
      .select('tone_grammar, compat')
      .eq('brand_id', b.id)
      .eq('line_id', l.id)
      .maybeSingle();
    if (!r) return { grammar: null, compat: null };
    let grammar = (r as any).tone_grammar || null;
    const compat = (r as any).compat || null;
    // Provide a safe default grammar if none set
    if (!grammar) {
      grammar = {
        notation: 'mixed',
        levels: [1,2,3,4,5,6,7,8,9,10],
        families: ['N','A','V','G','M','C','P','T','B','R','K','W','NA','NV','NW','0','1','2','3','6','7','8','9','11','21','23'],
        family_map: { N:'0', A:'1', V:'2', G:'3', M:'3', C:'2', P:'2', T:'1', B:'1', R:'3', K:'1', W:'0', NA:'01', NV:'02', NW:'03' }
      } as ToneGrammar;
    }
    return { grammar, compat };
  } catch (_e) {
    return { grammar: null, compat: null };
  }
}

function decideNeutralFamilyByBackground(level: number): string {
  if (level >= 9) return 'V'; // yellow-pale -> violet
  if (level === 8) return 'V'; // yellow -> violet/perl
  if (level === 7) return 'A'; // orange -> ash/blue
  return 'N';
}

function normalizeToneByGrammar(level: number, family: string, grammar: ToneGrammar | null): string {
  const notation = (grammar?.notation as any) || 'slash';
  const mapped = grammar?.family_map?.[family] || family;
  if (notation === 'letter') {
    // e.g., 9V
    return `${level}${family}`;
  }
  if (notation === 'dot') {
    // e.g., 9.2
    return `${level}.${mapped}`;
  }
  // default 'slash'
  return `${level}/${mapped}`;
}

function extractTargetFamilyFromDesired(desiredResult: any): string | null {
  const t = (desiredResult?.desiredColor?.tone || desiredResult?.desiredColor?.reflect || '').toString().toLowerCase();
  if (!t) return null;
  if (/(cool|ceniza|ash|azul|smok|frío)/i.test(t)) return 'A';
  if (/(violet|violeta|perla|pearl|v)/i.test(t)) return 'V';
  if (/(neutral|natural|n)/i.test(t)) return 'N';
  if (/(gold|dorado|warm|cálido)/i.test(t)) return 'G';
  return null;
}

function isPlaceholderTone(p: any): boolean {
  const name = String(p?.productName || '').toLowerCase();
  return name.includes('seleccionar tono') || !p?.shade;
}

export function ensureTonerFromCompat(step: any, compat: any, brand?: string) {
  if (!compat) return;
  const hasToner = (step.mix || []).some((m: any) => /toner|matizador/i.test(String(m?.type || m?.productName || '')));
  if (!hasToner) return;
  const tonerLine = compat?.toner_line as string | undefined;
  const tonerDev  = compat?.toner_developer as string | undefined;
  if (!tonerLine && !tonerDev) return;
  if (tonerLine) {
    (step.mix || []).forEach((m: any, idx: number) => {
      if (/toner|matizador/i.test(String(m?.type || m?.productName || ''))) {
        step.mix[idx] = { ...m, brand: brand || m?.brand, line: tonerLine };
      }
    });
  }
  if (tonerDev) {
    const devIdx = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador|lotion/i.test(String(m?.type || m?.productName || '')));
    const activatorName = brand ? `${brand} ${tonerDev}` : tonerDev;
    if (devIdx >= 0) {
      step.mix[devIdx] = { ...step.mix[devIdx], brand: brand || step.mix[devIdx]?.brand, line: tonerDev, type: 'developer', shade: undefined, productName: activatorName };
    } else {
      const totalToner = (step.mix || []).reduce((acc: number, m: any) => acc + (/toner|matizador/i.test(String(m?.type || m?.productName || '')) ? (Number(m?.quantity) || 0) : 0), 0);
      step.mix.push({ brand, line: tonerDev, type: 'developer', unit: 'ml', quantity: totalToner || 60, productName: activatorName });
    }
  }
}

export function applyDeveloperCompatibility(step: any, compat: any, brand?: string) {
  if (!compat) return;
  const hasBleach = (step.mix || []).some((m: any) => /bleach|decolorante/i.test(String(m?.type || m?.productName || '')));
  if (!hasBleach) return;
  const devIndex = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador/i.test(String(m?.type || m?.productName || '')));
  if (devIndex >= 0) {
    const dev = step.mix[devIndex];
    // If developer line is not the expected bleach developer, rename it preserving volume
    const volMatch = String(dev?.productName || '')?.match(/(\d+)\s*vol/i) || String(dev?.volume || '')?.match(/(\d+)/);
    const vol = volMatch ? `${parseInt(volMatch[1])} vol` : '';
    const expected = compat?.bleach_developer as string | undefined;
    if (expected && !String(dev?.productName || '').toLowerCase().includes(expected.toLowerCase())) {
      step.mix[devIndex] = {
        ...dev,
        brand: brand || dev?.brand,
        line: expected,
        type: 'developer',
        productName: brand ? `${brand} ${expected}${vol ? ' ' + vol : ''}` : `${expected}${vol ? ' ' + vol : ''}`,
      };
    }
  }
}

export function ensureTonerProcessingSolution(step: any, brand?: string) {
  // For Redken demi (Shades EQ), enforce Processing Solution instead of 10 vol
  const hasToner = (step.mix || []).some((m: any) => /toner|matizador/i.test(String(m?.type || m?.productName || '')));
  if (!hasToner) return;
  const devIdx = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador/i.test(String(m?.type || m?.productName || '')));
  const processingName = brand ? `${brand} Shades EQ Processing Solution` : `Shades EQ Processing Solution`;
  if (devIdx >= 0) {
    step.mix[devIdx] = {
      ...step.mix[devIdx],
      brand: brand || step.mix[devIdx]?.brand,
      line: 'Shades EQ Processing Solution',
      type: 'developer',
      shade: undefined,
      productName: processingName,
    };
  } else {
    // Add developer item in 1:1 with total toner amount if quantities exist
    const totalToner = (step.mix || []).reduce((acc: number, m: any) => {
      const isToner = /toner|matizador/i.test(String(m?.type || m?.productName || ''));
      return acc + (isToner ? (Number(m?.quantity) || 0) : 0);
    }, 0);
    step.mix.push({
      brand: brand,
      line: 'Shades EQ Processing Solution',
      type: 'developer',
      unit: 'ml',
      quantity: totalToner || 60,
      productName: processingName,
    });
  }
}

export function ensureRevlonTonerActivator(step: any, brand?: string) {
  const hasToner = (step.mix || []).some((m: any) => /toner|matizador/i.test(String(m?.type || m?.productName || '')));
  if (!hasToner) return;
  // Set toner line to Young Color Excel when brand is Revlon
  (step.mix || []).forEach((m: any, idx: number) => {
    if (/toner|matizador/i.test(String(m?.type || m?.productName || ''))) {
      step.mix[idx] = {
        ...m,
        brand: brand || m?.brand,
        line: 'Young Color Excel',
      };
    }
  });
  // Enforce Young Color Excel Activator 1:1
  const devIdx = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador/i.test(String(m?.type || m?.productName || '')));
  const activatorName = brand ? `${brand} Young Color Excel Activator` : `Young Color Excel Activator`;
  if (devIdx >= 0) {
    step.mix[devIdx] = {
      ...step.mix[devIdx],
      brand: brand || step.mix[devIdx]?.brand,
      line: 'Young Color Excel Activator',
      type: 'developer',
      shade: undefined,
      productName: activatorName,
    };
  } else {
    const totalToner = (step.mix || []).reduce((acc: number, m: any) => {
      const isToner = /toner|matizador/i.test(String(m?.type || m?.productName || ''));
      return acc + (isToner ? (Number(m?.quantity) || 0) : 0);
    }, 0);
    step.mix.push({
      brand: brand,
      line: 'Young Color Excel Activator',
      type: 'developer',
      unit: 'ml',
      quantity: totalToner || 60,
      productName: activatorName,
    });
  }
}

export function ensureMatrixTonerActivator(step: any, brand?: string) {
  const hasToner = (step.mix || []).some((m: any) => /toner|matizador/i.test(String(m?.type || m?.productName || '')));
  if (!hasToner) return;
  (step.mix || []).forEach((m: any, idx: number) => {
    if (/toner|matizador/i.test(String(m?.type || m?.productName || ''))) {
      step.mix[idx] = { ...m, brand: brand || m?.brand, line: 'Color Sync' };
    }
  });
  const devIdx = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador/i.test(String(m?.type || m?.productName || '')));
  const activatorName = brand ? `${brand} Color Sync Activator` : `Color Sync Activator`;
  if (devIdx >= 0) {
    step.mix[devIdx] = { ...step.mix[devIdx], brand: brand || step.mix[devIdx]?.brand, line: 'Color Sync Activator', type: 'developer', shade: undefined, productName: activatorName };
  } else {
    const totalToner = (step.mix || []).reduce((acc: number, m: any) => acc + (/toner|matizador/i.test(String(m?.type || m?.productName || '')) ? (Number(m?.quantity) || 0) : 0), 0);
    step.mix.push({ brand, line: 'Color Sync Activator', type: 'developer', unit: 'ml', quantity: totalToner || 60, productName: activatorName });
  }
}

export function ensureGoldwellTonerActivator(step: any, brand?: string) {
  const hasToner = (step.mix || []).some((m: any) => /toner|matizador/i.test(String(m?.type || m?.productName || '')));
  if (!hasToner) return;
  (step.mix || []).forEach((m: any, idx: number) => {
    if (/toner|matizador/i.test(String(m?.type || m?.productName || ''))) {
      step.mix[idx] = { ...m, brand: brand || m?.brand, line: 'Colorance' };
    }
  });
  const devIdx = (step.mix || []).findIndex((m: any) => /developer|oxidante|activador|lotion/i.test(String(m?.type || m?.productName || '')));
  const activatorName = brand ? `${brand} Colorance Developer Lotion` : `Colorance Developer Lotion`;
  if (devIdx >= 0) {
    step.mix[devIdx] = { ...step.mix[devIdx], brand: brand || step.mix[devIdx]?.brand, line: 'Colorance Developer Lotion', type: 'developer', shade: undefined, productName: activatorName };
  } else {
    const totalToner = (step.mix || []).reduce((acc: number, m: any) => acc + (/toner|matizador/i.test(String(m?.type || m?.productName || '')) ? (Number(m?.quantity) || 0) : 0), 0);
    step.mix.push({ brand, line: 'Colorance Developer Lotion', type: 'developer', unit: 'ml', quantity: totalToner || 60, productName: activatorName });
  }
}
export function autoSelectTonesInFormula(formulationData: any, grammar: ToneGrammar | null, compat: any | null, diagnosis: any, desiredResult: any, brand?: string, line?: string): { changed: boolean; notes: string[] } {
  if (!formulationData?.steps) return { changed: false, notes: [] };
  const notes: string[] = [];
  let changed = false;

  const levelNow = diagnosis?.averageLevel || diagnosis?.zoneAnalysis?.mids?.level || extractHairLevel(diagnosis);
  const levelTarget = extractDesiredLevel(desiredResult) || levelNow;
  const familyFromDesired = extractTargetFamilyFromDesired(desiredResult);
  const family = familyFromDesired || decideNeutralFamilyByBackground(levelTarget);
  const toneCode = normalizeToneByGrammar(levelTarget, family, grammar);

  (formulationData.steps || []).forEach((step: any, idx: number) => {
    // Developer compatibility when bleaching
    if (step?.mix) applyDeveloperCompatibility(step, compat, brand);
    // Toner processing from compat (generic)
    if (step?.mix) ensureTonerFromCompat(step, compat, brand);
    // Brand-specific fallbacks
    if (brand && /redken/i.test(brand) && step?.mix) ensureTonerProcessingSolution(step, brand);
    if (brand && /revlon/i.test(brand) && step?.mix) ensureRevlonTonerActivator(step, brand);
    if (brand && /matrix/i.test(brand) && step?.mix) ensureMatrixTonerActivator(step, brand);
    if (brand && /goldwell/i.test(brand) && step?.mix) ensureGoldwellTonerActivator(step, brand);

    (step?.mix || []).forEach((p: any, j: number) => {
      const type = String(p?.type || '').toLowerCase();
      if (!['color','tinte','toner','matizador'].includes(type)) return;
      if (isPlaceholderTone(p)) {
        const newShade = toneCode;
        const productName = brand && line ? `${brand} ${line} ${newShade}` : `${line || 'Color'} ${newShade}`;
        step.mix[j] = {
          ...p,
          shade: newShade,
          productName,
          brand: brand || p?.brand,
          line: (/toner|matizador/i.test(String(p?.type || '')) && brand && /redken/i.test(brand)) ? 'Shades EQ Gloss' : (line || p?.line),
          type: type || 'color',
        };
        notes.push(`Paso ${idx + 1}: Autoselección de tono → ${newShade}`);
        changed = true;
      }
    });
  });

  return { changed, notes };
}