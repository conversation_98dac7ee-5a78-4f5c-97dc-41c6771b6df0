# Data Model: File Structure Organization

## Core Entities

### 1. FileSystemNode

**Purpose**: Represents any file or folder in the project structure
**Attributes**:

- `path`: string - Relative path from project root
- `type`: 'file' | 'directory'
- `category`: FileCategory - What type of content this represents
- `lastModified`: Date - When last changed
- `size`: number - File size in bytes (files only)

**Relationships**:

- Has parent FileSystemNode (directory)
- Has children FileSystemNodes (for directories)

### 2. FileCategory

**Purpose**: Categorizes files by their role in the project
**Values**:

- `SOURCE_CODE`: Application source code
- `COMPONENT`: React components
- `SCREEN`: Screen-level components
- `STORE`: State management files
- `SERVICE`: Business logic and API calls
- `UTILITY`: Pure utility functions
- `HOOK`: Custom React hooks
- `TYPE`: TypeScript type definitions
- `CONSTANT`: Configuration constants
- `ASSET`: Images, fonts, icons
- `CONFIGURATION`: Build and environment config
- `DOCUMENTATION`: README, guides, specs
- `TEST`: Test files
- `BUILD_ARTIFACT`: Generated/compiled files

### 3. OrganizationRule

**Purpose**: Defines rules for where files should be placed
**Attributes**:

- `pattern`: string - File name/extension pattern
- `targetDirectory`: string - Where files matching pattern should go
- `category`: FileCategory - What category this rule applies to
- `priority`: number - Rule precedence (higher = more specific)

**Validation Rules**:

- Pattern must be valid regex
- Target directory must exist or be creatable
- No circular dependencies in directory structure

### 4. MigrationPlan

**Purpose**: Represents a plan to reorganize existing files
**Attributes**:

- `currentPath`: string - Where file currently exists
- `targetPath`: string - Where file should be moved
- `action`: 'MOVE' | 'RENAME' | 'CREATE_DIRECTORY' | 'DELETE'
- `reason`: string - Why this change is needed
- `dependencies`: string[] - Other files that must be moved first
- `conflicts`: string[] - Files that would conflict with this move

**State Transitions**:

- PLANNED → VALIDATED → EXECUTED → VERIFIED
- Can rollback from EXECUTED to previous state

### 5. ProjectStructure

**Purpose**: Represents the overall organization schema
**Attributes**:

- `type`: 'MOBILE_APP' | 'WEB_APP' | 'SINGLE_PROJECT'
- `platform`: 'REACT_NATIVE' | 'NEXT_JS' | 'VANILLA'
- `features`: string[] - Enabled organizational features
- `conventions`: NamingConvention[] - Naming rules to follow

**Relationships**:

- Contains multiple OrganizationRules
- Has one root FileSystemNode

### 6. NamingConvention

**Purpose**: Defines naming standards for files and folders
**Attributes**:

- `scope`: 'FILE' | 'DIRECTORY' | 'COMPONENT'
- `pattern`: string - Regex pattern for valid names
- `case`: 'camelCase' | 'PascalCase' | 'kebab-case' | 'snake_case'
- `suffix`: string - Required suffix (e.g., '.store.ts')
- `prefix`: string - Required prefix (e.g., 'use' for hooks)

## Validation Rules

### File Organization Constraints

1. **Single Responsibility**: Each file should have one clear purpose
2. **Clear Dependencies**: Import paths should be predictable
3. **No Circular Dependencies**: Avoid circular import relationships
4. **Consistent Naming**: Follow established naming conventions
5. **Appropriate Nesting**: Limit directory depth to 4-5 levels maximum

### Platform-Specific Rules

1. **React Native Assets**: Must be in `/assets/` for proper bundling
2. **Expo Configuration**: `app.json` must be at project root
3. **TypeScript**: All source files should have proper type definitions
4. **Testing**: Test files should mirror source structure

### Multi-Platform Considerations

1. **Shared Code**: Common logic in `/src/shared/` or similar
2. **Platform Overrides**: Platform-specific files clearly marked
3. **Asset Resolution**: Follow React Native asset resolution patterns

## State Transitions

### File Migration Process

1. **ANALYSIS**: Scan current structure and identify issues
2. **PLANNING**: Generate migration plan with dependencies
3. **VALIDATION**: Check for conflicts and verify plan safety
4. **EXECUTION**: Apply changes in dependency order
5. **VERIFICATION**: Confirm all files moved correctly and app still works
6. **CLEANUP**: Remove empty directories and update references

### Structure Evolution

- **INITIAL**: Current disorganized state
- **PLANNED**: Target structure defined
- **MIGRATING**: In process of reorganization
- **ORGANIZED**: Target structure achieved
- **MAINTAINED**: Ongoing adherence to structure rules
