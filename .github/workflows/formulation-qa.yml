name: Formulation QA
on:
  push:
    branches: [ main ]
  workflow_dispatch: {}
  schedule:
    - cron: '0 4 */3 * *'
jobs:
  run-qa:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm ci || npm i
      - name: Generate QA scenarios from Supabase
        run: node scripts/generate-formulation-scenarios.mjs
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      - name: QA diagnostics (secrets sanity)
        run: |
          node -e "const u=process.env.SUPABASE_URL||''; const s=(process.env.SUPABASE_SERVICE_ROLE_KEY||'').length; const bad=/\/(auth|rest)\/v1/i.test(u); console.log('SUPABASE_URL:', u); console.log('Contains /auth|/rest ? ', bad); console.log('Service role length:', s); if(bad){ console.log('Tip: use https://<project>.supabase.co (no /auth/v1 or /rest/v1).'); }"
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      - name: Ensure test user exists
        run: node scripts/ensure-test-user.mjs
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          SUPABASE_TEST_EMAIL: ${{ secrets.SUPABASE_TEST_EMAIL }}
          SUPABASE_TEST_PASSWORD: ${{ secrets.SUPABASE_TEST_PASSWORD }}
      - name: Seed compat rules (optional)
        run: node scripts/seed-compat-rules.mjs || true
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      - run: npm run qa:formulations
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_TEST_EMAIL: ${{ secrets.SUPABASE_TEST_EMAIL }}
          SUPABASE_TEST_PASSWORD: ${{ secrets.SUPABASE_TEST_PASSWORD }}
      - name: Upload QA results artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: qa-results
          path: qa/results-latest.json
          if-no-files-found: warn
      - name: Summarize QA in job summary
        if: always()
        run: |
          node -e '
          const fs=require("fs");
          let out="";
          try{
            const r=JSON.parse(fs.readFileSync("qa/results-latest.json","utf-8"));
            const total=r.results?.length||0; const passed=(r.results||[]).filter(x=>x.pass).length; const failed=total-passed;
            out+=`# Formulation QA\n\n**${passed}/${total} OK** (Failed: ${failed})\n\n`;
            out+=`| Scenario | Result | Failing assertions |\n|---|---|---|\n`;
            for(const it of (r.results||[])){
              const fails=(it.assertions||[]).filter(a=>!a.pass).map(a=>a.name).join(", ")||(it.error||"");
              out+=`| ${it.name} | ${it.pass?"OK":"FAIL"} | ${fails||""} |\n`;
            }
          }catch(e){ out=`No QA results found or parse error: ${e.message}`; }
          require("fs").writeFileSync(process.env.GITHUB_STEP_SUMMARY, out);
          '
