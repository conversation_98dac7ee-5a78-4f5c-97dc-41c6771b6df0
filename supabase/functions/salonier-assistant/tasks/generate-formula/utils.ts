// Smaller, generic utility functions.
import { logger } from '../../src/utils/security.ts';

/**
 * Extract tone information for mixing suggestions even from partially unknown products
 */
export function extractTonesFromFormula(formulationData: any): string[] {
  const tones: string[] = [];

  if (!formulationData?.steps) {
    return tones;
  }

  formulationData.steps.forEach((step: any) => {
    if (step.mix && Array.isArray(step.mix)) {
      step.mix.forEach((product: any) => {
        // Extract tones even if product name contains "unknown"
        if (product.shade) {
          tones.push(product.shade);
        } else if (product.productName) {
          // Try to extract tone from product name
          const toneMatch = product.productName.match(/([0-9]+[\.\,][0-9]+|[0-9]+)/g);
          if (toneMatch) {
            tones.push(...toneMatch);
          }
        }
      });
    }
  });

  return [...new Set(tones)]; // Remove duplicates
}

/**
 * Generate mock inventory tones for mixing suggestions
 */
export function generateMockInventoryTones(brand?: string, line?: string): string[] {
  const commonTones = [
    '7.0', '7.1', '7.3', '7.43', '7.81',
    '8.0', '8.1', '8.3', '8.43',
    '9.0', '9.1', '9.3',
    '6.0', '6.1', '6.3', '6.43',
    '5.0', '5.1', '5.3'
  ];

  // Add brand-specific tones if available
  if (brand?.toLowerCase().includes('wella')) {
    return [...commonTones, '10.16', '10.38', '12.11', '12.16'];
  }

  if (brand?.toLowerCase().includes('loreal')) {
    return [...commonTones, '9.31', '9.13', '8.31', '10.13'];
  }

  return commonTones;
}

/**
 * Check for mixing opportunities with timeout
 */
export async function checkForMixingOpportunities(
  extractedTones: string[],
  availableTones: string[],
  timeoutMs: number = 300
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Mixing suggestions timeout'));
    }, timeoutMs);

    try {
      const suggestions = [];

      // Quick algorithm for mixing suggestions
      for (const tone of extractedTones) {
        const baseLevel = parseInt(tone.split('.')[0]);

        // Look for complementary tones
        const complementaryTones = availableTones.filter(availTone => {
          const availLevel = parseInt(availTone.split('.')[0]);
          return Math.abs(availLevel - baseLevel) <= 1 && availTone !== tone;
        });

        if (complementaryTones.length > 0) {
          suggestions.push(
            `¡Mejora el tono ${tone}! Puedes mezclarlo con ${complementaryTones[0]} para un resultado más suave`
          );
        }
      }

      clearTimeout(timeout);
      resolve(suggestions.slice(0, 3)); // Max 3 suggestions
    } catch (error) {
      clearTimeout(timeout);
      reject(error);
    }
  });
}

/**
 * Attempt to recover partial content from a failed AI response
 */
export async function attemptPartialRecovery(
  aiResponse: string,
  brand?: string,
  line?: string,
  issues?: string[]
): Promise<{
  recoverable: boolean;
  fallbackResponse?: string;
  partialFormula?: any;
  warnings: string[];
}> {
  const warnings: string[] = [];
  warnings.push('⚠️ Esta fórmula fue parcialmente recuperada de una respuesta AI problemática');
  warnings.push(`Problemas detectados: ${issues?.join(', ') || 'Validación fallida'}`);
  warnings.push('🔍 REVISAR CUIDADOSAMENTE antes de aplicar');

  try {
    // Try to extract any JSON-like structure
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);

    if (!jsonMatch) {
      // No JSON structure found - create minimal fallback
      const fallbackFormula = createMinimalFallbackFormula(brand, line);
      warnings.push('🚑 No se pudo extraer fórmula original - usando fallback mínimo');

      return {
        recoverable: true,
        fallbackResponse: JSON.stringify(fallbackFormula),
        partialFormula: fallbackFormula,
        warnings
      };
    }

    // Try to parse the extracted JSON
    let partialData: any;
    try {
      partialData = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      // JSON is malformed - try to fix common issues
      let fixedJson = jsonMatch[0]
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/unknown\s+unknown/gi, 'Producto Profesional') // Replace unknown unknowns
        .replace(/"unknown"/gi, '"Producto Profesional"'); // Replace quoted unknowns

      try {
        partialData = JSON.parse(fixedJson);
        warnings.push('🔧 JSON parcialmente reparado');
      } catch (secondError) {
        // Still can't parse - use fallback
        const fallbackFormula = createMinimalFallbackFormula(brand, line);
        warnings.push('🚑 JSON irreparable - usando fallback completo');

        return {
          recoverable: true,
          fallbackResponse: JSON.stringify(fallbackFormula),
          partialFormula: fallbackFormula,
          warnings
        };
      }
    }

    // Clean up the partially recovered data
    if (partialData) {
      // Ensure basic structure exists
      if (!partialData.formulaTitle) {
        partialData.formulaTitle = brand && line ?
          `${brand} ${line} - Fórmula Recuperada` :
          'Fórmula Recuperada';
      }

      if (!partialData.summary) {
        partialData.summary = 'Fórmula parcialmente recuperada de respuesta AI problemática';
      }

      if (!partialData.steps || !Array.isArray(partialData.steps)) {
        // Create minimal steps if none exist
        partialData.steps = [{
          stepNumber: 1,
          stepTitle: 'Aplicación de Color',
          instructions: 'Revisar fórmula cuidadosamente antes de proceder',
          processingTime: 30,
          mix: [{
            productName: brand && line ? `${brand} ${line} Color` : 'Tinte Profesional',
            brand: brand || 'Profesional',
            line: line || '',
            type: 'tinte',
            quantity: '50',
            unit: 'ml'
          }]
        }];
        warnings.push('🛠️ Pasos de aplicación generados automáticamente');
      }

      if (!partialData.totalTime) {
        partialData.totalTime = 45;
      }

      // Ensure warnings array exists and add our warnings
      if (!partialData.warnings) {
        partialData.warnings = [];
      }
      partialData.warnings.unshift(...warnings);

      return {
        recoverable: true,
        fallbackResponse: JSON.stringify(partialData),
        partialFormula: partialData,
        warnings
      };
    }

    return { recoverable: false, warnings };
  } catch (error: any) {
    logger.error('Partial recovery failed:', error.message);

    // Last resort - create minimal fallback
    const fallbackFormula = createMinimalFallbackFormula(brand, line);
    warnings.push('🆘 Sistema de emergencia activado - fórmula mínima generada');

    return {
      recoverable: true,
      fallbackResponse: JSON.stringify(fallbackFormula),
      partialFormula: fallbackFormula,
      warnings
    };
  }
}

/**
 * Create a minimal safe fallback formula when everything else fails
 */
export function createMinimalFallbackFormula(brand?: string, line?: string): any {
  return {
    formulaTitle: brand && line ?
      `${brand} ${line} - Fórmula de Emergencia` :
      'Fórmula de Emergencia',
    summary: 'Fórmula básica generada por sistema de emergencia. REQUIERE revisión profesional antes de aplicar.',
    steps: [
      {
        stepNumber: 1,
        stepTitle: 'Consulta Profesional Requerida',
        instructions: '🚨 Esta fórmula fue generada por el sistema de emergencia debido a problemas con la IA. Por favor, consulta con un colorista profesional antes de proceder.',
        processingTime: 0,
        mix: [
          {
            productName: 'CONSULTAR PROFESIONAL',
            brand: brand || 'N/A',
            line: line || 'N/A',
            type: 'consultation',
            quantity: '0',
            unit: 'ml',
            shade: 'N/A'
          }
        ]
      }
    ],
    totalTime: 0,
    warnings: [
      '🚨 FÓRMULA DE EMERGENCIA: Esta fórmula requiere revisión profesional',
      '⚠️ NO APLICAR sin consultar con un colorista experimentado',
      '🔍 El sistema AI no pudo generar una fórmula confiable para este caso',
      '📞 Contacta al soporte técnico si este problema persiste'
    ]
  };
}