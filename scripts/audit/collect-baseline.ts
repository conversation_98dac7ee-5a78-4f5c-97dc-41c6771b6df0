#!/usr/bin/env npx ts-node

import { execSync } from 'child_process';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface BaselineMetrics {
  timestamp: string;
  eslintWarnings: number;
  eslintErrors: number;
  prettierViolations: number;
  typescriptErrors: number;
  testCoverage?: number;
  bundleSize?: number;
  auditSummary: {
    totalFiles: number;
    lintedFiles: number;
    errorFiles: number;
  };
}

async function collectBaselineMetrics(): Promise<BaselineMetrics> {
  console.log('🔍 Collecting baseline code quality metrics...');

  const metrics: BaselineMetrics = {
    timestamp: new Date().toISOString(),
    eslintWarnings: 0,
    eslintErrors: 0,
    prettierViolations: 0,
    typescriptErrors: 0,
    auditSummary: {
      totalFiles: 0,
      lintedFiles: 0,
      errorFiles: 0
    }
  };

  // Collect ESLint metrics
  try {
    console.log('📊 Running ESLint analysis...');
    const eslintOutput = execSync('npm run lint 2>&1', { encoding: 'utf-8' });

    // Parse ESLint output for warnings and errors
    const warningMatches = eslintOutput.match(/(\d+) warning/g);
    const errorMatches = eslintOutput.match(/(\d+) error/g);

    if (warningMatches) {
      metrics.eslintWarnings = warningMatches.reduce((sum, match) => {
        const num = parseInt(match.match(/\d+/)?.[0] || '0');
        return sum + num;
      }, 0);
    }

    if (errorMatches) {
      metrics.eslintErrors = errorMatches.reduce((sum, match) => {
        const num = parseInt(match.match(/\d+/)?.[0] || '0');
        return sum + num;
      }, 0);
    }

    // Count total problems from summary line
    const problemsMatch = eslintOutput.match(/✖ (\d+) problem/);
    if (problemsMatch) {
      const totalProblems = parseInt(problemsMatch[1]);
      console.log(`📋 Found ${totalProblems} total ESLint issues`);
    }

  } catch (error) {
    console.log('⚠️  ESLint analysis completed with issues (expected)');
    // ESLint exits with non-zero when there are issues, which is expected
    const errorOutput = (error as any).stdout || (error as any).message;
    if (errorOutput && typeof errorOutput === 'string') {
      const warningMatches = errorOutput.match(/(\d+) warning/g);
      if (warningMatches) {
        metrics.eslintWarnings = warningMatches.reduce((sum, match) => {
          const num = parseInt(match.match(/\d+/)?.[0] || '0');
          return sum + num;
        }, 0);
      }
    }
  }

  // Collect Prettier metrics
  try {
    console.log('🎨 Checking Prettier formatting...');
    execSync('npm run format:check 2>&1', { encoding: 'utf-8' });
    metrics.prettierViolations = 0;
  } catch (error) {
    console.log('⚠️  Found Prettier formatting violations');
    const errorOutput = (error as any).stdout || (error as any).message;
    // Count files that would be reformatted
    if (errorOutput && typeof errorOutput === 'string') {
      const lines = errorOutput.split('\n');
      metrics.prettierViolations = lines.filter(line =>
        line.trim() && !line.includes('npm') && !line.includes('prettier')
      ).length;
    }
  }

  // Collect TypeScript compilation metrics
  try {
    console.log('🔧 Checking TypeScript compilation...');
    execSync('npx tsc --noEmit 2>&1', { encoding: 'utf-8' });
    metrics.typescriptErrors = 0;
  } catch (error) {
    console.log('⚠️  Found TypeScript compilation issues');
    const errorOutput = (error as any).stdout || (error as any).message;
    if (errorOutput && typeof errorOutput === 'string') {
      // Count TypeScript errors
      const errorLines = errorOutput.split('\n').filter(line =>
        line.includes('error TS') || line.includes('): error')
      );
      metrics.typescriptErrors = errorLines.length;
    }
  }

  // Collect test coverage if available
  try {
    console.log('🧪 Checking test coverage...');
    const coverageOutput = execSync('npm run test:coverage 2>&1', { encoding: 'utf-8' });
    const coverageMatch = coverageOutput.match(/All files.*?(\d+\.?\d*)/);
    if (coverageMatch) {
      metrics.testCoverage = parseFloat(coverageMatch[1]);
    }
  } catch (error) {
    console.log('ℹ️  Test coverage collection skipped (not critical)');
  }

  // File counting
  try {
    const fileCount = execSync('find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v .expo | wc -l', { encoding: 'utf-8' });
    metrics.auditSummary.totalFiles = parseInt(fileCount.trim());
    metrics.auditSummary.lintedFiles = metrics.auditSummary.totalFiles;
    metrics.auditSummary.errorFiles = metrics.eslintWarnings > 0 || metrics.eslintErrors > 0 ? 1 : 0;
  } catch (error) {
    console.log('ℹ️  File counting failed, using defaults');
  }

  return metrics;
}

async function main() {
  try {
    const metrics = await collectBaselineMetrics();

    // Generate report filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const reportPath = join(process.cwd(), 'audit-reports', `baseline-${timestamp}.json`);

    // Save metrics to file
    writeFileSync(reportPath, JSON.stringify(metrics, null, 2));

    // Display summary
    console.log('\n📊 BASELINE METRICS SUMMARY');
    console.log('═══════════════════════════');
    console.log(`📅 Timestamp: ${metrics.timestamp}`);
    console.log(`⚠️  ESLint Warnings: ${metrics.eslintWarnings}`);
    console.log(`❌ ESLint Errors: ${metrics.eslintErrors}`);
    console.log(`🎨 Prettier Violations: ${metrics.prettierViolations}`);
    console.log(`🔧 TypeScript Errors: ${metrics.typescriptErrors}`);
    console.log(`📁 Total Files Analyzed: ${metrics.auditSummary.totalFiles}`);
    if (metrics.testCoverage) {
      console.log(`🧪 Test Coverage: ${metrics.testCoverage}%`);
    }
    console.log(`\n💾 Report saved to: ${reportPath}`);

    // Set exit code based on critical issues
    const criticalIssues = metrics.eslintErrors + metrics.typescriptErrors;
    if (criticalIssues > 0) {
      console.log(`\n🚨 Found ${criticalIssues} critical issues that must be addressed`);
      process.exit(1);
    } else {
      console.log('\n✅ Baseline collection completed successfully');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Failed to collect baseline metrics:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}