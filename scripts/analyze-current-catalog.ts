#!/usr/bin/env npx tsx

import {
  professionalHairColorBrands,
  getBrandLinesStats,
  validateBrandLines,
  getBrandsWithFormulableLines,
  getAllCountries
} from '../src/constants/brands-data';

console.log('=== ANÁLISIS DEL CATÁLOGO ACTUAL ===\n');

// Estadísticas generales
const stats = getBrandLinesStats();
console.log('📊 ESTADÍSTICAS GENERALES:');
console.log(`- Total de marcas: ${professionalHairColorBrands.length}`);
console.log(`- Total de líneas: ${stats.total}`);
console.log(`- Líneas de coloración (isColorLine: true): ${stats.colorLines}`);
console.log(`- Líneas de decoloración: ${stats.bleachingLines}`);
console.log(`- Líneas de tratamiento: ${stats.treatmentLines}`);
console.log(`- Líneas de peinado: ${stats.stylingLines}`);
console.log(`- Líneas de desarrollador: ${stats.developerLines}`);
console.log(`- Otras líneas: ${stats.otherLines}`);
console.log(`- Marcas CON líneas de coloración: ${stats.brandsWithColorLines}`);
console.log(`- Marcas SIN líneas de coloración: ${stats.brandsWithoutColorLines}\n`);

// Validación
const validation = validateBrandLines();
console.log('🔍 VALIDACIÓN:');
console.log(`- Estado: ${validation.valid ? '✅ VÁLIDO' : '❌ ERRORES ENCONTRADOS'}`);
console.log(`- Errores críticos: ${validation.issues.length}`);
console.log(`- Advertencias: ${validation.warnings.length}\n`);

if (validation.issues.length > 0) {
  console.log('❌ ERRORES CRÍTICOS:');
  validation.issues.forEach(issue => console.log(`  - ${issue}`));
  console.log('');
}

if (validation.warnings.length > 0) {
  console.log('⚠️ ADVERTENCIAS:');
  validation.warnings.slice(0, 10).forEach(warning => console.log(`  - ${warning}`));
  if (validation.warnings.length > 10) {
    console.log(`  ... y ${validation.warnings.length - 10} advertencias más`);
  }
  console.log('');
}

// Marcas por país
const countries = getAllCountries();
console.log('🌍 MARCAS POR PAÍS:');
countries.forEach(country => {
  const brandsInCountry = professionalHairColorBrands.filter(b => b.country === country);
  const brandsWithColorLines = brandsInCountry.filter(b => 
    b.lines.some(line => line.isColorLine === true)
  );
  console.log(`- ${country}: ${brandsInCountry.length} marcas (${brandsWithColorLines.length} con líneas de coloración)`);
});
console.log('');

// Marcas formulables
const formulableBrands = getBrandsWithFormulableLines();
console.log('🎨 MARCAS FORMULABLES (aparecen en la app):');
formulableBrands.slice(0, 15).forEach(brand => {
  const colorLines = brand.lines.filter(line => line.isColorLine === true);
  console.log(`- ${brand.name} (${brand.country}): ${colorLines.length} líneas`);
});
if (formulableBrands.length > 15) {
  console.log(`... y ${formulableBrands.length - 15} marcas más\n`);
}

console.log('=== FIN DEL ANÁLISIS ===');
