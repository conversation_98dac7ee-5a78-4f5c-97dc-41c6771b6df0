#!/usr/bin/env ts-node
/**
 * Database Migration Verification Script
 * 
 * This script automates the verification of the brands/product_lines table migration
 * to ensure the live application is working correctly with the new database structure.
 * 
 * Usage:
 *   npm run verify-migration
 *   or
 *   npx ts-node scripts/verify-database-migration.ts
 */

import { createClient } from '@supabase/supabase-js';
import { brandService, getBrandsAsync, searchBrandsAsync } from '../src/services/brandService';
import { logger } from '../src/utils/logger';

// Test configuration
const TEST_CONFIG = {
  DATABASE_TIMEOUT: 5000, // 5 seconds
  CACHE_TEST_ITERATIONS: 5,
  PERFORMANCE_TARGETS: {
    FIRST_LOAD_MS: 500,
    CACHED_LOAD_MS: 5,
    CACHE_HIT_RATE: 0.4, // 40%
  },
  LEGACY_BRAND_MAPPINGS: [
    { search: 'wella', expected: 'wella professionals' },
    { search: 'loreal', expected: 'l\'oréal professionnel' },
    { search: 'schwarzkopf', expected: 'schwarzkopf professional' },
  ],
};

// Test results tracking
interface TestResult {
  name: string;
  success: boolean;
  duration?: number;
  error?: string;
  details?: any;
}

class MigrationVerifier {
  private results: TestResult[] = [];
  private supabase: any;

  constructor() {
    // Initialize Supabase client for direct database testing
    const supabaseUrl = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
    }
  }

  private addResult(result: TestResult) {
    this.results.push(result);
    const status = result.success ? '✅' : '❌';
    const duration = result.duration ? ` (${result.duration}ms)` : '';
    console.log(`${status} ${result.name}${duration}`);
    if (result.error) {
      console.error(`   Error: ${result.error}`);
    }
  }

  private async timeOperation<T>(operation: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    try {
      const result = await operation();
      const duration = Date.now() - startTime;
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      throw { error, duration };
    }
  }

  // Phase 1: Database Infrastructure Tests
  async verifyDatabaseInfrastructure(): Promise<void> {
    console.log('\n🔍 Phase 1: Database Infrastructure Verification\n');

    // Test 1.1: Direct database connection
    try {
      if (!this.supabase) {
        throw new Error('Supabase client not initialized - check environment variables');
      }

      const { result: brandsData, duration } = await this.timeOperation(async () => {
        const { data, error } = await this.supabase
          .from('brands')
          .select('id, name, is_active')
          .eq('is_active', true)
          .limit(10);
        
        if (error) throw error;
        return data;
      });

      this.addResult({
        name: 'Direct database connection to brands table',
        success: true,
        duration,
        details: { brandCount: brandsData?.length || 0 }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Direct database connection to brands table',
        success: false,
        error: error.message || String(error),
        duration: error.duration
      });
    }

    // Test 1.2: Product lines table connection
    try {
      const { result: linesData, duration } = await this.timeOperation(async () => {
        const { data, error } = await this.supabase
          .from('product_lines')
          .select('id, name, brand_id, is_active')
          .eq('is_active', true)
          .eq('discontinued', false)
          .limit(10);
        
        if (error) throw error;
        return data;
      });

      this.addResult({
        name: 'Direct database connection to product_lines table',
        success: true,
        duration,
        details: { lineCount: linesData?.length || 0 }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Direct database connection to product_lines table',
        success: false,
        error: error.message || String(error),
        duration: error.duration
      });
    }

    // Test 1.3: Brand service initialization
    try {
      const { result: brands, duration } = await this.timeOperation(async () => {
        return await getBrandsAsync();
      });

      const performanceCheck = duration < TEST_CONFIG.PERFORMANCE_TARGETS.FIRST_LOAD_MS;
      
      this.addResult({
        name: 'Brand service data loading',
        success: brands.length > 0 && performanceCheck,
        duration,
        details: { 
          brandCount: brands.length,
          performanceTarget: TEST_CONFIG.PERFORMANCE_TARGETS.FIRST_LOAD_MS,
          metPerformanceTarget: performanceCheck
        }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Brand service data loading',
        success: false,
        error: error.message || String(error)
      });
    }
  }

  // Phase 2: Cache System Tests
  async verifyCacheSystem(): Promise<void> {
    console.log('\n⚡ Phase 2: Cache System Verification\n');

    // Test 2.1: Cache invalidation and refresh
    try {
      // Invalidate cache
      await brandService.invalidateCache();
      
      // First load (should be slow - database query)
      const { result: firstLoad, duration: firstDuration } = await this.timeOperation(async () => {
        return await getBrandsAsync();
      });

      // Second load (should be fast - cached)
      const { result: secondLoad, duration: secondDuration } = await this.timeOperation(async () => {
        return await getBrandsAsync();
      });

      const cacheWorking = secondDuration < firstDuration && secondDuration < TEST_CONFIG.PERFORMANCE_TARGETS.CACHED_LOAD_MS;
      
      this.addResult({
        name: 'Cache performance (fresh vs cached)',
        success: cacheWorking && firstLoad.length === secondLoad.length,
        details: {
          firstLoadTime: firstDuration,
          secondLoadTime: secondDuration,
          cacheSpeedupFactor: Math.round(firstDuration / secondDuration * 10) / 10,
          dataConsistency: firstLoad.length === secondLoad.length
        }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Cache performance test',
        success: false,
        error: error.message || String(error)
      });
    }

    // Test 2.2: Cache status inspection
    try {
      const cacheStatus = await brandService.getCacheStatus();
      
      this.addResult({
        name: 'Cache status inspection',
        success: true,
        details: {
          hasMemoryCache: cacheStatus.hasMemoryCache,
          hasStorageCache: cacheStatus.hasStorageCache,
          cacheAge: cacheStatus.cacheAge,
          isExpired: cacheStatus.isExpired
        }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Cache status inspection',
        success: false,
        error: error.message || String(error)
      });
    }
  }

  // Phase 3: Legacy Compatibility Tests
  async verifyLegacyCompatibility(): Promise<void> {
    console.log('\n🔄 Phase 3: Legacy Compatibility Verification\n');

    for (const mapping of TEST_CONFIG.LEGACY_BRAND_MAPPINGS) {
      try {
        const { result: searchResults, duration } = await this.timeOperation(async () => {
          return await searchBrandsAsync(mapping.search);
        });

        const foundExpectedBrand = searchResults.some(brand => 
          brand.name.toLowerCase().includes(mapping.expected.toLowerCase())
        );

        this.addResult({
          name: `Legacy brand mapping: "${mapping.search}" → "${mapping.expected}"`,
          success: foundExpectedBrand,
          duration,
          details: {
            searchTerm: mapping.search,
            expectedBrand: mapping.expected,
            resultsCount: searchResults.length,
            foundBrands: searchResults.map(b => b.name).slice(0, 3)
          }
        });
      } catch (error: any) {
        this.addResult({
          name: `Legacy brand mapping: "${mapping.search}" → "${mapping.expected}"`,
          success: false,
          error: error.message || String(error)
        });
      }
    }
  }

  // Phase 4: Data Integrity Tests
  async verifyDataIntegrity(): Promise<void> {
    console.log('\n🔒 Phase 4: Data Integrity Verification\n');

    try {
      const brands = await getBrandsAsync();
      
      // Test 4.1: Brand-Line relationships
      let totalLines = 0;
      let formulableLines = 0;
      let brandsWithoutLines = 0;
      
      for (const brand of brands) {
        totalLines += brand.lines.length;
        formulableLines += brand.lines.filter(line => 
          line.category === 'hair-color' || line.category === 'bleaching'
        ).length;
        
        if (brand.lines.length === 0) {
          brandsWithoutLines++;
        }
      }

      this.addResult({
        name: 'Brand-line relationship integrity',
        success: totalLines > 0 && formulableLines > 0,
        details: {
          totalBrands: brands.length,
          totalLines,
          formulableLines,
          brandsWithoutLines,
          avgLinesPerBrand: Math.round(totalLines / brands.length * 10) / 10
        }
      });

      // Test 4.2: Category mapping validation
      const categoryMappings = new Set();
      brands.forEach(brand => {
        brand.lines.forEach(line => {
          categoryMappings.add(line.category);
        });
      });

      const expectedCategories = ['hair-color', 'developer', 'bleaching', 'treatment', 'other'];
      const hasValidCategories = Array.from(categoryMappings).every(cat => 
        expectedCategories.includes(cat as string)
      );

      this.addResult({
        name: 'Category mapping validation',
        success: hasValidCategories,
        details: {
          foundCategories: Array.from(categoryMappings),
          expectedCategories,
          allCategoriesValid: hasValidCategories
        }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Data integrity verification',
        success: false,
        error: error.message || String(error)
      });
    }
  }

  // Phase 5: Integration Readiness
  async verifyIntegrationReadiness(): Promise<void> {
    console.log('\n🔗 Phase 5: Integration Readiness Verification\n');

    try {
      const brands = await getBrandsAsync();
      
      // Test 5.1: AI-ready brand data
      const aiReadyBrands = brands.filter(brand => {
        return brand.lines.some(line => line.isColorLine === true) &&
               brand.name.length > 0 &&
               brand.country.length > 0;
      });

      this.addResult({
        name: 'AI formulation readiness',
        success: aiReadyBrands.length > 10, // Should have at least 10 AI-ready brands
        details: {
          totalBrands: brands.length,
          aiReadyBrands: aiReadyBrands.length,
          aiReadyBrandNames: aiReadyBrands.slice(0, 5).map(b => b.name)
        }
      });

      // Test 5.2: Search functionality comprehensive test
      const searchTerms = ['professional', 'color', 'blonde', 'brown', 'red'];
      let totalSearchResults = 0;
      
      for (const term of searchTerms) {
        const results = await searchBrandsAsync(term);
        totalSearchResults += results.length;
      }

      this.addResult({
        name: 'Search functionality comprehensive test',
        success: totalSearchResults > 0,
        details: {
          searchTerms,
          totalSearchResults,
          avgResultsPerTerm: Math.round(totalSearchResults / searchTerms.length * 10) / 10
        }
      });
    } catch (error: any) {
      this.addResult({
        name: 'Integration readiness verification',
        success: false,
        error: error.message || String(error)
      });
    }
  }

  // Generate comprehensive report
  generateReport(): void {
    console.log('\n📊 MIGRATION VERIFICATION REPORT\n');
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(`Results Summary:`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${successRate}%\n`);

    if (failedTests > 0) {
      console.log('❌ FAILED TESTS:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`   • ${result.name}: ${result.error}`);
      });
      console.log('');
    }

    // Performance summary
    const performanceResults = this.results.filter(r => r.duration !== undefined);
    if (performanceResults.length > 0) {
      const avgDuration = performanceResults.reduce((sum, r) => sum + (r.duration || 0), 0) / performanceResults.length;
      console.log(`⚡ Performance Summary:`);
      console.log(`   Average operation time: ${Math.round(avgDuration)}ms`);
      console.log(`   Target first load: <${TEST_CONFIG.PERFORMANCE_TARGETS.FIRST_LOAD_MS}ms`);
      console.log(`   Target cached load: <${TEST_CONFIG.PERFORMANCE_TARGETS.CACHED_LOAD_MS}ms\n`);
    }

    // Migration status
    if (successRate >= 90) {
      console.log('🎉 MIGRATION STATUS: ✅ SUCCESS');
      console.log('   All critical systems are working correctly.');
      console.log('   The database migration is ready for production use.');
    } else if (successRate >= 75) {
      console.log('⚠️ MIGRATION STATUS: 🟡 PARTIAL SUCCESS');
      console.log('   Most systems are working but some issues need attention.');
      console.log('   Review failed tests before full production deployment.');
    } else {
      console.log('🚨 MIGRATION STATUS: ❌ FAILURE');
      console.log('   Critical issues detected. DO NOT deploy to production.');
      console.log('   Investigate and fix failed tests before proceeding.');
    }

    console.log('\n---\nVerification completed at:', new Date().toISOString());
    console.log('For detailed analysis, check the database migration verification plan.');
  }

  async runFullVerification(): Promise<void> {
    console.log('🚀 Starting Database Migration Verification');
    console.log('==========================================\n');

    try {
      await this.verifyDatabaseInfrastructure();
      await this.verifyCacheSystem();
      await this.verifyLegacyCompatibility();
      await this.verifyDataIntegrity();
      await this.verifyIntegrationReadiness();
    } catch (error) {
      console.error('\n❌ Verification process failed:', error);
    } finally {
      this.generateReport();
    }
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new MigrationVerifier();
  verifier.runFullVerification().catch(console.error);
}

export { MigrationVerifier };