/**
 * Comprehensive Test Suite for Stock Store
 *
 * Tests stock movements, consumption tracking, alerts, analytics,
 * low stock detection, sync functionality, and formula consumption.
 */

import { useStockStore, StockConsumptionService } from '../../src/stores/stock-store.new';
import { useSyncQueueStore } from '../../src/stores/sync-queue-store';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import type { Product, StockMovement } from '@/types/inventory';
import type { ColorFormula } from '@/types/formulation';

// Mock dependencies
jest.mock('../../src/stores/sync-queue-store');
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');

// Mock data factories
const createMockProduct = (overrides: Partial<Product> = {}): Product => ({
  id: 'product-123',
  brand: 'Wella',
  line: 'Color Perfect',
  type: 'Tinte',
  shade: '7.1',
  displayName: 'Wella Color Perfect 7.1 Tinte',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  currentStock: 500,
  minStock: 100,
  maxStock: 1000,
  unitType: 'ml',
  unitSize: 60,
  purchasePrice: 15.5,
  costPerUnit: 0.26,
  barcode: '1234567890',
  supplier: 'Beauty Supply Co',
  notes: 'Popular shade',
  colorCode: '7.1',
  isActive: true,
  lastUpdated: '2024-01-01T00:00:00Z',
  _syncStatus: 'synced',
  ...overrides,
});

const createMockMovement = (overrides: Partial<StockMovement> = {}): StockMovement => ({
  id: 'movement-123',
  productId: 'product-123',
  quantity: -25,
  type: 'use',
  date: new Date().toISOString(),
  userId: 'user-123',
  notes: 'Service consumption',
  ...overrides,
});

const createMockSupabaseMovement = (overrides: any = {}) => ({
  id: 'supabase-movement-123',
  product_id: 'product-123',
  quantity_ml: -25,
  type: 'use',
  created_at: new Date().toISOString(),
  created_by: 'user-123',
  reference_id: 'service-123',
  notes: 'Service consumption',
  salon_id: 'salon-123',
  ...overrides,
});

const createMockLowStockProduct = (overrides: any = {}) => ({
  product_id: 'product-123',
  brand: 'Wella',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  stock_ml: 25,
  minimum_stock_ml: 100,
  percentage_remaining: 25,
  color_code: '7.1',
  ...overrides,
});

const createMockColorFormula = (overrides: Partial<ColorFormula> = {}): ColorFormula => ({
  colors: [
    { tone: '7.1 Rubio Medio Ceniza', amount: 30 },
    { tone: '8.0 Rubio Claro Natural', amount: 15 },
  ],
  developerVolume: 20,
  developerRatio: '1:1.5',
  additives: ['Anti-Yellow Additive'],
  processingTime: 35,
  technique: 'Global',
  mixingInstructions: 'Mix thoroughly',
  applicationNotes: 'Apply from roots to ends',
  ...overrides,
});

describe('StockStore', () => {
  let mockSyncQueue: any;
  let mockSupabase: any;
  let store: any;

  beforeEach(() => {
    // Reset store state
    useStockStore.setState({
      movements: [],
      alerts: [],
      lowStockProducts: [],
      isLoadingLowStock: false,
      currentStock: {},
      lastSync: null,
    });

    // Mock sync queue
    mockSyncQueue = {
      isOnline: true,
      addToQueue: jest.fn(),
    };
    (useSyncQueueStore.getState as jest.Mock).mockReturnValue(mockSyncQueue);

    // Mock Supabase
    mockSupabase = {
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        single: jest.fn(),
      }),
      rpc: jest.fn(),
    };
    (supabase as any).from = mockSupabase.from;
    (supabase as any).rpc = mockSupabase.rpc;

    // Mock getCurrentSalonId
    (getCurrentSalonId as jest.Mock).mockResolvedValue('salon-123');

    store = useStockStore.getState();

    jest.clearAllMocks();
  });

  describe('Stock Movement Operations', () => {
    describe('updateStock', () => {
      it('should update stock locally and sync to Supabase when online', async () => {
        // Set initial stock
        store.setCurrentStock('product-123', 500);

        mockSupabase.from().insert.mockResolvedValue({ error: null });

        await store.updateStock('product-123', -50, 'use', 'Client service', 'service-123');

        // Check local state update
        expect(store.getCurrentStock('product-123')).toBe(450);

        // Check movement was added
        const movements = useStockStore.getState().movements;
        expect(movements).toHaveLength(1);
        expect(movements[0].quantity).toBe(-50);
        expect(movements[0].type).toBe('use');
        expect(movements[0].notes).toBe('Client service');
        expect(movements[0].referenceId).toBe('service-123');

        // Check Supabase sync
        expect(mockSupabase.from).toHaveBeenCalledWith('stock_movements');
      });

      it('should handle positive stock adjustments', async () => {
        store.setCurrentStock('product-123', 100);
        mockSupabase.from().insert.mockResolvedValue({ error: null });

        await store.updateStock('product-123', 200, 'purchase', 'New inventory', 'purchase-456');

        expect(store.getCurrentStock('product-123')).toBe(300);

        const movements = useStockStore.getState().movements;
        expect(movements[0].type).toBe('purchase');
        expect(movements[0].quantity).toBe(200);
      });

      it('should queue for sync when offline', async () => {
        mockSyncQueue.isOnline = false;
        store.setCurrentStock('product-123', 500);

        await store.updateStock('product-123', -25, 'use', 'Offline service');

        // Should update locally
        expect(store.getCurrentStock('product-123')).toBe(475);

        // Should queue for sync
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();

        // Should not call Supabase
        expect(mockSupabase.from).not.toHaveBeenCalled();
      });

      it('should handle sync errors gracefully', async () => {
        store.setCurrentStock('product-123', 500);
        mockSupabase.from().insert.mockResolvedValue({
          error: new Error('Database error'),
        });

        await store.updateStock('product-123', -30, 'use', 'Error test');

        // Should still update locally
        expect(store.getCurrentStock('product-123')).toBe(470);

        // Should queue for retry
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });

      it('should translate Spanish types to English for database', async () => {
        mockSupabase.from().insert.mockResolvedValue({ error: null });

        await store.updateStock('product-123', -10, 'consumo', 'Spanish type test');

        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'use', // Spanish 'consumo' should become 'use'
          })
        );
      });
    });

    describe('consumeProducts', () => {
      it('should consume multiple products in parallel', async () => {
        const consumptions = [
          { productId: 'product-1', quantity: 30 },
          { productId: 'product-2', quantity: 20 },
          { productId: 'product-3', quantity: 15 },
        ];

        // Mock updateStock to track calls
        const updateStockSpy = jest.spyOn(store, 'updateStock').mockResolvedValue(undefined);

        await store.consumeProducts(consumptions, 'service-123', 'Maria Garcia');

        // Should call updateStock for each consumption
        expect(updateStockSpy).toHaveBeenCalledTimes(3);
        expect(updateStockSpy).toHaveBeenCalledWith(
          'product-1',
          -30,
          'use',
          'Servicio para Maria Garcia',
          'service-123'
        );
        expect(updateStockSpy).toHaveBeenCalledWith(
          'product-2',
          -20,
          'use',
          'Servicio para Maria Garcia',
          'service-123'
        );
        expect(updateStockSpy).toHaveBeenCalledWith(
          'product-3',
          -15,
          'use',
          'Servicio para Maria Garcia',
          'service-123'
        );
      });

      it('should handle partial failures gracefully', async () => {
        const consumptions = [
          { productId: 'product-1', quantity: 30 },
          { productId: 'product-2', quantity: 20 },
        ];

        // Mock one success and one failure
        const updateStockSpy = jest
          .spyOn(store, 'updateStock')
          .mockResolvedValueOnce(undefined)
          .mockRejectedValueOnce(new Error('Product not found'));

        await store.consumeProducts(consumptions, 'service-123', 'John Doe');

        expect(updateStockSpy).toHaveBeenCalledTimes(2);
        // Should not throw error even if some products fail
      });
    });

    describe('getStockMovements', () => {
      beforeEach(() => {
        const movements = [
          createMockMovement({ id: 'movement-1', productId: 'product-1' }),
          createMockMovement({ id: 'movement-2', productId: 'product-2' }),
          createMockMovement({ id: 'movement-3', productId: 'product-1' }),
        ];
        useStockStore.setState({ movements });
      });

      it('should return all movements when no productId specified', () => {
        const movements = store.getStockMovements();
        expect(movements).toHaveLength(3);
      });

      it('should filter movements by productId', () => {
        const movements = store.getStockMovements('product-1');
        expect(movements).toHaveLength(2);
        movements.forEach(m => expect(m.productId).toBe('product-1'));
      });

      it('should respect limit parameter', () => {
        const movements = store.getStockMovements(undefined, 2);
        expect(movements).toHaveLength(2);
      });

      it('should combine productId filter and limit', () => {
        const movements = store.getStockMovements('product-1', 1);
        expect(movements).toHaveLength(1);
        expect(movements[0].productId).toBe('product-1');
      });
    });
  });

  describe('Alert Management', () => {
    describe('createAlert', () => {
      it('should create alert with correct properties', () => {
        store.createAlert('product-123', 'low_stock', 'Product running low', 'warning');

        const alerts = useStockStore.getState().alerts;
        expect(alerts).toHaveLength(1);
        expect(alerts[0]).toMatchObject({
          productId: 'product-123',
          type: 'low_stock',
          message: 'Product running low',
          severity: 'warning',
          isActive: true,
        });
      });

      it('should use default severity when not provided', () => {
        store.createAlert('product-123', 'low_stock', 'Test message');

        const alerts = useStockStore.getState().alerts;
        expect(alerts[0].severity).toBe('warning');
      });
    });

    describe('acknowledgeAlert', () => {
      beforeEach(() => {
        store.createAlert('product-123', 'low_stock', 'Test alert', 'warning');
      });

      it('should acknowledge alert and set user info', () => {
        const alertId = useStockStore.getState().alerts[0].id;

        store.acknowledgeAlert(alertId, 'user-456');

        const alert = useStockStore.getState().alerts[0];
        expect(alert.isActive).toBe(false);
        expect(alert.acknowledgedBy).toBe('user-456');
        expect(alert.acknowledgedAt).toBeTruthy();
      });
    });

    describe('getActiveAlerts', () => {
      beforeEach(() => {
        store.createAlert('product-1', 'low_stock', 'Alert 1', 'warning');
        store.createAlert('product-2', 'out_of_stock', 'Alert 2', 'error');

        // Acknowledge one alert
        const alerts = useStockStore.getState().alerts;
        store.acknowledgeAlert(alerts[0].id, 'user-123');
      });

      it('should return only active alerts', () => {
        const activeAlerts = store.getActiveAlerts();
        expect(activeAlerts).toHaveLength(1);
        expect(activeAlerts[0].type).toBe('out_of_stock');
      });
    });
  });

  describe('Low Stock Management', () => {
    describe('loadLowStockProducts', () => {
      it('should load low stock products from RPC', async () => {
        const mockLowStockData = [
          createMockLowStockProduct(),
          createMockLowStockProduct({
            product_id: 'product-456',
            stock_ml: 5,
            percentage_remaining: 10,
          }),
        ];

        mockSupabase.rpc.mockResolvedValue({
          data: mockLowStockData,
          error: null,
        });

        await store.loadLowStockProducts();

        expect(mockSupabase.rpc).toHaveBeenCalledWith('get_low_stock_products', {
          p_salon_id: 'salon-123',
        });

        const lowStockProducts = useStockStore.getState().lowStockProducts;
        expect(lowStockProducts).toHaveLength(2);
        expect(lowStockProducts[0].product_id).toBe('product-123');
        expect(useStockStore.getState().isLoadingLowStock).toBe(false);
      });

      it('should handle RPC errors gracefully', async () => {
        mockSupabase.rpc.mockResolvedValue({
          data: null,
          error: new Error('RPC failed'),
        });

        await store.loadLowStockProducts();

        expect(useStockStore.getState().lowStockProducts).toHaveLength(0);
        expect(useStockStore.getState().isLoadingLowStock).toBe(false);
      });

      it('should skip loading when no salon ID available', async () => {
        (getCurrentSalonId as jest.Mock).mockResolvedValue(null);

        await store.loadLowStockProducts();

        expect(mockSupabase.rpc).not.toHaveBeenCalled();
        expect(useStockStore.getState().isLoadingLowStock).toBe(false);
      });
    });

    describe('getLowStockProducts', () => {
      it('should return current low stock products', () => {
        const lowStockData = [createMockLowStockProduct()];
        useStockStore.setState({ lowStockProducts: lowStockData });

        const result = store.getLowStockProducts();
        expect(result).toEqual(lowStockData);
      });
    });
  });

  describe('Analytics and Reports', () => {
    describe('getConsumptionAnalysis', () => {
      beforeEach(() => {
        const now = new Date();
        const movements = [
          createMockMovement({
            productId: 'product-123',
            type: 'use',
            quantity: -30,
            date: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          }),
          createMockMovement({
            productId: 'product-123',
            type: 'use',
            quantity: -20,
            date: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          }),
          createMockMovement({
            productId: 'product-123',
            type: 'purchase',
            quantity: 100,
            date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          }),
          // Old movement outside analysis period
          createMockMovement({
            productId: 'product-123',
            type: 'use',
            quantity: -40,
            date: new Date(now.getTime() - 35 * 24 * 60 * 60 * 1000).toISOString(),
          }),
        ];
        useStockStore.setState({ movements });
        store.setCurrentStock('product-123', 400);
      });

      it('should analyze daily consumption', () => {
        const analysis = store.getConsumptionAnalysis('product-123', 'daily');

        expect(analysis).toBeTruthy();
        expect(analysis.productId).toBe('product-123');
        expect(analysis.period).toBe('daily');
        expect(analysis.totalConsumed).toBe(50); // Only use movements: 30 + 20
        expect(analysis.avgConsumption).toBe(50); // totalConsumed / 1 day
      });

      it('should analyze weekly consumption', () => {
        const analysis = store.getConsumptionAnalysis('product-123', 'weekly');

        expect(analysis).toBeTruthy();
        expect(analysis.totalConsumed).toBe(50);
        expect(analysis.avgConsumption).toBeCloseTo(50 / 7); // totalConsumed / 7 days
        expect(analysis.daysUntilEmpty).toBeCloseTo(400 / (50 / 7)); // currentStock / avgConsumption
      });

      it('should analyze monthly consumption', () => {
        const analysis = store.getConsumptionAnalysis('product-123', 'monthly');

        expect(analysis).toBeTruthy();
        expect(analysis.totalConsumed).toBe(50);
        expect(analysis.avgConsumption).toBeCloseTo(50 / 30); // totalConsumed / 30 days
      });

      it('should return null for products with no consumption', () => {
        const analysis = store.getConsumptionAnalysis('non-existent-product', 'daily');
        expect(analysis).toBeNull();
      });

      it('should find last consumption date', () => {
        const analysis = store.getConsumptionAnalysis('product-123', 'monthly');
        expect(analysis!.lastConsumption).toBeTruthy();
        // Should be the most recent use movement date
      });
    });

    describe('calculateStockMetrics', () => {
      it('should calculate comprehensive stock metrics', () => {
        const products = [
          createMockProduct({
            id: 'product-1',
            currentStock: 50,
            minStock: 100,
            costPerUnit: 0.3,
            isActive: true,
          }),
          createMockProduct({
            id: 'product-2',
            currentStock: 0,
            minStock: 50,
            costPerUnit: 0.25,
            isActive: true,
          }),
          createMockProduct({
            id: 'product-3',
            currentStock: 600,
            minStock: 100,
            maxStock: 500,
            costPerUnit: 0.2,
            isActive: true,
          }),
          createMockProduct({
            id: 'product-4',
            currentStock: 200,
            minStock: 100,
            costPerUnit: 0.35,
            isActive: false, // Inactive
          }),
        ];

        const metrics = store.calculateStockMetrics(products);

        expect(metrics.totalValue).toBeCloseTo(50 * 0.3 + 0 * 0.25 + 600 * 0.2 + 200 * 0.35);
        expect(metrics.lowStockCount).toBe(1); // product-1 (only active products)
        expect(metrics.outOfStockCount).toBe(1); // product-2
        expect(metrics.overstockCount).toBe(1); // product-3
        expect(metrics.activeProducts).toHaveLength(3); // Excludes inactive
      });
    });

    describe('calculateMostUsedProducts', () => {
      it('should identify most used products from recent movements', () => {
        const products = [
          createMockProduct({ id: 'product-1' }),
          createMockProduct({ id: 'product-2' }),
          createMockProduct({ id: 'product-3' }),
        ];

        const movements = [
          // Product 1 - high usage
          createMockMovement({ productId: 'product-1', type: 'use', quantity: -50 }),
          createMockMovement({ productId: 'product-1', type: 'use', quantity: -30 }),
          createMockMovement({ productId: 'product-1', type: 'use', quantity: -20 }),
          // Product 2 - medium usage
          createMockMovement({ productId: 'product-2', type: 'use', quantity: -40 }),
          createMockMovement({ productId: 'product-2', type: 'use', quantity: -25 }),
          // Product 3 - low usage
          createMockMovement({ productId: 'product-3', type: 'use', quantity: -15 }),
          // Non-use movement (should be ignored)
          createMockMovement({ productId: 'product-1', type: 'purchase', quantity: 100 }),
        ];

        const _mostUsed = store.calculateMostUsedProducts(products);
        store.setState({ movements });

        const result = store.calculateMostUsedProducts(products);

        expect(result).toHaveLength(3);

        // Should be sorted by total consumption (descending)
        expect(result[0].product.id).toBe('product-1');
        expect(result[0].totalConsumed).toBe(100);
        expect(result[0].usageCount).toBe(3);

        expect(result[1].product.id).toBe('product-2');
        expect(result[1].totalConsumed).toBe(65);
        expect(result[1].usageCount).toBe(2);
      });

      it('should only consider movements from last 30 days', () => {
        const products = [createMockProduct({ id: 'product-1' })];

        const movements = [
          // Recent movement
          createMockMovement({
            productId: 'product-1',
            type: 'use',
            quantity: -30,
            date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          }),
          // Old movement (should be excluded)
          createMockMovement({
            productId: 'product-1',
            type: 'use',
            quantity: -50,
            date: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
          }),
        ];

        useStockStore.setState({ movements });
        const result = store.calculateMostUsedProducts(products);

        expect(result[0].totalConsumed).toBe(30); // Only recent movement
        expect(result[0].usageCount).toBe(1);
      });
    });
  });

  describe('Data Loading and Sync', () => {
    describe('loadMovements', () => {
      it('should load movements from Supabase', async () => {
        const mockMovements = [
          createMockSupabaseMovement(),
          createMockSupabaseMovement({ id: 'movement-2', quantity_ml: -35 }),
        ];

        mockSupabase.from().limit.mockResolvedValue({
          data: mockMovements,
          error: null,
        });

        await store.loadMovements();

        expect(mockSupabase.from).toHaveBeenCalledWith('stock_movements');

        const movements = useStockStore.getState().movements;
        expect(movements).toHaveLength(2);
        expect(movements[0].quantity).toBe(-25);
        expect(movements[0].type).toBe('use');
        expect(useStockStore.getState().lastSync).toBeTruthy();
      });

      it('should handle invalid movement types gracefully', async () => {
        const invalidMovement = createMockSupabaseMovement({
          type: 'invalid_type',
        });

        mockSupabase.from().limit.mockResolvedValue({
          data: [invalidMovement],
          error: null,
        });

        await store.loadMovements();

        const movements = useStockStore.getState().movements;
        expect(movements[0].type).toBe('adjustment'); // Default fallback
      });
    });

    describe('syncWithSupabase', () => {
      it('should sync movements and low stock products', async () => {
        const loadMovementsSpy = jest.spyOn(store, 'loadMovements').mockResolvedValue(undefined);
        const loadLowStockSpy = jest
          .spyOn(store, 'loadLowStockProducts')
          .mockResolvedValue(undefined);

        await store.syncWithSupabase();

        expect(loadMovementsSpy).toHaveBeenCalled();
        expect(loadLowStockSpy).toHaveBeenCalled();
      });
    });
  });

  describe('Mock Data Generation', () => {
    describe('generateMockMovements', () => {
      it('should generate realistic mock movements for products', () => {
        const products = [
          createMockProduct({ id: 'product-1', unitSize: 50 }),
          createMockProduct({ id: 'product-2', unitSize: 100 }),
        ];

        store.generateMockMovements(products);

        const movements = useStockStore.getState().movements;
        expect(movements.length).toBeGreaterThan(10); // Should generate 5-10 per product

        // Should have movements for both products
        const product1Movements = movements.filter(m => m.productId === 'product-1');
        const product2Movements = movements.filter(m => m.productId === 'product-2');
        expect(product1Movements.length).toBeGreaterThan(0);
        expect(product2Movements.length).toBeGreaterThan(0);

        // Should have different types of movements
        const types = [...new Set(movements.map(m => m.type))];
        expect(types.length).toBeGreaterThan(1);
        expect(types).toContain('use');
        expect(types).toContain('purchase');
      });

      it('should generate movements within the last 90 days', () => {
        const products = [createMockProduct()];

        store.generateMockMovements(products);

        const movements = useStockStore.getState().movements;
        const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

        movements.forEach(movement => {
          const movementDate = new Date(movement.date);
          expect(movementDate).toBeInstanceOf(Date);
          expect(movementDate.getTime()).toBeGreaterThanOrEqual(ninetyDaysAgo.getTime());
        });
      });
    });
  });

  describe('Stock Consumption Service', () => {
    describe('consumeFromFormula', () => {
      it('should consume products based on color formula', async () => {
        const formula = createMockColorFormula();
        const consumeProductsSpy = jest
          .spyOn(useStockStore.getState(), 'consumeProducts')
          .mockResolvedValue(undefined);

        await StockConsumptionService.consumeFromFormula(formula, 'service-123', 'Maria Garcia');

        expect(consumeProductsSpy).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              productId: expect.stringContaining('color_'),
              quantity: expect.any(Number),
            }),
            expect.objectContaining({
              productId: 'developer_20vol',
              quantity: expect.any(Number),
            }),
            expect.objectContaining({
              productId: expect.stringContaining('additive_'),
              quantity: expect.any(Number),
            }),
          ]),
          'service-123',
          'Maria Garcia'
        );
      });

      it('should calculate developer amount based on ratio', async () => {
        const formula = createMockColorFormula({
          colors: [{ tone: 'Test Color', amount: 60 }],
          developerRatio: '1:2',
          developerVolume: 30,
        });

        const consumeProductsSpy = jest
          .spyOn(useStockStore.getState(), 'consumeProducts')
          .mockResolvedValue(undefined);

        await StockConsumptionService.consumeFromFormula(formula, 'service-123', 'Test Client');

        const consumptions = consumeProductsSpy.mock.calls[0][0];
        const developerConsumption = consumptions.find(c => c.productId.includes('developer'));

        // With 60ml color and 1:2 ratio, should consume 120ml developer
        expect(developerConsumption?.quantity).toBe(120);
      });

      it('should handle formulas without developer', async () => {
        const formula = createMockColorFormula({
          developerVolume: 0,
          developerRatio: '1:0',
        });

        const consumeProductsSpy = jest
          .spyOn(useStockStore.getState(), 'consumeProducts')
          .mockResolvedValue(undefined);

        await StockConsumptionService.consumeFromFormula(formula, 'service-123', 'Test Client');

        const consumptions = consumeProductsSpy.mock.calls[0][0];
        const developerConsumption = consumptions.find(c => c.productId.includes('developer'));

        expect(developerConsumption).toBeUndefined();
      });
    });

    describe('checkFormulaStock', () => {
      beforeEach(() => {
        // Mock current stock levels
        store.setCurrentStock('color_7_1_rubio_medio_ceniza', 100);
        store.setCurrentStock('color_8_0_rubio_claro_natural', 50);
        store.setCurrentStock('developer_20vol', 200);
        store.setCurrentStock('additive_anti_yellow_additive', 30);
      });

      it('should check if sufficient stock is available', () => {
        const formula = createMockColorFormula({
          colors: [
            { tone: '7.1 Rubio Medio Ceniza', amount: 30 },
            { tone: '8.0 Rubio Claro Natural', amount: 15 },
          ],
          developerVolume: 20,
          developerRatio: '1:1.5',
        });

        const stockCheck = StockConsumptionService.checkFormulaStock(formula);

        expect(stockCheck.sufficient).toBe(true);
        expect(stockCheck.shortages).toHaveLength(0);
      });

      it('should identify stock shortages', () => {
        // Set low stock for one color
        store.setCurrentStock('color_7_1_rubio_medio_ceniza', 20);

        const formula = createMockColorFormula({
          colors: [{ tone: '7.1 Rubio Medio Ceniza', amount: 30 }],
        });

        const stockCheck = StockConsumptionService.checkFormulaStock(formula);

        expect(stockCheck.sufficient).toBe(false);
        expect(stockCheck.shortages).toHaveLength(1);
        expect(stockCheck.shortages[0]).toMatchObject({
          product: '7.1 Rubio Medio Ceniza',
          required: 30,
          available: 20,
        });
      });

      it('should check developer stock requirements', () => {
        store.setCurrentStock('developer_20vol', 30); // Not enough for 1:1.5 ratio

        const formula = createMockColorFormula({
          colors: [{ tone: 'Test Color', amount: 60 }], // 60ml color
          developerRatio: '1:1.5', // Should need 90ml developer
        });

        const stockCheck = StockConsumptionService.checkFormulaStock(formula);

        expect(stockCheck.sufficient).toBe(false);
        expect(stockCheck.shortages.find(s => s.product.includes('Developer'))).toBeTruthy();
      });
    });
  });

  describe('Utility Functions', () => {
    describe('setCurrentStock and getCurrentStock', () => {
      it('should set and retrieve current stock levels', () => {
        store.setCurrentStock('product-123', 250);
        expect(store.getCurrentStock('product-123')).toBe(250);
      });

      it('should return 0 for unknown products', () => {
        expect(store.getCurrentStock('unknown-product')).toBe(0);
      });
    });

    describe('clearMovements', () => {
      it('should clear all stock data', () => {
        // Set some data
        useStockStore.setState({
          movements: [createMockMovement()],
          alerts: [{ id: 'alert-1' } as any],
          lowStockProducts: [createMockLowStockProduct()],
          currentStock: { 'product-123': 500 },
          lastSync: '2024-01-01T00:00:00Z',
        });

        store.clearMovements();

        const state = useStockStore.getState();
        expect(state.movements).toHaveLength(0);
        expect(state.alerts).toHaveLength(0);
        expect(state.lowStockProducts).toHaveLength(0);
        expect(state.currentStock).toEqual({});
        expect(state.lastSync).toBeNull();
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle concurrent stock updates safely', async () => {
      store.setCurrentStock('product-123', 500);

      const updatePromises = [
        store.updateStock('product-123', -50, 'use', 'Service 1'),
        store.updateStock('product-123', -30, 'use', 'Service 2'),
        store.updateStock('product-123', -25, 'use', 'Service 3'),
      ];

      await Promise.allSettled(updatePromises);

      // Final stock should reflect all updates
      expect(store.getCurrentStock('product-123')).toBe(395); // 500 - 50 - 30 - 25
    });

    it('should handle network timeouts gracefully', async () => {
      mockSupabase.from().insert.mockRejectedValue(new Error('Network timeout'));

      await store.updateStock('product-123', -25, 'use', 'Timeout test');

      // Should still update locally and queue for sync
      expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
    });

    it('should handle invalid stock quantities', async () => {
      // Test negative quantities for purchase (should still work)
      await expect(
        store.updateStock('product-123', -100, 'purchase', 'Negative purchase')
      ).resolves.not.toThrow();

      // Test zero quantities
      await expect(
        store.updateStock('product-123', 0, 'adjustment', 'Zero adjustment')
      ).resolves.not.toThrow();
    });
  });

  describe('Type Safety and Validation', () => {
    it('should maintain type safety for movement types', () => {
      const validTypes: StockMovement['type'][] = [
        'purchase',
        'use',
        'adjustment',
        'return',
        'waste',
      ];

      validTypes.forEach(type => {
        expect(() => store.updateStock('product-123', 10, type, 'Test')).not.toThrow();
      });
    });

    it('should handle Spanish type translations correctly', () => {
      const spanishTypes = ['compra', 'consumo', 'ajuste', 'devolución', 'pérdida'];

      spanishTypes.forEach(type => {
        expect(() => store.updateStock('product-123', 10, type as any, 'Test')).not.toThrow();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of movements efficiently', () => {
      const movements = Array.from({ length: 1000 }, (_, i) =>
        createMockMovement({ id: `movement-${i}` })
      );

      const startTime = performance.now();
      useStockStore.setState({ movements });
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // Should be fast
      expect(useStockStore.getState().movements).toHaveLength(1000);
    });

    it('should efficiently generate analytics for large datasets', () => {
      const products = Array.from({ length: 100 }, (_, i) =>
        createMockProduct({ id: `product-${i}` })
      );

      const startTime = performance.now();
      const metrics = store.calculateStockMetrics(products);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should be fast
      expect(metrics).toBeTruthy();
    });
  });
});
