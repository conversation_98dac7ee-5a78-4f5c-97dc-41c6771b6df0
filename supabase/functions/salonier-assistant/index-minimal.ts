// Salonier Assistant Edge Function - Minimal Working Version
// For debugging boot errors

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async req => {
  console.log('Edge function invoked:', {
    method: req.method,
    url: req.url,
    hasOpenAIKey: !!openaiApiKey,
  });

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Basic functionality test
    const result = {
      success: true,
      data: {
        status: 'Function is running',
        version: '44-minimal',
        timestamp: new Date().toISOString(),
        hasOpenAIKey: !!openaiApiKey,
        environment: {
          supabaseUrl: !!supabaseUrl,
          serviceKey: !!supabaseServiceKey,
        }
      }
    };

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Edge function error occurred', error);
    const errorResponse = {
      success: false,
      error: 'Internal server error.',
      details: error.message
    };
    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});