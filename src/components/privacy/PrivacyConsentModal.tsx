/**
 * Privacy Consent Modal
 *
 * GDPR/CCPA compliant consent modal for photo processing and AI analysis.
 * Shows on first photo capture and allows users to understand and control
 * how their data is processed.
 */

import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import Colors from '@/constants/colors';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { useAuthStore } from '@/stores/auth-store';

interface PrivacyConsentModalProps {
  visible: boolean;
  onAccept: (settings: PrivacySettings) => void;
  onDecline: () => void;
  onClose: () => void;
}

interface PrivacySettings {
  photoRetentionDays: number;
  allowAiAnalysis: boolean;
  allowAnonymization: boolean;
  analyticsConsent: boolean;
  marketingConsent: boolean;
  dataProcessingConsent: boolean;
}

const DEFAULT_SETTINGS: PrivacySettings = {
  photoRetentionDays: 90,
  allowAiAnalysis: true,
  allowAnonymization: true,
  analyticsConsent: false,
  marketingConsent: false,
  dataProcessingConsent: true,
};

export function PrivacyConsentModal({
  visible,
  onAccept,
  onDecline,
  onClose,
}: PrivacyConsentModalProps) {
  const [settings, setSettings] = useState<PrivacySettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthStore();

  const handleAccept = async () => {
    if (!user?.id || !user?.salonId) {
      Alert.alert('Error', 'Usuario no autenticado correctamente');
      return;
    }

    try {
      setIsLoading(true);

      // Save privacy settings to database
      const { error } = await supabase.from('user_privacy_settings').upsert(
        {
          user_id: user.id,
          salon_id: user.salonId,
          photo_retention_days: settings.photoRetentionDays,
          allow_ai_analysis: settings.allowAiAnalysis,
          allow_anonymization: settings.allowAnonymization,
          analytics_consent: settings.analyticsConsent,
          marketing_consent: settings.marketingConsent,
          data_processing_consent: settings.dataProcessingConsent,
          consent_date: new Date().toISOString(),
          last_updated: new Date().toISOString(),
        },
        {
          onConflict: 'user_id',
        }
      );

      if (error) {
        throw error;
      }

      logger.info('Privacy settings saved', 'PrivacyConsentModal', {
        userId: user.id,
        settings,
      });
      onAccept(settings);
    } catch (error: unknown) {
      logger.error('Failed to save privacy settings', error);
      Alert.alert(
        'Error',
        'No se pudieron guardar las configuraciones de privacidad. Inténtalo de nuevo.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDecline = () => {
    Alert.alert(
      'Sin consentimiento',
      'Sin tu consentimiento, no podremos procesar fotos con IA. Podrás continuar usando otras funciones de la aplicación.',
      [
        { text: 'Revisar', style: 'cancel' },
        { text: 'Confirmar', onPress: onDecline, style: 'destructive' },
      ]
    );
  };

  const openPrivacyPolicy = () => {
    Linking.openURL('https://salonier.app/privacy-policy');
  };

  const updateSetting = <K extends keyof PrivacySettings>(key: K, value: PrivacySettings[K]) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Configuración de Privacidad</Text>
          <Text style={styles.subtitle}>
            Tu privacidad es importante. Configura cómo procesamos tus fotos.
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Data Processing Consent */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Procesamiento de Datos Requerido</Text>
            <View style={styles.consentItem}>
              <View style={styles.consentTextContainer}>
                <Text style={styles.consentText}>
                  Procesar fotos con IA para análisis capilar y generación de fórmulas
                </Text>
                <Text style={styles.consentDescription}>
                  ✅ Fotos almacenadas en buckets privados con acceso restringido{'\n'}✅ URLs con
                  firma temporal (expiran en 1 hora){'\n'}✅ Eliminación automática según
                  configuración de retención{'\n'}✅ Derecho al olvido garantizado
                </Text>
              </View>
              <View style={[styles.checkbox, styles.checkboxRequired]}>
                <Text style={styles.checkboxText}>✓</Text>
              </View>
            </View>
          </View>

          {/* Retention Period */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Retención de Fotos</Text>
            <Text style={styles.sectionDescription}>
              ¿Por cuánto tiempo podemos mantener tus fotos para mejorar el análisis?
            </Text>
            <View style={styles.retentionOptions}>
              {[30, 60, 90].map(days => (
                <TouchableOpacity
                  key={days}
                  style={[
                    styles.retentionOption,
                    settings.photoRetentionDays === days && styles.retentionOptionSelected,
                  ]}
                  onPress={() => updateSetting('photoRetentionDays', days)}
                >
                  <Text
                    style={[
                      styles.retentionOptionText,
                      settings.photoRetentionDays === days && styles.retentionOptionTextSelected,
                    ]}
                  >
                    {days} días
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Optional Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Características Opcionales</Text>

            <TouchableOpacity
              style={styles.consentItem}
              onPress={() => updateSetting('allowAnonymization', !settings.allowAnonymization)}
            >
              <View style={styles.consentTextContainer}>
                <Text style={styles.consentText}>Anonimización automática de rostros</Text>
                <Text style={styles.consentDescription}>
                  Difumina automáticamente los rostros en las fotos para mayor privacidad
                </Text>
              </View>
              <View
                style={[styles.checkbox, settings.allowAnonymization && styles.checkboxSelected]}
              >
                {settings.allowAnonymization && <Text style={styles.checkboxText}>✓</Text>}
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.consentItem}
              onPress={() => updateSetting('analyticsConsent', !settings.analyticsConsent)}
            >
              <View style={styles.consentTextContainer}>
                <Text style={styles.consentText}>Análisis de uso anónimo</Text>
                <Text style={styles.consentDescription}>
                  Ayúdanos a mejorar la aplicación compartiendo datos de uso anónimos
                </Text>
              </View>
              <View style={[styles.checkbox, settings.analyticsConsent && styles.checkboxSelected]}>
                {settings.analyticsConsent && <Text style={styles.checkboxText}>✓</Text>}
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.consentItem}
              onPress={() => updateSetting('marketingConsent', !settings.marketingConsent)}
            >
              <View style={styles.consentTextContainer}>
                <Text style={styles.consentText}>Comunicaciones de marketing</Text>
                <Text style={styles.consentDescription}>
                  Recibir noticias, actualizaciones y ofertas especiales por email
                </Text>
              </View>
              <View style={[styles.checkbox, settings.marketingConsent && styles.checkboxSelected]}>
                {settings.marketingConsent && <Text style={styles.checkboxText}>✓</Text>}
              </View>
            </TouchableOpacity>
          </View>

          {/* Legal Information */}
          <View style={styles.section}>
            <Text style={styles.legalTitle}>Tus Derechos</Text>
            <Text style={styles.legalText}>
              • Puedes cambiar estas configuraciones en cualquier momento{'\n'}• Puedes solicitar
              una copia de tus datos{'\n'}• Puedes solicitar la eliminación de tus datos{'\n'}• Tus
              fotos se eliminan automáticamente después del período seleccionado
            </Text>

            <TouchableOpacity onPress={openPrivacyPolicy} style={styles.linkButton}>
              <Text style={styles.linkText}>Leer Política de Privacidad Completa</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.declineButton}
            onPress={handleDecline}
            disabled={isLoading}
          >
            <Text style={styles.declineButtonText}>No Aceptar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.acceptButton, isLoading && styles.acceptButtonDisabled]}
            onPress={handleAccept}
            disabled={isLoading}
          >
            <Text style={styles.acceptButtonText}>
              {isLoading ? 'Guardando...' : 'Aceptar y Continuar'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    padding: 24,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    lineHeight: 22,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  consentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  consentTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  consentText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  consentDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 18,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.border.default,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  checkboxSelected: {
    backgroundColor: Colors.primary.default,
    borderColor: Colors.primary.default,
  },
  checkboxRequired: {
    backgroundColor: Colors.primary.default,
    borderColor: Colors.primary.default,
  },
  checkboxText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: 'bold',
  },
  retentionOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  retentionOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: Colors.border.default,
    alignItems: 'center',
  },
  retentionOptionSelected: {
    borderColor: Colors.primary.default,
    backgroundColor: Colors.primary.light,
  },
  retentionOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  retentionOptionTextSelected: {
    color: Colors.primary.default,
    fontWeight: '600',
  },
  legalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  legalText: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  linkButton: {
    alignSelf: 'flex-start',
  },
  linkText: {
    fontSize: 14,
    color: Colors.primary.default,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 24,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  declineButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.border.default,
    alignItems: 'center',
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.secondary,
  },
  acceptButton: {
    flex: 2,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary.default,
    alignItems: 'center',
  },
  acceptButtonDisabled: {
    backgroundColor: Colors.primary.light,
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
});

export default PrivacyConsentModal;
