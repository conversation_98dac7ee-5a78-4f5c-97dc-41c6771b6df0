import {
  computeDeveloperAmount,
  simplifyRatioFromAmounts,
  updateProductNameRatio,
} from '@/services/mixing-engine';

describe('Mixing Engine', () => {
  it('computes developer for 1:2 correctly (30g → 60ml)', () => {
    const ml = computeDeveloperAmount(30, '1:2');
    expect(ml).toBe(60);
  });

  it('computes developer for 1:1.5 correctly (40g → 60ml)', () => {
    const ml = computeDeveloperAmount(40, '1:1.5');
    expect(ml).toBe(60);
  });

  it('simplifies ratio from amounts (30g + 60ml → 1:2)', () => {
    expect(simplifyRatioFromAmounts(30, 60)).toBe('1:2');
  });

  it('updates product name ratio hint', () => {
    const name = 'Wella Color Touch 9/16 (mezcla 1:2)';
    const out = updateProductNameRatio(name, '1:1.5');
    expect(out).toContain('(mezcla 1:1.5)');
  });
});
