# 🔧 Auth Fix Guide - Infinite Recursion Issue

## Problem Identified

**Root Cause:** The AuthWeakPasswordError users are experiencing is NOT actually a password policy issue. It's a **Row Level Security (RLS) infinite recursion bug** that prevents any database operations from working.

**Error Details:**

```
infinite recursion detected in policy for relation "profiles"
```

## What's Happening

1. The `auth.salon_id()` function queries the `profiles` table
2. The `profiles` table has RLS policies that call `auth.salon_id()`
3. This creates an infinite loop that breaks all database operations
4. User registration fails because profiles can't be created
5. The frontend receives a generic auth error that gets interpreted as "weak password"

## Solution

Apply the migration `022_fix_rls_infinite_recursion.sql` which:

1. **Removes circular dependencies** by dropping problematic functions
2. **Simplifies RLS policies** to avoid recursion
3. **Fixes auth triggers** to work properly
4. **Maintains security** while eliminating infinite loops

## How to Apply the Fix

### Option 1: Via Supabase Dashboard (Recommended)

1. Go to your Supabase Dashboard
2. Navigate to SQL Editor
3. Copy and paste the entire content of `022_fix_rls_infinite_recursion.sql`
4. Run the migration
5. Verify with the test script below

### Option 2: Via CLI

```bash
# Apply the specific migration
npx supabase db push --include-all

# Or apply just this migration file
psql -h db.ajsamgugqfbttkrlgvbr.supabase.co -U postgres -d postgres -f supabase/migrations/022_fix_rls_infinite_recursion.sql
```

## Test the Fix

After applying the migration, run this test:

```bash
node -e "
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient('https://ajsamgugqfbttkrlgvbr.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY');
supabase.from('profiles').select('count').limit(1).then(({data, error}) => {
  if (error && error.message.includes('infinite recursion')) {
    console.log('❌ Migration not applied yet');
  } else {
    console.log('✅ RLS fixed! Database is working');
  }
});
"
```

## Test User Registration

After the fix, create a test user to verify everything works:

```bash
node test_auth_fixed.js
```

## Current Supabase Auth Settings

Your current auth settings are correct:

- `minimum_password_length = 6` ✅
- `password_requirements = ""` ✅ (no additional requirements)
- `enable_signup = true` ✅
- Auth triggers are properly configured ✅

The issue was never about password policies - it was about the RLS infinite recursion.

## What This Fix Does

### Before Fix:

```
User tries to register
  ↓
Supabase tries to create user
  ↓
Auth trigger tries to create profile
  ↓
RLS policy checks auth.salon_id()
  ↓
auth.salon_id() queries profiles table
  ↓
Profiles table RLS policy calls auth.salon_id()
  ↓
INFINITE RECURSION ERROR
  ↓
Registration fails with confusing error message
```

### After Fix:

```
User tries to register
  ↓
Supabase creates user successfully
  ↓
Auth trigger creates profile (no RLS recursion)
  ↓
Profile created with proper salon_id
  ↓
User is authenticated and ready to use app
```

## Security Impact

The fix maintains the same security model:

- Multi-tenant isolation still works
- Users can only see their salon's data
- Permission checking still functions
- Row-level security is still active

The only change is eliminating the circular dependency that caused infinite recursion.

## Demo User Creation

Once the fix is applied, you can create demo users with any password ≥6 characters:

```javascript
// This will work after the fix
await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'demo123', // 6+ characters is fine
  options: {
    data: {
      full_name: 'Demo User',
      salon_name: 'Demo Salon',
    },
  },
});
```

## Files Changed

- ✅ `supabase/migrations/022_fix_rls_infinite_recursion.sql` - The migration
- ✅ `AUTH_FIX_GUIDE.md` - This guide
- ✅ `test_auth_fixed.js` - Test script

## Next Steps

1. Apply the migration via Supabase Dashboard SQL Editor
2. Test with the provided scripts
3. Try user registration in the app
4. Create demo users as needed
5. Verify all auth functions work properly

The "AuthWeakPasswordError" should be completely resolved after this fix.
