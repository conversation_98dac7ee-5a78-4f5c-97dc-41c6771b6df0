openapi: 3.0.0
info:
  title: File Organization API
  description: Contracts for file structure reorganization operations
  version: 1.0.0

paths:
  /analyze-structure:
    post:
      summary: Analyze current project structure
      description: Scans the current project and identifies organizational issues
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                projectRoot:
                  type: string
                  description: Absolute path to project root
                excludePatterns:
                  type: array
                  items:
                    type: string
                  description: Patterns to exclude from analysis
              required:
                - projectRoot
      responses:
        '200':
          description: Analysis complete
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalFiles:
                    type: number
                  totalDirectories:
                    type: number
                  issues:
                    type: array
                    items:
                      $ref: '#/components/schemas/StructureIssue'
                  suggestions:
                    type: array
                    items:
                      $ref: '#/components/schemas/OrganizationSuggestion'

  /generate-migration-plan:
    post:
      summary: Generate migration plan
      description: Creates a plan to reorganize files based on target structure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                currentStructure:
                  $ref: '#/components/schemas/ProjectStructure'
                targetStructure:
                  $ref: '#/components/schemas/ProjectStructure'
                preferences:
                  $ref: '#/components/schemas/MigrationPreferences'
              required:
                - currentStructure
                - targetStructure
      responses:
        '200':
          description: Migration plan generated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrationPlan'

  /validate-migration:
    post:
      summary: Validate migration plan
      description: Checks migration plan for conflicts and safety
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrationPlan'
      responses:
        '200':
          description: Validation complete
          content:
            application/json:
              schema:
                type: object
                properties:
                  isValid:
                    type: boolean
                  conflicts:
                    type: array
                    items:
                      $ref: '#/components/schemas/MigrationConflict'
                  warnings:
                    type: array
                    items:
                      type: string

  /execute-migration:
    post:
      summary: Execute migration plan
      description: Applies the migration plan to reorganize files
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                migrationPlan:
                  $ref: '#/components/schemas/MigrationPlan'
                dryRun:
                  type: boolean
                  default: false
              required:
                - migrationPlan
      responses:
        '200':
          description: Migration executed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  executedActions:
                    type: array
                    items:
                      $ref: '#/components/schemas/MigrationAction'
                  errors:
                    type: array
                    items:
                      type: string

components:
  schemas:
    StructureIssue:
      type: object
      properties:
        type:
          type: string
          enum: [MISPLACED_FILE, NAMING_VIOLATION, CIRCULAR_DEPENDENCY, DEEP_NESTING]
        path:
          type: string
        severity:
          type: string
          enum: [LOW, MEDIUM, HIGH, CRITICAL]
        description:
          type: string
        suggestedFix:
          type: string

    OrganizationSuggestion:
      type: object
      properties:
        type:
          type: string
          enum: [MOVE_FILE, RENAME_FILE, CREATE_DIRECTORY, MERGE_DIRECTORIES]
        currentPath:
          type: string
        suggestedPath:
          type: string
        reason:
          type: string
        benefits:
          type: array
          items:
            type: string

    ProjectStructure:
      type: object
      properties:
        type:
          type: string
          enum: [MOBILE_APP, WEB_APP, SINGLE_PROJECT]
        platform:
          type: string
          enum: [REACT_NATIVE, NEXT_JS, VANILLA]
        directories:
          type: array
          items:
            $ref: '#/components/schemas/DirectorySpec'
        namingConventions:
          type: array
          items:
            $ref: '#/components/schemas/NamingConvention'

    DirectorySpec:
      type: object
      properties:
        path:
          type: string
        purpose:
          type: string
        fileTypes:
          type: array
          items:
            type: string
        namingPattern:
          type: string
        maxDepth:
          type: number

    NamingConvention:
      type: object
      properties:
        scope:
          type: string
          enum: [FILE, DIRECTORY, COMPONENT]
        pattern:
          type: string
        case:
          type: string
          enum: [camelCase, PascalCase, kebab-case, snake_case]
        suffix:
          type: string
        prefix:
          type: string

    MigrationPreferences:
      type: object
      properties:
        preserveGitHistory:
          type: boolean
          default: true
        updateImports:
          type: boolean
          default: true
        createBackup:
          type: boolean
          default: true
        validateAfterMove:
          type: boolean
          default: true

    MigrationPlan:
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: '#/components/schemas/MigrationAction'
        estimatedDuration:
          type: string
        riskLevel:
          type: string
          enum: [LOW, MEDIUM, HIGH]
        prerequisites:
          type: array
          items:
            type: string

    MigrationAction:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [MOVE, RENAME, CREATE_DIRECTORY, DELETE, UPDATE_IMPORTS]
        currentPath:
          type: string
        targetPath:
          type: string
        dependencies:
          type: array
          items:
            type: string
        estimatedTime:
          type: string

    MigrationConflict:
      type: object
      properties:
        type:
          type: string
          enum: [FILE_EXISTS, CIRCULAR_DEPENDENCY, IMPORT_BREAK, PERMISSION_DENIED]
        affectedPaths:
          type: array
          items:
            type: string
        description:
          type: string
        resolution:
          type: string
