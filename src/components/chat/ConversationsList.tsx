import React, { useState, useMemo, useCallback } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
  Modal,
  Pressable,
  KeyboardAvoidingView,
  Platform,
  StyleProp,
  ViewStyle,
} from 'react-native';
import Animated, {
  FadeIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  withSequence,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Search, X, Edit3, Trash2, Pin, MessageSquare } from 'lucide-react-native';
import { SwipeableRow } from '@/components/base/SwipeableRow';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { ChatConversation, useChatStore } from '@/stores/chat-store';
import { OptimisticFeedback } from './OptimisticFeedback';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';

interface ConversationsListProps {
  onSelectConversation: (id: string) => void;
  onDeleteConversation?: (id: string) => void;
  onNewConversation: () => void;
  onToggleFavorite?: (id: string) => void;
}

export default function ConversationsList({
  onSelectConversation,
  onDeleteConversation,
  onNewConversation,
  onToggleFavorite,
}: ConversationsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [editingConversation, setEditingConversation] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showRenameModal, setShowRenameModal] = useState(false);

  // Network status for optimistic feedback
  const networkStatus = useNetworkStatus();

  // FINAL FIX: Use individual selectors to prevent object recreation
  const conversations = useChatStore(state => state.conversations);
  const activeConversationId = useChatStore(state => state.activeConversationId);
  const isLoading = useChatStore(state => state.isLoading);
  const error = useChatStore(state => state.error);
  const pendingSyncMessages = useChatStore(state => state.pendingSyncMessages);

  // Filter and sort conversations
  const filteredConversations = useMemo(() => {
    // Create a new array to avoid mutating the store's conversations reference
    const base = searchQuery.trim()
      ? conversations.filter(conv => {
          const query = searchQuery.toLowerCase();
          return (
            conv.title.toLowerCase().includes(query) ||
            conv.contextType?.toLowerCase().includes(query) ||
            conv.lastMessage?.toLowerCase().includes(query)
          );
        })
      : [...conversations];

    // Sort: favorites first, then by date (non-mutating)
    return base.sort((a, b) => {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      return (
        (b.lastMessageAt || b.updatedAt).getTime() - (a.lastMessageAt || a.updatedAt).getTime()
      );
    });
  }, [conversations, searchQuery]);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today - show time
      return date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffDays === 1) {
      return 'Ayer';
    } else if (diffDays < 7) {
      return date.toLocaleDateString('es-ES', { weekday: 'long' });
    } else {
      return date.toLocaleDateString('es-ES', {
        day: 'numeric',
        month: 'short',
      });
    }
  };
  const handleToggleFavorite = useCallback(
    async (id: string) => {
      try {
        if (onToggleFavorite) {
          await onToggleFavorite(id);
        }
      } catch {
        // Error is already handled by the optimistic store with rollback
        logger.warn('Toggle favorite completed with error (rolled back)', 'ConversationsList', {
          id,
        });
      }
    },
    [onToggleFavorite]
  );

  const handleLongPress = useCallback((conversation: ChatConversation) => {
    setSelectedConversation(conversation.id);
    setShowActionMenu(true);
  }, []);

  const startEditing = useCallback((conversationId: string, currentTitle: string) => {
    setEditingConversation(conversationId);
    setEditingTitle(currentTitle);
    setShowRenameModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const cancelEditing = useCallback(() => {
    setEditingConversation(null);
    setEditingTitle('');
    setShowRenameModal(false);
  }, []);

  const saveEditing = useCallback(async () => {
    if (!editingConversation || !editingTitle.trim()) return;

    const newTitle = editingTitle.trim();
    const conversationId = editingConversation;

    // Clear editing state immediately (optimistic)
    setEditingConversation(null);
    setEditingTitle('');
    setShowRenameModal(false);

    try {
      const { updateConversation } = useChatStore.getState();
      await updateConversation(conversationId, { title: newTitle });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch {
      // If update fails, restore editing state
      setEditingConversation(conversationId);
      setEditingTitle(newTitle);
      setShowRenameModal(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [editingConversation, editingTitle]);

  const handleDeletePermanently = useCallback(
    (id: string) => {
      Alert.alert('Eliminar conversación', 'Esta acción no se puede deshacer. ¿Estás seguro?', [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            if (onDeleteConversation) {
              try {
                // Immediate haptic feedback
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                await onDeleteConversation(id);
                logger.info('Conversation permanently deleted', 'ConversationsList', { id });
              } catch {
                // Error handling is already done in the optimistic store
                logger.warn(
                  'Delete conversation completed with error (rolled back)',
                  'ConversationsList',
                  { id }
                );
              }
            }
          },
        },
      ]);
    },
    [onDeleteConversation]
  );

  // Separate conversation item component to fix React hooks rules
  const ConversationItem = React.memo(
    function ConversationItem({ item, index: _index }: { item: ChatConversation; index: number }) {
      const isActive = item.id === activeConversationId;
      const lastMessageDate = item.lastMessageAt || item.updatedAt;
      const favoriteScale = useSharedValue(1);
      const borderOpacity = useSharedValue(isActive ? 1 : 0);

      // Update border animation when active state changes
      React.useEffect(() => {
        borderOpacity.value = withTiming(isActive ? 1 : 0, {
          duration: 180,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [isActive]);

      const favoriteAnimatedStyle = useAnimatedStyle(() => ({
        transform: [{ scale: favoriteScale.value }],
      }));

      const borderAnimatedStyle = useAnimatedStyle(() => ({
        borderLeftColor: `rgba(59, 130, 246, ${borderOpacity.value})`, // Blue with animated opacity
      }));

      // Removed press-in/out scaling to eliminate unpleasant bounce animation

      const handlePress = () => {
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
        runOnJS(onSelectConversation)(item.id);
      };

      const handleLongPressGesture = () => {
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
        runOnJS(handleLongPress)(item);
      };

      // CRITICAL FIX: Separate layout animations from opacity animations to fix Reanimated conflicts
      const conversationContent = (
        <Animated.View style={[styles.conversationItem, borderAnimatedStyle]}>
          <View>
            <Pressable
              style={styles.conversationPressable}
              onPress={handlePress}
              onLongPress={handleLongPressGesture}
            >
              {/* Favorite Pin with Claude-style micro-animation */}
              {item.isFavorite && (
                <Animated.View style={[styles.favoriteIcon, favoriteAnimatedStyle]}>
                  <Pin size={12} color={Colors.light.warning} fill={Colors.light.warning} />
                </Animated.View>
              )}

              <View style={styles.conversationContent}>
                <View>
                  <Text
                    style={[styles.conversationTitle, isActive && styles.activeText]}
                    numberOfLines={1}
                  >
                    {item.title}
                  </Text>
                </View>

                {item.lastMessage && (
                  <Text style={styles.lastMessage} numberOfLines={1}>
                    {item.lastMessage}
                  </Text>
                )}

                <View style={styles.conversationMeta}>
                  {item.contextType && (
                    <Text style={styles.contextType}>
                      {item.contextType === 'client' && 'Cliente'}
                      {item.contextType === 'service' && 'Servicio'}
                      {item.contextType === 'formula' && 'Fórmula'}
                      {item.contextType === 'inventory' && 'Inventario'}
                    </Text>
                  )}
                </View>
              </View>

              <View style={styles.conversationRight}>
                <Text style={styles.conversationDate}>{formatDate(lastMessageDate)}</Text>
              </View>
            </Pressable>
          </View>
        </Animated.View>
      );

      const handleSwipeAction = () => {
        // Claude-style micro-interaction: bounce + success feedback
        favoriteScale.value = withSequence(
          withSpring(1.3, { damping: 8, stiffness: 400 }),
          withSpring(0.9, { damping: 10, stiffness: 300 }),
          withSpring(1.1, { damping: 12, stiffness: 350 }),
          withSpring(1, { damping: 15, stiffness: 300 })
        );

        // Immediate haptic feedback
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);

        // Execute optimistic toggle
        runOnJS(handleToggleFavorite)(item.id);
      };

      return (
        <SwipeableRow
          onSwipeRight={handleSwipeAction}
          rightActionIcon={
            item.isFavorite ? (
              <Pin size={20} color={Colors.light.surface} fill={Colors.light.surface} />
            ) : (
              <Pin size={20} color={Colors.light.surface} />
            )
          }
          rightActionColor={Colors.light.warning}
        >
          {conversationContent}
        </SwipeableRow>
      );
    },
    (prevProps, nextProps) => {
      // CRITICAL FIX: Custom comparison to prevent unnecessary re-renders and infinite loops
      return (
        prevProps.item.id === nextProps.item.id &&
        prevProps.item.title === nextProps.item.title &&
        prevProps.item.lastMessage === nextProps.item.lastMessage &&
        prevProps.item.isFavorite === nextProps.item.isFavorite &&
        prevProps.item.updatedAt?.getTime() === nextProps.item.updatedAt?.getTime() &&
        prevProps.index === nextProps.index &&
        (prevProps.item.id === activeConversationId) ===
          (nextProps.item.id === activeConversationId)
      );
    }
  );

  const renderConversation = useCallback(
    ({ item, index }: { item: ChatConversation; index: number }) => (
      <ConversationItem item={item} index={index} />
    ),
    [ConversationItem]
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Conversaciones</Text>
          <OptimisticFeedback
            isPending={isLoading}
            error={error}
            isOffline={networkStatus.shouldShowOfflineIndicator}
            pendingCount={pendingSyncMessages.length}
          />
        </View>
        <AnimatedButton onPress={onNewConversation} style={styles.newButton}>
          <Text style={styles.newButtonText}>Nueva</Text>
        </AnimatedButton>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Search size={20} color={Colors.light.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar conversaciones..."
          placeholderTextColor={Colors.light.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <AnimatedButton onPress={() => setSearchQuery('')}>
            <X size={20} color={Colors.light.textSecondary} />
          </AnimatedButton>
        )}
      </View>

      {/* Conversations List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.light.primary} />
        </View>
      ) : filteredConversations.length === 0 ? (
        <EmptyState
          searchQuery={searchQuery}
          onPress={() => {
            if (!searchQuery) {
              onNewConversation();
            }
          }}
        />
      ) : (
        <FlatList
          data={filteredConversations}
          keyExtractor={item => item.id}
          renderItem={renderConversation}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={false}
          initialNumToRender={12}
          windowSize={7}
        />
      )}

      {/* Action Menu Modal */}
      <Modal
        visible={showActionMenu}
        transparent
        animationType="none"
        onRequestClose={() => setShowActionMenu(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setShowActionMenu(false)}>
          <View style={styles.actionSheet}>
            <Pressable
              style={styles.actionItem}
              onPress={() => {
                setShowActionMenu(false);
                if (selectedConversation) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  handleToggleFavorite(selectedConversation);
                }
              }}
            >
              <View style={styles.actionContent}>
                <Pin size={20} color={Colors.light.text} />
                <Text style={styles.actionText}>
                  {conversations.find(c => c.id === selectedConversation)?.isFavorite
                    ? 'Desmarcar'
                    : 'Destacar'}
                </Text>
              </View>
            </Pressable>

            <Pressable
              style={styles.actionItem}
              onPress={() => {
                setShowActionMenu(false);
                if (selectedConversation) {
                  const conversation = conversations.find(c => c.id === selectedConversation);
                  if (conversation) {
                    startEditing(selectedConversation, conversation.title);
                  }
                }
              }}
            >
              <View style={styles.actionContent}>
                <Edit3 size={20} color={Colors.light.text} />
                <Text style={styles.actionText}>Cambiar nombre</Text>
              </View>
            </Pressable>

            <Pressable
              style={[styles.actionItem, styles.destructiveAction]}
              onPress={() => {
                setShowActionMenu(false);
                if (selectedConversation) {
                  handleDeletePermanently(selectedConversation);
                }
              }}
            >
              <View style={styles.actionContent}>
                <Trash2 size={20} color={Colors.light.error} />
                <Text style={[styles.actionText, styles.destructiveText]}>Eliminar</Text>
              </View>
            </Pressable>
          </View>
        </Pressable>
      </Modal>

      {/* Rename Modal - Static (no animation) */}
      <Modal
        visible={showRenameModal}
        transparent
        animationType="none"
        onRequestClose={cancelEditing}
      >
        <KeyboardAvoidingView
          style={styles.renameModalOverlay}
          behavior={Platform.OS === 'ios' ? 'height' : 'height'}
        >
          <Pressable style={styles.renameModalOverlay} onPress={cancelEditing}>
            <View style={styles.renameModalContent}>
              <Pressable>
                {/* Modal Header */}
                <View style={styles.renameModalHeader}>
                  <Text style={styles.renameModalTitle}>Cambiar el nombre de la conversación</Text>
                  <Text style={styles.renameModalSubtitle}>Introduce un nuevo nombre</Text>
                </View>

                {/* Input Field */}
                <View style={styles.renameInputContainer}>
                  <TextInput
                    style={styles.renameInput}
                    value={editingTitle}
                    onChangeText={setEditingTitle}
                    placeholder="Nombre de la conversación"
                    placeholderTextColor={Colors.light.textSecondary}
                    autoFocus
                    selectTextOnFocus
                    onSubmitEditing={saveEditing}
                    maxLength={50}
                    returnKeyType="done"
                  />
                </View>

                {/* Action Buttons */}
                <View style={styles.renameModalFooter}>
                  <Pressable
                    style={[styles.renameButton, styles.renameCancelButton]}
                    onPress={cancelEditing}
                  >
                    <Text style={styles.renameCancelText}>Cancelar</Text>
                  </Pressable>

                  <Pressable
                    style={[styles.renameButton, styles.renameSaveButton]}
                    onPress={saveEditing}
                    disabled={!editingTitle.trim()}
                  >
                    <Text
                      style={[
                        styles.renameSaveText,
                        !editingTitle.trim() && styles.renameDisabledText,
                      ]}
                    >
                      Vale
                    </Text>
                  </Pressable>
                </View>
              </Pressable>
            </View>
          </Pressable>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerLeft: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: spacing.xs,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  newButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
  },
  newButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.surface,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    marginHorizontal: spacing.sm,
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
  },
  listContent: {
    paddingVertical: 0, // Optimized: Removed vertical padding for maximum density
  },
  conversationItem: {
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    borderLeftWidth: 3, // Fixed width to avoid layout shift; color animates
  },
  conversationPressable: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16, // Optimized: Reduced from 20px to save horizontal space
    paddingVertical: 12, // Optimized: Reduced from 20px to 12px (Claude-style density)
    minHeight: 56, // Optimized: Reduced from 72px to 56px for better density
    flex: 1,
  },
  favoriteIcon: {
    marginRight: spacing.xs, // Optimized: Small margin for inline favorite indicator
    justifyContent: 'center',
    alignItems: 'center',
  },
  conversationContent: {
    flex: 1,
    marginRight: spacing.xs, // Optimized: Reduced margin for tighter layout
  },
  conversationTitle: {
    fontSize: typography.sizes.base, // 16px - maintained size
    fontWeight: typography.weights.semibold, // Semibold for prominence
    color: Colors.light.text,
    marginBottom: 2, // Optimized: Reduced from 4px to 2px for tighter spacing
    lineHeight: 20, // Optimized: Tighter line height (was 22.4px)
  },
  activeText: {
    color: Colors.light.primary,
  },
  lastMessage: {
    fontSize: typography.sizes.sm, // 14px - maintained size
    fontWeight: typography.weights.regular,
    color: Colors.light.textSecondary,
    marginTop: 1, // Optimized: Minimal spacing for density
    marginBottom: 2, // Optimized: Reduced spacing
    lineHeight: 18, // Optimized: Tighter line height (was 19.6px)
  },
  conversationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs, // Optimized: Reduced gap for tighter layout
    marginTop: 2, // Optimized: Minimal top margin
  },
  contextType: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    backgroundColor: Colors.light.primaryTransparent10,
    paddingHorizontal: 6, // Optimized: Reduced padding
    paddingVertical: 1, // Optimized: Minimal vertical padding
    borderRadius: radius.sm,
  },
  conversationRight: {
    alignItems: 'flex-end',
  },
  conversationDate: {
    fontSize: typography.sizes.xs, // 12px - maintained size
    fontWeight: typography.weights.regular, // Optimized: Regular weight for better readability
    color: Colors.light.textSecondary, // Optimized: Secondary color (textTertiary might be too light)
    lineHeight: 16, // Optimized: Tighter line height
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.common.shadowColor + '33', // ~0.2 opacity for lighter backdrop
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionSheet: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    marginHorizontal: spacing.xl,
    padding: spacing.sm,
    width: '90%',
    maxWidth: 400,
  },
  actionItem: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: radius.md,
    minHeight: 48, // Minimum 48px height for better touch target
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  actionText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
  },
  destructiveAction: {
    backgroundColor: Colors.light.errorTransparent10,
  },
  destructiveText: {
    color: Colors.light.error,
  },
  // Rename Modal Styles - Claude Style
  renameModalOverlay: {
    flex: 1,
    backgroundColor: Colors.common.shadowColor + '33', // ~0.2 opacity backdrop
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  renameModalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    padding: spacing.xl,
    width: '100%',
    maxWidth: 400,
    // Remove shadows/elevation to avoid visual/performant issues on some devices
  },
  renameModalHeader: {
    marginBottom: spacing.lg,
  },
  renameModalTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  renameModalSubtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  renameInputContainer: {
    marginBottom: spacing.xl,
  },
  renameInput: {
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    backgroundColor: Colors.light.surface,
  },
  renameModalFooter: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  renameButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  renameCancelButton: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  renameSaveButton: {
    backgroundColor: Colors.light.primary,
  },
  renameCancelText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
  },
  renameSaveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textLight,
  },
  renameDisabledText: {
    opacity: 0.5,
  },
});

// Interactive empty state with delightful animation
const EmptyState: React.FC<{
  searchQuery: string;
  onPress: () => void;
}> = ({ searchQuery, onPress }) => {
  const wobble = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${wobble.value}deg` }, { scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePress = () => {
    if (!searchQuery) {
      // Playful wiggle animation
      wobble.value = withSpring(0, { damping: 8, stiffness: 100 }, () => {
        wobble.value = withSpring(-8, { damping: 8, stiffness: 100 }, () => {
          wobble.value = withSpring(8, { damping: 8, stiffness: 100 }, () => {
            wobble.value = withSpring(-5, { damping: 8, stiffness: 100 }, () => {
              wobble.value = withSpring(5, { damping: 8, stiffness: 100 }, () => {
                wobble.value = withSpring(0, { damping: 12, stiffness: 150 });
              });
            });
          });
        });
      });

      // Scale bounce
      scale.value = withSpring(1.1, { damping: 10, stiffness: 300 }, () => {
        scale.value = withSpring(1, { damping: 12, stiffness: 250 });
      });

      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
      runOnJS(onPress)();
    }
  };

  const handlePressIn = () => {
    'worklet';
    if (!searchQuery) {
      scale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
      opacity.value = withTiming(0.8, { duration: 100 });
    }
  };

  const handlePressOut = () => {
    'worklet';
    if (!searchQuery) {
      scale.value = withSpring(1, { damping: 12, stiffness: 250 });
      opacity.value = withTiming(1, { duration: 150 });
    }
  };

  return (
    <Pressable
      style={styles.emptyContainer}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={!!searchQuery}
    >
      <Animated.View style={[iconAnimatedStyle]} entering={FadeIn.duration(600).delay(200)}>
        <MessageSquare size={48} color={Colors.light.textSecondary} />
      </Animated.View>

      <Animated.Text style={styles.emptyTitle} entering={FadeIn.duration(500).delay(400)}>
        {searchQuery ? 'Sin resultados' : 'Sin conversaciones'}
      </Animated.Text>

      <Animated.Text style={styles.emptyText} entering={FadeIn.duration(500).delay(600)}>
        {searchQuery
          ? 'Intenta con otros términos de búsqueda'
          : 'Toca para iniciar una nueva conversación'}
      </Animated.Text>
    </Pressable>
  );
};

// Reusable animated button component with subtle micro-interactions
const AnimatedButton: React.FC<{
  children: React.ReactNode;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}> = ({ children, onPress, style }) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    'worklet';
    scale.value = withSpring(0.95, { damping: 15, stiffness: 400 });
    opacity.value = withTiming(0.8, { duration: 100 });
  };

  const handlePressOut = () => {
    'worklet';
    scale.value = withSpring(1, { damping: 12, stiffness: 350 });
    opacity.value = withTiming(1, { duration: 150 });
  };

  const handlePress = () => {
    runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
    runOnJS(onPress)();
  };

  return (
    <Animated.View style={animatedStyle}>
      <Pressable
        style={style}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
      >
        {children}
      </Pressable>
    </Animated.View>
  );
};
