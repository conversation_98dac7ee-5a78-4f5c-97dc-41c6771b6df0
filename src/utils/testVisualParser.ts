import { parseVisualFormula } from './visualFormulaParser';

// Test formula text from MockFormulationService
const testFormula = `Wella Koleston:

Fórmula Tinte Completo:
- Koleston 7/1 (30g)
- <PERSON><PERSON><PERSON> 7/69 (10g)
- Oxidante 20 vol (60g)

Tiempo de proceso: 35 minutos

Pasos de aplicación:
1. Aplicar primero en raíces, dejar 20 minutos
2. Extender a medios y puntas 15 minutos adicionales
3. Emulsionar antes de enjuagar`;

const testFormulaWithZones = `L'Oréal Majirel:

FÓRMULA RAÍCES (0-3cm):
- Majirel 6.3 (30g)
- Majirel 6.0 (15g)
- Oxidante 20 vol (67.5g)
- Mix Violeta (2cm)

FÓRMULA MEDIOS-PUNTAS:
- Majirel 7.3 (40g)
- Majirel 7.31 (20g)
- Oxidante 30 vol (90g)

Proporción: 1:1.5

Tiempo de proceso: 45 minutos`;

// Test the parser
export function testParser() {
  // Debug logging removed for production

  // Test 1: Simple formula
  // Debug logging removed for production
  const result1 = parseVisualFormula(testFormula);
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production

  // Test 2: Formula with zones
  // Debug logging removed for production
  const result2 = parseVisualFormula(testFormulaWithZones);
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production

  // Test 3: With context
  // Debug logging removed for production
  const context = {
    currentLevel: 4.5,
    targetLevel: 7.0,
    currentTone: 'Castaño Oscuro',
    targetTone: 'Rubio Dorado',
    clientName: 'María García',
    levelDifference: 2.5,
  };
  const result3 = parseVisualFormula(testFormula, {}, context);
  // Debug logging removed for production
  // Debug logging removed for production

  return { result1, result2, result3 };
}

// Test formula with bond builders and treatments
const testFormulaWithTreatments = `Schwarzkopf Igora:

FÓRMULA RAÍCES:
- Igora Royal 6.0 (40g)
- Igora Royal 6.12 (20g)
- Oxidante 20 vol (90g)
- Olaplex N°1 (3.75ml)
- Bond Ultim8 N°1 (5ml)

FÓRMULA MEDIOS-PUNTAS:
- Igora Royal 7.0 (30g)
- Igora Royal 7.1 (30g)
- Oxidante 30 vol (90g)
- K18 Leave-in Treatment (2ml)
- Mix Violeta (3cm)

Tiempo de proceso: 40 minutos
Proporción: 1:1.5`;

// Test formula with various additives
const testFormulaWithAdditives = `Wella Color Touch:

Fórmula Completa:
- Color Touch 8/81 (60g)
- Color Touch 8/3 (20g)
- Emulsión Color Touch 1.9% (120g)
- WellaPlex N°1 (8ml)
- Pure Pigment Blue (2cm)
- Mix Rojo (5 gotas)

Tiempo de proceso: 20 minutos`;

// Additional test for new products
export function testEnhancedParser() {
  // Debug logging removed for production

  // Test 4: Formula with treatments
  // Debug logging removed for production
  const result4 = parseVisualFormula(testFormulaWithTreatments);
  // Debug logging removed for production
  // Debug logging removed for production
  // Zones analysis:
  const _zones = result4.data?.zones.map(z => ({
    zone: z.zone,
    ingredients: z.ingredients.map(i => ({
      name: i.name,
      type: i.type,
      amount: i.amount,
      unit: i.unit,
    })),
  }));
  // Debug logging removed for production

  // Test 5: Formula with additives
  // Debug logging removed for production
  const result5 = parseVisualFormula(testFormulaWithAdditives);
  // Debug logging removed for production
  // Debug logging removed for production
  // Ingredients analysis:
  const _ingredients = result5.data?.zones[0]?.ingredients.map(i => ({
    name: i.name,
    type: i.type,
    amount: i.amount,
    unit: i.unit,
  }));

  // Verify specific product detection
  const _olaplexDetected = result4.data?.zones.some(z =>
    z.ingredients.some(i => i.name.toLowerCase().includes('olaplex') && i.type === 'treatment')
  );
  const _bondUltim8Detected = result4.data?.zones.some(z =>
    z.ingredients.some(i => i.name.toLowerCase().includes('bond ultim8') && i.type === 'treatment')
  );
  const _cmDetected = result5.data?.zones.some(z =>
    z.ingredients.some(i => i.unit === 'cm' && i.type === 'additive')
  );
  const _gotasDetected = result5.data?.zones.some(z =>
    z.ingredients.some(i => i.unit === 'drops' && i.type === 'additive')
  );

  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production
  // Debug logging removed for production

  return { result4, result5 };
}

// Uncomment to run tests
// testParser();
// testEnhancedParser();
