import { useState, useEffect } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { logger } from '@/utils/logger';

export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  connectionType: string | null;
  wasOffline: boolean;
  lastOnlineAt: Date | null;
}

/**
 * Hook for managing network status with Claude-style UX
 * Tracks connection state and provides offline/online transitions
 */
export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    isInternetReachable: null,
    connectionType: null,
    wasOffline: false,
    lastOnlineAt: null,
  });

  const [justCameOnline, setJustCameOnline] = useState(false);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state: NetInfoState) => {
      const isConnected = state.isConnected ?? false;
      const isInternetReachable = state.isInternetReachable;
      const connectionType = state.type;

      const wasConnected = networkStatus.isConnected;

      setNetworkStatus(prev => ({
        ...prev,
        isConnected,
        isInternetReachable,
        connectionType,
        wasOffline: prev.wasOffline || (!isConnected && wasConnected),
        lastOnlineAt: isConnected ? new Date() : prev.lastOnlineAt,
      }));

      // Handle came back online event
      if (isConnected && !wasConnected) {
        setJustCameOnline(true);
        logger.info('Network came back online', 'NetworkStatus', {
          connectionType,
          isInternetReachable,
        });

        // Clear the "just came online" flag after 3 seconds
        setTimeout(() => {
          setJustCameOnline(false);
        }, 3000);
      }

      // Handle went offline event
      if (!isConnected && wasConnected) {
        logger.info('Network went offline', 'NetworkStatus', {
          previousConnectionType: networkStatus.connectionType,
        });
      }
    });

    return () => {
      unsubscribe();
    };
  }, [networkStatus.isConnected, networkStatus.connectionType]);

  const getConnectionQuality = (): 'excellent' | 'good' | 'poor' | 'offline' => {
    if (!networkStatus.isConnected) {
      return 'offline';
    }

    if (networkStatus.isInternetReachable === false) {
      return 'poor';
    }

    switch (networkStatus.connectionType) {
      case 'wifi':
        return 'excellent';
      case 'cellular':
        return 'good';
      default:
        return 'poor';
    }
  };

  const shouldShowOfflineIndicator = () => {
    return !networkStatus.isConnected || networkStatus.isInternetReachable === false;
  };

  const shouldShowReconnectedIndicator = () => {
    return justCameOnline && networkStatus.wasOffline;
  };

  return {
    ...networkStatus,
    justCameOnline,
    connectionQuality: getConnectionQuality(),
    shouldShowOfflineIndicator: shouldShowOfflineIndicator(),
    shouldShowReconnectedIndicator: shouldShowReconnectedIndicator(),
  };
}
