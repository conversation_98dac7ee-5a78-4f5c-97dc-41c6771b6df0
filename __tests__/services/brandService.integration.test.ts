/**
 * Integration Test for Brand Service
 *
 * This test validates the real integration without mocking
 * to ensure the service actually works with the database.
 */

import { describe, it, expect, beforeAll } from '@jest/globals';
import {
  getBrandsAsync,
  getBrandByIdAsync,
  getLinesByBrandIdAsync,
  brandService,
  professionalHairColorBrands,
  getBrandById,
  getLinesByBrandId,
} from '../../src/services/brandService';

describe('BrandService Integration', () => {
  beforeAll(async () => {
    // Clear cache to ensure fresh test
    await brandService.invalidateCache();
  });

  describe('Async API', () => {
    it('should load brands from service', async () => {
      const brands = await getBrandsAsync();

      expect(Array.isArray(brands)).toBe(true);
      expect(brands.length).toBeGreaterThan(0);

      // Validate brand structure
      const firstBrand = brands[0];
      expect(firstBrand).toHaveProperty('id');
      expect(firstBrand).toHaveProperty('name');
      expect(firstBrand).toHaveProperty('country');
      expect(firstBrand).toHaveProperty('lines');
      expect(Array.isArray(firstBrand.lines)).toBe(true);
    });

    it('should get specific brand by ID', async () => {
      const wella = await getBrandByIdAsync('wella');

      expect(wella).toBeDefined();
      expect(wella?.id).toBe('wella');
      expect(wella?.name).toContain('Wella');
      expect(wella?.lines.length).toBeGreaterThan(0);
    });

    it('should get lines for specific brand', async () => {
      const lines = await getLinesByBrandIdAsync('wella');

      expect(Array.isArray(lines)).toBe(true);
      expect(lines.length).toBeGreaterThan(0);

      // Validate line structure
      const firstLine = lines[0];
      expect(firstLine).toHaveProperty('id');
      expect(firstLine).toHaveProperty('name');
      expect(firstLine).toHaveProperty('category');
      expect(firstLine).toHaveProperty('isColorLine');
    });
  });

  describe('Backward Compatibility', () => {
    it('should work with synchronous access patterns', () => {
      // Note: This might be empty initially until background loading completes
      expect(Array.isArray(professionalHairColorBrands)).toBe(true);
    });

    it('should work with synchronous helper functions', () => {
      // These should work after data is loaded
      const _wella = getBrandById('wella');
      const lines = getLinesByBrandId('wella');

      // Functions should exist and return appropriate types
      expect(typeof getBrandById).toBe('function');
      expect(typeof getLinesByBrandId).toBe('function');
      expect(Array.isArray(lines)).toBe(true);
    });
  });

  describe('Category Mapping', () => {
    it('should correctly map categories from database to interface', async () => {
      const brands = await getBrandsAsync();
      const allLines = brands.flatMap(brand => brand.lines);

      // Should have various categories
      const categories = [...new Set(allLines.map(line => line.category))];
      expect(categories).toContain('hair-color');

      // Should correctly set isColorLine for formulable categories
      const colorLines = allLines.filter(line => line.category === 'hair-color');
      colorLines.forEach(line => {
        expect(line.isColorLine).toBe(true);
      });

      const treatmentLines = allLines.filter(line => line.category === 'treatment');
      treatmentLines.forEach(line => {
        expect(line.isColorLine).toBe(false);
      });
    });
  });

  describe('Cache Performance', () => {
    it('should cache and retrieve data efficiently', async () => {
      // First call - should populate cache
      const start1 = Date.now();
      const brands1 = await getBrandsAsync();
      const duration1 = Date.now() - start1;

      // Second call - should use cache
      const start2 = Date.now();
      const brands2 = await getBrandsAsync();
      const duration2 = Date.now() - start2;

      // Results should be identical
      expect(brands1).toEqual(brands2);

      // Second call should be faster (cached)
      expect(duration2).toBeLessThan(duration1);

      console.log(`First call: ${duration1}ms, Second call: ${duration2}ms`);
    });

    it('should provide cache status', async () => {
      const status = await brandService.getCacheStatus();

      expect(status).toHaveProperty('hasMemoryCache');
      expect(status).toHaveProperty('hasStorageCache');
      expect(typeof status.hasMemoryCache).toBe('boolean');
      expect(typeof status.hasStorageCache).toBe('boolean');
    });
  });

  describe('Data Validation', () => {
    it('should have well-formed brand data', async () => {
      const brands = await getBrandsAsync();

      brands.forEach(brand => {
        // Required fields
        expect(typeof brand.id).toBe('string');
        expect(brand.id.length).toBeGreaterThan(0);
        expect(typeof brand.name).toBe('string');
        expect(brand.name.length).toBeGreaterThan(0);
        expect(typeof brand.country).toBe('string');

        // Lines validation
        expect(Array.isArray(brand.lines)).toBe(true);
        brand.lines.forEach(line => {
          expect(typeof line.id).toBe('string');
          expect(line.id.length).toBeGreaterThan(0);
          expect(typeof line.name).toBe('string');
          expect(line.name.length).toBeGreaterThan(0);
          expect([
            'hair-color',
            'treatment',
            'styling',
            'bleaching',
            'developer',
            'other',
          ]).toContain(line.category);
          expect(typeof line.isColorLine).toBe('boolean');
        });
      });
    });

    it('should have popular brands available', async () => {
      const brands = await getBrandsAsync();
      const brandIds = brands.map(b => b.id);

      // Check for some popular brands
      const popularBrands = ['wella', 'loreal', 'schwarzkopf'];
      popularBrands.forEach(brandId => {
        expect(brandIds).toContain(brandId);
      });
    });
  });
});
