#!/usr/bin/env npx tsx

import fs from 'fs';
import path from 'path';

const BRANDS_DATA_PATH = '../src/constants/brands-data.ts';

console.log('=== CORRECCIÓN AUTOMÁTICA DE ERRORES CRÍTICOS ===\n');

// Leer el archivo
let content = fs.readFileSync(BRANDS_DATA_PATH, 'utf8');

// Función para determinar la categoría basada en el nombre de la línea
function determineCategory(lineName: string, lineDescription: string): string {
  const name = lineName.toLowerCase();
  const desc = lineDescription.toLowerCase();
  
  // Categorías de coloración
  if (name.includes('color') || name.includes('colour') || 
      desc.includes('permanent') || desc.includes('demi') || 
      desc.includes('semi') || desc.includes('toner') ||
      name.includes('tinta') || name.includes('cromatone') ||
      name.includes('chromasilk') || name.includes('majirel') ||
      name.includes('igora') || name.includes('koleston')) {
    return 'hair-color';
  }
  
  // Categorías de decoloración
  if (name.includes('bleach') || name.includes('lightening') || 
      name.includes('blonde') || name.includes('blond') ||
      name.includes('decolor') || name.includes('lighten') ||
      desc.includes('lightening') || desc.includes('bleaching')) {
    return 'bleaching';
  }
  
  // Categorías de tratamiento
  if (name.includes('treatment') || name.includes('care') || 
      name.includes('keratin') || name.includes('protein') ||
      desc.includes('treatment') || desc.includes('care') ||
      name.includes('plex') || name.includes('bond')) {
    return 'treatment';
  }
  
  // Categorías de desarrollador
  if (name.includes('developer') || name.includes('activator') ||
      name.includes('peroxide') || desc.includes('developer')) {
    return 'developer';
  }
  
  // Por defecto, si contiene "color" en algún lugar, es hair-color
  if (name.includes('color') || desc.includes('color')) {
    return 'hair-color';
  }
  
  return 'hair-color'; // Por defecto
}

// Función para determinar si es línea de coloración
function isColorLine(lineName: string, lineDescription: string, category: string): boolean {
  const name = lineName.toLowerCase();
  const desc = lineDescription.toLowerCase();
  
  // Si es categoría de coloración, probablemente es línea de coloración
  if (category === 'hair-color') {
    return true;
  }
  
  // Si es decoloración y se usa para coloración (como mechas), también es línea de coloración
  if (category === 'bleaching' && (
    name.includes('color') || desc.includes('color') ||
    name.includes('toner') || desc.includes('toner')
  )) {
    return true;
  }
  
  // Tratamientos y desarrolladores no son líneas de coloración
  if (category === 'treatment' || category === 'developer' || category === 'styling') {
    return false;
  }
  
  return false;
}

// Contador de correcciones
let corrections = 0;

// Regex para encontrar líneas sin categoría o isColorLine
const lineRegex = /{\s*id:\s*['"`]([^'"`]+)['"`],\s*name:\s*['"`]([^'"`]+)['"`],\s*description:\s*['"`]([^'"`]+)['"`]\s*}/g;

let match;
const linesToFix: Array<{
  fullMatch: string;
  id: string;
  name: string;
  description: string;
  startIndex: number;
  endIndex: number;
}> = [];

// Encontrar todas las líneas que necesitan corrección
while ((match = lineRegex.exec(content)) !== null) {
  const [fullMatch, id, name, description] = match;
  
  // Si no tiene category o isColorLine, necesita corrección
  if (!fullMatch.includes('category:') || !fullMatch.includes('isColorLine:')) {
    linesToFix.push({
      fullMatch,
      id,
      name,
      description,
      startIndex: match.index,
      endIndex: match.index + fullMatch.length
    });
  }
}

console.log(`📋 Encontradas ${linesToFix.length} líneas que necesitan corrección\n`);

// Procesar las correcciones de atrás hacia adelante para no afectar los índices
for (let i = linesToFix.length - 1; i >= 0; i--) {
  const line = linesToFix[i];
  
  const category = determineCategory(line.name, line.description);
  const isColorLineValue = isColorLine(line.name, line.description, category);
  
  const newLine = `{
        id: '${line.id}',
        name: '${line.name}',
        description: '${line.description}',
        category: '${category}',
        isColorLine: ${isColorLineValue},
      }`;
  
  content = content.substring(0, line.startIndex) + newLine + content.substring(line.endIndex);
  corrections++;
  
  console.log(`✅ Corregido: ${line.name} -> category: '${category}', isColorLine: ${isColorLineValue}`);
}

// Escribir el archivo corregido
fs.writeFileSync(BRANDS_DATA_PATH, content);

console.log(`\n🎉 CORRECCIÓN COMPLETADA:`);
console.log(`- Líneas corregidas: ${corrections}`);
console.log(`- Archivo actualizado: ${BRANDS_DATA_PATH}`);
console.log('\n=== FIN DE CORRECCIÓN ===');
