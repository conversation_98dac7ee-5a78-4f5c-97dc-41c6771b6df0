import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
import { HairZone } from '@/types/hair-diagnosis';

// Import all step components
import {
  PreparationStep,
  MixingStep,
  ApplicationStep,
  ProcessingStep,
  RinsingStep,
  CompletionStep,
} from '../steps';

// Import reusable components
import {
  ProgressIndicator,
  StepCard,
  MaterialsList,
  TimerCard,
  ZoneVisualizer,
} from '../components';

interface StepComponentsDemoProps {
  testID?: string;
}

export const StepComponentsDemo: React.FC<StepComponentsDemoProps> = ({
  testID = 'step-components-demo',
}) => {
  const [activeDemo, setActiveDemo] = useState<'steps' | 'components'>('steps');
  const [selectedStep, setSelectedStep] = useState(0);
  const [selectedZone, setSelectedZone] = useState(0);

  // Mock data for demonstrations
  const mockFormulaData: VisualFormulationData = {
    zones: [
      {
        zone: HairZone.ROOTS,
        ingredients: [
          { name: 'Illumina Color 8/69', amount: '60', unit: 'g', type: 'color' },
          { name: 'Welloxon Perfect 20vol', amount: '90', unit: 'ml', type: 'developer' },
        ],
      },
      {
        zone: HairZone.MIDS,
        ingredients: [
          { name: 'Illumina Color 8/36', amount: '30', unit: 'g', type: 'color' },
          { name: 'Welloxon Perfect 20vol', amount: '45', unit: 'ml', type: 'developer' },
        ],
      },
    ],
    colorTransition: {
      current: { level: '5', tone: 'N', hex: '#8B4513' },
      target: { level: '8', tone: '69', hex: '#D2B48C' },
    },
    mixingProportions: {
      ratio: '1:1.5',
      developer: { name: 'Welloxon Perfect', volume: '20vol', amount: '135' },
    },
    applicationGuide: {
      technique: 'zonal',
      totalTime: 45,
      zones: [
        { id: 'roots', name: 'Raíces', description: 'Aplicar desde el cuero cabelludo', order: 1 },
        { id: 'mids', name: 'Medios', description: 'Zona intermedia del cabello', order: 2 },
        { id: 'ends', name: 'Puntas', description: 'Extremos del cabello', order: 3 },
      ],
    },
    timeline: [
      {
        id: 'application',
        label: 'Aplicación',
        startTime: 0,
        endTime: 15,
        description: 'Aplicar la fórmula según la técnica elegida',
        zones: ['roots', 'mids', 'ends'],
      },
      {
        id: 'processing',
        label: 'Procesamiento',
        startTime: 15,
        endTime: 35,
        description: 'Tiempo de procesamiento para el desarrollo del color',
        zones: ['roots', 'mids', 'ends'],
      },
    ],
    expectedResult: {
      description: 'Color uniforme y brillante con excelente cobertura',
      coverage: '95%',
      duration: '6-8 semanas',
      maintenance: [
        'Usar shampoo sin sulfatos',
        'Tratamiento hidratante semanal',
        'Proteger del sol excesivo',
      ],
    },
  };

  const stepComponents = [
    { name: 'PreparationStep', component: PreparationStep },
    { name: 'MixingStep', component: MixingStep },
    { name: 'ApplicationStep', component: ApplicationStep },
    { name: 'ProcessingStep', component: ProcessingStep },
    { name: 'RinsingStep', component: RinsingStep },
    { name: 'CompletionStep', component: CompletionStep },
  ];

  const mockMaterials = [
    { id: 'bowl', name: 'Bowl de mezcla', type: 'material' },
    { id: 'brush', name: 'Pincel aplicador', type: 'material' },
    { id: 'gloves', name: 'Guantes protectores', type: 'material' },
  ];

  const mockSteps = [
    { id: 'prep', color: Colors.light.primary, title: 'Preparación' },
    { id: 'mix', color: Colors.light.warning, title: 'Mezcla' },
    { id: 'apply', color: Colors.light.accent, title: 'Aplicación' },
    { id: 'process', color: Colors.light.success, title: 'Procesamiento' },
    { id: 'rinse', color: Colors.light.secondary, title: 'Enjuague' },
    { id: 'complete', color: Colors.light.primary, title: 'Finalizar' },
  ];

  const [checkedMaterials, setCheckedMaterials] = useState<string[]>([]);

  const renderStepDemo = () => {
    const StepComponent = stepComponents[selectedStep].component;
    return (
      <View style={styles.demoContainer}>
        <StepComponent
          formulaData={mockFormulaData}
          onComplete={() => {}}
          testID={`demo-${stepComponents[selectedStep].name.toLowerCase()}`}
        />
      </View>
    );
  };

  const renderComponentsDemo = () => {
    return (
      <ScrollView style={styles.demoContainer} showsVerticalScrollIndicator={false}>
        {/* ProgressIndicator Demo */}
        <StepCard title="ProgressIndicator Component" style={styles.demoCard}>
          <ProgressIndicator steps={mockSteps} currentStep={2} testID="demo-progress-indicator" />
        </StepCard>

        {/* TimerCard Demo */}
        <StepCard title="TimerCard Component" style={styles.demoCard}>
          <TimerCard
            title="Demo Timer"
            subtitle="This is a sample timer"
            initialTime={120}
            onComplete={() => {}}
            testID="demo-timer"
          />
        </StepCard>

        {/* MaterialsList Demo */}
        <StepCard title="MaterialsList Component" style={styles.demoCard}>
          <MaterialsList
            items={mockMaterials}
            checkedItems={checkedMaterials}
            onToggleItem={id => {
              setCheckedMaterials(prev =>
                prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
              );
            }}
            title="Demo Materials"
            groupBy="type"
            testID="demo-materials"
          />
        </StepCard>

        {/* ZoneVisualizer Demo */}
        <StepCard title="ZoneVisualizer Component" style={styles.demoCard}>
          <ZoneVisualizer
            zones={mockFormulaData.applicationGuide?.zones || []}
            selectedZone={selectedZone}
            onZoneSelect={setSelectedZone}
            technique="zonal"
            testID="demo-zone-visualizer"
          />
        </StepCard>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container} testID={testID}>
      {/* Demo Type Selector */}
      <View style={styles.selectorContainer}>
        <TouchableOpacity
          style={[styles.selectorButton, activeDemo === 'steps' && styles.selectorButtonActive]}
          onPress={() => setActiveDemo('steps')}
        >
          <Text style={[styles.selectorText, activeDemo === 'steps' && styles.selectorTextActive]}>
            Step Components
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.selectorButton,
            activeDemo === 'components' && styles.selectorButtonActive,
          ]}
          onPress={() => setActiveDemo('components')}
        >
          <Text
            style={[styles.selectorText, activeDemo === 'components' && styles.selectorTextActive]}
          >
            UI Components
          </Text>
        </TouchableOpacity>
      </View>

      {/* Step Selector (for steps demo) */}
      {activeDemo === 'steps' && (
        <View style={styles.stepSelector}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {stepComponents.map((step, index) => (
              <TouchableOpacity
                key={step.name}
                style={[styles.stepButton, selectedStep === index && styles.stepButtonActive]}
                onPress={() => setSelectedStep(index)}
              >
                <Text
                  style={[
                    styles.stepButtonText,
                    selectedStep === index && styles.stepButtonTextActive,
                  ]}
                >
                  {step.name.replace('Step', '')}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Demo Content */}
      {activeDemo === 'steps' ? renderStepDemo() : renderComponentsDemo()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.surface,
  },
  selectorContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
  },
  selectorButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
  },
  selectorButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  selectorText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  selectorTextActive: {
    color: Colors.common.white,
  },
  stepSelector: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  stepButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.background,
  },
  stepButtonActive: {
    backgroundColor: Colors.light.accent,
  },
  stepButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.textSecondary,
  },
  stepButtonTextActive: {
    color: Colors.common.white,
  },
  demoContainer: {
    flex: 1,
  },
  demoCard: {
    marginBottom: 16,
  },
});
