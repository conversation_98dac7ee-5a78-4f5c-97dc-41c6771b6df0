import type { BrandPacksIndex, BrandPack } from '@/types/brand-pack';
import { professionalHairColorBrands } from '@/services/brandService';

function p(brand: string, line: string) {
  return `${brand.toLowerCase().trim()}:${line.toLowerCase().trim()}`;
}

// Base packs curados (sobre estos se auto-generan el resto)
export const BRAND_PACKS_BASE: BrandPacksIndex = {
  // Wella
  [p('Wella Professionals', 'Koleston Perfect')]: {
    brand: 'Wella Professionals',
    line: 'Koleston Perfect',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    developerHints: [
      '10 vol para oscurecer o igualar',
      '20 vol para cobertura estándar y 1 nivel',
      '30 vol para 2 niveles con tinte',
    ],
    grayCoverage: { rules: ['Añadir 30-50% base natural según canas >30%'] },
    techniqueTimes: { fullColor: 35, retouch: 30, refresh: 10 },
  },
  [p('Wella Professionals', 'Illumina Color')]: {
    brand: 'Wella Professionals',
    line: 'Illumina Color',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    developerHints: ['Color translúcido; ajustar a 10–30 vol según elevación'],
    techniqueTimes: { fullColor: 35, retouch: 30, refresh: 10 },
  },
  [p('Wella Professionals', 'Color Touch')]: {
    brand: 'Wella Professionals',
    line: 'Color Touch',
    category: 'demi',
    defaultMixRatio: '1:2',
    defaultDeveloper: 13,
    developerHints: ['Emulsión 1.9% o 4% según tono y porosidad'],
    techniqueTimes: { toner: 20, refresh: 15 },
  },
  [p('Wella Professionals', 'Shinefinity')]: {
    brand: 'Wella Professionals',
    line: 'Shinefinity',
    category: 'toner',
    defaultMixRatio: '1:1',
    defaultDeveloper: 10,
    developerHints: ['Glaze sin elevación; 10–20 min'],
    techniqueTimes: { toner: 20 },
  },

  // Schwarzkopf
  [p('Schwarzkopf Professional', 'IGORA ROYAL')]: {
    brand: 'Schwarzkopf Professional',
    line: 'IGORA ROYAL',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    grayCoverage: { rules: ['Usar Absolutes para canas resistentes'] },
    techniqueTimes: { fullColor: 35, retouch: 30 },
  },
  [p('Schwarzkopf Professional', 'IGORA VIBRANCE')]: {
    brand: 'Schwarzkopf Professional',
    line: 'IGORA VIBRANCE',
    category: 'demi',
    defaultMixRatio: '1:1',
    defaultDeveloper: 13,
    techniqueTimes: { toner: 20 },
  },
  [p('Schwarzkopf Professional', 'IGORA ROYAL Highlifts')]: {
    brand: 'Schwarzkopf Professional',
    line: 'IGORA ROYAL Highlifts',
    category: 'highlift',
    defaultMixRatio: '1:2',
    defaultDeveloper: 30,
    developerHints: ['Hasta 4 niveles; 30–40 vol según caso'],
    techniqueTimes: { highlift: 45 },
  },

  // L'Oréal Professionnel
  [p("L'Oréal Professionnel", 'Majirel')]: {
    brand: "L'Oréal Professionnel",
    line: 'Majirel',
    category: 'permanent',
    defaultMixRatio: '1:1.5',
    defaultDeveloper: 20,
    grayCoverage: { rules: ['Canas >50%: aumentar base natural 50–100%'] },
    techniqueTimes: { fullColor: 35, retouch: 30 },
  },
  [p("L'Oréal Professionnel", 'iNOA')]: {
    brand: "L'Oréal Professionnel",
    line: 'iNOA',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    developerHints: ['Sistema ODS sin amoníaco; 10–30–40 vol'],
    techniqueTimes: { fullColor: 35 },
  },
  [p("L'Oréal Professionnel", 'Dia Light')]: {
    brand: "L'Oréal Professionnel",
    line: 'Dia Light',
    category: 'toner',
    defaultMixRatio: '1:1.5',
    defaultDeveloper: 10,
    developerHints: ['1.8%–4.5% según intensidad y porosidad'],
    techniqueTimes: { toner: 20 },
  },
  [p("L'Oréal Professionnel", 'Dia Color')]: {
    brand: "L'Oréal Professionnel",
    line: 'Dia Color',
    category: 'demi',
    defaultMixRatio: '1:1',
    defaultDeveloper: 13,
    techniqueTimes: { toner: 20, refresh: 15 },
  },

  // Goldwell
  [p('Goldwell', 'Topchic')]: {
    brand: 'Goldwell',
    line: 'Topchic',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    techniqueTimes: { fullColor: 35 },
  },
  [p('Goldwell', 'Colorance')]: {
    brand: 'Goldwell',
    line: 'Colorance',
    category: 'demi',
    defaultMixRatio: '2:1',
    defaultDeveloper: 13,
    techniqueTimes: { toner: 20 },
  },
  [p('Goldwell', 'Elumen')]: {
    brand: 'Goldwell',
    line: 'Elumen',
    category: 'direct',
    defaultMixRatio: 'sin oxidante',
    techniqueTimes: { fullColor: 30 },
    notes: ['Pigmento directo no oxidativo; requiere pre‑tratamientos específicos'],
  },

  // Genéricos
  [p('Generic', 'Permanent')]: {
    brand: 'Generic',
    line: 'Permanent',
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    techniqueTimes: { fullColor: 35, retouch: 30, refresh: 10 },
  },
  [p('Generic', 'Demi')]: {
    brand: 'Generic',
    line: 'Demi',
    category: 'demi',
    defaultMixRatio: '1:2',
    defaultDeveloper: 13,
    techniqueTimes: { toner: 20, refresh: 15 },
  },
  [p('Generic', 'Toner')]: {
    brand: 'Generic',
    line: 'Toner',
    category: 'toner',
    defaultMixRatio: '1:1.5',
    defaultDeveloper: 10,
    techniqueTimes: { toner: 20 },
  },
  [p('Generic', 'Highlift')]: {
    brand: 'Generic',
    line: 'Highlift',
    category: 'highlift',
    defaultMixRatio: '1:2',
    defaultDeveloper: 30,
    techniqueTimes: { highlift: 45 },
  },
};

// Plantillas por categoría
const TEMPLATE: Record<BrandPack['category'], Omit<BrandPack, 'brand' | 'line'>> = {
  permanent: {
    category: 'permanent',
    defaultMixRatio: '1:1',
    defaultDeveloper: 20,
    techniqueTimes: { fullColor: 35, retouch: 30, refresh: 10 },
    notes: [],
  },
  demi: {
    category: 'demi',
    defaultMixRatio: '1:2',
    defaultDeveloper: 13,
    techniqueTimes: { toner: 20, refresh: 15 },
    notes: [],
  },
  toner: {
    category: 'toner',
    defaultMixRatio: '1:1.5',
    defaultDeveloper: 10,
    techniqueTimes: { toner: 20 },
    notes: [],
  },
  highlift: {
    category: 'highlift',
    defaultMixRatio: '1:2',
    defaultDeveloper: 30,
    techniqueTimes: { highlift: 45 },
    notes: ['Hasta 4 niveles con highlift; revisar cada 10 min'],
  },
  direct: {
    category: 'direct',
    defaultMixRatio: 'sin oxidante',
    techniqueTimes: { fullColor: 30 },
    notes: ['Pigmento directo no oxidativo'],
  },
};

function detectCategory(lineName: string, catalogCategory?: string): BrandPack['category'] {
  const n = lineName.toLowerCase();
  if (catalogCategory === 'bleaching') return 'highlift';
  if (n.includes('elumen') || n.includes('finest pigments') || n.includes('direct'))
    return 'direct';
  if (
    n.includes('highlift') ||
    n.includes('highlifts') ||
    n.includes('ultra blond') ||
    n.includes('ultra blonde') ||
    n.includes('special blonde') ||
    n.includes('super')
  )
    return 'highlift';
  if (
    n.includes('toner') ||
    n.includes('gloss') ||
    n.includes('glaze') ||
    n.includes('shinefinity')
  )
    return 'toner';
  if (
    n.includes('touch') ||
    n.includes('vibrance') ||
    n.includes('dia') ||
    n.includes('color wear') ||
    n.includes('semi color') ||
    n.includes('semi') ||
    n.includes('liquid') ||
    n.includes('view')
  )
    return 'demi';
  return 'permanent';
}

function buildFromTemplate(
  brand: string,
  line: string,
  category: BrandPack['category']
): BrandPack {
  const tpl = TEMPLATE[category];
  return {
    brand,
    line,
    category: tpl.category,
    defaultMixRatio: tpl.defaultMixRatio,
    defaultDeveloper: tpl.defaultDeveloper,
    techniqueTimes: tpl.techniqueTimes,
    notes: tpl.notes,
  } as BrandPack;
}

function buildPacksFromCatalog(): BrandPacksIndex {
  const out: BrandPacksIndex = {};
  for (const b of professionalHairColorBrands) {
    for (const l of b.lines) {
      // Solo generar para líneas formulables
      const isColor = l.isColorLine || l.category === 'hair-color' || l.category === 'bleaching';
      if (!isColor) continue;
      const cat = detectCategory(l.name, l.category);
      const pack = buildFromTemplate(b.name, l.name, cat);
      const key = p(b.name, l.name);
      if (!out[key]) out[key] = pack;
    }
  }
  return out;
}

// Merge base + generados del catálogo
const GENERATED = buildPacksFromCatalog();
export const BRAND_PACKS: BrandPacksIndex = { ...GENERATED, ...BRAND_PACKS_BASE };

export function getBrandPack(brand?: string, line?: string) {
  const key = brand && line ? `${brand.toLowerCase().trim()}:${line.toLowerCase().trim()}` : '';
  if (key && BRAND_PACKS[key]) return BRAND_PACKS[key];
  // Intentar clasificar por datos de catálogo
  if (brand && line) {
    try {
      const b = professionalHairColorBrands.find(
        x => x.name.toLowerCase() === brand.toLowerCase().trim()
      );
      const l = b?.lines.find(x => x.name.toLowerCase() === line.toLowerCase().trim());
      if (l) {
        // Si es bleaching, devolver highlift/lightener genérico
        if (l.category === 'bleaching') return BRAND_PACKS[p('Generic', 'Highlift')];
        // Si no es color formulable, usar permanent genérico
        if (!l.isColorLine && l.category !== 'hair-color')
          return BRAND_PACKS[p('Generic', 'Permanent')];
      }
    } catch {
      // ignorar
    }
  }

  // Fallbacks por tokens en el nombre de la línea
  const lname = (line || '').toLowerCase();
  if (
    lname.includes('highlift') ||
    lname.includes('highlifts') ||
    lname.includes('ultra blond') ||
    lname.includes('ultra blonde') ||
    lname.includes('special blonde') ||
    lname.includes('super')
  )
    return BRAND_PACKS[p('Generic', 'Highlift')];
  if (
    lname.includes('touch') ||
    lname.includes('vibrance') ||
    lname.includes('dia') ||
    lname.includes('shinefinity') ||
    lname.includes('colorance') ||
    lname.includes('gloss') ||
    lname.includes('glaze') ||
    lname.includes('toner') ||
    lname.includes('semi') ||
    lname.includes('view') ||
    lname.includes('liquid')
  )
    return BRAND_PACKS[p('Generic', 'Demi')];
  if (lname.includes('elumen') || lname.includes('finest pigments') || lname.includes('direct')) {
    const direct: BrandPack = {
      brand: brand || 'Generic',
      line: line || 'Direct',
      category: 'direct',
      defaultMixRatio: 'sin oxidante',
      techniqueTimes: { fullColor: 30 },
      notes: ['Pigmento directo no oxidativo'],
    };
    return direct;
  }
  return BRAND_PACKS[p('Generic', 'Permanent')];
}
