import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import * as Haptics from 'expo-haptics';
import { Sparkles } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface TypingIndicatorProps {
  visible: boolean;
  message?: string;
  status?: 'idle' | 'thinking' | 'analyzing' | 'writing';
}

export default function TypingIndicator({
  visible,
  message,
  status = 'writing',
}: TypingIndicatorProps) {
  // Dynamic message based on status - IMPROVED WITH TIME ESTIMATES
  const getStatusMessage = () => {
    if (message) return message;
    switch (status) {
      case 'thinking':
        return 'Analizando tu consulta... (5-10 seg)';
      case 'analyzing':
        return 'Procesando información... (15-30 seg)';
      case 'writing':
        return 'Escribiendo respuesta...';
      default:
        return 'Salonier está escribiendo...';
    }
  };
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  const sparkleRotation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Premium fade in animation with haptic feedback
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();

      // Subtle haptic feedback when typing indicator appears
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Sparkle rotation animation
      Animated.loop(
        Animated.timing(sparkleRotation, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();

      // Premium wave-like dots animation
      const animateDots = () => {
        const waveAnimation = (animValue: Animated.Value, delay: number) => {
          return Animated.loop(
            Animated.sequence([
              Animated.delay(delay),
              Animated.timing(animValue, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.cubic), // Premium ease-out curve
                useNativeDriver: true,
              }),
              Animated.timing(animValue, {
                toValue: 0,
                duration: 600,
                easing: Easing.in(Easing.cubic), // Smooth ease-in for return
                useNativeDriver: true,
              }),
            ])
          );
        };

        Animated.parallel([
          waveAnimation(dot1Anim, 0),
          waveAnimation(dot2Anim, 150), // Staggered wave effect
          waveAnimation(dot3Anim, 300),
        ]).start();
      };

      animateDots();
    } else {
      // Premium fade out animation
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim, sparkleRotation, dot1Anim, dot2Anim, dot3Anim]);

  const sparkleRotationInterpolate = sparkleRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Premium wave-like dot animation with elastic bounce
  const getDotStyle = (animValue: Animated.Value) => ({
    transform: [
      {
        scale: animValue.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [0.6, 1.4, 0.8], // Elastic wave with overshoot
        }),
      },
      {
        translateY: animValue.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [0, -8, -2], // Higher wave peak with gentle settle
        }),
      },
    ],
    opacity: animValue.interpolate({
      inputRange: [0, 0.3, 1],
      outputRange: [0.3, 1, 0.7], // Smoother opacity transition
    }),
  });

  if (!visible) return null;

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.messageContainer}>
        <View style={styles.avatarContainer}>
          <Animated.View
            style={[
              styles.avatar,
              {
                transform: [{ rotate: sparkleRotationInterpolate }],
              },
            ]}
          >
            <Sparkles size={16} color={Colors.light.primary} />
          </Animated.View>
        </View>

        <View style={styles.bubbleContainer}>
          <View style={styles.bubble}>
            <View style={styles.textContainer}>
              <Text style={styles.message}>{getStatusMessage()}</Text>
            </View>

            <View style={styles.dotsContainer}>
              <Animated.View style={[styles.dot, getDotStyle(dot1Anim)]} />
              <Animated.View style={[styles.dot, getDotStyle(dot2Anim)]} />
              <Animated.View style={[styles.dot, getDotStyle(dot3Anim)]} />
            </View>
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    marginRight: spacing.sm,
    marginTop: spacing.xs,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bubbleContainer: {
    flex: 1,
    maxWidth: '80%',
  },
  bubble: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  textContainer: {
    marginBottom: spacing.sm,
  },
  message: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.2,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.primary,
    marginRight: spacing.xs,
    // Premium shadow for depth
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
});
