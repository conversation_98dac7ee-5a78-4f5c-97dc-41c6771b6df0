import { create } from 'zustand';
import { PhotoQuality } from '@/types/photo-capture';

interface CapturedPhotoData {
  uri: string;
  quality: PhotoQuality;
  photoType?: number;
}

interface PhotoCaptureStore {
  pendingPhoto: CapturedPhotoData | null;
  setPendingPhoto: (photo: CapturedPhotoData | null) => void;
  clearPendingPhoto: () => void;
}

export const usePhotoCaptureStore = create<PhotoCaptureStore>(set => ({
  pendingPhoto: null,

  setPendingPhoto: photo => set({ pendingPhoto: photo }),

  clearPendingPhoto: () => set({ pendingPhoto: null }),
}));
