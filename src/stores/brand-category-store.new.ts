import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Product, StockMovement } from '@/types/inventory';
import { brandInventoryIntegration } from '@/services/brandInventoryIntegration';
import { logger } from '@/utils/logger';

// Brand and category management types
export interface BrandInfo {
  name: string;
  lines: string[];
  productCount: number;
  totalValue: number;
}

export interface CategoryInfo {
  name: Product['category'];
  displayName: string;
  productCount: number;
  totalValue: number;
}

export interface ProductGroup {
  key: string;
  displayName: string;
  products: Product[];
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
}

export interface ActiveFilters {
  stockStatus: 'all' | 'low' | 'out' | 'ok';
  categories: string[];
  brands: string[];
  searchQuery: string;
}

export type SortBy = 'name' | 'stock' | 'usage' | 'price' | 'brand';
export type GroupBy = 'none' | 'brand' | 'line' | 'category' | 'type';

interface BrandCategoryStore {
  // Filter and Grouping State
  activeFilters: ActiveFilters;
  sortBy: SortBy;
  groupBy: GroupBy;

  // Filter and Grouping Actions
  setFilter: (
    filterType: 'stockStatus' | 'categories' | 'brands' | 'searchQuery',
    value: string | string[] | 'all' | 'low' | 'out' | 'ok'
  ) => void;
  setSortBy: (sortBy: SortBy) => void;
  setGroupBy: (groupBy: GroupBy) => void;
  resetFilters: () => void;

  // Product Organization Methods
  getFilteredAndSortedProducts: (products: Product[], movements?: StockMovement[]) => Product[];
  getGroupedProducts: (products: Product[], movements?: StockMovement[]) => Map<string, Product[]>;

  // Brand and Category Analysis
  getAllBrands: (products: Product[]) => BrandInfo[];
  getAllCategories: (products: Product[]) => CategoryInfo[];
  getBrandLines: (products: Product[], brandName: string) => string[];
  getProductsByBrand: (products: Product[], brandName: string) => Product[];
  getProductsByCategory: (products: Product[], category: Product['category']) => Product[];
  getProductsByLine: (products: Product[], brandName: string, lineName: string) => Product[];

  // Utility Methods
  getFrequentlyUsedProducts: (
    products: Product[],
    movements: StockMovement[],
    limit?: number
  ) => Product[];
  searchProducts: (products: Product[], query: string) => Product[];

  // Enhanced Brand Integration
  getValidatedBrands: (products: Product[]) => Promise<BrandInfo[]>;
  getBrandSuggestions: (
    query: string
  ) => Promise<Array<{ id: string; name: string; type: 'brand' }>>;
  getLineSuggestions: (
    brandName: string,
    query?: string
  ) => Promise<Array<{ id: string; name: string; type: 'line'; category?: string }>>;
  validateBrandData: (products: Product[]) => Promise<{
    validCount: number;
    invalidCount: number;
    suggestions: Array<{
      productId: string;
      currentBrand: string;
      suggestedBrand: string;
      confidence: number;
    }>;
  }>;

  // Hierarchical Structure
  getProductHierarchy: (products: Product[]) => Map<string, Map<string, Map<string, Product[]>>>;
}

// Type translations
const typeEnglishToSpanish: Record<string, string> = {
  purchase: 'compra',
  use: 'consumo',
  adjustment: 'ajuste',
  return: 'devolución',
  waste: 'pérdida',
};

const typeSpanishToEnglish: Record<string, string> = {
  compra: 'purchase',
  consumo: 'use',
  ajuste: 'adjustment',
  devolución: 'return',
  pérdida: 'waste',
  inventario_inicial: 'purchase',
  entrada: 'purchase',
  salida: 'use',
};

// Category display names
const categoryDisplayNames: Record<Product['category'], string> = {
  tinte: 'Tintes',
  oxidante: 'Oxidantes',
  decolorante: 'Decolorantes',
  matizador: 'Matizadores',
  tratamiento: 'Tratamientos',
  aditivo: 'Aditivos',
  'pre-pigmentacion': 'Pre-pigmentación',
  otro: 'Otros',
};

export const useBrandCategoryStore = create<BrandCategoryStore>()(
  persist(
    (set, get) => ({
      // Initialize filter and grouping state
      activeFilters: {
        stockStatus: 'all',
        categories: [],
        brands: [],
        searchQuery: '',
      },
      sortBy: 'name',
      groupBy: 'none',

      // Filter Actions
      setFilter: (filterType, value) => {
        set(state => ({
          activeFilters: {
            ...state.activeFilters,
            [filterType]: value,
          },
        }));
      },

      setSortBy: sortBy => {
        set({ sortBy });
      },

      setGroupBy: groupBy => {
        set({ groupBy });
      },

      resetFilters: () => {
        set({
          activeFilters: {
            stockStatus: 'all',
            categories: [],
            brands: [],
            searchQuery: '',
          },
          sortBy: 'name',
          groupBy: 'none',
        });
      },

      // Core filtering and sorting logic
      getFilteredAndSortedProducts: (products, movements = []) => {
        const state = get();
        let filtered = [...products];

        // Apply search filter
        if (state.activeFilters.searchQuery) {
          const query = state.activeFilters.searchQuery.toLowerCase();
          filtered = filtered.filter(
            p =>
              p.name?.toLowerCase().includes(query) ||
              p.displayName?.toLowerCase().includes(query) ||
              p.brand?.toLowerCase().includes(query) ||
              p.line?.toLowerCase().includes(query) ||
              p.shade?.toLowerCase().includes(query) ||
              p.type?.toLowerCase().includes(query)
          );
        }

        // Apply stock status filter
        if (state.activeFilters.stockStatus !== 'all') {
          filtered = filtered.filter(p => {
            switch (state.activeFilters.stockStatus) {
              case 'low':
                return p.currentStock <= p.minStock && p.currentStock > 0;
              case 'out':
                return p.currentStock === 0;
              case 'ok':
                return p.currentStock > p.minStock;
              default:
                return true;
            }
          });
        }

        // Apply category filter
        if (state.activeFilters.categories.length > 0) {
          filtered = filtered.filter(p => state.activeFilters.categories.includes(p.category));
        }

        // Apply brand filter
        if (state.activeFilters.brands.length > 0) {
          filtered = filtered.filter(p => p.brand && state.activeFilters.brands.includes(p.brand));
        }

        // Sort products
        filtered.sort((a, b) => {
          switch (state.sortBy) {
            case 'name':
              return (a.displayName || a.name || '').localeCompare(b.displayName || b.name || '');
            case 'stock':
              return a.currentStock - b.currentStock;
            case 'price':
              return a.costPerUnit - b.costPerUnit;
            case 'brand':
              return (a.brand || '').localeCompare(b.brand || '');
            case 'usage': {
              // Count movements for each product in the last 30 days
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

              const aUsage = movements.filter(
                m => m.productId === a.id && new Date(m.date) > thirtyDaysAgo
              ).length;
              const bUsage = movements.filter(
                m => m.productId === b.id && new Date(m.date) > thirtyDaysAgo
              ).length;
              return bUsage - aUsage;
            }
            default:
              return 0;
          }
        });

        return filtered;
      },

      // Grouping logic
      getGroupedProducts: (products, movements = []) => {
        const state = get();
        const filtered = get().getFilteredAndSortedProducts(products, movements);
        const grouped = new Map<string, Product[]>();

        if (state.groupBy === 'none') {
          grouped.set('Todos los productos', filtered);
          return grouped;
        }

        filtered.forEach(product => {
          let groupKey = 'Sin categoría';

          switch (state.groupBy) {
            case 'brand':
              groupKey = product.brand || 'Sin marca';
              break;
            case 'line':
              groupKey = product.line || 'Sin línea';
              break;
            case 'category':
              groupKey = categoryDisplayNames[product.category] || 'Otros';
              break;
            case 'type':
              groupKey = product.type || 'Sin tipo';
              break;
          }

          if (!grouped.has(groupKey)) {
            grouped.set(groupKey, []);
          }
          grouped.get(groupKey)!.push(product);
        });

        // Sort groups alphabetically
        return new Map([...grouped.entries()].sort((a, b) => a[0].localeCompare(b[0])));
      },

      // Enhanced Brand analysis with database integration
      getAllBrands: products => {
        const brandMap = new Map<string, BrandInfo>();

        products.forEach(product => {
          if (!product.brand) return;

          const existing = brandMap.get(product.brand) || {
            name: product.brand,
            lines: [],
            productCount: 0,
            totalValue: 0,
          };

          // Add unique lines
          if (product.line && !existing.lines.includes(product.line)) {
            existing.lines.push(product.line);
          }

          existing.productCount += 1;
          existing.totalValue += product.currentStock * product.costPerUnit;

          brandMap.set(product.brand, existing);
        });

        return Array.from(brandMap.values()).sort((a, b) => a.name.localeCompare(b.name));
      },

      // Enhanced brand methods with database validation
      getValidatedBrands: async (products: Product[]) => {
        const brandMap = new Map<string, BrandInfo>();
        const brandLogger = logger.withContext('BrandCategoryStore');

        try {
          // Get all brands with validation
          const brandValidationPromises = products
            .filter(product => product.brand)
            .map(async product => {
              try {
                const validation = await brandInventoryIntegration.validateProduct(product);
                return {
                  product,
                  validation,
                  normalizedBrand: validation.brandMatch?.brandName || product.brand,
                };
              } catch (error) {
                brandLogger.warn('Brand validation failed for product', {
                  productId: product.id,
                  error,
                });
                return {
                  product,
                  validation: null,
                  normalizedBrand: product.brand,
                };
              }
            });

          const validatedProducts = await Promise.all(brandValidationPromises);

          // Build brand map with validated data
          validatedProducts.forEach(({ product, normalizedBrand }) => {
            const existing = brandMap.get(normalizedBrand) || {
              name: normalizedBrand,
              lines: [],
              productCount: 0,
              totalValue: 0,
            };

            // Add unique lines
            if (product.line && !existing.lines.includes(product.line)) {
              existing.lines.push(product.line);
            }

            existing.productCount += 1;
            existing.totalValue += product.currentStock * product.costPerUnit;

            brandMap.set(normalizedBrand, existing);
          });

          return Array.from(brandMap.values()).sort((a, b) => a.name.localeCompare(b.name));
        } catch (error) {
          brandLogger.error('Error in getValidatedBrands, falling back to standard method', {
            error,
          });
          return get().getAllBrands(products);
        }
      },

      getBrandSuggestions: async (query: string) => {
        try {
          return await brandInventoryIntegration.getBrandAutocomplete(query);
        } catch (error) {
          logger
            .withContext('BrandCategoryStore')
            .error('Error getting brand suggestions', { error });
          return [];
        }
      },

      getLineSuggestions: async (brandName: string, query = '') => {
        try {
          return await brandInventoryIntegration.getLineAutocomplete(brandName, query);
        } catch (error) {
          logger
            .withContext('BrandCategoryStore')
            .error('Error getting line suggestions', { error });
          return [];
        }
      },

      validateBrandData: async (products: Product[]) => {
        const brandLogger = logger.withContext('BrandCategoryStore');
        let validCount = 0;
        let invalidCount = 0;
        const suggestions: Array<{
          productId: string;
          currentBrand: string;
          suggestedBrand: string;
          confidence: number;
        }> = [];

        try {
          const validationPromises = products
            .filter(product => product.brand)
            .map(async product => {
              try {
                const validation = await brandInventoryIntegration.validateProduct(product);

                if (validation.isValid) {
                  validCount++;
                } else {
                  invalidCount++;

                  // Add suggestions for invalid brands
                  if (validation.brandMatch && validation.brandMatch.confidence > 70) {
                    suggestions.push({
                      productId: product.id,
                      currentBrand: product.brand!,
                      suggestedBrand: validation.brandMatch.brandName,
                      confidence: validation.brandMatch.confidence,
                    });
                  }
                }
              } catch (error) {
                brandLogger.warn('Validation failed for product', { productId: product.id, error });
                invalidCount++;
              }
            });

          await Promise.all(validationPromises);

          return {
            validCount,
            invalidCount,
            suggestions: suggestions.sort((a, b) => b.confidence - a.confidence),
          };
        } catch (error) {
          brandLogger.error('Error in validateBrandData', { error });
          return {
            validCount: products.length,
            invalidCount: 0,
            suggestions: [],
          };
        }
      },

      // Category analysis
      getAllCategories: products => {
        const categoryMap = new Map<Product['category'], CategoryInfo>();

        products.forEach(product => {
          const existing = categoryMap.get(product.category) || {
            name: product.category,
            displayName: categoryDisplayNames[product.category] || product.category,
            productCount: 0,
            totalValue: 0,
          };

          existing.productCount += 1;
          existing.totalValue += product.currentStock * product.costPerUnit;

          categoryMap.set(product.category, existing);
        });

        return Array.from(categoryMap.values()).sort((a, b) =>
          a.displayName.localeCompare(b.displayName)
        );
      },

      // Brand-specific methods
      getBrandLines: (products, brandName) => {
        const lines = new Set<string>();
        products.forEach(product => {
          if (product.brand === brandName && product.line) {
            lines.add(product.line);
          }
        });
        return Array.from(lines).sort();
      },

      getProductsByBrand: (products, brandName) => {
        return products.filter(p => p.brand === brandName);
      },

      getProductsByCategory: (products, category) => {
        return products.filter(p => p.category === category);
      },

      getProductsByLine: (products, brandName, lineName) => {
        return products.filter(p => p.brand === brandName && p.line === lineName);
      },

      // Frequently used products logic
      getFrequentlyUsedProducts: (products, movements, limit = 10) => {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        // Count usage for each product
        const usageMap = new Map<string, number>();
        movements.forEach(movement => {
          if (
            new Date(movement.date) > thirtyDaysAgo &&
            (movement.type === 'consumo' || movement.type === 'use')
          ) {
            const count = usageMap.get(movement.productId) || 0;
            usageMap.set(movement.productId, count + 1);
          }
        });

        // Sort products by usage
        const productsWithUsage = products
          .map(product => ({
            product,
            usage: usageMap.get(product.id) || 0,
          }))
          .filter(item => item.usage > 0)
          .sort((a, b) => b.usage - a.usage)
          .slice(0, limit)
          .map(item => item.product);

        return productsWithUsage;
      },

      // Enhanced search functionality with brand intelligence
      searchProducts: (products, query) => {
        const searchTerms = query.toLowerCase().split(' ');

        return products.filter(product => {
          const searchableText = [
            product.name || '',
            product.displayName || '',
            product.brand || '',
            product.line || '',
            product.shade || '',
            product.type || '',
            product.category || '',
          ]
            .join(' ')
            .toLowerCase();

          // Standard term matching
          const standardMatch = searchTerms.every(term => searchableText.includes(term));

          if (standardMatch) return true;

          // Enhanced brand-aware search for partial matches
          try {
            // Check if any search term could be a brand variation
            const brandTerm = searchTerms.find(term => term.length > 2);
            if (brandTerm && product.brand) {
              // Use the normalization logic from brand integration
              const normalizedProductBrand = product.brand
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .trim();
              const normalizedSearchTerm = brandTerm.replace(/[^a-z0-9\s]/g, '').trim();

              if (
                normalizedProductBrand.includes(normalizedSearchTerm) ||
                normalizedSearchTerm.includes(normalizedProductBrand)
              ) {
                return true;
              }

              // Check common brand abbreviations
              const brandAbbreviations: Record<string, string[]> = {
                loreal: ['lor', 'lp'],
                schwarzkopf: ['schwarz', 'skp'],
                wella: ['wel'],
                matrix: ['mtx'],
                redken: ['rdk'],
                salerm: ['sal'],
              };

              const productBrandKey = Object.keys(brandAbbreviations).find(key =>
                normalizedProductBrand.includes(key)
              );

              if (productBrandKey) {
                const abbreviations = brandAbbreviations[productBrandKey];
                if (abbreviations.some(abbr => normalizedSearchTerm.includes(abbr))) {
                  return true;
                }
              }
            }
          } catch (error) {
            // If enhanced search fails, fall back to standard matching
            logger.withContext('BrandCategoryStore').warn('Enhanced search failed', { error });
          }

          return false;
        });
      },

      // Hierarchical product structure: Brand -> Line -> Type -> Products
      getProductHierarchy: products => {
        const hierarchy = new Map<string, Map<string, Map<string, Product[]>>>();

        products.forEach(product => {
          const brand = product.brand || 'Sin marca';
          const line = product.line || 'Sin línea';
          const type = product.type || 'Sin tipo';

          if (!hierarchy.has(brand)) {
            hierarchy.set(brand, new Map());
          }

          const brandMap = hierarchy.get(brand)!;
          if (!brandMap.has(line)) {
            brandMap.set(line, new Map());
          }

          const lineMap = brandMap.get(line)!;
          if (!lineMap.has(type)) {
            lineMap.set(type, []);
          }

          lineMap.get(type)!.push(product);
        });

        return hierarchy;
      },
    }),
    {
      name: 'brand-category-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        // Only persist filter preferences, not the product data itself
        activeFilters: state.activeFilters,
        sortBy: state.sortBy,
        groupBy: state.groupBy,
      }),
    }
  )
);

// Export type translation utilities for backward compatibility
export { typeEnglishToSpanish, typeSpanishToEnglish, categoryDisplayNames };

// Helper functions for external use
export class ProductOrganizer {
  /**
   * Get products with stock status indicators
   */
  static getProductsWithStockStatus(products: Product[]) {
    return products.map(product => ({
      ...product,
      stockStatus:
        product.currentStock === 0
          ? 'out'
          : product.currentStock <= product.minStock
            ? 'low'
            : product.maxStock && product.currentStock > product.maxStock
              ? 'overstock'
              : 'ok',
    }));
  }

  /**
   * Calculate inventory value by brand
   */
  static getInventoryValueByBrand(products: Product[]) {
    const brandValues = new Map<string, number>();

    products.forEach(product => {
      const brand = product.brand || 'Sin marca';
      const value = product.currentStock * product.costPerUnit;
      brandValues.set(brand, (brandValues.get(brand) || 0) + value);
    });

    return Array.from(brandValues.entries())
      .map(([brand, value]) => ({ brand, value }))
      .sort((a, b) => b.value - a.value);
  }

  /**
   * Calculate inventory value by category
   */
  static getInventoryValueByCategory(products: Product[]) {
    const categoryValues = new Map<Product['category'], number>();

    products.forEach(product => {
      const value = product.currentStock * product.costPerUnit;
      categoryValues.set(product.category, (categoryValues.get(product.category) || 0) + value);
    });

    return Array.from(categoryValues.entries())
      .map(([category, value]) => ({
        category,
        displayName: categoryDisplayNames[category] || category,
        value,
      }))
      .sort((a, b) => b.value - a.value);
  }

  /**
   * Get products that need attention (low stock, out of stock, overstock)
   */
  static getProductsNeedingAttention(products: Product[]) {
    return {
      lowStock: products.filter(p => p.currentStock <= p.minStock && p.currentStock > 0),
      outOfStock: products.filter(p => p.currentStock === 0),
      overstock: products.filter(p => p.maxStock && p.currentStock > p.maxStock),
    };
  }

  /**
   * Generate product suggestions based on usage patterns
   */
  static getProductSuggestions(products: Product[], movements: StockMovement[]) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Find products with high usage and low stock
    const suggestions: Array<{
      product: Product;
      reason: 'high_usage_low_stock' | 'frequently_used' | 'alternative_needed';
      priority: 'high' | 'medium' | 'low';
    }> = [];

    const usageMap = new Map<string, number>();
    movements.forEach(movement => {
      if (
        new Date(movement.date) > thirtyDaysAgo &&
        (movement.type === 'consumo' || movement.type === 'use')
      ) {
        usageMap.set(movement.productId, (usageMap.get(movement.productId) || 0) + 1);
      }
    });

    products.forEach(product => {
      const usage = usageMap.get(product.id) || 0;

      // High usage + low stock = high priority
      if (usage >= 5 && product.currentStock <= product.minStock) {
        suggestions.push({
          product,
          reason: 'high_usage_low_stock',
          priority: 'high',
        });
      }
      // Frequently used products
      else if (usage >= 10) {
        suggestions.push({
          product,
          reason: 'frequently_used',
          priority: 'medium',
        });
      }
      // Out of stock products
      else if (product.currentStock === 0 && usage > 0) {
        suggestions.push({
          product,
          reason: 'alternative_needed',
          priority: 'high',
        });
      }
    });

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }
}
