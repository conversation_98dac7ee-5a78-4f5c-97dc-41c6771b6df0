import React, { useState } from 'react';
import {
  Modal,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { X, DollarSign, Percent, Hash, ChevronDown } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { PricingConfiguration } from '@/types/inventory';
import { commonStyles } from '@/styles/commonStyles';

interface PricingSettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function PricingSettingsModal({ visible, onClose }: PricingSettingsModalProps) {
  const { configuration, updatePricing } = useSalonConfigStore();

  const [markupPercentage, setMarkupPercentage] = useState(
    configuration.pricing.defaultMarkupPercentage.toString()
  );
  const [roundingPolicy, setRoundingPolicy] = useState(configuration.pricing.roundingPolicy);
  const [roundingIncrement, setRoundingIncrement] = useState(
    configuration.pricing.roundingIncrement.toString()
  );
  const [minimumServicePrice, setMinimumServicePrice] = useState(
    configuration.pricing.minimumServicePrice.toString()
  );
  const [includeTaxInPrice, setIncludeTaxInPrice] = useState(
    configuration.pricing.includeTaxInPrice
  );
  const [taxPercentage, setTaxPercentage] = useState(
    configuration.pricing.taxPercentage.toString()
  );
  const [showRoundingPicker, setShowRoundingPicker] = useState(false);
  const [showIncrementPicker, setShowIncrementPicker] = useState(false);

  const roundingOptions: Array<{
    value: PricingConfiguration['roundingPolicy'];
    label: string;
    description: string;
  }> = [
    {
      value: 'none',
      label: 'Sin redondeo',
      description: 'Precio exacto calculado',
    },
    {
      value: 'nearest',
      label: 'Más cercano',
      description: 'Al incremento más cercano',
    },
    {
      value: 'up',
      label: 'Hacia arriba',
      description: 'Siempre al siguiente incremento',
    },
    {
      value: 'down',
      label: 'Hacia abajo',
      description: 'Siempre al incremento anterior',
    },
  ];

  const incrementOptions = [
    { value: '0.5', label: '0.50' },
    { value: '1', label: '1.00' },
    { value: '5', label: '5.00' },
    { value: '10', label: '10.00' },
  ];

  const validateAndSave = () => {
    const markup = parseFloat(markupPercentage);
    const minPrice = parseFloat(minimumServicePrice);
    const tax = parseFloat(taxPercentage);
    const increment = parseFloat(roundingIncrement);

    if (isNaN(markup) || markup < 0 || markup > 500) {
      Alert.alert('Error', 'El margen debe estar entre 0% y 500%');
      return;
    }

    if (isNaN(minPrice) || minPrice < 0) {
      Alert.alert('Error', 'El precio mínimo debe ser mayor o igual a 0');
      return;
    }

    if (isNaN(tax) || tax < 0 || tax > 100) {
      Alert.alert('Error', 'El impuesto debe estar entre 0% y 100%');
      return;
    }

    const newConfig: PricingConfiguration = {
      ...configuration.pricing,
      defaultMarkupPercentage: markup,
      roundingPolicy,
      roundingIncrement: increment,
      minimumServicePrice: minPrice,
      includeTaxInPrice,
      taxPercentage: tax,
      lastUpdated: new Date().toISOString(),
    };

    updatePricing(newConfig);
    Alert.alert('Éxito', 'Configuración de precios actualizada', [
      { text: 'OK', onPress: onClose },
    ]);
  };

  const calculateExamplePrice = (basePrice: number) => {
    const markup = parseFloat(markupPercentage) || 0;
    const priceWithMarkup = basePrice * (1 + markup / 100);

    let finalPrice = priceWithMarkup;
    if (includeTaxInPrice) {
      const tax = parseFloat(taxPercentage) || 0;
      finalPrice = priceWithMarkup * (1 + tax / 100);
    }

    // Apply rounding
    const increment = parseFloat(roundingIncrement) || 1;
    switch (roundingPolicy) {
      case 'nearest':
        finalPrice = Math.round(finalPrice / increment) * increment;
        break;
      case 'up':
        finalPrice = Math.ceil(finalPrice / increment) * increment;
        break;
      case 'down':
        finalPrice = Math.floor(finalPrice / increment) * increment;
        break;
    }

    // Apply minimum price
    const minPrice = parseFloat(minimumServicePrice) || 0;
    finalPrice = Math.max(finalPrice, minPrice);

    return finalPrice;
  };

  return (
    <Modal visible={visible} animationType="slide" transparent onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.backdrop}>
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <View style={styles.headerLeft}>
                <DollarSign size={24} color={Colors.light.primary} />
                <Text style={styles.headerTitle}>Configuración de Precios</Text>
              </View>
              <TouchableOpacity onPress={onClose}>
                <X size={24} color={Colors.light.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.body}>
              {/* Markup Section */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Percent size={20} color={Colors.light.primary} />
                  <Text style={styles.sectionTitle}>Margen de Ganancia</Text>
                </View>
                <View style={styles.inputGroup}>
                  <TextInput
                    style={styles.input}
                    placeholder="0"
                    value={markupPercentage}
                    onChangeText={setMarkupPercentage}
                    keyboardType="decimal-pad"
                  />
                  <Text style={styles.inputSuffix}>%</Text>
                </View>
                <Text style={styles.helperText}>
                  Margen por defecto aplicado a los costos (0-500%)
                </Text>
              </View>

              {/* Rounding Section */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Hash size={20} color={Colors.light.primary} />
                  <Text style={styles.sectionTitle}>Redondeo de Precios</Text>
                </View>

                <Text style={styles.label}>Política de Redondeo</Text>
                <TouchableOpacity
                  style={styles.picker}
                  onPress={() => setShowRoundingPicker(!showRoundingPicker)}
                >
                  <View>
                    <Text style={styles.pickerValue}>
                      {roundingOptions.find(o => o.value === roundingPolicy)?.label}
                    </Text>
                    <Text style={styles.pickerDescription}>
                      {roundingOptions.find(o => o.value === roundingPolicy)?.description}
                    </Text>
                  </View>
                  <ChevronDown size={20} color={Colors.light.gray} />
                </TouchableOpacity>

                {showRoundingPicker && (
                  <View style={styles.pickerOptions}>
                    {roundingOptions.map(option => (
                      <TouchableOpacity
                        key={option.value}
                        style={styles.pickerOption}
                        onPress={() => {
                          setRoundingPolicy(option.value);
                          setShowRoundingPicker(false);
                        }}
                      >
                        <View>
                          <Text
                            style={[
                              styles.pickerOptionText,
                              roundingPolicy === option.value && styles.pickerOptionTextSelected,
                            ]}
                          >
                            {option.label}
                          </Text>
                          <Text style={styles.pickerOptionDescription}>{option.description}</Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}

                {roundingPolicy !== 'none' && (
                  <>
                    <Text style={[styles.label, commonStyles.marginTop15]}>
                      Incremento de Redondeo
                    </Text>
                    <TouchableOpacity
                      style={styles.picker}
                      onPress={() => setShowIncrementPicker(!showIncrementPicker)}
                    >
                      <Text style={styles.pickerValue}>
                        {configuration.pricing.currencySymbol}
                        {roundingIncrement}
                      </Text>
                      <ChevronDown size={20} color={Colors.light.gray} />
                    </TouchableOpacity>

                    {showIncrementPicker && (
                      <View style={styles.pickerOptions}>
                        {incrementOptions.map(option => (
                          <TouchableOpacity
                            key={option.value}
                            style={styles.pickerOption}
                            onPress={() => {
                              setRoundingIncrement(option.value);
                              setShowIncrementPicker(false);
                            }}
                          >
                            <Text
                              style={[
                                styles.pickerOptionText,
                                roundingIncrement === option.value &&
                                  styles.pickerOptionTextSelected,
                              ]}
                            >
                              {configuration.pricing.currencySymbol}
                              {option.label}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}
                  </>
                )}
              </View>

              {/* Minimum Price Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Precio Mínimo por Servicio</Text>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputPrefix}>{configuration.pricing.currencySymbol}</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="0"
                    value={minimumServicePrice}
                    onChangeText={setMinimumServicePrice}
                    keyboardType="decimal-pad"
                  />
                </View>
                <Text style={styles.helperText}>
                  Ningún servicio se cobrará por debajo de este precio
                </Text>
              </View>

              {/* Tax Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Configuración de Impuestos</Text>
                <View style={styles.switchRow}>
                  <View style={styles.switchInfo}>
                    <Text style={styles.switchLabel}>Incluir impuestos en precio</Text>
                    <Text style={styles.switchDescription}>
                      Los precios mostrados incluirán los impuestos
                    </Text>
                  </View>
                  <Switch
                    value={includeTaxInPrice}
                    onValueChange={setIncludeTaxInPrice}
                    trackColor={{
                      false: Colors.light.lightGray,
                      true: Colors.light.primary,
                    }}
                  />
                </View>
                {includeTaxInPrice && (
                  <View style={[styles.inputGroup, commonStyles.marginTop10]}>
                    <TextInput
                      style={styles.input}
                      placeholder="0"
                      value={taxPercentage}
                      onChangeText={setTaxPercentage}
                      keyboardType="decimal-pad"
                    />
                    <Text style={styles.inputSuffix}>%</Text>
                  </View>
                )}
              </View>

              {/* Example Calculation */}
              <View style={styles.exampleSection}>
                <Text style={styles.exampleTitle}>Ejemplo de Cálculo</Text>
                <View style={styles.exampleRow}>
                  <Text style={styles.exampleLabel}>Costo base:</Text>
                  <Text style={styles.exampleValue}>
                    {configuration.pricing.currencySymbol}10.00
                  </Text>
                </View>
                <View style={styles.exampleRow}>
                  <Text style={styles.exampleLabel}>Con margen ({markupPercentage}%):</Text>
                  <Text style={styles.exampleValue}>
                    {configuration.pricing.currencySymbol}
                    {(10 * (1 + (parseFloat(markupPercentage) || 0) / 100)).toFixed(2)}
                  </Text>
                </View>
                <View style={[styles.exampleRow, styles.exampleTotal]}>
                  <Text style={styles.exampleLabel}>Precio final:</Text>
                  <Text style={styles.exampleTotalValue}>
                    {configuration.pricing.currencySymbol}
                    {calculateExamplePrice(10).toFixed(2)}
                  </Text>
                </View>
              </View>
            </ScrollView>

            <View style={styles.footer}>
              <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.saveButton} onPress={validateAndSave}>
                <Text style={styles.saveButtonText}>Guardar Cambios</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backdrop: {
    flex: 1,
    backgroundColor: Colors.common.shadowColor + '80', // 0.5 opacity
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  body: {
    padding: 20,
  },
  section: {
    marginBottom: 25,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: Colors.light.text,
  },
  inputGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputPrefix: {
    fontSize: 16,
    color: Colors.light.gray,
    marginRight: 5,
  },
  inputSuffix: {
    fontSize: 16,
    color: Colors.light.gray,
    marginLeft: 5,
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 5,
  },
  picker: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    padding: 12,
  },
  pickerValue: {
    fontSize: 16,
    color: Colors.light.text,
  },
  pickerDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  pickerOptions: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    marginTop: 5,
    overflow: 'hidden',
  },
  pickerOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  pickerOptionDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchInfo: {
    flex: 1,
    marginRight: 10,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  switchDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  exampleSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  exampleTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 10,
  },
  exampleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  exampleLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  exampleValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  exampleTotal: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingTop: 5,
    marginTop: 5,
  },
  exampleTotalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  footer: {
    flexDirection: 'row',
    gap: 10,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  saveButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
});
