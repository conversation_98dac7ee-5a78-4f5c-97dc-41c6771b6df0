import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { useSyncQueueStore, generateLocalId } from '@/stores/sync-queue-store';
import { useAuthStore } from '@/stores/auth-store';
import { logger } from '@/utils/logger';
import {
  extractBrandLine,
  evaluateCatalogStatusIfNeeded,
  convertSupabaseServiceToFormula,
  convertSupabaseConsentToLocal,
} from '@/utils/clientHistoryUtils';
import {
  ServiceDetails,
  ClientHistoryProfile,
  PreviousFormula,
  ConsentRecord,
  SupabaseConsent,
} from '@/types/client-history.types';

const historyLogger = logger.withContext('ClientHistoryService');

async function syncRecord<T>(
  recordType: string,
  data: Record<string, unknown>,
  syncOperation: () => Promise<T>
): Promise<T | null> {
  historyLogger.startTimer(`sync${recordType}`);
  const { isOnline } = useSyncQueueStore.getState();

  if (isOnline) {
    try {
      const result = await syncOperation();
      historyLogger.endTimer(`sync${recordType}`);
      return result;
    } catch (error) {
      historyLogger.error(`Error syncing ${recordType}:`, error);
      useSyncQueueStore.getState().addToQueue({
        type: 'create',
        table: recordType.toLowerCase(),
        data,
      });
      return null;
    }
  } else {
    useSyncQueueStore.getState().addToQueue({
      type: 'create',
      table: recordType.toLowerCase(),
      data,
    });
    return null;
  }
}

export const clientHistoryService = {
  async loadClientHistory(clientId: string): Promise<Partial<ClientHistoryProfile> | null> {
    historyLogger.startTimer('loadClientHistory');
    historyLogger.info('Loading client history', { clientId });

    const salonId = await getCurrentSalonId();
    if (!salonId || !clientId) {
      historyLogger.warn('Missing salon ID or client ID', { salonId, clientId });
      return null;
    }

    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('*, formulas(*)')
      .eq('client_id', clientId)
      .eq('salon_id', salonId)
      .order('service_date', { ascending: false });

    if (servicesError) throw servicesError;

    const { data: consents, error: consentsError } = await supabase
      .from('client_consents')
      .select('*')
      .eq('client_id', clientId)
      .eq('salon_id', salonId)
      .order('created_at', { ascending: false });

    if (consentsError) throw consentsError;

    const previousFormulas =
      (services
        ?.map(service => convertSupabaseServiceToFormula(service, service.formulas?.[0]))
        .filter(Boolean) as PreviousFormula[]) || [];

    const consentRecords = consents?.map(convertSupabaseConsentToLocal) || [];

    const satisfactionScores =
      services?.map(s => s.satisfaction_score).filter(s => s !== null) || [];
    const averageSatisfaction =
      satisfactionScores.length > 0
        ? satisfactionScores.reduce((a, b) => a! + b!, 0)! / satisfactionScores.length
        : 0;

    historyLogger.endTimer('loadClientHistory');

    return {
      previousFormulas,
      consentRecords,
      lastAnalysisDate: services?.[0]?.service_date,
      totalServices: services?.length || 0,
      averageSatisfaction,
    };
  },

  async saveCompletedService(serviceData: Record<string, unknown>): Promise<ServiceDetails> {
    historyLogger.info('Saving completed service', { clientId: serviceData.clientId });

    const salonId = await getCurrentSalonId();
    const userId = useAuthStore.getState().user?.id;
    if (!salonId || !userId) {
      throw new Error('No salon ID or user ID available');
    }

    const { data: service, error: serviceError } = await supabase
      .from('services')
      .insert({
        salon_id: salonId,
        client_id: serviceData.clientId,
        stylist_id: userId,
        service_type: serviceData.serviceType || 'color_service',
        status: 'completed',
        service_date: new Date().toISOString(),
        notes: serviceData.notes || '',
        before_photos: serviceData.beforePhotos || [],
        after_photos: serviceData.afterPhotos || [],
        ai_analysis: serviceData.aiAnalysis || null,
        satisfaction_score: serviceData.satisfaction || null,
      })
      .select()
      .single();

    if (serviceError) throw serviceError;

    let saveMeta: { skippedFormula?: boolean; duplicateOf?: string | null } = {};
    if (serviceData.formula && service) {
      try {
        let contentHash: string | null = null;
        try {
          const { data: hashRes, error: hashErr } = await supabase.rpc(
            'calc_formula_content_hash',
            {
              p_formula: (serviceData.formulaData || {}) as any,
            }
          );
          if (hashErr) throw hashErr;
          contentHash = (hashRes as any) || null;
        } catch (e) {
          historyLogger.warn('Could not compute content hash', e as any);
        }

        const scenarioHash: string | null =
          ((serviceData.formulaData as any)?.scenarioHash as string) || null;

        let duplicateOf: string | null = null;
        if (contentHash) {
          const { data: dup, error: dupErr } = await supabase
            .from('formulas')
            .select('id')
            .eq('salon_id', salonId)
            .eq('content_hash', contentHash)
            .limit(1)
            .maybeSingle();
          if (!dupErr && dup?.id) {
            duplicateOf = dup.id;
          }
        }

        const aiStatus = (serviceData.formulaData as any)?.validation?.status as
          | 'passed'
          | 'corrected'
          | 'failed'
          | undefined;
        const validationStatus =
          aiStatus === 'passed'
            ? 'auto_passed'
            : aiStatus === 'corrected'
              ? 'auto_corrected'
              : aiStatus === 'failed'
                ? 'auto_rejected'
                : 'pending';

        let catalogStatus =
          ((serviceData.formulaData as any)?.catalogStatus as
            | 'unverified'
            | 'matched'
            | 'mismatch'
            | 'partial'
            | 'unknown_brand') || 'unverified';
        let catalogIssues = ((serviceData.formulaData as any)?.catalogIssues as string[]) || [];

        const hasPlaceholder = !!(serviceData.formulaData as any)?.steps?.some((s: any) =>
          (s?.mix || []).some((p: any) =>
            String(p?.productName || '')
              .toLowerCase()
              .includes('seleccionar tono')
          )
        );

        if (duplicateOf) {
          historyLogger.warn('Duplicate formula detected. Skipping insert.', { duplicateOf });
          saveMeta = { skippedFormula: true, duplicateOf };
        } else {
          const { brand: brandDerived, line: lineDerived } = extractBrandLine(
            serviceData.formulaData || {}
          );

          const fallback = await evaluateCatalogStatusIfNeeded(
            catalogStatus,
            serviceData.formulaData || {}
          );
          if (fallback) {
            catalogStatus = fallback.catalogStatus;
            catalogIssues = fallback.catalogIssues;
          }

          if (hasPlaceholder) {
            (validationStatus as any) = 'manual_review';
            catalogStatus = catalogStatus || ('partial' as any);
            catalogIssues = [
              ...catalogIssues,
              'Falta seleccionar uno o más tonos (placeholder)'.trim(),
            ];
          }

          const { error: formulaError } = await supabase.from('formulas').insert({
            salon_id: salonId,
            service_id: service.id,
            formula_text: serviceData.formula,
            formula_data: serviceData.formulaData || {},
            technique: serviceData.technique || null,
            processing_time: serviceData.processingTime || null,
            developer_volume: serviceData.developerVolume || null,
            created_by: userId,
            scenario_hash: scenarioHash,
            content_hash: contentHash,
            duplicate_of: duplicateOf,
            validation_status: validationStatus as any,
            validation_errors: ((serviceData.formulaData as any)?.warnings || []) as any,
            catalog_status: catalogStatus as any,
            catalog_issues: catalogIssues as any,
            brand: brandDerived || (serviceData.formulaData as any)?.brand || null,
            line: lineDerived || (serviceData.formulaData as any)?.line || null,
            personalization_summary: {
              hairLengthCm: (serviceData as any)?.hairLength || undefined,
              hairDensity: (serviceData as any)?.hairDensity || undefined,
            } as any,
          });
          if (formulaError) {
            historyLogger.error('Error saving formula', formulaError);
          }
        }
      } catch (e) {
        historyLogger.error('Unexpected error saving formula', e as any);
      }
    }

    historyLogger.info('Service saved successfully', {
      serviceId: service.id,
      skippedFormula: !!saveMeta.skippedFormula,
      duplicateOf: saveMeta.duplicateOf || null,
    });

    return { ...service, _saveMeta: saveMeta } as any;
  },

  async addConsentRecord(
    clientId: string,
    consent: ConsentRecord
  ): Promise<SupabaseConsent | null> {
    const salonId = await getCurrentSalonId();
    if (!salonId) return null;

    const now = new Date().toISOString();
    const tempId = generateLocalId('consent');

    return syncRecord(
      'client_consents',
      {
        client_id: clientId,
        salon_id: salonId,
        consent_type: 'chemical_process',
        consent_text: 'Consentimiento informado para servicio de coloración capilar',
        consent_data: { items: consent.consentItems },
        signature_data: consent.signature,
        safety_checklist: consent.safetyChecklist,
        skip_safety: consent.skipSafetyVerification || false,
        ip_address: consent.ipAddress,
        user_agent: consent.userAgent,
        signed_at: now,
        _tempId: tempId,
      },
      async () => {
        const { data, error } = await supabase
          .from('client_consents')
          .insert({
            client_id: clientId,
            salon_id: salonId,
            consent_type: 'chemical_process',
            consent_text: 'Consentimiento informado para servicio de coloración capilar',
            consent_data: { items: consent.consentItems },
            signature_data: consent.signature,
            safety_checklist: consent.safetyChecklist,
            skip_safety: consent.skipSafetyVerification || false,
            ip_address: consent.ipAddress,
            user_agent: consent.userAgent,
            signed_at: now,
          })
          .select()
          .single();
        if (error) throw error;
        return data;
      }
    );
  },

  async getServiceDetails(serviceId: string): Promise<ServiceDetails | null> {
    historyLogger.info('Getting service details', { serviceId });

    const salonId = await getCurrentSalonId();
    if (!salonId) {
      throw new Error('No salon ID found');
    }

    let serviceIdToUse: string | null = serviceId;

    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select(
        '*, formulas(*), clients(id, name, phone, email), profiles!services_stylist_id_fkey(id, full_name, email), client_consents(*)'
      )
      .eq('id', serviceId)
      .maybeSingle();

    if (serviceError) {
      historyLogger.error('Error fetching service details', serviceError);
      throw serviceError;
    }

    if (!service) {
      const { data: fx, error: fxErr } = await supabase
        .from('formulas')
        .select('service_id')
        .eq('id', serviceId)
        .maybeSingle();
      if (fxErr) historyLogger.error('Formula lookup failed', fxErr);
      if (fx?.service_id) {
        serviceIdToUse = fx.service_id as string;
        const retry = await supabase
          .from('services')
          .select(
            '*, formulas(*), clients(id, name, phone, email), profiles!services_stylist_id_fkey(id, full_name, email), client_consents(*)'
          )
          .eq('id', serviceIdToUse)
          .maybeSingle();
        service = retry.data as any;
      }
    }

    if (!service) {
      const { data: svcOnly, error: svcErr } = await supabase
        .from('services')
        .select('*')
        .eq('id', serviceIdToUse || serviceId)
        .maybeSingle();
      if (svcErr) {
        historyLogger.error('Fallback service fetch failed', svcErr);
        throw svcErr;
      }
      if (!svcOnly) {
        historyLogger.warn('Service not found in fallback query', {
          serviceId: serviceIdToUse || serviceId,
        });
        return null;
      }
      return {
        id: svcOnly.id,
        serviceDate: svcOnly.service_date,
        serviceType: svcOnly.service_type,
        notes: svcOnly.notes,
        status: svcOnly.status,
        satisfactionScore: svcOnly.satisfaction_score,
      } as any;
    }

    const formattedService = {
      id: service.id,
      serviceDate: service.service_date,
      serviceType: service.service_type,
      notes: service.notes,
      client: service.clients ? { id: service.clients.id, name: service.clients.name } : null,
      stylist: service.profiles
        ? { id: service.profiles.id, name: service.profiles.full_name }
        : null,
      formula: service.formulas?.[0]
        ? {
            id: service.formulas[0].id,
            formulaText: service.formulas[0].formula_text,
            formulaData: service.formulas[0].formula_data,
          }
        : null,
      consent: service.client_consents?.[0] || null,
    };

    historyLogger.info('Service details retrieved successfully', { serviceId });
    return formattedService;
  },

  async syncWithSupabase(): Promise<void> {
    historyLogger.startTimer('syncWithSupabase');
    const salonId = await getCurrentSalonId();
    if (!salonId) {
      historyLogger.warn('No salon ID available for sync');
      return;
    }

    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .select('id, name')
      .eq('salon_id', salonId);
    if (clientsError) {
      historyLogger.error('Error fetching clients for sync:', clientsError);
      return;
    }

    if (!clients || clients.length === 0) {
      historyLogger.info('No clients found for salon');
      return;
    }

    const loadPromises = clients.map(client => this.loadClientHistory(client.id));
    await Promise.allSettled(loadPromises);

    historyLogger.endTimer('syncWithSupabase');
  },
};
