import { Platform } from 'react-native';

// Typography scale (Claude-inspired density and hierarchy)
export const typography = {
  // Font families
  fonts: {
    primary: {
      ios: 'SF Pro Text',
      android: 'Roboto',
      web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },
    display: {
      ios: 'SF Pro Display',
      android: 'Roboto',
      web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },
    mono: {
      ios: 'SF Mono',
      android: 'Roboto Mono',
      web: '"SF Mono", Monaco, "Cascadia Code", monospace',
    },
  },

  // Font sizes (Claude-inspired reduced scale for better content density)
  sizes: {
    caption: 11, // Timestamps, metadata
    xs: 12, // Labels, badges, chips (was small in beauty-minimal)
    sm: 14, // Body text, messages, descriptions (reduced from 16px)
    base: 16, // Subheadings, section headers (reduced from 18px)
    lg: 18, // Screen titles, main headings (reduced from 20px)
    xl: 22, // Main titles (reduced from 24px)
    '2xl': 28, // Hero text, display (reduced from 30px)
    '3xl': 36, // Large displays (kept for compatibility)
    '4xl': 42, // Extended for special cases
  },

  // Font weights
  weights: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },

  // Line heights (optimized for readability - Claude-inspired)
  lineHeights: {
    tight: 1.25, // Headlines, titles
    normal: 1.4, // Body text (improved from 1.5 for better density)
    relaxed: 1.6, // Long form content (reduced from 1.75)
  },

  // Semantic text styles (updated with new scale)
  h1: {
    fontSize: 36,
    fontWeight: '700' as const,
    lineHeight: 36 * 1.25,
  },
  h2: {
    fontSize: 28,
    fontWeight: '600' as const,
    lineHeight: 28 * 1.25,
  },
  h3: {
    fontSize: 22,
    fontWeight: '600' as const,
    lineHeight: 22 * 1.25,
  },
  h4: {
    fontSize: 18,
    fontWeight: '600' as const,
    lineHeight: 18 * 1.3,
  },
  h5: {
    fontSize: 16,
    fontWeight: '500' as const,
    lineHeight: 16 * 1.3,
  },
  title: {
    fontSize: 18, // Reduced from 20px for better density
    fontWeight: '600' as const,
    lineHeight: 18 * 1.3,
  },
  body: {
    fontSize: 14, // Reduced from 16px for Claude-like density
    fontWeight: '400' as const,
    lineHeight: 14 * 1.4,
  },
  caption: {
    fontSize: 12, // Reduced from 14px, more appropriate for captions
    fontWeight: '400' as const,
    lineHeight: 12 * 1.4,
  },
  button: {
    fontSize: 14, // Reduced from 16px for consistency with body
    fontWeight: '600' as const,
    lineHeight: 14 * 1.2,
  },
  buttonMedium: {
    fontSize: 14, // Reduced from 16px for consistency
    fontWeight: '500' as const,
    lineHeight: 14 * 1.2,
  },

  // Beauty-tech specific text styles
  chat: {
    message: {
      fontSize: 14, // Optimal for chat density
      fontWeight: '400' as const,
      lineHeight: 14 * 1.4,
    },
    timestamp: {
      fontSize: 11, // Subtle timestamps
      fontWeight: '400' as const,
      lineHeight: 11 * 1.4,
    },
  },
  professional: {
    data: {
      fontSize: 13, // Technical information, formula details
      fontWeight: '400' as const,
      lineHeight: 13 * 1.4,
    },
    label: {
      fontSize: 12, // Form labels, field descriptions
      fontWeight: '500' as const,
      lineHeight: 12 * 1.4,
    },
  },
  ai: {
    confidence: {
      fontSize: 12, // AI confidence indicators
      fontWeight: '500' as const,
      lineHeight: 12 * 1.4,
    },
    explanation: {
      fontSize: 13, // AI explanations and reasoning
      fontWeight: '400' as const,
      lineHeight: 13 * 1.5,
    },
  },
};

// Spacing scale (based on 8px grid)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Border radius scale
export const radius = {
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

// Shadows for elevation
export const shadows = {
  none: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
};

// Animation durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Z-index scale
export const zIndex = {
  base: 0,
  dropdown: 100,
  sticky: 200,
  modal: 300,
  popover: 400,
  tooltip: 500,
};

// Layout constants
export const layout = {
  tabBarHeight: 70,
  headerHeight: Platform.OS === 'ios' ? 96 : 80,
  contentPadding: spacing.md,
  cardPadding: spacing.md,
  screenPadding: spacing.lg,
};

// Component-specific constants
export const components = {
  button: {
    height: {
      sm: 36,
      md: 44,
      lg: 52,
    },
    paddingHorizontal: {
      sm: spacing.md,
      md: spacing.lg,
      lg: spacing.xl,
    },
  },
  input: {
    height: 48,
    borderWidth: 1,
    paddingHorizontal: spacing.md,
  },
  card: {
    borderRadius: radius.lg,
    padding: spacing.md,
  },
};
