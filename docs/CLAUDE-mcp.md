# CLAUDE-mcp.md

MCP (Model Context Protocol) integrations and tool usage patterns.

## 🔌 MCP Integrations Available

### 🗄️ Supabase MCP - Database Operations

#### Core Functions

- `mcp__supabase__list_tables` - Inspect database schema
- `mcp__supabase__execute_sql` - Run SQL queries safely
- `mcp__supabase__apply_migration` - Apply schema changes
- `mcp__supabase__get_logs` - Debug Edge Functions
- `mcp__supabase__generate_typescript_types` - Update type definitions

#### Edge Functions

- `mcp__supabase__list_edge_functions` - List deployed functions
- `mcp__supabase__deploy_edge_function` - Deploy with validation
- `mcp__supabase__get_advisors` - Security and performance recommendations

#### Branching & Development

- `mcp__supabase__create_branch` - Create development branches
- `mcp__supabase__merge_branch` - Merge to production
- `mcp__supabase__list_branches` - Manage development workflow

### 📚 Context7 MCP - Documentation Access

#### Library Documentation

- `mcp__context7__resolve_library_id` - Find correct library references
- `mcp__context7__get_library_docs` - Get up-to-date documentation
- Use for React Native, Expo, Zustand, and other dependencies

```bash
# Get React Native documentation
mcp__context7__resolve_library_id: "react-native"
mcp__context7__get_library_docs: "/facebook/react-native"
```

### 🛠️ IDE MCP - Development Tools

#### Diagnostics

- `mcp__ide__getDiagnostics` - Get TypeScript/ESLint errors
- `mcp__ide__executeCode` - Run Python/JavaScript in Jupyter kernel

## 💡 MCP Usage Examples

### Database Analysis and Optimization

```bash
mcp__supabase__list_tables
mcp__supabase__get_advisors: "performance"
```

### Code Quality Checks

```bash
mcp__ide__getDiagnostics
```

### Library Help

```bash
mcp__context7__resolve_library_id: "zustand"
```

## 🔄 Agent + MCP Combinations

### Recommended Patterns

1. **Database Work**: `database-architect` + Supabase MCP
2. **Code Analysis**: `debug-specialist` + IDE MCP
3. **Documentation**: `ai-integration-specialist` + Context7 MCP
4. **Quality Checks**: `test-runner` + IDE MCP

## 🎯 MCP Tool Groups

### Essential (Always Available)

- `mcp__supabase__list_tables`
- `mcp__supabase__execute_sql`
- `mcp__supabase__get_logs`
- `mcp__ide__getDiagnostics`

### Database Operations

- `mcp__supabase__apply_migration`
- `mcp__supabase__generate_typescript_types`
- `mcp__supabase__get_advisors`

### Edge Functions

- `mcp__supabase__list_edge_functions`
- `mcp__supabase__deploy_edge_function`

### Branching & Development

- `mcp__supabase__create_branch`
- `mcp__supabase__merge_branch`
- `mcp__supabase__list_branches`

### Documentation & Research

- `mcp__context7__resolve_library_id`
- `mcp__context7__get_library_docs`
