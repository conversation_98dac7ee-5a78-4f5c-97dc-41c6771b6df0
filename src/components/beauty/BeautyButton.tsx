/**
 * BEAUTY BUTTON COMPONENT
 *
 * Consistent button styling following beauty-minimalist design principles.
 * Implements strategic 90/10 color usage with professional trust patterns.
 *
 * Features:
 * - Multiple variants with clear visual hierarchy
 * - Professional trust colors for technical actions
 * - Beauty accents for primary user actions
 * - Proper accessibility and touch targets
 * - Haptic feedback for premium experience
 */

import React, { memo, useCallback, useRef } from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, Animated } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface BeautyButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'professional' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  loadingTitle?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  haptic?: boolean;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

/**
 * BeautyButton - Professional button with beauty-minimalist styling
 *
 * USAGE STRATEGY:
 * - primary: Main actions (10% barn-red)
 * - secondary: Alternative actions (10% fire-brick)
 * - professional: Technical actions (10% air-superiority-blue)
 * - ghost: Subtle actions (90% neutral)
 * - destructive: Delete actions (semantic error color)
 */
export const BeautyButton = memo<BeautyButtonProps>(
  ({
    title,
    onPress,
    variant = 'primary',
    size = 'md',
    icon: Icon,
    iconPosition = 'left',
    disabled = false,
    loading = false,
    loadingTitle,
    style,
    textStyle,
    haptic = true,
    testID = 'beauty-button',
    accessibilityLabel,
    accessibilityHint,
  }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePressIn = useCallback(() => {
      if (!disabled && !loading) {
        Animated.spring(scaleAnim, {
          toValue: 0.95,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [disabled, loading, scaleAnim]);

    const handlePressOut = useCallback(() => {
      if (!disabled && !loading) {
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          speed: 20,
          bounciness: 0,
        }).start();
      }
    }, [disabled, loading, scaleAnim]);

    const handlePress = useCallback(() => {
      if (!disabled && !loading) {
        if (haptic) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        onPress();
      }
    }, [disabled, loading, onPress, haptic]);

    const getVariantStyles = useCallback(() => {
      switch (variant) {
        case 'primary':
          return {
            container: styles.primaryContainer,
            text: styles.primaryText,
            icon: BeautyMinimalTheme.semantic.text.inverse,
          };
        case 'secondary':
          return {
            container: styles.secondaryContainer,
            text: styles.secondaryText,
            icon: BeautyMinimalTheme.semantic.text.inverse,
          };
        case 'professional':
          return {
            container: styles.professionalContainer,
            text: styles.professionalText,
            icon: BeautyMinimalTheme.semantic.text.inverse,
          };
        case 'ghost':
          return {
            container: styles.ghostContainer,
            text: styles.ghostText,
            icon: BeautyMinimalTheme.semantic.text.secondary,
          };
        case 'destructive':
          return {
            container: styles.destructiveContainer,
            text: styles.destructiveText,
            icon: BeautyMinimalTheme.semantic.status.error,
          };
        default:
          return {
            container: styles.primaryContainer,
            text: styles.primaryText,
            icon: BeautyMinimalTheme.semantic.text.inverse,
          };
      }
    }, [variant]);

    const getSizeStyles = useCallback(() => {
      switch (size) {
        case 'sm':
          return {
            container: styles.smContainer,
            text: styles.smText,
            iconSize: 16,
          };
        case 'lg':
          return {
            container: styles.lgContainer,
            text: styles.lgText,
            iconSize: 20,
          };
        default:
          return {
            container: styles.mdContainer,
            text: styles.mdText,
            iconSize: 18,
          };
      }
    }, [size]);

    const variantStyles = getVariantStyles();
    const sizeStyles = getSizeStyles();

    const containerStyle = [
      styles.base,
      variantStyles.container,
      sizeStyles.container,
      disabled && styles.disabled,
      loading && styles.loading,
      style,
    ];

    const combinedTextStyle = [styles.baseText, variantStyles.text, sizeStyles.text, textStyle];

    const accessibilityProps = {
      accessible: true,
      accessibilityRole: 'button' as const,
      accessibilityLabel: accessibilityLabel || title,
      accessibilityHint: accessibilityHint || 'Toca para realizar la acción',
      accessibilityState: {
        disabled: disabled || loading,
      },
    };

    const renderIcon = () => {
      if (!Icon) return null;

      return (
        <Icon
          size={sizeStyles.iconSize}
          color={disabled ? BeautyMinimalTheme.semantic.text.tertiary : variantStyles.icon}
          style={iconPosition === 'right' ? styles.iconRight : styles.iconLeft}
        />
      );
    };

    return (
      <TouchableOpacity
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={1}
        testID={testID}
        {...accessibilityProps}
      >
        <Animated.View style={[containerStyle, { transform: [{ scale: scaleAnim }] }]}>
          {iconPosition === 'left' && renderIcon()}
          <Text style={combinedTextStyle} numberOfLines={1}>
            {loading ? loadingTitle || 'Cargando...' : title}
          </Text>
          {iconPosition === 'right' && renderIcon()}
        </Animated.View>
      </TouchableOpacity>
    );
  }
);

BeautyButton.displayName = 'BeautyButton';

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BeautyMinimalTheme.radius.md,
    borderWidth: 1,
    ...BeautyMinimalTheme.shadows.subtle,
  },

  // Size Variants
  smContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    minHeight: 36,
  },
  mdContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.minimum,
  },
  lgContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.xl,
    paddingVertical: BeautyMinimalTheme.spacing.lg,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.comfortable,
  },

  // Color Variants (Following 90/10 Strategy)
  primaryContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default, // 10% barn-red
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  secondaryContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.secondary.default, // 10% fire-brick
    borderColor: BeautyMinimalTheme.semantic.interactive.secondary.default,
  },
  professionalContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.professional.default, // 10% air-superiority-blue
    borderColor: BeautyMinimalTheme.semantic.interactive.professional.default,
  },
  ghostContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary, // 90% warm papaya-whip
    borderColor: BeautyMinimalTheme.semantic.border.default,
  },
  destructiveContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderColor: BeautyMinimalTheme.semantic.status.error,
  },

  // Text Styles
  baseText: {
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    textAlign: 'center',
  },
  smText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
  },
  mdText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
  },
  lgText: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
  },

  // Text Colors
  primaryText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  secondaryText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  professionalText: {
    color: BeautyMinimalTheme.semantic.text.inverse,
  },
  ghostText: {
    color: BeautyMinimalTheme.semantic.text.primary, // 90% prussian-blue charcoal
  },
  destructiveText: {
    color: BeautyMinimalTheme.semantic.status.error,
  },

  // Icon Positioning
  iconLeft: {
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  iconRight: {
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },

  // States
  disabled: {
    opacity: 0.6,
    ...BeautyMinimalTheme.shadows.none,
  },
  loading: {
    opacity: 0.8,
  },
});
