import { Product } from '@/types/inventory';
import { RegionalConfig } from '@/types/regional';

/**
 * Service for generating consistent product display names
 * Handles regional variations and inventory control levels
 */
export class ProductNamingService {
  /**
   * Generate display name based on product data and regional config
   */
  static generateDisplayName(
    product: Partial<Product>,
    regionalConfig?: RegionalConfig,
    inventoryLevel: 'solo-formulas' | 'smart-cost' | 'control-total' = 'control-total'
  ): string {
    // Get localized type name
    const typeLabel = this.getLocalizedType(product.type || '', regionalConfig);

    // For formula-only mode, don't include type prefix
    if (inventoryLevel === 'solo-formulas') {
      return this.buildNameWithoutType(product);
    }

    // Build name based on product type
    switch (product.type?.toLowerCase()) {
      case 'tinte':
      case 'color':
        return this.buildTinteName(product, typeLabel);

      case 'oxidante':
      case 'developer':
        return this.buildDeveloperName(product, typeLabel);

      case 'decolorante':
      case 'bleach':
        return this.buildBleachName(product, typeLabel);

      default:
        return this.buildGenericName(product, typeLabel);
    }
  }

  /**
   * Get localized type name based on regional config
   */
  private static getLocalizedType(type: string, regionalConfig?: RegionalConfig): string {
    const normalizedType = type.toLowerCase();

    // Use regional terminology if available
    if (regionalConfig) {
      if (normalizedType === 'tinte' || normalizedType === 'color') {
        return this.capitalize(regionalConfig.colorTerminology || 'Tinte');
      }
      if (normalizedType === 'oxidante' || normalizedType === 'developer') {
        return this.capitalize(regionalConfig.developerTerminology || 'Oxidante');
      }
    }

    // Default Spanish mappings
    const typeMap: Record<string, string> = {
      color: 'Tinte',
      tinte: 'Tinte',
      developer: 'Oxidante',
      oxidante: 'Oxidante',
      bleach: 'Decolorante',
      decolorante: 'Decolorante',
      treatment: 'Tratamiento',
      tratamiento: 'Tratamiento',
      toner: 'Matizador',
      matizador: 'Matizador',
      shampoo: 'Champú',
      champú: 'Champú',
      conditioner: 'Acondicionador',
      acondicionador: 'Acondicionador',
      styling: 'Styling',
      additive: 'Aditivo',
      aditivo: 'Aditivo',
      pre_pigment: 'Pre-pigmentación',
      'pre-pigmentacion': 'Pre-pigmentación',
      other: 'Otro',
      otro: 'Otro',
    };

    return typeMap[normalizedType] || this.capitalize(type);
  }

  /**
   * Build name for tinte/color products
   */
  private static buildTinteName(product: Partial<Product>, typeLabel: string): string {
    const parts = [typeLabel];

    if (product.brand) parts.push(product.brand);
    if (product.line) parts.push(product.line);
    if (product.shade) parts.push(product.shade);

    return parts.join(' ').trim();
  }

  /**
   * Build name for oxidante/developer products
   */
  private static buildDeveloperName(product: Partial<Product>, typeLabel: string): string {
    const parts = [typeLabel];

    // For developers, brand is optional in the name
    if (product.brand && product.line) {
      parts.push(product.brand);
      parts.push(product.line);
    }

    if (product.shade) {
      // Normalize volume format (20 vol, 20 volúmenes, etc.)
      const normalizedShade = this.normalizeVolume(product.shade);
      parts.push(normalizedShade);
    }

    return parts.join(' ').trim();
  }

  /**
   * Build name for decolorante/bleach products
   */
  private static buildBleachName(product: Partial<Product>, typeLabel: string): string {
    const parts = [typeLabel];

    if (product.brand) parts.push(product.brand);
    if (product.line) parts.push(product.line);

    return parts.join(' ').trim();
  }

  /**
   * Build generic product name
   */
  private static buildGenericName(product: Partial<Product>, typeLabel: string): string {
    const parts = [typeLabel];

    if (product.brand) parts.push(product.brand);
    if (product.line) parts.push(product.line);
    if (product.shade) parts.push(product.shade);

    return parts.join(' ').trim();
  }

  /**
   * Build name without type prefix (for formula-only mode)
   */
  private static buildNameWithoutType(product: Partial<Product>): string {
    const parts = [];

    if (product.brand) parts.push(product.brand);
    if (product.line) parts.push(product.line);
    if (product.shade) parts.push(product.shade);

    // Fallback to name if no structured data
    if (parts.length === 0 && product.name) {
      return product.name;
    }

    return parts.join(' ').trim();
  }

  /**
   * Normalize volume formats (20 vol, 20 volúmenes, etc.)
   */
  private static normalizeVolume(volume: string): string {
    // Extract number
    const match = volume.match(/(\d+)/);
    if (match) {
      const number = match[1];
      // Always use "vol" for consistency
      return `${number} vol`;
    }
    return volume;
  }

  /**
   * Normalize shade/tone formats (7.81, 7/81, 7,81)
   */
  static normalizeShade(shade: string): string {
    if (!shade) return '';

    // Replace common separators with dot
    return shade.replace(/[\/,]/g, '.');
  }

  /**
   * Parse AI-generated product name into components
   */
  static parseProductName(name: string): {
    type?: string;
    brand?: string;
    line?: string;
    shade?: string;
  } {
    const components: {
      type?: string;
      brand?: string;
      line?: string;
      shade?: string;
    } = {};

    // Common patterns in AI-generated names
    const patterns = [
      // "Illumina Color 7.81"
      /^(\w+)\s+Color\s+([\d\.\/]+)$/i,
      // "Wella Illumina Color 7.81"
      /^(\w+)\s+(\w+)\s+Color\s+([\d\.\/]+)$/i,
      // "Oxidante 20 vol"
      /^(Oxidante|Developer)\s+(\d+)\s*vol/i,
      // "Decolorante en polvo"
      /^(Decolorante|Bleach)\s+(.+)$/i,
      // Generic "Brand Line Shade"
      /^(\w+)\s+(\w+)\s+([\d\.\/]+)$/,
    ];

    // Try to extract type keywords
    const typeKeywords = [
      'tinte',
      'color',
      'oxidante',
      'developer',
      'decolorante',
      'bleach',
      'tratamiento',
      'treatment',
      'matizador',
      'toner',
    ];

    const lowerName = name.toLowerCase();
    for (const keyword of typeKeywords) {
      if (lowerName.includes(keyword)) {
        components.type = this.capitalize(keyword);
        break;
      }
    }

    // Try patterns
    for (const pattern of patterns) {
      const match = name.match(pattern);
      if (match) {
        // Extract based on pattern
        // This is simplified - real implementation would be more sophisticated
        break;
      }
    }

    // Fallback: split and analyze
    const parts = name.split(/\s+/);
    if (parts.length >= 2) {
      // Simple heuristic
      if (!components.type && typeKeywords.includes(parts[0].toLowerCase())) {
        components.type = parts[0];
        parts.shift();
      }

      if (parts.length >= 3) {
        components.brand = parts[0];
        components.line = parts[1];
        components.shade = parts[parts.length - 1];
      } else if (parts.length === 2) {
        components.brand = parts[0];
        components.shade = parts[1];
      }
    }

    return components;
  }

  /**
   * Calculate similarity between two product names
   */
  static calculateSimilarity(name1: string, name2: string): number {
    // Normalize both names
    const norm1 = name1.toLowerCase().trim();
    const norm2 = name2.toLowerCase().trim();

    // Exact match
    if (norm1 === norm2) return 100;

    // Extract components and compare
    const comp1 = this.parseProductName(name1);
    const comp2 = this.parseProductName(name2);

    let score = 0;
    let factors = 0;

    // Compare each component
    if (comp1.brand && comp2.brand) {
      factors++;
      if (comp1.brand.toLowerCase() === comp2.brand.toLowerCase()) {
        score += 30;
      }
    }

    if (comp1.line && comp2.line) {
      factors++;
      if (comp1.line.toLowerCase() === comp2.line.toLowerCase()) {
        score += 25;
      }
    }

    if (comp1.shade && comp2.shade) {
      factors++;
      const shade1 = this.normalizeShade(comp1.shade);
      const shade2 = this.normalizeShade(comp2.shade);
      if (shade1 === shade2) {
        score += 35;
      }
    }

    if (comp1.type && comp2.type) {
      factors++;
      if (this.getLocalizedType(comp1.type) === this.getLocalizedType(comp2.type)) {
        score += 10;
      }
    }

    // Adjust score based on number of factors compared
    if (factors === 0) {
      // Fallback to simple string comparison
      return this.levenshteinSimilarity(norm1, norm2);
    }

    return Math.min(100, score);
  }

  /**
   * Calculate Levenshtein distance-based similarity
   */
  private static levenshteinSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 100;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return Math.round(((longer.length - editDistance) / longer.length) * 100);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Capitalize first letter
   */
  private static capitalize(str: string): string {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }
}
