// Functions for checking catalog consistency.
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { logger } from '../../src/utils/security.ts';

type CatalogStatus = 'unverified' | 'matched' | 'mismatch' | 'partial' | 'unknown_brand';

export interface CatalogEvaluation {
  status: CatalogStatus;
  issues: string[];
  matchedProducts: number;
  totalRelevantProducts: number;
}

function normalizeShade(value: string | null | undefined): string | null {
  if (!value) return null;
  return value
    .toLowerCase()
    .replace(/[^a-z0-9./-]/g, '')
    .replace(/\s+/g, '')
    .trim();
}

function extractShadeCandidate(product: any): string | null {
  if (product?.shade) {
    return normalizeShade(product.shade);
  }
  const reference: string = product?.productName || product?.productId || '';
  const match = reference.match(/(\d+[./]\d+|\d+\.?\d+|\d+\/\d+)/);
  if (match) {
    return normalizeShade(match[1]);
  }
  return null;
}

function grammarAcceptsTone(grammar: any, candidateRaw: string | null | undefined): boolean {
  if (!grammar || !candidateRaw) return false;
  const candidate = normalizeShade(candidateRaw);
  if (!candidate) return false;
  const notation = (grammar?.notation || 'slash').toString().toLowerCase();
  const families: string[] = Array.isArray(grammar?.families) ? grammar.families : [];
  // Detect level and family parts according to notation
  let level: number | null = null;
  let family: string | null = null;
  if (notation === 'letter') {
    // e.g., 9V, 10N, 5NW, 7NA
    const m = candidate.match(/^(\d{1,2})([a-z]+)$/i);
    if (m) {
      level = parseInt(m[1], 10);
      family = m[2].toUpperCase();
    }
  } else if (notation === 'dot') {
    // e.g., 10.1 or 9.21 (we consider digits after the dot as family code)
    const m = candidate.match(/^(\d{1,2})\.(\d{1,2})$/);
    if (m) {
      level = parseInt(m[1], 10);
      family = m[2];
    }
  } else {
    // slash by default: 9/1, 8/11
    const m = candidate.match(/^(\d{1,2})\/(\d{1,2})$/);
    if (m) {
      level = parseInt(m[1], 10);
      family = m[2];
    }
  }
  if (level == null || family == null) return false;
  // If we have families defined, accept when family or mapped family present; otherwise accept by structure only
  if (families.length > 0) {
    const famUpper = family.toUpperCase();
    if (families.includes(famUpper)) return true;
    // Also accept numeric families when families list is numeric-like
    if (families.includes(family)) return true;
    // Map letters using family_map if provided
    const mapped = grammar?.family_map?.[famUpper];
    if (mapped && families.includes(mapped)) return true;
    return false;
  }
  return true;
}

export async function evaluateCatalogConsistency(
  supabase: SupabaseClient,
  brand: string | null | undefined,
  line: string | null | undefined,
  formulationData: any
): Promise<CatalogEvaluation> {
  const issues: string[] = [];
  const relevantTypes = new Set(['color', 'tinte', 'coloración', 'toner', 'matizador', 'bleach', 'decolorante']);

  const normalizedBrand = brand?.trim();
  if (!normalizedBrand) {
    return {
      status: 'unknown_brand',
      issues: ['No se especificó una marca para validar la fórmula.'],
      matchedProducts: 0,
      totalRelevantProducts: 0,
    };
  }

  const { data: brandRow, error: brandError } = await supabase
    .from('brands')
    .select('id, name')
    .ilike('name', normalizedBrand)
    .maybeSingle();

  if (brandError) {
    logger.error('Catalog validation: failed to fetch brand', {
      brand: normalizedBrand,
      error: brandError.message,
    });
    return {
      status: 'unverified',
      issues: ['No se pudo verificar la marca en el catálogo.'],
      matchedProducts: 0,
      totalRelevantProducts: 0,
    };
  }

  if (!brandRow) {
    return {
      status: 'unknown_brand',
      issues: [`La marca "${normalizedBrand}" no está registrada en el catálogo.`],
      matchedProducts: 0,
      totalRelevantProducts: 0,
    };
  }

  let lineId: string | null = null;
  if (line) {
    const { data: lineRow, error: lineError } = await supabase
      .from('product_lines')
      .select('id, name')
      .eq('brand_id', brandRow.id)
      .ilike('name', line.trim())
      .maybeSingle();

    if (lineError) {
      logger.error('Catalog validation: failed to fetch line', {
        brandId: brandRow.id,
        line,
        error: lineError.message,
      });
      issues.push('No se pudo validar la línea en el catálogo.');
    } else if (!lineRow) {
      issues.push(`La línea "${line}" no está registrada para la marca seleccionada.`);
    } else {
      lineId = lineRow.id;
    }
  }

  let shadeSet: Set<string> | null = null;
  let grammar: any | null = null;
  if (lineId) {
    const { data: shades, error: shadesError } = await supabase
      .from('brand_line_shades')
      .select('shade_code')
      .eq('brand_id', brandRow.id)
      .eq('line_id', lineId)
      .eq('is_active', true);

    if (shadesError) {
      logger.error('Catalog validation: failed to fetch shades', {
        brandId: brandRow.id,
        lineId,
        error: shadesError.message,
      });
      issues.push('No se pudo acceder al listado de tonos para la línea seleccionada.');
    } else if (shades && shades.length > 0) {
      shadeSet = new Set(shades.map(s => normalizeShade(s.shade_code)).filter(Boolean) as string[]);
    } else {
      issues.push('La línea seleccionada no tiene tonos registrados en el catálogo.');
    }

    // Fetch grammar as virtual catalog fallback
    const { data: rulesRow } = await supabase
      .from('brand_line_rules')
      .select('tone_grammar')
      .eq('brand_id', brandRow.id)
      .eq('line_id', lineId)
      .maybeSingle();
    grammar = rulesRow?.tone_grammar || null;
  }

  let matchedProducts = 0;
  let totalRelevantProducts = 0;

  (formulationData?.steps || []).forEach((step: any) => {
    (step?.mix || []).forEach((product: any) => {
      const productType = (product?.type || '').toLowerCase();
      if (!relevantTypes.has(productType)) {
        return;
      }
      totalRelevantProducts += 1;

      const productBrand = product?.brand?.toString().trim().toLowerCase();
      if (productBrand && productBrand !== normalizedBrand.toLowerCase()) {
        issues.push(
          `El producto "${product?.productName || product?.productId || 'sin nombre'}" pertenece a la marca "${product.brand}".`
        );
        return;
      }

      if (line && product?.line && lineId && product.line.toString().trim().toLowerCase() !== line.trim().toLowerCase()) {
        issues.push(
          `El producto "${product?.productName || product?.productId || 'sin nombre'}" hace referencia a la línea "${product.line}" en lugar de "${line}".`
        );
      }

      const candidate = extractShadeCandidate(product);
      let matchedByCatalog = false;
      let matchedByGrammar = false;
      if (candidate && shadeSet && shadeSet.size > 0) {
        matchedByCatalog = shadeSet.has(candidate);
      }
      if (candidate && grammar) {
        matchedByGrammar = grammarAcceptsTone(grammar, candidate);
      }
      if (matchedByCatalog || matchedByGrammar) {
        matchedProducts += 1;
        if (!matchedByCatalog && matchedByGrammar) {
          issues.push(`Aceptado por gramática: "${candidate}"`);
        }
      } else if (candidate) {
        issues.push(`El tono "${candidate}" no coincide con el catálogo ni la gramática de la línea.`);
      }
    });
  });

  let status: CatalogStatus = 'unverified';
  if (!lineId) {
    status = issues.length === 0 ? 'unverified' : 'partial';
  } else if (!shadeSet || shadeSet.size === 0) {
    status = issues.length === 0 ? 'unverified' : 'partial';
  } else if (issues.length === 0) {
    status = 'matched';
  } else if (matchedProducts > 0) {
    status = 'partial';
  } else {
    status = 'mismatch';
  }

  return {
    status,
    issues,
    matchedProducts,
    totalRelevantProducts,
  };
}