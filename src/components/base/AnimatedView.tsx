import React, { useEffect, useRef, useCallback } from 'react';
import { Animated, ViewStyle, LayoutChangeEvent, StyleSheet } from 'react-native';
import { animations, durations, easings } from '@/constants/animations';

interface AnimatedViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
  animation?: 'fadeIn' | 'fadeOut' | 'slideInFromRight' | 'slideInFromBottom' | 'scaleIn' | 'none';
  duration?: number;
  delay?: number;
  onAnimationComplete?: () => void;
  startAnimation?: boolean;
}

export const AnimatedView: React.FC<AnimatedViewProps> = ({
  children,
  style,
  animation = 'fadeIn',
  duration = durations.normal,
  delay = 0,
  onAnimationComplete,
  startAnimation = true,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const translateX = useRef(new Animated.Value(100)).current;
  const translateY = useRef(new Animated.Value(100)).current;
  const scale = useRef(new Animated.Value(0)).current;

  const stableOnAnimationComplete = useCallback(() => {
    if (onAnimationComplete) {
      onAnimationComplete();
    }
  }, [onAnimationComplete]);

  useEffect(() => {
    if (!startAnimation || animation === 'none') return;

    const runAnimation = () => {
      let animationToRun: Animated.CompositeAnimation;

      switch (animation) {
        case 'fadeIn':
          animatedValue.setValue(0);
          animationToRun = animations.fadeIn(animatedValue, duration);
          break;
        case 'fadeOut':
          animatedValue.setValue(1);
          animationToRun = animations.fadeOut(animatedValue, duration);
          break;
        case 'slideInFromRight':
          translateX.setValue(100);
          animationToRun = animations.slideInFromRight(translateX, duration);
          break;
        case 'slideInFromBottom':
          translateY.setValue(100);
          animationToRun = animations.slideInFromBottom(translateY, duration);
          break;
        case 'scaleIn':
          scale.setValue(0);
          animationToRun = animations.scaleIn(scale, duration);
          break;
        default:
          return;
      }

      animationToRun.start(() => {
        if (onAnimationComplete) {
          stableOnAnimationComplete();
        }
      });
    };

    if (delay > 0) {
      const timeout = setTimeout(runAnimation, delay);
      return () => clearTimeout(timeout);
    } else {
      runAnimation();
    }
  }, [
    animation,
    duration,
    delay,
    startAnimation,
    animatedValue,
    translateX,
    translateY,
    scale,
    onAnimationComplete,
    stableOnAnimationComplete,
  ]);

  const getAnimatedStyle = () => {
    switch (animation) {
      case 'fadeIn':
      case 'fadeOut':
        return { opacity: animatedValue };
      case 'slideInFromRight':
        return {
          transform: [{ translateX }],
        };
      case 'slideInFromBottom':
        return {
          transform: [{ translateY }],
        };
      case 'scaleIn':
        return {
          transform: [{ scale }],
        };
      default:
        return {};
    }
  };

  return <Animated.View style={[style, getAnimatedStyle()]}>{children}</Animated.View>;
};

interface AnimatedPressableProps extends AnimatedViewProps {
  onPress?: () => void;
  disabled?: boolean;
}

export const AnimatedPressable: React.FC<AnimatedPressableProps> = ({
  children,
  style,
  onPress,
  disabled = false,
  ...animatedProps
}) => {
  const scale = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (disabled) return;

    Animated.timing(scale, {
      toValue: 0.95,
      duration: 100,
      easing: easings.easeOut,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled) return;

    Animated.spring(scale, {
      toValue: 1,
      tension: 100,
      friction: 10,
      useNativeDriver: true,
    }).start(() => {
      if (onPress) {
        onPress();
      }
    });
  };

  return (
    <AnimatedView {...animatedProps}>
      <Animated.View
        style={[style, { transform: [{ scale }] }]}
        onTouchStart={handlePressIn}
        onTouchEnd={handlePressOut}
        onTouchCancel={handlePressOut}
      >
        {children}
      </Animated.View>
    </AnimatedView>
  );
};

interface StaggeredListProps {
  children: React.ReactElement[];
  staggerDelay?: number;
  animation?: 'fadeIn' | 'slideInFromRight' | 'slideInFromBottom' | 'scaleIn';
  duration?: number;
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 50,
  animation = 'fadeIn',
  duration = durations.normal,
}) => {
  return (
    <>
      {React.Children.map(children, (child, index) => (
        <AnimatedView
          key={index}
          animation={animation}
          duration={duration}
          delay={index * staggerDelay}
        >
          {child}
        </AnimatedView>
      ))}
    </>
  );
};

// Animated layout transitions
interface AnimatedLayoutProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const AnimatedLayout: React.FC<AnimatedLayoutProps> = ({ children, style }) => {
  const height = useRef(new Animated.Value(0)).current;

  const handleLayout = (event: LayoutChangeEvent) => {
    const newHeight = event.nativeEvent.layout.height;

    Animated.timing(height, {
      toValue: newHeight,
      duration: durations.normal,
      easing: easings.easeInOut,
      useNativeDriver: false,
    }).start();
  };

  return (
    <Animated.View style={[style, styles.animatedContainer, { height }]} onLayout={handleLayout}>
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  animatedContainer: {
    overflow: 'hidden',
  },
});
