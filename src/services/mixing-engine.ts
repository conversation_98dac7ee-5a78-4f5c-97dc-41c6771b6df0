import type { ProductMix } from '@/types/formulation';
import { getBrandPack } from '@/data/brand-packs';

export type MixContext = 'deposit' | 'tone' | 'lighten' | 'highlift' | 'bleach';

export interface ComputeMixArgs {
  brand?: string;
  line?: string;
  context: MixContext;
  colorGrams: number; // total grams of color/toner
  ratio?: string; // e.g. "1:1.5"; defaults to brand pack
}

export interface ComputeMixResult {
  ratioLabel: string; // simplified from amounts
  developerMl: number; // rounded to safe increment
}

export function parseRatio(ratio?: string): { color: number; developer: number } {
  if (!ratio) return { color: 1, developer: 1 };
  const m = ratio.replace(/\s/g, '').match(/(\d+(?:\.\d+)?):(\d+(?:\.\d+)?)/);
  if (!m) return { color: 1, developer: 1 };
  return { color: parseFloat(m[1]), developer: parseFloat(m[2]) };
}

export function simplifyRatioFromAmounts(colorAmount: number, developerAmount: number): string {
  if (colorAmount <= 0 || developerAmount <= 0) return '1:1';
  // Convert to integers by scaling to avoid float artifacts
  const scale = 10; // enough for .5 steps
  let a = Math.round((colorAmount as number) * scale);
  let b = Math.round((developerAmount as number) * scale);
  const gcd = (x: number, y: number): number => (y === 0 ? x : gcd(y, x % y));
  const d = gcd(a, b);
  a /= d;
  b /= d;
  return `${a}:${b}`;
}

export function computeDeveloperAmount(colorGrams: number, ratio?: string): number {
  const { color, developer } = parseRatio(ratio);
  const ml = (colorGrams * developer) / color;
  // Round to nearest 5 ml for salon practicality
  return Math.max(5, Math.round(ml / 5) * 5);
}

export function computeMix(args: ComputeMixArgs): ComputeMixResult {
  const pack = getBrandPack(args.brand, args.line);
  const ratio = args.ratio || pack.defaultMixRatio || '1:1';
  const developerMl = computeDeveloperAmount(args.colorGrams, ratio);
  const ratioLabel = simplifyRatioFromAmounts(args.colorGrams, developerMl);
  return { ratioLabel, developerMl };
}

export function updateProductNameRatio(name: string, ratioLabel: string): string {
  // Replace any existing "(mezcla X:Y)" or "(mix X:Y)" segment; if none, append it
  if (/\((?:mezcla|mix)\s+[^)]+\)/i.test(name)) {
    return name.replace(/\((?:mezcla|mix)\s+[^)]+\)/i, `(mezcla ${ratioLabel})`);
  }
  return `${name} (mezcla ${ratioLabel})`;
}

export function normalizeStepMix(
  mix: ProductMix[],
  opts: {
    brand?: string;
    line?: string;
    context: MixContext;
    assumedColorGrams?: number;
    ratio?: string;
  }
): ProductMix[] {
  if (!mix || mix.length === 0) return mix;
  const colorItems = mix.filter(m => m.unit === 'gr');
  const developerItemIndex = mix.findIndex(m =>
    /oxidante|developer|peróxido|revelador/i.test(m.productName)
  );
  const colorTotal = colorItems.reduce((acc, m) => acc + (m.quantity || 0), 0);
  const { developerMl, ratioLabel } = computeMix({
    brand: opts.brand,
    line: opts.line,
    context: opts.context,
    colorGrams: opts.assumedColorGrams || colorTotal || 30,
    ratio: opts.ratio,
  });

  const out = [...mix];
  if (developerItemIndex >= 0) {
    out[developerItemIndex] = {
      ...out[developerItemIndex],
      quantity: developerMl,
      unit: 'ml',
      productName: updateProductNameRatio(out[developerItemIndex].productName, ratioLabel),
    };
  }

  // Also update first color item name ratio hint if present
  const firstColorIndex = mix.findIndex(m => m.unit === 'gr');
  if (firstColorIndex >= 0) {
    out[firstColorIndex] = {
      ...out[firstColorIndex],
      productName: updateProductNameRatio(out[firstColorIndex].productName, ratioLabel),
    };
  }
  return out;
}
