import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { View, Text, ScrollView, Animated, TouchableOpacity, StyleSheet } from 'react-native';
import {
  Sparkles,
  Trophy,
  ChevronRight,
  Package,
  Calendar,
  AlertCircle,
  Check,
  Target,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
import { StepCard } from '../components/StepCard';
import { AnimatedTouchable } from '../components/AnimatedTouchable';
import { instructionStyles as styles } from '../styles';

interface CompletionStepProps {
  formulaData: VisualFormulationData;
  onSummaryPress?: () => void;
  onComplete?: () => void;
  testID?: string;
}

interface ExpectedResult {
  description: string;
  coverage: string;
  duration: string;
  maintenance: string[];
}

export const CompletionStep: React.FC<CompletionStepProps> = ({
  formulaData,
  onSummaryPress,
  onComplete,
  testID = 'completion-step',
}) => {
  const result: ExpectedResult = formulaData.expectedResult || {
    description: 'Color uniforme y brillante',
    coverage: '100%',
    duration: '6-8 semanas',
    maintenance: [
      'Usar shampoo sin sulfatos',
      'Tratamiento hidratante semanal',
      'Proteger del sol excesivo',
      'Evitar agua muy caliente',
    ],
  };

  const currentColor = formulaData.colorTransition?.current;
  const targetColor = formulaData.colorTransition?.target;

  return (
    <ScrollView
      style={styles.screenContent}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={localStyles.scrollContent}
      testID={testID}
    >
      {/* Hero Section */}
      <View style={localStyles.heroSection}>
        <Sparkles size={64} color="white" />
        <Text style={localStyles.heroTitle}>Servicio Completado</Text>
        <Text style={localStyles.heroSubtitle}>Tu transformación está completa</Text>
      </View>

      {/* Color Transformation Card */}
      <StepCard title="Transformación Completa" testID={`${testID}-transformation`}>
        <View style={localStyles.colorTransformation}>
          {/* Before */}
          <View style={localStyles.colorComparisonItem}>
            <Text style={localStyles.colorLabel}>Antes</Text>
            <View
              style={[
                localStyles.colorCircle,
                {
                  backgroundColor: currentColor?.hex || Colors.light.primary,
                  transform: [{ scale: 0.9 }],
                },
              ]}
            >
              <Text style={localStyles.colorLevel}>{currentColor?.level || '5'}</Text>
            </View>
          </View>

          {/* Arrow */}
          <View style={localStyles.transformArrow}>
            <ChevronRight size={28} color={Colors.light.primary} />
          </View>

          {/* After */}
          <View style={localStyles.colorComparisonItem}>
            <Text style={localStyles.colorLabel}>Después</Text>
            <View
              style={[
                localStyles.colorCircle,
                {
                  backgroundColor: targetColor?.hex || Colors.light.accent,
                },
              ]}
            >
              <View style={localStyles.colorInner}>
                <Text style={localStyles.colorLevel}>{targetColor?.level || '8'}</Text>
                <View style={localStyles.colorDivider} />
                <Text style={localStyles.colorTone}>{targetColor?.tone || '69'}</Text>
              </View>
            </View>
          </View>
        </View>

        <Text style={localStyles.resultDescription}>{result.description}</Text>
      </StepCard>

      {/* Result Metrics */}
      <View style={localStyles.metricsContainer}>
        <View style={localStyles.metricCard}>
          <View style={[localStyles.metricIcon, { backgroundColor: Colors.light.secondary }]}>
            <Target size={24} color="white" />
          </View>
          <Text style={localStyles.metricValue}>{result.coverage}</Text>
          <Text style={localStyles.metricLabel}>Cobertura de Canas</Text>
        </View>

        <View style={localStyles.metricCard}>
          <View style={[localStyles.metricIcon, { backgroundColor: Colors.light.accent }]}>
            <Calendar size={24} color="white" />
          </View>
          <Text style={localStyles.metricValue}>{result.duration}</Text>
          <Text style={localStyles.metricLabel}>Duración Estimada</Text>
        </View>

        <View style={localStyles.metricCard}>
          <View style={[localStyles.metricIcon, { backgroundColor: Colors.light.warning }]}>
            <Sparkles size={24} color="white" />
          </View>
          <Text style={localStyles.metricValue}>Alto</Text>
          <Text style={localStyles.metricLabel}>Brillo y Luminosidad</Text>
        </View>
      </View>

      {/* Post-Color Care */}
      <StepCard
        title="Cuidados Post-Color"
        icon={<AlertCircle size={24} color={Colors.light.primary} />}
        testID={`${testID}-maintenance`}
      >
        <View style={localStyles.maintenanceContent}>
          {result.maintenance.map((item: string, index: number) => (
            <View key={index} style={localStyles.maintenanceStep}>
              <View style={localStyles.maintenanceCheckbox}>
                <Check size={16} color="white" />
              </View>
              <Text style={localStyles.maintenanceText}>{item}</Text>
            </View>
          ))}
        </View>

        <View style={localStyles.maintenanceTip}>
          <Text style={localStyles.maintenanceTipText}>
            Programa tu próxima visita en 4-6 semanas para mantener el color vibrante
          </Text>
        </View>
      </StepCard>

      {/* Success Badge */}
      <View style={localStyles.completionCard}>
        <View style={localStyles.completionContent}>
          <Trophy size={40} color="white" />
          <Text style={localStyles.completionTitle}>¡Proceso Completado con Éxito!</Text>
          <Text style={localStyles.completionSubtitle}>
            Has realizado una coloración profesional de alta calidad
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={localStyles.actionsContainer}>
        <AnimatedTouchable
          style={localStyles.summaryButton}
          onPress={onSummaryPress}
          testID={`${testID}-summary`}
        >
          <View style={localStyles.summaryButtonContent}>
            <Package size={24} color="white" />
            <Text style={localStyles.summaryButtonText}>Ver Resumen de la Fórmula</Text>
            <ChevronRight size={20} color="white" />
          </View>
        </AnimatedTouchable>

        {onComplete && (
          <AnimatedTouchable
            style={localStyles.completeButton}
            onPress={onComplete}
            testID={`${testID}-complete`}
          >
            <View style={localStyles.completeButtonContent}>
              <Check size={24} color={Colors.light.primary} />
              <Text style={localStyles.completeButtonText}>Finalizar Servicio</Text>
            </View>
          </AnimatedTouchable>
        )}
      </View>
    </ScrollView>
  );
};

const localStyles = StyleSheet.create({
  scrollContent: {
    paddingBottom: 40,
  },
  heroSection: {
    backgroundColor: Colors.light.success,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 24,
    elevation: 4,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.textLight,
    marginTop: 16,
    marginBottom: 8,
  },
  heroSubtitle: {
    fontSize: 16,
    color: Colors.light.textLight,
    opacity: 0.9,
  },
  colorTransformation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingVertical: 16,
  },
  colorComparisonItem: {
    alignItems: 'center',
    flex: 1,
  },
  colorLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
    fontWeight: '500',
  },
  colorCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  colorLevel: {
    color: Colors.light.textLight,
    fontSize: 18,
    fontWeight: '700',
  },
  colorInner: {
    alignItems: 'center',
  },
  colorDivider: {
    width: 20,
    height: 1,
    backgroundColor: Colors.light.background,
    marginVertical: 4,
  },
  colorTone: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
  },
  transformArrow: {
    paddingHorizontal: 20,
  },
  resultDescription: {
    fontSize: 16,
    color: Colors.light.text,
    textAlign: 'center',
    lineHeight: 24,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 12,
  },
  metricCard: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  maintenanceContent: {
    gap: 12,
    marginBottom: 16,
  },
  maintenanceStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  maintenanceCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  maintenanceText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  maintenanceTip: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
  },
  maintenanceTipText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  completionCard: {
    marginHorizontal: 16,
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  completionContent: {
    backgroundColor: Colors.light.primary,
    padding: 32,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.textLight,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  completionSubtitle: {
    fontSize: 14,
    color: Colors.light.textLight,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 20,
  },
  actionsContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
  summaryButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    elevation: 3,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  summaryButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  summaryButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
    marginLeft: 12,
    textAlign: 'center',
  },
  completeButton: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary,
  },
  completeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
    marginLeft: 12,
  },
});
