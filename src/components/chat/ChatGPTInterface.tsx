import React, { useState, useRef, memo } from 'react';
import { useMemoryMonitor } from '@/utils/memory-cleanup';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Animated,
  Easing,
} from 'react-native';

import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

import { useChatStore, ChatAttachment, ChatMessage } from '@/stores/chat-store';
// import { Skeleton } from '@/components/base/ProgressSkeleton'; // Removed as not currently used

import WelcomeScreen from './components/WelcomeScreen';
import MessageBubble from './components/MessageBubble';
import ChatInput from './components/ChatInput';
import ConversationsList from './ConversationsList';
import TypingIndicator from './TypingIndicator';
import { useMessageSender } from '@/service/hooks/useMessageSender';
import { useImageUploader } from '@/service/hooks/useImageUploader';
import { useKeyboardManager } from '@/service/hooks/useKeyboardManager';
import { useChatInitialization } from '@/service/hooks/useChatInitialization';
import ChatHeader from './components/ChatHeader';
// Simplified ChatGPT/Claude-style direct attachment - no modals needed

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface ChatGPTInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
  onClose?: () => void;
  isModal?: boolean;
}

function ChatGPTInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose: _onClose,
  isModal: _isModal = false,
}: ChatGPTInterfaceProps) {
  // Memory monitoring for performance optimization
  useMemoryMonitor('ChatGPTInterface');

  // Safe async operations - keeping for potential future use
  // const { execute: _safeExecute } = useSafeAsync();

  // Timer management with automatic cleanup - keeping for potential future use
  // const { setTimeout: _safeSetTimeout, clearAll: _clearAllTimers } = useTimer();

  // Safe area handled by parent SafeAreaView in assistant screen
  const scrollViewRef = useRef<ScrollView>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [message, setMessage] = useState('');
  const [pendingAttachments, setPendingAttachments] = useState<ChatAttachment[]>([]);
  // Dynamic input states for Claude-like behavior
  const [inputHeight, setInputHeight] = useState(40); // Back to normal size
  const [isInputFocused, setIsInputFocused] = useState(false);

  // Animation values for premium micro-interactions
  const sendButtonScale = useRef(new Animated.Value(1)).current;
  const attachButtonScale = useRef(new Animated.Value(1)).current;
  const messagesFadeAnim = useRef(new Animated.Value(1)).current;

  // Enhanced premium button press animation with haptic feedback
  const animateButtonPress = (animValue: Animated.Value, callback?: () => void) => {
    // Premium haptic feedback first
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    Animated.sequence([
      Animated.timing(animValue, {
        toValue: 0.92, // Slightly more pronounced press
        duration: 80, // Faster initial press
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(animValue, {
        toValue: 1,
        duration: 200, // Longer bounce back for premium feel
        easing: Easing.out(Easing.back(1.5)), // More pronounced bounce
        useNativeDriver: true,
      }),
    ]).start(callback);
  };

  // User authentication - keeping for potential future use
  // const { user: _user } = useAuthStore();

  // Check if device is tablet
  const { width } = Dimensions.get('window');
  const IS_TABLET = width >= 768;

  const { scrollToBottom } = useKeyboardManager({ scrollViewRef });

  // Context stores - keeping for potential future use
  // const { clients: _clients } = useClientStore();
  // const { configuration: _configuration } = useSalonConfigStore();

  const {
    currentMessages,
    conversationStarters,
    activeConversationId,
    isSending,
    streamingMessage,
    typingStatus,
    // error, // Not used in ChatGPTInterface.tsx
    handleSelectConversation,
    handleCreateNewConversation,
  } = useChatInitialization({
    propConversationId,
    contextType,
    contextId,
    contextData,
    isTablet: IS_TABLET,
  });

  // Handler for suggestion selection from welcome screen
  const handleSuggestionSelect = (suggestion: string) => {
    setMessage(suggestion);
  };

  const { handleSend } = useMessageSender({
    activeConversationId,
    isSending,
    scrollViewRef,
    sendButtonScale,
    animateButtonPress,
  });

  // Photo analysis hooks - keeping for potential future use
  // const { takePhoto: _takePhoto, pickImage: _pickImage } = usePhotoAnalysis();

  const { handleImageUpload } = useImageUploader({
    setPendingAttachments,
    scrollViewRef,
    animateButtonPress,
    attachButtonScale,
    isSending,
    pendingAttachmentsLength: pendingAttachments.length,
  });

  // Open sidebar explicitly to avoid double toggle from overlapping touches
  const openSidebar = () => {
    setShowSidebar(true);
  };

  // Calculate proper keyboard offset with enhanced logic
  const getKeyboardVerticalOffset = () => {
    // Stabilize layout: avoid dynamic offsets that can cause jumping on iOS/Fabric
    return 0;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'height' : undefined}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      enabled={Platform.OS === 'ios' ? isInputFocused : false}
    >
      {/* Sidebar for tablets or when shown */}
      {(IS_TABLET || showSidebar) && (
        <>
          {!IS_TABLET && showSidebar && (
            <TouchableOpacity
              style={styles.sidebarBackdrop}
              activeOpacity={1}
              onPress={() => setShowSidebar(false)}
            />
          )}
          <View style={[styles.sidebar, !IS_TABLET && styles.sidebarOverlay]}>
            <ConversationsList
              onSelectConversation={id => {
                handleSelectConversation(id);
                if (!IS_TABLET) {
                  setShowSidebar(false);
                }
              }}
              onDeleteConversation={id => useChatStore.getState().deleteConversation(id)}
              onNewConversation={async () => {
                await handleCreateNewConversation();
                if (!IS_TABLET) {
                  setShowSidebar(false);
                }
              }}
              onToggleFavorite={id => useChatStore.getState().toggleFavorite(id)}
            />
          </View>
        </>
      )}

      {/* Main chat area */}
      <View
        style={[styles.mainContent, (IS_TABLET || showSidebar) && styles.mainContentWithSidebar]}
      >
        <ChatHeader
          isTablet={IS_TABLET}
          onMenuPress={openSidebar}
          onNewChatPress={handleCreateNewConversation}
        />

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          contentInsetAdjustmentBehavior="never"
          automaticallyAdjustContentInsets={false}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          scrollEventThrottle={16}
        >
          {currentMessages.length === 0 ? (
            <WelcomeScreen
              starters={conversationStarters}
              onSuggestionSelect={handleSuggestionSelect}
            />
          ) : (
            <Animated.View style={{ opacity: messagesFadeAnim }}>
              {/* Debug: Rendering ${currentMessages.length} messages */}
              {currentMessages.map((msg, index) => (
                <MessageBubble
                  key={msg.id}
                  msg={msg}
                  fadeAnim={messagesFadeAnim}
                  isStreaming={
                    msg.role !== 'user' && index === currentMessages.length - 1 && isSending
                  }
                />
              ))}
              {/* Render streaming message if active */}
              {streamingMessage && streamingMessage.conversationId === activeConversationId && (
                <MessageBubble
                  key={`streaming-${streamingMessage.conversationId}`}
                  msg={
                    {
                      id: 'streaming',
                      role: 'assistant',
                      content: streamingMessage.content,
                      createdAt: new Date(),
                      conversationId: streamingMessage.conversationId,
                    } as ChatMessage
                  }
                  fadeAnim={messagesFadeAnim}
                  isStreaming={true}
                />
              )}
              <TypingIndicator visible={isSending && !streamingMessage} status={typingStatus} />
            </Animated.View>
          )}
        </ScrollView>

        <ChatInput
          isSending={isSending}
          isInputFocused={isInputFocused}
          inputHeight={inputHeight}
          pendingAttachments={pendingAttachments}
          contextType={contextType}
          contextData={contextData}
          sendButtonScale={sendButtonScale}
          attachButtonScale={attachButtonScale}
          message={message}
          setInputHeight={setInputHeight}
          setIsInputFocused={setIsInputFocused}
          setMessage={setMessage}
          onSendMessage={(msg, attachments) => handleSend(msg, attachments)}
          animateButtonPress={animateButtonPress}
          handleImageUpload={handleImageUpload}
          setPendingAttachments={setPendingAttachments}
          onSuggestionSelected={suggestion => setMessage(suggestion)}
          scrollToBottom={scrollToBottom}
        />
      </View>

      {/* Streamlined ChatGPT/Claude-style interface - no modals needed */}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },

  // Sidebar
  sidebar: {
    width: 300,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  sidebarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  sidebarBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.backdropColor,
    zIndex: 999,
  },

  // Main content
  mainContent: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainContentWithSidebar: {
    marginLeft: 0, // Will be handled by flexDirection: 'row'
  },

  // CLAUDE-005: Refined Messages Layout - Claude-style conversation flow
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: BeautyMinimalTheme.spacing.md, // 12px compact Claude-style padding
    paddingBottom: BeautyMinimalTheme.spacing.xl, // 24px balanced bottom spacing
    flexGrow: 1,
  },
});

// Export memoized component for performance optimization
export default memo(ChatGPTInterface, (prevProps, nextProps) => {
  // CRITICAL: Shallow comparison instead of JSON.stringify for performance and stability
  const prevContext = prevProps.contextData || {};
  const nextContext = nextProps.contextData || {};

  // Compare primitive values only to avoid object recreation issues
  const contextDataEqual =
    prevContext.name === nextContext.name &&
    Object.keys(prevContext).length === Object.keys(nextContext).length;

  return (
    prevProps.conversationId === nextProps.conversationId &&
    prevProps.contextType === nextProps.contextType &&
    prevProps.contextId === nextProps.contextId &&
    prevProps.isModal === nextProps.isModal &&
    contextDataEqual
  );
});
