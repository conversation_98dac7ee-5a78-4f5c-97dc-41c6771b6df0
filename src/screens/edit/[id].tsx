import React, { useState, useEffect, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Switch,
} from 'react-native';
import { logger } from '@/utils/logger';
import { router, useLocalSearchParams } from 'expo-router';
import { ChevronLeft, ChevronDown } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { _commonStyles } from '@/styles/commonStyles';
import { useInventoryStore } from '@/stores/inventory-store';
import { Product } from '@/types/inventory';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';
import { ProductNamingService } from '@/services/productNamingService';
import { useSalonConfigStore } from '@/stores/salon-config-store';

export default function EditInventoryItemScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getProduct, updateProduct, searchProducts } = useInventoryStore();
  const {
    // volumeUnit,
    // weightUnit,
    // toBaseVolume,
    // toBaseWeight,
    // isMetric,
    // developerTerm,
    // colorTerm,
    // formatCurrency,
  } = useRegionalUnits();
  const { regionalConfig, configuration } = useSalonConfigStore();

  const [brand, setBrand] = useState('');
  const [productType, setProductType] = useState('');
  const [line, setLine] = useState('');
  const [shade, setShade] = useState('');
  const [unit, setUnit] = useState<'ml' | 'g' | 'unidad'>('ml');
  const [size, setSize] = useState('');
  const [lowStockThreshold, setLowStockThreshold] = useState('');
  const [barcode, setBarcode] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [category, setCategory] = useState<Product['category']>('otro');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [notes, setNotes] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [originalProduct, setOriginalProduct] = useState<Product | null>(null);

  const categories: Array<{ value: Product['category']; label: string }> = [
    { value: 'tinte', label: 'Tinte' },
    { value: 'oxidante', label: 'Oxidante' },
    { value: 'decolorante', label: 'Decolorante' },
    { value: 'tratamiento', label: 'Tratamiento' },
    { value: 'otro', label: 'Otro' },
  ];

  // Generate display name based on current form values
  const displayName = useMemo(() => {
    const product = {
      type: productType,
      brand,
      line,
      shade,
    };
    return ProductNamingService.generateDisplayName(
      product,
      regionalConfig || undefined,
      configuration.inventoryControlLevel
    );
  }, [productType, brand, line, shade, regionalConfig, configuration.inventoryControlLevel]);

  useEffect(() => {
    if (id) {
      const product = getProduct(id);
      if (product) {
        setOriginalProduct(product);

        // Load structured fields
        setBrand(product.brand);
        setProductType(product.type || '');
        setLine(product.line || '');
        setShade(product.shade || '');

        setCategory(product.category);
        setUnit(product.unitType);
        setSize(product.unitSize.toString());
        setLowStockThreshold(product.minStock.toString());
        setPurchasePrice(product.purchasePrice.toString());
        setBarcode(product.barcode || '');
        setNotes(product.notes || '');
        setIsActive(product.isActive);
      } else {
        Alert.alert('Error', 'Producto no encontrado');
        router.back();
      }
    }
  }, [id, getProduct]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!brand.trim()) {
      newErrors.brand = 'La marca es obligatoria';
    }

    if (!productType.trim()) {
      newErrors.productType = 'El tipo de producto es obligatorio';
    }

    if (!size.trim() || parseFloat(size) <= 0) {
      newErrors.size = 'El tamaño del envase es obligatorio';
    }

    if (!purchasePrice.trim() || parseFloat(purchasePrice) <= 0) {
      newErrors.purchasePrice = 'El precio de compra es obligatorio';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm() || !originalProduct) {
      return;
    }

    setIsLoading(true);

    try {
      const _costPerUnit = parseFloat(purchasePrice) / parseFloat(size);

      // Check for duplicates using similarity (excluding current product)
      const searchQuery = displayName || `${brand} ${productType} ${shade}`.trim();
      const existingProducts = searchProducts(searchQuery);

      const duplicates = existingProducts.filter(p => {
        if (p.id === originalProduct.id) return false; // Exclude self

        const similarity = ProductNamingService.calculateSimilarity(
          displayName,
          p.displayName || p.name || ''
        );
        return similarity > 80; // 80% similarity threshold
      });

      if (duplicates.length > 0) {
        const duplicate = duplicates[0];
        Alert.alert(
          'Producto Duplicado',
          `Ya existe un producto similar:\n\n${duplicate.displayName || duplicate.name}\n\n¿Deseas continuar?`,
          [
            { text: 'Cancelar', style: 'cancel' },
            { text: 'Continuar', onPress: () => saveProduct() },
          ]
        );
        return;
      }

      await saveProduct();
    } catch (error) {
      logger.error('Error saving inventory item', 'EditInventoryItemScreen', error);
      Alert.alert('Error', 'No se pudo guardar el producto');
    } finally {
      setIsLoading(false);
    }
  };

  const saveProduct = async () => {
    if (!originalProduct) return;

    const costPerUnit = parseFloat(purchasePrice) / parseFloat(size);

    await updateProduct(originalProduct.id, {
      brand: brand.trim(),
      type: productType.trim(),
      line: line.trim() || undefined,
      shade: shade.trim() || undefined,
      displayName: displayName,
      category,
      minStock: parseFloat(lowStockThreshold) || 0,
      unitType: unit,
      unitSize: parseFloat(size),
      purchasePrice: parseFloat(purchasePrice),
      costPerUnit,
      isActive,
      barcode: barcode.trim() || undefined,
      notes: notes.trim() || undefined,
    });

    Alert.alert('Éxito', 'Producto actualizado correctamente', [
      { text: 'OK', onPress: () => router.back() },
    ]);
  };

  if (!originalProduct) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Editar Producto</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Información del Artículo</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Marca *</Text>
            <TextInput
              style={[styles.input, errors.brand && styles.inputError]}
              value={brand}
              onChangeText={setBrand}
              placeholder="Ej: Wella Professionals"
            />
            {errors.brand && <Text style={styles.errorText}>{errors.brand}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Categoría *</Text>
            <TouchableOpacity
              style={[styles.input, styles.pickerInput]}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={styles.pickerText}>
                {categories.find(c => c.value === category)?.label || 'Seleccionar'}
              </Text>
              <ChevronDown size={20} color={Colors.light.text} />
            </TouchableOpacity>
            {showCategoryPicker && (
              <View style={styles.pickerOptions}>
                {categories.map(cat => (
                  <TouchableOpacity
                    key={cat.value}
                    style={styles.pickerOption}
                    onPress={() => {
                      setCategory(cat.value);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text
                      style={[
                        styles.pickerOptionText,
                        category === cat.value && styles.pickerOptionTextSelected,
                      ]}
                    >
                      {cat.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Producto *</Text>
            <TextInput
              style={[styles.input, errors.productType && styles.inputError]}
              value={productType}
              onChangeText={setProductType}
              placeholder="Ej: Tinte, Oxidante, Champú"
            />
            {errors.productType && <Text style={styles.errorText}>{errors.productType}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={line}
              onChangeText={setLine}
              placeholder="Ej: Illumina Color, Koleston Perfect"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tono/Referencia</Text>
            <TextInput
              style={styles.input}
              value={shade}
              onChangeText={setShade}
              placeholder="Ej: 9.3, 8/0, Rubio Claro"
            />
          </View>

          <View style={styles.rowContainer}>
            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Tamaño del Envase *</Text>
              <TextInput
                style={[styles.input, errors.size && styles.inputError]}
                value={size}
                onChangeText={setSize}
                placeholder="Ej: 60"
                keyboardType="numeric"
              />
              {errors.size && <Text style={styles.errorText}>{errors.size}</Text>}
            </View>

            <View style={[styles.formGroup, styles.rowItem]}>
              <Text style={styles.label}>Umbral Bajo Stock</Text>
              <TextInput
                style={styles.input}
                value={lowStockThreshold}
                onChangeText={setLowStockThreshold}
                placeholder="Ej: 2"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Precio de Compra *</Text>
            <TextInput
              style={[styles.input, errors.purchasePrice && styles.inputError]}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="Ej: 25.50"
              keyboardType="decimal-pad"
            />
            {errors.purchasePrice && <Text style={styles.errorText}>{errors.purchasePrice}</Text>}
            {purchasePrice && size && (
              <Text style={styles.helperText}>
                Costo por {unit}: ${(parseFloat(purchasePrice) / parseFloat(size)).toFixed(3)}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Código Barras EAN (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={barcode}
              onChangeText={setBarcode}
              placeholder="Ej: 8005610433172"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notas (Opcional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Añade notas o información adicional"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.switchGroup}>
            <View>
              <Text style={styles.label}>Producto Activo</Text>
              <Text style={styles.switchHelperText}>
                Los productos inactivos no aparecerán en las búsquedas
              </Text>
            </View>
            <Switch
              value={isActive}
              onValueChange={setIsActive}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
            />
          </View>

          <View style={styles.stockInfo}>
            <Text style={styles.stockInfoTitle}>Stock Actual</Text>
            <Text style={styles.stockInfoValue}>
              {originalProduct.currentStock} {originalProduct.unitType}
            </Text>
            <Text style={styles.stockInfoNote}>
              Para modificar el stock, usa los botones de entrada/salida en el detalle del producto
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => router.back()}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? 'Guardando...' : 'Guardar Cambios'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 34,
  },
  formContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  inputError: {
    borderColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 12,
    marginTop: 5,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowItem: {
    flex: 1,
    marginRight: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerText: {
    fontSize: 15,
    color: Colors.light.text,
  },
  pickerOptions: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    marginTop: 5,
    overflow: 'hidden',
  },
  pickerOption: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: 15,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 5,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingVertical: 10,
  },
  switchHelperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 4,
  },
  stockInfo: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  stockInfoTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  stockInfoValue: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 8,
  },
  stockInfoNote: {
    fontSize: 12,
    color: Colors.light.gray,
  },
});
