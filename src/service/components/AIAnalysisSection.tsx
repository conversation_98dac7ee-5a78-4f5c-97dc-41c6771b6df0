import React, { memo, useMemo } from 'react';
import { Text, StyleSheet } from 'react-native';
import { Zap } from 'lucide-react-native';
import { BeautyButton, BeautyCard } from '@/components/beauty';
import PhotoGallery from '@/components/PhotoGallery';
import { PhotoAnalysisLoading } from '@/components/animation/PhotoAnalysisLoading';
import { ConfidenceIndicator } from '@/components/ai/ConfidenceIndicator';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useDiagnosisContext } from '@/service/contexts/DiagnosisContext';

interface AIAnalysisSectionProps {
  hairPhotos: Array<{
    id: string;
    uri: string;
    angle: string;
    quality: object;
    timestamp: Date;
  }>;
  onAddPhoto: () => Promise<void>;
  onCameraCapture: () => Promise<void>;
  onRemovePhoto: (photoId: string) => void;
  handleAIAnalysis: () => Promise<void>;
  isAnalyzing: boolean;
}

const AIAnalysisSectionComponent: React.FC<AIAnalysisSectionProps> = ({
  hairPhotos,
  onAddPhoto,
  onCameraCapture,
  onRemovePhoto,
  handleAIAnalysis,
  isAnalyzing,
}) => {
  const { analysisResult } = useDiagnosisContext();

  // Memoize confidence indicator reasoning to avoid recalculation
  const confidenceReasoning = useMemo(() => {
    if (!analysisResult) return [];
    return [
      'Estructura capilar analizada mediante IA',
      `Tono detectado: ${analysisResult.overallTone}`,
      `Nivel promedio: ${analysisResult.averageLevel}`,
      `Reflejo identificado: ${analysisResult.overallReflect}`,
    ];
  }, [analysisResult]);

  // Memoize analysis data for ConfidenceIndicator
  const analysisData = useMemo(() => {
    if (!analysisResult) return undefined;
    return {
      tone: analysisResult.overallTone,
      level: analysisResult.averageLevel,
      reflect: analysisResult.overallReflect,
      confidence: analysisResult.overallConfidence,
    };
  }, [analysisResult]);
  return (
    <>
      <Text style={styles.sectionTitle}>Fotografías del cabello (3-5 imágenes)</Text>

      <PhotoGallery
        photos={hairPhotos}
        onAddPhoto={onAddPhoto}
        onCameraCapture={onCameraCapture}
        onRemovePhoto={onRemovePhoto}
        maxPhotos={5}
        showQuality={true}
      />

      {hairPhotos.length > 0 && (
        <BeautyButton
          title="Analizar con IA"
          loadingTitle="Analizando..."
          onPress={handleAIAnalysis}
          disabled={isAnalyzing}
          loading={isAnalyzing}
          variant="primary"
          size="lg"
          icon={Zap}
          fullWidth
          style={styles.aiButton}
        />
      )}

      {isAnalyzing && (
        <BeautyCard variant="default" style={styles.loadingCard}>
          <PhotoAnalysisLoading stage="analyzing-color" showProgressText={true} />
        </BeautyCard>
      )}

      {analysisResult && analysisResult.overallConfidence && !isAnalyzing && (
        <ConfidenceIndicator
          confidence={analysisResult.overallConfidence}
          context="diagnosis"
          showDetailed={true}
          reasoning={confidenceReasoning}
          analysisData={analysisData}
        />
      )}
    </>
  );
};

// Export memoized component
export const AIAnalysisSection = memo(AIAnalysisSectionComponent);

// Display name for debugging
AIAnalysisSection.displayName = 'AIAnalysisSection';

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  aiButton: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  loadingCard: {
    marginVertical: 12,
    padding: 16,
  },
});
