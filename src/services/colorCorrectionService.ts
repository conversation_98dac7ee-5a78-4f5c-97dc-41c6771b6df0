import { HairZone, UnwantedTone, HairState } from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

export interface CorrectionNeed {
  type: 'neutralization' | 'pre-pigmentation' | 're-pigmentation' | 'pre-lightening';
  reason: string;
  zones: HairZone[];
  severity: 'light' | 'medium' | 'strong';
}

export interface CorrectionStep {
  stepNumber: number;
  title: string;
  products: string[];
  applicationTime: number;
  instructions: string[];
  rinse: boolean;
}

export interface ColorCorrectionAnalysis {
  needsCorrection: boolean;
  corrections: CorrectionNeed[];
  steps: CorrectionStep[];
  totalTime: number;
  warnings: string[];
}

// Tabla de neutralización basada en la rueda de color
const _NEUTRALIZATION_MAP: Record<UnwantedTone, string> = {
  [UnwantedTone.ORANGE]: 'Azul/Cenizo',
  [UnwantedTone.YELLOW]: 'Violeta/Irisado',
  [UnwantedTone.GREEN]: 'Rojo/Cobrizo',
  [UnwantedTone.RED]: 'Verde/Mate',
  [UnwantedTone.ASHY_EXCESS]: 'Dorado/Cálido',
};

// Pigmentos necesarios por nivel para pre-pigmentación
const _PRE_PIGMENTATION_MAP: Record<number, string> = {
  3: 'Rojo intenso',
  4: 'Rojo cobrizo',
  5: 'Cobrizo',
  6: 'Cobrizo dorado',
  7: 'Dorado',
  8: 'Dorado claro',
  9: 'Beige',
  10: 'Platino',
};

export class ColorCorrectionService {
  static detectCorrectionNeeds(
    currentAnalysis: Record<HairZone, ZoneColorAnalysis>,
    desiredAnalysis: DesiredColorAnalysisResult,
    unwantedTones?: Partial<Record<HairZone, UnwantedTone>>
  ): CorrectionNeed[] {
    const corrections: CorrectionNeed[] = [];

    // Detectar necesidad de neutralización
    if (unwantedTones) {
      Object.entries(unwantedTones).forEach(([zone, tone]) => {
        if (tone) {
          corrections.push({
            type: 'neutralization',
            reason: `Neutralizar tono ${tone} en ${zone}`,
            zones: [zone as HairZone],
            severity: this.calculateSeverity(currentAnalysis[zone as HairZone]),
          });
        }
      });
    }

    // Detectar necesidad de pre-pigmentación
    const currentLevel = Math.min(...Object.values(currentAnalysis).map(z => z.level));
    const desiredLevel = parseInt(desiredAnalysis.general.overallLevel);

    if (currentLevel > desiredLevel && currentLevel - desiredLevel >= 3) {
      const affectedZones = Object.entries(currentAnalysis)
        .filter(([_, analysis]) => analysis.level > desiredLevel + 2)
        .map(([zone, _]) => zone as HairZone);

      corrections.push({
        type: 'pre-pigmentation',
        reason: `Oscurecer de nivel ${currentLevel} a ${desiredLevel} requiere reponer pigmentos`,
        zones: affectedZones,
        severity: currentLevel - desiredLevel > 4 ? 'strong' : 'medium',
      });
    }

    // Detectar necesidad de re-pigmentación para cabellos decolorados
    const hasDecoloredZones = Object.values(currentAnalysis).some(
      z => z.state === HairState.BLEACHED
    );

    if (hasDecoloredZones && desiredLevel < 8) {
      const decoloredZones = Object.entries(currentAnalysis)
        .filter(([_, analysis]) => analysis.state === HairState.BLEACHED)
        .map(([zone, _]) => zone as HairZone);

      corrections.push({
        type: 're-pigmentation',
        reason: 'Cabello decolorado necesita pigmentos cálidos para evitar resultado verdoso',
        zones: decoloredZones,
        severity: 'medium',
      });
    }

    // Detectar necesidad de pre-aclaración
    if (desiredLevel > currentLevel && desiredLevel - currentLevel > 2) {
      corrections.push({
        type: 'pre-lightening',
        reason: `Aclarar de nivel ${currentLevel} a ${desiredLevel} requiere decoloración previa`,
        zones: [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS],
        severity: desiredLevel - currentLevel > 4 ? 'strong' : 'medium',
      });
    }

    return corrections;
  }

  static generateCorrectionSteps(
    corrections: CorrectionNeed[],
    brand: string,
    line: string
  ): CorrectionStep[] {
    const steps: CorrectionStep[] = [];
    let stepNumber = 1;

    // Generar pasos de neutralización
    const neutralizations = corrections.filter(c => c.type === 'neutralization');
    if (neutralizations.length > 0) {
      const _products: string[] = [];
      const zones = new Set<HairZone>();

      neutralizations.forEach(correction => {
        correction.zones.forEach(zone => zones.add(zone));
      });

      steps.push({
        stepNumber: stepNumber++,
        title: 'NEUTRALIZACIÓN DE MATICES NO DESEADOS',
        products: [
          `${brand} ${line} /2 Mate (10g)`,
          `${brand} ${line} /69 Violeta Rojizo (5g)`,
          'Oxidante 10 vol (30g)',
        ],
        applicationTime: 20,
        instructions: [
          `Aplicar en las zonas con matices no deseados: ${Array.from(zones).join(', ')}`,
          'Peinar para distribuir uniformemente',
          'NO aplicar calor',
          'Vigilar el proceso cada 5 minutos',
        ],
        rinse: true,
      });
    }

    // Generar pasos de pre-pigmentación
    const prePigmentations = corrections.filter(
      c => c.type === 'pre-pigmentation' || c.type === 're-pigmentation'
    );
    if (prePigmentations.length > 0) {
      const targetLevel = 6; // Nivel medio para pre-pigmentación

      steps.push({
        stepNumber: stepNumber++,
        title: 'PRE-PIGMENTACIÓN',
        products: [
          `${brand} ${line} ${targetLevel}/4 Cobrizo (20g)`,
          'Agua tibia (10ml)',
          'NO usar oxidante',
        ],
        applicationTime: 15,
        instructions: [
          'Mezclar el tinte con agua hasta obtener consistencia cremosa',
          'Aplicar en medios y puntas (evitar raíces si están naturales)',
          'Peinar para distribuir',
          'NO enjuagar antes del siguiente paso',
        ],
        rinse: false,
      });
    }

    // Generar pasos de pre-aclaración
    const preLightenings = corrections.filter(c => c.type === 'pre-lightening');
    if (preLightenings.length > 0) {
      steps.push({
        stepNumber: stepNumber++,
        title: 'DECOLORACIÓN CONTROLADA',
        products: [
          'Polvo decolorante (30g)',
          'Oxidante 20-30 vol según necesidad (60g)',
          'Plex/Protector capilar (según instrucciones)',
        ],
        applicationTime: 35,
        instructions: [
          'Aplicar primero en zonas más oscuras',
          'Evitar el cuero cabelludo (dejar 1cm)',
          'Controlar cada 10 minutos',
          'Detener cuando alcance el fondo de aclaración deseado',
        ],
        rinse: true,
      });
    }

    return steps;
  }

  static analyzeColorCorrection(
    currentAnalysis: Record<HairZone, ZoneColorAnalysis>,
    desiredAnalysis: DesiredColorAnalysisResult,
    unwantedTones?: Partial<Record<HairZone, UnwantedTone>>,
    brand = "L'Oréal",
    line = 'Majirel'
  ): ColorCorrectionAnalysis {
    const corrections = this.detectCorrectionNeeds(currentAnalysis, desiredAnalysis, unwantedTones);
    const steps = this.generateCorrectionSteps(corrections, brand, line);

    const totalTime = steps.reduce((sum, step) => sum + step.applicationTime, 0);

    const warnings: string[] = [];

    if (corrections.some(c => c.severity === 'strong')) {
      warnings.push('Proceso complejo: Se recomienda realizarlo en varias sesiones');
    }

    if (corrections.some(c => c.type === 'pre-lightening')) {
      warnings.push('La decoloración puede causar daño: Usar protector capilar obligatorio');
    }

    if (totalTime > 60) {
      warnings.push(`Tiempo total elevado (${totalTime} min): Considerar dividir el proceso`);
    }

    return {
      needsCorrection: corrections.length > 0,
      corrections,
      steps,
      totalTime,
      warnings,
    };
  }

  private static calculateSeverity(analysis: ZoneColorAnalysis): 'light' | 'medium' | 'strong' {
    // Lógica simplificada - en producción sería más compleja
    if (analysis.damage === 'Alto') return 'strong';
    if (analysis.damage === 'Medio') return 'medium';
    return 'light';
  }

  static formatCorrectionForFormula(correctionAnalysis: ColorCorrectionAnalysis): string {
    if (!correctionAnalysis.needsCorrection) return '';

    let formattedCorrection = '\n⚠️ CORRECCIÓN DE COLOR NECESARIA:\n';
    formattedCorrection += '━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n';

    // Añadir razones
    correctionAnalysis.corrections.forEach(correction => {
      formattedCorrection += `• ${correction.reason}\n`;
    });

    formattedCorrection += '\n';

    // Añadir pasos
    correctionAnalysis.steps.forEach(step => {
      formattedCorrection += `PASO ${step.stepNumber} - ${step.title} (${step.applicationTime} min):\n`;

      step.products.forEach(product => {
        formattedCorrection += `• ${product}\n`;
      });

      formattedCorrection += '\nInstrucciones:\n';
      step.instructions.forEach((instruction, index) => {
        formattedCorrection += `${index + 1}. ${instruction}\n`;
      });

      if (step.rinse) {
        formattedCorrection += '\n✓ Enjuagar completamente antes del siguiente paso\n';
      } else {
        formattedCorrection += '\n✗ NO enjuagar - Proceder directamente al siguiente paso\n';
      }

      formattedCorrection += '\n';
    });

    // Añadir advertencias
    if (correctionAnalysis.warnings.length > 0) {
      formattedCorrection += 'ADVERTENCIAS:\n';
      correctionAnalysis.warnings.forEach(warning => {
        formattedCorrection += `⚠️ ${warning}\n`;
      });
    }

    return formattedCorrection;
  }
}
