import {
  VisualFormulationData,
  ZoneFormula,
  FormulaIngredient,
  TimelineBlock,
  ColorTransition,
  MixingProportions,
  ApplicationGuide,
  ContextualTip,
  ConversionInfo,
  ParseResult,
  ParserOptions,
} from '@/types/visual-formulation';

interface ParseContext {
  currentLevel?: number;
  targetLevel?: number;
  currentTone?: string;
  targetTone?: string;
  clientName?: string;
  serviceDate?: string;
  stylistName?: string;
  levelDifference?: number;
  correctionNeeded?: boolean;
  [key: string]: unknown;
}

/**
 * Advanced formula parser that converts text formulas to structured visual data
 * Compatible with both MockFormulationService and future OpenAI responses
 */
export class VisualFormulaParser {
  private options: ParserOptions;

  constructor(options: ParserOptions = {}) {
    this.options = {
      language: options.language || 'es',
      measurementSystem: options.measurementSystem || 'metric',
      includeDefaults: options.includeDefaults !== false,
      strictMode: options.strictMode || false,
    };
  }

  /**
   * Main parse method - converts formula text to visual data
   */
  parse(formulaText: string, context?: ParseContext): ParseResult {
    try {
      const data = this.createBaseStructure(formulaText);

      // Extract all components
      data.brand = this.extractBrand(formulaText);
      data.line = this.extractLine(formulaText);
      data.zones = this.extractZoneFormulas(formulaText);
      data.colorTransition = this.extractColorTransition(formulaText, context);
      data.timeline = this.extractTimeline(formulaText);
      data.mixingProportions = this.extractMixingProportions(formulaText, data.zones);
      data.applicationGuide = this.extractApplicationGuide(formulaText);
      data.tips = this.extractTips(formulaText, context);
      data.conversion = this.extractConversionInfo(formulaText);
      data.expectedResult = this.extractExpectedResult(formulaText);

      // Add client info if available
      if (context?.clientName) data.clientName = context.clientName;
      if (context?.serviceDate) data.serviceDate = context.serviceDate;
      if (context?.stylistName) data.stylistName = context.stylistName;

      return {
        success: true,
        data,
        warnings: this.validateData(data),
      };
    } catch (error) {
      return {
        success: false,
        errors: [
          error instanceof Error ? error.message : 'Error desconocido al parsear la fórmula',
        ],
      };
    }
  }

  /**
   * Create base structure with defaults
   */
  private createBaseStructure(originalText: string): VisualFormulationData {
    return {
      brand: 'Unknown',
      line: 'Unknown',
      zones: [],
      colorTransition: {
        current: { level: 5, tone: 'Natural' },
        target: { level: 7, tone: 'Dorado' },
        difficulty: 'moderate',
      },
      timeline: [],
      mixingProportions: {
        ratio: '1:1.5',
        colorAmount: 60,
        developerAmount: 90,
        totalAmount: 150,
        unit: 'g',
        presets: [],
      },
      applicationGuide: {
        technique: 'full_color',
        zones: [],
        totalTime: 45,
        steps: [],
      },
      tips: [],
      originalText,
      confidence: 0.85,
      source: 'mock',
      parseVersion: '1.0.0',
    };
  }

  /**
   * Extract brand from formula text
   */
  private extractBrand(text: string): string {
    // Look for brand patterns
    const brandPatterns = [
      /^([A-Za-z\s&]+?)(?:\s+[A-Za-z]+)?:/m,
      /Marca:\s*([A-Za-z\s&]+)/i,
      /Brand:\s*([A-Za-z\s&]+)/i,
    ];

    for (const pattern of brandPatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1].trim().split(/\s+/)[0];
      }
    }

    return 'Unknown';
  }

  /**
   * Extract product line from formula text
   */
  private extractLine(text: string): string {
    // Look for line patterns after brand
    const linePatterns = [
      /^[A-Za-z\s&]+?\s+([A-Za-z]+):/m,
      /Línea:\s*([A-Za-z\s]+)/i,
      /Line:\s*([A-Za-z\s]+)/i,
    ];

    for (const pattern of linePatterns) {
      const match = text.match(pattern);
      if (match) {
        const parts = match[0].split(/\s+/);
        return parts[parts.length - 1].replace(':', '');
      }
    }

    return 'Unknown';
  }

  /**
   * Extract zone-specific formulas
   */
  private extractZoneFormulas(text: string): ZoneFormula[] {
    const zones: ZoneFormula[] = [];
    const lines = text.split('\n');

    // First, determine the coloring type (for future enhancement)

    // Enhanced patterns with more synonyms and variations
    const zonePatterns = {
      roots: /FÓRMULA RAÍCES|raíc|root|raiz|\(0-\d+cm\)|nacimiento|crecimiento|retoque|base/i,
      mids: /FÓRMULA MEDIOS|medio|mid|longitud|largo|tallo|shaft/i,
      ends: /PUNTAS|punta|end|tip|extremo|final/i,
      global: /Fórmula Tinte Completo|completo|full|global|uniforme|todo|general|única/i,
      midsEnds: /FÓRMULA MEDIOS-PUNTAS|medios.*puntas|largos|lengths/i,
    };

    let currentZone: ZoneFormula | null = null;
    let isInFormulaSection = false;

    for (const line of lines) {
      // Check if we're entering a formula section
      if (line.includes('Fórmula') || line.includes('Formula')) {
        isInFormulaSection = true;

        // Determine which zone this formula is for
        for (const [zone, pattern] of Object.entries(zonePatterns)) {
          if (pattern.test(line)) {
            // Handle special case for mids-ends combined
            const actualZone = zone === 'midsEnds' ? 'mids' : zone;
            currentZone = {
              zone: actualZone as ZoneFormula['zone'],
              title: this.cleanTitle(line),
              ingredients: [],
              specialNotes: zone === 'midsEnds' ? ['Aplicar en medios y puntas'] : [],
            };
            zones.push(currentZone);
            break;
          }
        }

        // If no specific zone found, assume global
        if (!currentZone && isInFormulaSection) {
          currentZone = {
            zone: 'global',
            title: 'Fórmula Principal',
            ingredients: [],
          };
          zones.push(currentZone);
        }
      }

      // Extract ingredients
      if (currentZone && line.startsWith('-')) {
        const ingredient = this.parseIngredientLine(line);
        if (ingredient) {
          currentZone.ingredients.push(ingredient);
        }
      }

      // Extract mixing ratio
      if (
        currentZone &&
        line.includes(':') &&
        (line.includes('1:') || line.includes('Proporción'))
      ) {
        const ratioMatch = line.match(/\b(1:\d+(?:\.\d+)?)\b/);
        if (ratioMatch) {
          currentZone.mixingRatio = ratioMatch[1];
        }
      }

      // Extract processing time for zone
      if (currentZone && line.includes('Tiempo de proceso')) {
        const timeMatch = line.match(/(\d+)\s*min/i);
        if (timeMatch) {
          currentZone.processingTime = parseInt(timeMatch[1]);
        }
      }
    }

    // If no zones found, create a default global zone
    if (zones.length === 0 && this.options.includeDefaults) {
      zones.push(this.createDefaultZone(text));
    }

    return zones;
  }

  /**
   * Parse a single ingredient line
   */
  private parseIngredientLine(line: string): FormulaIngredient | null {
    // Enhanced patterns to match more ingredient line formats
    const patterns = [
      // Products with numbers like Olaplex N°1, K18 No.2
      /[-–•]\s*(.+?[nN][°º]\s*\d+.*?)\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|gramos?|mililitros?|cm|gotas?)[\)\]]?/i,
      /[-–•]\s*(.+?[nN]o\.\s*\d+.*?)\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|gramos?|mililitros?|cm|gotas?)[\)\]]?/i,
      /[-–•]\s*(.+?#\s*\d+.*?)\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|gramos?|mililitros?|cm|gotas?)[\)\]]?/i,
      // Standard format: - Product Name Code (amount unit)
      /[-–•]\s*(.+?)\s+(\d+[/.,-]?\d*)\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|gramos?|mililitros?|cm|gotas?)[\)\]]?/i,
      // Volume format: - Product 20 vol (amount unit)
      /[-–•]\s*(.+?)\s+(\d+)\s*vol\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|cm|gotas?)[\)\]]?/i,
      // Simple format: - Product (amount unit)
      /[-–•]\s*(.+?)\s*[\(\[]?(\d+(?:\.\d+)?)\s*([gmlo]+z?|cm|gotas?)[\)\]]?/i,
      // Colon format: Product: amount unit
      /\s*(.+?)\s*:\s*(\d+(?:\.\d+)?)\s*([gmlo]+z?|cm|gotas?)/i,
      // Dash format: Product - amount unit
      /\s*(.+?)\s*[-–]\s*(\d+(?:\.\d+)?)\s*([gmlo]+z?|cm|gotas?)/i,
    ];

    for (const pattern of patterns) {
      const match = line.match(pattern);
      if (match) {
        const productName = match[1].trim();
        const amount = parseFloat(match[match.length - 2]);
        const unit = this.normalizeUnit(match[match.length - 1]);

        // Determine type and icon
        const { type, icon } = this.categorizeIngredient(productName);

        // Extract code if present
        const codeMatch = productName.match(/\b(\d+[/.,-]?\d*)\b/);
        const code = codeMatch ? codeMatch[1] : undefined;

        return {
          type,
          name: productName,
          code,
          amount,
          unit: unit as FormulaIngredient['unit'],
          icon,
        };
      }
    }

    return null;
  }

  /**
   * Categorize ingredient type and assign icon with enhanced patterns
   */
  private categorizeIngredient(name: string): {
    type: FormulaIngredient['type'];
    icon: FormulaIngredient['icon'];
  } {
    const lowerName = name.toLowerCase();

    // Enhanced developer patterns
    const developerPatterns = [
      'oxidante',
      'developer',
      'revelador',
      'emulsión',
      'peroxide',
      'peróxido',
      'activador',
      'activator',
      'vol',
      'volúmenes',
      'cream developer',
      'agua oxigenada',
      'h2o2',
      '10v',
      '20v',
      '30v',
      '40v',
    ];

    if (developerPatterns.some(pattern => lowerName.includes(pattern))) {
      return { type: 'developer', icon: 'bottle' };
    }

    // Enhanced additive patterns - including color additives and pigments
    const additivePatterns = [
      'mix',
      'corrector',
      'neutraliz',
      'matizador',
      'toner',
      'booster',
      'intensificador',
      'concentrado',
      'pigment',
      'intensifier',
      'violeta',
      'violet',
      'azul',
      'blue',
      'verde',
      'green',
      'rojo',
      'red',
      'pure pigment',
      'pigmento puro',
      'color touch',
      'koleston perfect',
    ];

    // Check for measurement in cm as indicator of additive
    if (
      additivePatterns.some(pattern => lowerName.includes(pattern)) ||
      /\d+\s*cm/.test(name) ||
      /\d+\s*gotas?/.test(lowerName)
    ) {
      return { type: 'additive', icon: 'dropper' };
    }

    // Enhanced treatment patterns - including commercial bond builders
    const treatmentPatterns = [
      'tratamiento',
      'plex',
      'protector',
      'bond',
      'shield',
      'treatment',
      'serum',
      'ampolla',
      'ampoule',
      'protección',
      'reparador',
      // Commercial bond builders
      'olaplex',
      'k18',
      'b3',
      'ultim8',
      'wellaplex',
      'fibreplex',
      'smartbond',
      'lisaplex',
      'salonplex',
      'ph bonder',
      'bondfusion',
      'blondme bond',
      'bond ultim8',
      'bond enforcing',
      'bond multiplier',
      'bond rescue',
      'bond maintenance',
      'bond protect',
    ];

    // Check for product numbers like N°1, No.2, #3
    const hasProductNumber = /[nN][°º]\s*\d+|[nN]o\.\s*\d+|#\s*\d+/.test(name);

    if (treatmentPatterns.some(pattern => lowerName.includes(pattern)) || hasProductNumber) {
      return { type: 'treatment', icon: 'jar' };
    }

    // Default to color
    return { type: 'color', icon: 'tube' };
  }

  /**
   * Extract color transition information
   */
  private extractColorTransition(text: string, context?: ParseContext): ColorTransition {
    // Use context if available
    if (context?.currentLevel && context?.targetLevel) {
      const levelDiff = Math.abs(context.targetLevel - context.currentLevel);
      return {
        current: {
          level: context.currentLevel,
          tone: context.currentTone || 'Natural',
          hex: this.levelToHex(context.currentLevel),
        },
        target: {
          level: context.targetLevel,
          tone: context.targetTone || 'Objetivo',
          hex: this.levelToHex(context.targetLevel),
        },
        difficulty: this.calculateDifficulty(levelDiff, context),
        sessions: levelDiff > 4 ? Math.ceil(levelDiff / 3) : 1,
      };
    }

    // Try to extract from text
    const currentMatch = text.match(/Nivel actual[:\s]+(\d+(?:\.\d+)?)/i);
    const targetMatch = text.match(/Nivel deseado[:\s]+(\d+(?:\.\d+)?)/i);

    const currentLevel = currentMatch ? parseFloat(currentMatch[1]) : 5;
    const targetLevel = targetMatch ? parseFloat(targetMatch[1]) : 7;

    return {
      current: {
        level: currentLevel,
        tone: 'Natural',
        hex: this.levelToHex(currentLevel),
      },
      target: {
        level: targetLevel,
        tone: 'Objetivo',
        hex: this.levelToHex(targetLevel),
      },
      difficulty: this.calculateDifficulty(Math.abs(targetLevel - currentLevel)),
      sessions: Math.abs(targetLevel - currentLevel) > 4 ? 2 : 1,
    };
  }

  /**
   * Extract timeline information
   */
  private extractTimeline(text: string): TimelineBlock[] {
    const timeline: TimelineBlock[] = [];

    // Enhanced time extraction with multiple patterns
    const timePatterns = [
      /Tiempo de proceso:\s*(\d+)\s*min/i,
      /Tiempo total:\s*(\d+)\s*min/i,
      /Procesamiento:\s*(\d+)\s*min/i,
      /Duración:\s*(\d+)\s*min/i,
      /\b(\d+)\s*minutos?\s+(?:total|proceso)/i,
    ];

    let totalTime = 45; // default
    for (const pattern of timePatterns) {
      const match = text.match(pattern);
      if (match) {
        totalTime = parseInt(match[1]);
        break;
      }
    }

    // Check for step-based timing in application steps
    const stepPattern =
      /(?:dejar|leave)\s+(\d+)\s+min.*?(?:extender|extend).*?(\d+)\s+(?:minutos?\s+)?adicionales/is;
    const stepMatch = text.match(stepPattern);

    if (stepMatch) {
      // Extract step-based timing from application steps
      const rootTime = parseInt(stepMatch[1]);
      const additionalTime = parseInt(stepMatch[2]);

      timeline.push({
        id: 'roots',
        label: 'Raíces',
        startTime: 0,
        endTime: rootTime,
        zones: ['roots'],
        description: 'Aplicar primero en raíces',
      });

      timeline.push({
        id: 'mids-ends',
        label: 'Medios y Puntas',
        startTime: rootTime,
        endTime: rootTime + additionalTime,
        zones: ['mids', 'ends'],
        description: 'Extender a medios y puntas',
      });

      timeline.push({
        id: 'emulsify',
        label: 'Emulsionar',
        startTime: rootTime + additionalTime,
        endTime: totalTime,
        zones: ['global'],
        description: 'Emulsionar antes de enjuagar',
      });
    } else if (text.toLowerCase().includes('balayage')) {
      // Balayage timeline
      timeline.push({
        id: 'application',
        label: 'Aplicación Balayage',
        startTime: 0,
        endTime: totalTime * 0.7,
        zones: ['mids', 'ends'],
        description: 'Aplicar técnica balayage',
      });

      timeline.push({
        id: 'process',
        label: 'Procesamiento',
        startTime: totalTime * 0.7,
        endTime: totalTime,
        zones: ['mids', 'ends'],
        description: 'Dejar procesar',
      });
    } else if (text.toLowerCase().includes('highlights') || text.toLowerCase().includes('mechas')) {
      // Highlights timeline
      timeline.push({
        id: 'bleach',
        label: 'Decoloración',
        startTime: 0,
        endTime: totalTime * 0.8,
        zones: ['highlights'],
        description: 'Procesar mechas',
      });

      timeline.push({
        id: 'tone',
        label: 'Tonalización',
        startTime: totalTime * 0.8,
        endTime: totalTime,
        zones: ['highlights'],
        description: 'Tonalizar si es necesario',
      });
    } else if (this.options.includeDefaults) {
      // Default timeline
      timeline.push(
        {
          id: 'application',
          label: 'Aplicación',
          startTime: 0,
          endTime: 10,
          zones: ['all'],
          description: 'Aplicar producto',
        },
        {
          id: 'processing',
          label: 'Procesamiento',
          startTime: 10,
          endTime: 40,
          zones: ['all'],
          description: 'Dejar procesar',
        },
        {
          id: 'rinse',
          label: 'Enjuague',
          startTime: 40,
          endTime: 45,
          zones: ['all'],
          description: 'Enjuagar y finalizar',
        }
      );
    }

    return timeline;
  }

  /**
   * Extract mixing proportions
   */
  private extractMixingProportions(text: string, zones: ZoneFormula[]): MixingProportions {
    // Extract ratio
    const ratioMatch = text.match(/\b(1:\d+(?:\.\d+)?)\b/);
    const ratio = ratioMatch ? ratioMatch[1] : '1:1.5';

    // Calculate amounts from zones
    let totalColor = 0;
    let totalDeveloper = 0;

    zones.forEach(zone => {
      zone.ingredients.forEach(ing => {
        if (ing.type === 'color') {
          totalColor += ing.amount;
        } else if (ing.type === 'developer') {
          totalDeveloper += ing.amount;
        }
      });
    });

    // If no amounts found, use defaults
    if (totalColor === 0) totalColor = 60;
    if (totalDeveloper === 0) {
      const ratioNum = parseFloat(ratio.split(':')[1]);
      totalDeveloper = totalColor * ratioNum;
    }

    return {
      ratio,
      colorAmount: totalColor,
      developerAmount: totalDeveloper,
      totalAmount: totalColor + totalDeveloper,
      unit: this.options.measurementSystem === 'metric' ? 'g' : 'oz',
      presets: [
        {
          ratio: '1:1',
          description: 'Máxima cobertura',
          use: 'Tonos oscuros, canas resistentes',
        },
        {
          ratio: '1:1.5',
          description: 'Estándar',
          use: 'Mayoría de trabajos',
        },
        {
          ratio: '1:2',
          description: 'Suave',
          use: 'Tonos claros, matizadores',
        },
      ],
    };
  }

  /**
   * Extract application guide
   */
  private extractApplicationGuide(text: string): ApplicationGuide {
    const guide: ApplicationGuide = {
      technique: 'full_color',
      zones: [],
      totalTime: 45,
      steps: [],
    };

    // Enhanced technique detection with synonyms
    const techniquePatterns = {
      balayage: [
        'balayage',
        'barrido',
        'degradado',
        'babylights',
        'pintado a mano',
        'sombré',
        'ombré',
      ],
      highlights: [
        'mecha',
        'highlight',
        'reflejos',
        'iluminación',
        'foils',
        'papel aluminio',
        'gorro',
        'rayitos',
      ],
      zonal: ['raíces', 'medios', 'puntas', 'zonas', 'diferenciada', 'por secciones', 'retoque'],
      full_color: ['completo', 'uniforme', 'total', 'global', 'todo el cabello', 'general'],
    };

    // Check each technique pattern
    for (const [technique, patterns] of Object.entries(techniquePatterns)) {
      if (patterns.some(pattern => text.toLowerCase().includes(pattern))) {
        guide.technique = technique as ApplicationGuide['technique'];
        break;
      }
    }

    // Additional check for zonal based on formula structure
    const hasZones = text.includes('FÓRMULA RAÍCES') || text.includes('FÓRMULA MEDIOS-PUNTAS');
    if (hasZones && guide.technique === 'full_color') {
      guide.technique = 'zonal' as ApplicationGuide['technique'];
    }

    // Extract application steps
    const stepsSection = text.match(/Pasos de aplicación:([\s\S]*?)(?:\n\n|$)/i);
    if (stepsSection) {
      const stepLines = stepsSection[1].split('\n').filter(line => line.trim());
      stepLines.forEach((line, index) => {
        const stepMatch = line.match(/\d+\.\s*(.+)/);
        if (stepMatch) {
          guide.steps.push({
            order: index + 1,
            zone: 'all',
            description: stepMatch[1].trim(),
          });
        }
      });
    }

    // Define zones based on technique
    if ((guide.technique as ApplicationGuide['technique']) === 'zonal' || hasZones) {
      // For formulas with roots/mids/ends zones
      guide.zones = [
        {
          id: 'roots',
          name: 'Raíces',
          order: 1,
          color: '#ff6b6b', // Red color for roots zone
          description: '0-3cm desde el cuero cabelludo',
        },
        {
          id: 'mids',
          name: 'Medios',
          order: 2,
          color: '#f59e0b', // Orange color for mids zone
          description: 'Zona media del cabello',
        },
        {
          id: 'ends',
          name: 'Puntas',
          order: 3,
          color: '#4ecdc4', // Teal color for ends zone
          description: 'Últimos centímetros',
        },
      ];
    } else if (guide.technique === 'balayage') {
      guide.zones = [
        {
          id: 'v-section',
          name: 'Sección en V',
          order: 1,
          color: '#667eea',
          description: 'Patrón de aplicación',
        },
        {
          id: 'surface',
          name: 'Superficie',
          order: 2,
          color: '#a855f7',
          description: 'Mechones superficiales',
        },
      ];
    } else if (guide.technique === 'highlights') {
      guide.zones = [
        {
          id: 'foils',
          name: 'Mechones',
          order: 1,
          color: '#ec4899',
          description: 'Secciones con papel aluminio',
        },
        {
          id: 'between',
          name: 'Cabello natural',
          order: 2,
          color: '#94a3b8',
          description: 'Cabello sin decolorar',
        },
      ];
    } else {
      // Full color application - anatomical zones
      guide.zones = [
        {
          id: 'nape',
          name: 'Nuca',
          order: 1,
          color: '#ff6b6b',
          description: 'Mayor calor, procesa más rápido',
        },
        {
          id: 'crown',
          name: 'Corona',
          order: 2,
          color: '#4ecdc4',
          description: 'Menor calor, procesa más lento',
        },
        {
          id: 'sides',
          name: 'Laterales',
          order: 3,
          color: '#f59e0b',
          description: 'Temperatura media',
        },
      ];
    }

    // Extract total time
    const totalTimeMatch = text.match(/tiempo total[:\s]+(\d+)/i);
    if (totalTimeMatch) {
      guide.totalTime = parseInt(totalTimeMatch[1]);
    }

    return guide;
  }

  /**
   * Extract contextual tips
   */
  private extractTips(text: string, context?: ParseContext): ContextualTip[] {
    const tips: ContextualTip[] = [];

    // Look for warning indicators
    if (text.includes('⚠️') || text.includes('ADVERTENCIA')) {
      const warningLines = text
        .split('\n')
        .filter(line => line.includes('⚠️') || line.includes('ADVERTENCIA'));
      warningLines.forEach((line, index) => {
        tips.push({
          id: `warning-${index}`,
          type: 'warning',
          title: 'Advertencia',
          description: line.replace('⚠️', '').replace('ADVERTENCIA:', '').trim(),
        });
      });
    }

    // Add tips based on level difference
    if (context?.levelDifference && context.levelDifference > 3) {
      tips.push({
        id: 'level-diff',
        type: 'warning',
        title: 'Cambio significativo',
        description:
          'Considerar realizar el proceso en varias sesiones para mantener la salud del cabello',
      });
    }

    // Look for conversion notes
    if (text.includes('confianza') || text.includes('conversión')) {
      tips.push({
        id: 'conversion',
        type: 'info',
        title: 'Fórmula convertida',
        description: 'Se recomienda realizar prueba de mechón antes de la aplicación completa',
      });
    }

    // Add standard tips
    tips.push(
      {
        id: 'temperature',
        type: 'tip',
        title: 'Temperatura',
        description: 'Mantener entre 20-25°C para resultados óptimos',
      },
      {
        id: 'check',
        type: 'tip',
        title: 'Control',
        description: 'Revisar el proceso cada 10 minutos',
      }
    );

    return tips;
  }

  /**
   * Extract conversion information if present
   */
  private extractConversionInfo(text: string): ConversionInfo | undefined {
    const conversionHeader = text.match(/FÓRMULA (?:ADAPTADA|CONVERTIDA)/i);
    if (!conversionHeader) return undefined;

    const originalMatch = text.match(/Original:\s*([^-]+?)\s*-\s*(.+)/);
    const equivalentMatch = text.match(/Equivalente:\s*([^-]+?)\s*-?\s*(.+)/);
    const confidenceMatch = text.match(/confianza[:\s]+(\d+)%/i);

    if (originalMatch && equivalentMatch) {
      const [origBrand, origLine] = originalMatch[1].trim().split(/\s+/);
      const [targetBrand, targetLine] = equivalentMatch[1].trim().split(/\s+/);

      return {
        originalBrand: origBrand,
        originalLine: origLine,
        originalFormula: originalMatch[2].trim(),
        targetBrand: targetBrand,
        targetLine: targetLine || '',
        confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 85,
        adjustments: ['Proporciones adaptadas', 'Tiempos ajustados'],
        warnings: ['Realizar prueba de mechón obligatoria'],
      };
    }

    return undefined;
  }

  /**
   * Extract expected result information
   */
  private extractExpectedResult(text: string): VisualFormulationData['expectedResult'] {
    const resultSection = text.match(/Resultado esperado:([\s\S]*?)(?:\n\n|$)/i);

    if (resultSection) {
      return {
        description: 'Color uniforme con reflejos naturales',
        coverage: '100%',
        duration: '6-8 semanas',
        maintenance: ['Usar shampoo sin sulfatos', 'Tratamiento cada 15 días'],
      };
    }

    return {
      description: 'Resultado profesional según objetivo',
      coverage: 'Según técnica aplicada',
      duration: '4-8 semanas',
      maintenance: ['Seguir recomendaciones del estilista'],
    };
  }

  /**
   * Helper methods
   */

  private cleanTitle(text: string): string {
    return text.replace(/[:\d.]/g, '').trim();
  }

  private normalizeUnit(unit: string): string {
    const normalized = unit.toLowerCase().trim();
    if (normalized.includes('g') && !normalized.includes('gota')) return 'g';
    if (normalized.includes('ml')) return 'ml';
    if (normalized.includes('oz')) return normalized.includes('fl') ? 'fl oz' : 'oz';
    if (normalized.includes('cm')) return 'cm';
    if (normalized.includes('gota')) return 'gotas';
    return 'g';
  }

  private levelToHex(level: number): string {
    // Approximate color based on level
    const HAIR_LEVEL_COLORS = {
      1: '#1a0a00',
      2: '#2d1810',
      3: '#402820',
      4: '#4a3c28',
      5: '#5c4a3a',
      6: '#8B4513',
      7: '#A0522D',
      8: '#CD853F',
      9: '#DEB887',
      10: '#F5DEB3',
    } as const;

    const FALLBACK_COLOR = '#8B4513';

    const roundedLevel = Math.round(level);
    return HAIR_LEVEL_COLORS[roundedLevel as keyof typeof HAIR_LEVEL_COLORS] || FALLBACK_COLOR;
  }

  private calculateDifficulty(
    levelDiff: number,
    context?: ParseContext
  ): ColorTransition['difficulty'] {
    if (context?.correctionNeeded) return 'complex';
    if (levelDiff > 4) return 'challenging';
    if (levelDiff > 2) return 'moderate';
    return 'easy';
  }

  private createDefaultZone(text: string): ZoneFormula {
    // Create a default zone with parsed ingredients
    const ingredients: FormulaIngredient[] = [];

    // Try to find any ingredient lines
    const lines = text.split('\n');
    lines.forEach(line => {
      if (line.startsWith('-')) {
        const ingredient = this.parseIngredientLine(line);
        if (ingredient) ingredients.push(ingredient);
      }
    });

    // If no ingredients found, add defaults
    if (ingredients.length === 0) {
      ingredients.push(
        {
          type: 'color',
          name: 'Tinte 7/1',
          code: '7/1',
          amount: 60,
          unit: 'g',
          icon: 'tube',
        },
        {
          type: 'developer',
          name: 'Oxidante 20 vol',
          amount: 90,
          unit: 'ml',
          icon: 'bottle',
        }
      );
    }

    return {
      zone: 'global',
      title: 'Fórmula Principal',
      ingredients,
      mixingRatio: '1:1.5',
      processingTime: 35,
    };
  }

  private validateData(data: VisualFormulationData): string[] {
    const warnings: string[] = [];

    if (data.zones.length === 0) {
      warnings.push('No se encontraron zonas específicas en la fórmula');
    }

    if (data.brand === 'Unknown') {
      warnings.push('No se pudo identificar la marca');
    }

    if (data.timeline.length === 0) {
      warnings.push('No se encontró información de tiempos');
    }

    return warnings;
  }
}

/**
 * Default parser instance
 */
export const defaultParser = new VisualFormulaParser();

/**
 * Convenience function for quick parsing
 */
export function parseVisualFormula(
  formulaText: string,
  options?: ParserOptions,
  context?: ParseContext
): ParseResult {
  const parser = new VisualFormulaParser(options);
  return parser.parse(formulaText, context);
}
