# Client History Components

Professional components for displaying client service history in Salonier's beauty-minimalist design system.

## CompactFormulaDisplay

Transforms plain text formulas into rich visual displays for client history cards. Provides professional visual hierarchy showing color swatches, brand information, processing details, and confidence indicators.

### Usage

```tsx
import { CompactFormulaDisplay } from '@/components/client-history';

// Basic usage in a service history card
<CompactFormulaDisplay
  service={previousFormula}
  variant="compact"
  showConfidence={true}
/>

// Detailed view with notes
<CompactFormulaDisplay
  service={previousFormula}
  variant="detailed"
  showConfidence={true}
/>
```

### Props

| Prop             | Type                      | Default                     | Description                                                  |
| ---------------- | ------------------------- | --------------------------- | ------------------------------------------------------------ |
| `service`        | `PreviousFormula`         | **required**                | Formula service data from client history                     |
| `variant`        | `'compact' \| 'detailed'` | `'compact'`                 | Display density - compact for cards, detailed for full views |
| `showConfidence` | `boolean`                 | `true`                      | Show AI confidence indicator based on satisfaction score     |
| `onPress`        | `() => void`              | `undefined`                 | Optional press handler for interactions                      |
| `style`          | `ViewStyle`               | `undefined`                 | Additional custom styling                                    |
| `testID`         | `string`                  | `'compact-formula-display'` | Test identifier                                              |

### Visual Features

#### Brand Hierarchy

- **Brand Name**: Primary typography, professional color
- **Product Line**: Secondary typography, muted color
- **Visual Icon**: Palette icon for context

#### Color Visualization

- **Color Swatches**: Circular indicators showing hair level colors
- **Shade Labels**: Technical shade numbers (6.35, etc.)
- **Visual Mapping**: Automatic color generation from hair levels

#### Processing Information

- **Time Badge**: Processing time with clock icon
- **Volume Badge**: Oxidant volume with droplet icon
- **Ratio Badge**: Mixing ratios with trending icon
- **Rating**: Star rating with satisfaction score

#### Confidence Indicators

- **High Confidence** (4-5 stars): Green badge with "Alta confianza" + ⭐
- **Medium Confidence** (3 stars): Yellow badge with "Confianza media" + 👌
- **Low Confidence** (1-2 stars): Red badge with "Baja confianza" + ⚠️

### Design System Integration

- Uses `BeautyMinimalTheme` for consistent styling
- Follows 90/10 design strategy (neutral foundation + beauty accents)
- WCAG 2.1 AA compliant color contrast
- Professional spacing and typography
- Animated entrance with `react-native-reanimated`

### Formula Parsing

The component automatically parses formula text to extract:

- **Product shades** from numeric patterns (6.35, 9.03, etc.)
- **Oxidant information** from "oxidante 20vol" patterns
- **Mixing ratios** from "1:1", "2:1" patterns
- **Processing details** for visual badges

### Accessibility

- Semantic labels for screen readers
- Color swatch accessibility labels
- High contrast ratios for text
- Touch-friendly spacing (44pt minimum targets)

### Testing

Comprehensive test coverage including:

- Basic rendering and props
- Formula parsing edge cases
- Accessibility compliance
- Different confidence levels
- Variant differences

Run tests:

```bash
npm test CompactFormulaDisplay
```

## ServiceHistoryCard

Enhanced service history card that now uses `CompactFormulaDisplay` for professional formula visualization instead of plain text.

### Migration

The formula section has been replaced:

```tsx
// Before: Plain text formula
<View style={styles.formulaContainer}>
  <Text style={styles.formulaLabel}>Fórmula</Text>
  <Text style={styles.formulaText}>{service.formula}</Text>
</View>

// After: Rich visual display
<CompactFormulaDisplay
  service={service}
  variant="compact"
  showConfidence={true}
/>
```

### Benefits

- **Professional Appearance**: Visual hierarchy suitable for salon professionals
- **Better Information Density**: More data in less space
- **Enhanced UX**: Color swatches and visual cues improve comprehension
- **Consistency**: Matches Salonier's beauty-minimalist design system
- **Accessibility**: Screen reader friendly and WCAG compliant
