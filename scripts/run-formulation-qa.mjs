#!/usr/bin/env node
// Lightweight QA runner for formulation generation
// Requires: SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_TEST_EMAIL, SUPABASE_TEST_PASSWORD

import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
const TEST_EMAIL = process.env.SUPABASE_TEST_EMAIL;
const TEST_PASSWORD = process.env.SUPABASE_TEST_PASSWORD;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !TEST_EMAIL || !TEST_PASSWORD) {
  console.error('Missing env vars: SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_TEST_EMAIL, SUPABASE_TEST_PASSWORD');
  process.exit(2);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const scenariosPath = path.resolve('qa/formulation-scenarios.json');
const scenarios = JSON.parse(fs.readFileSync(scenariosPath, 'utf-8'));
if (!Array.isArray(scenarios) || scenarios.length === 0) {
  console.error('No QA scenarios found. Please ensure generator produced scenarios.');
  process.exit(2);
}

function hasPlaceholder(formula) {
  const txt = JSON.stringify(formula).toLowerCase();
  return txt.includes('seleccionar tono');
}

function sumStepTimes(formula) {
  return (formula?.steps || []).reduce((acc, s) => acc + (Number(s?.processingTime) || 0), 0);
}

async function fetchRules(brand, line) {
  const { data: b } = await supabase.from('brands').select('id').ilike('name', brand).maybeSingle();
  if (!b?.id) return null;
  const { data: l } = await supabase.from('product_lines').select('id').eq('brand_id', b.id).ilike('name', line).maybeSingle();
  if (!l?.id) return null;
  const { data: r } = await supabase.from('brand_line_rules').select('ratio_exact,ratio_min,ratio_max,allowed_vols,tone_grammar,compat').eq('brand_id', b.id).eq('line_id', l.id).maybeSingle();
  return r || null;
}

function assertTonerActivator(brand, formula) {
  const txt = JSON.stringify(formula).toLowerCase();
  if (txt.includes('toner') || txt.includes('matizador')) {
    if (/redken/i.test(brand)) return txt.includes('processing solution') && txt.includes('shades eq');
    if (/revlon/i.test(brand)) return txt.includes('young color excel') && txt.includes('activator');
    if (/matrix/i.test(brand)) return txt.includes('color sync') && txt.includes('activator');
    if (/goldwell/i.test(brand)) return txt.includes('colorance') && (txt.includes('developer lotion') || txt.includes('lotion'));
    if (/wella/i.test(brand)) return txt.includes('color touch emulsion') || txt.includes('shinefinity activator');
  }
  return true; // no toner present
}

function assertNoInvalidDepositWarning(result) {
  const warnings = result?.warnings || [];
  const joined = warnings.join(' ').toLowerCase();
  return !joined.includes('depósito oscuro') && !joined.includes('≥20 vol');
}

function assertTimeConsistent(result) {
  const sum = sumStepTimes(result);
  return Number(result?.totalTime || 0) === sum;
}

function parseVolume(p) {
  const m = String(p?.productName || '').match(/(\d+)\s*vol/i) || String(p?.volume || '').match(/(\d+)/);
  return m ? parseInt(m[1], 10) : undefined;
}

function normalizeType(t) {
  const s = String(t || '').toLowerCase();
  if (/developer|oxidante|activador|lotion/.test(s)) return 'developer';
  if (/bleach|decolorante/.test(s)) return 'bleach';
  if (/toner|matizador/.test(s)) return 'toner';
  if (/color|tinte|coloración/.test(s)) return 'color';
  return s || 'color';
}

function calcTotals(formula) {
  let color = 0;
  let dev = 0;
  const vols = [];
  (formula?.steps || []).forEach(s => {
    (s?.mix || []).forEach(m => {
      const t = normalizeType(m?.type || m?.productName);
      const q = Number(m?.quantity) || 0;
      if (t === 'color' || t === 'toner') color += q;
      if (t === 'developer') {
        dev += q;
        const v = parseVolume(m); if (v) vols.push(v);
      }
    });
  });
  return { color, dev, vols };
}

function within(min, max, val, tol = 0.2) { return val >= (min - tol) && val <= (max + tol); }

function assertRatioAndVols(formula, rules) {
  if (!rules) return true; // nothing to check
  const { color, dev, vols } = calcTotals(formula);
  let ratioOk = true;
  if (color > 0 && dev > 0) {
    const r = dev / color; // esperado devPerColor
    if (rules.ratio_exact != null) {
      ratioOk = Math.abs(r - Number(rules.ratio_exact)) <= 0.2;
    } else if (rules.ratio_min != null && rules.ratio_max != null) {
      ratioOk = within(Number(rules.ratio_min), Number(rules.ratio_max), r);
    }
  }
  let volsOk = true;
  if (Array.isArray(rules.allowed_vols) && rules.allowed_vols.length > 0 && vols.length > 0) {
    volsOk = vols.every(v => rules.allowed_vols.includes(v));
  }
  return ratioOk && volsOk;
}

function assertNo10VolInToner(formula) {
  const txt = JSON.stringify(formula).toLowerCase();
  if (!(txt.includes('toner') || txt.includes('matizador'))) return true;
  // Extract volumes from developer items
  const vols = [];
  (formula?.steps || []).forEach(s => {
    (s?.mix || []).forEach(m => {
      const t = normalizeType(m?.type || m?.productName);
      if (t === 'developer') {
        const v = parseVolume(m);
        if (v) vols.push(v);
      }
    });
  });
  return !vols.includes(10);
}

function assertCatalogStatusOk(result) {
  const cs = result?.catalogStatus || result?.catalog_status || (result?.meta && result.meta.catalogStatus);
  if (!cs) return true; // if not provided, skip
  const val = String(cs).toLowerCase();
  return !(val === 'mismatch' || val === 'partial');
}

function assertValidationPassed(result) {
  const vs = (result?.validation && result.validation.status) || result?.validation_status;
  if (!vs) return true;
  const v = String(vs).toLowerCase();
  return !(v === 'failed' || v === 'manual_review' || v === 'auto_rejected');
}

async function runScenario(accessToken, sc) {
  const payload = {
    diagnosis: sc.diagnosis,
    desiredResult: sc.desired,
    brand: sc.brand,
    line: sc.line,
    regionalConfig: { locale: 'es-ES', measurementSystem: 'metric' }
  };

  const res = await fetch(`${SUPABASE_URL}/functions/v1/salonier-assistant`, {
    method: 'POST',
    headers: {
      'content-type': 'application/json',
      'authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify({ action: 'generate_formula', payload })
  });

  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Edge invoke failed: ${res.status} ${text}`);
  }

  const json = await res.json();
  if (!json?.success || !json?.data) {
    throw new Error(`Edge returned no data: ${JSON.stringify(json)}`);
  }
  const fd = json.data;

  // Assertions
  const a = [];
  a.push({ name: 'no_placeholders', pass: !hasPlaceholder(fd) });
  a.push({ name: 'toner_uses_correct_activator', pass: assertTonerActivator(sc.brand, fd) });
  a.push({ name: 'no_invalid_deposit_warning', pass: assertNoInvalidDepositWarning(fd) });
  a.push({ name: 'total_time_consistent', pass: assertTimeConsistent(fd) });
  a.push({ name: 'no_10vol_in_toner', pass: assertNo10VolInToner(fd) });
  a.push({ name: 'catalog_status_ok', pass: assertCatalogStatusOk(fd) });
  a.push({ name: 'validation_passed', pass: assertValidationPassed(fd) });

  // Rules-based assertions (ratio/vols/compat)
  try {
    const rules = await fetchRules(sc.brand, sc.line);
    if (rules) {
      a.push({ name: 'ratio_and_allowed_vols', pass: assertRatioAndVols(fd, rules) });
      // Developer compatibility when bleaching
      const hasBleach = JSON.stringify(fd).toLowerCase().includes('decolorante') || JSON.stringify(fd).toLowerCase().includes('bleach');
      if (hasBleach && rules?.compat?.bleach_developer) {
        const txt = JSON.stringify(fd).toLowerCase();
        const expected = String(rules.compat.bleach_developer).toLowerCase();
        a.push({ name: 'bleach_developer_compat', pass: txt.includes(expected) });
      }
    }
  } catch (e) {
    a.push({ name: 'rules_fetch_error', pass: false, info: e.message });
  }

  const pass = a.every(x => x.pass);
  return { name: sc.name, pass, assertions: a, sample: fd };
}

async function main() {
  const { data: session, error } = await supabase.auth.signInWithPassword({ email: TEST_EMAIL, password: TEST_PASSWORD });
  if (error || !session.session?.access_token) {
    console.error('Auth failed for test user:', error?.message);
    process.exit(2);
  }
  const token = session.session.access_token;

  const results = [];
  for (const sc of scenarios) {
    try {
      const r = await runScenario(token, sc);
      results.push(r);
      console.log(`• ${sc.name}: ${r.pass ? 'OK' : 'FALLÓ'}`);
      if (!r.pass) {
        for (const as of r.assertions) {
          if (!as.pass) console.log(`   - Falla: ${as.name}`);
        }
      }
    } catch (e) {
      console.error(`• ${sc.name}: ERROR`, e.message);
      results.push({ name: sc.name, pass: false, error: e.message });
    }
  }

  const failed = results.filter(r => !r.pass);
  const summaryPath = path.resolve('qa/results-latest.json');
  fs.writeFileSync(summaryPath, JSON.stringify({ date: new Date().toISOString(), results }, null, 2));
  console.log(`\nResumen QA: ${results.length - failed.length}/${results.length} OK`);
  console.log(`Resultados: ${summaryPath}`);

  if (failed.length > 0) process.exit(1);
}

main().catch(err => { console.error(err); process.exit(2); });
