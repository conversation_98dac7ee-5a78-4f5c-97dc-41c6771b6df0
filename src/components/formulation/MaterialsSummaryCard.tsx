import React, { useState, useEffect } from 'react';
import { logger } from '@/utils/logger';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  Package,
  Copy,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import { router } from 'expo-router';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { Formulation } from '@/types/formulation';
import { useInventoryStore } from '@/stores/inventory-store';
import { parseFormulaTextToProducts } from '@/utils/parseFormula';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth-store';
import { Product } from '@/types/inventory';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { ProductMappingModal } from '@/components/inventory/ProductMappingModal';

interface ProductMatch {
  product: Product;
  matchScore: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
  confidence: number;
}

interface MaterialsSummaryCardProps {
  formulationData: Formulation | null;
  formulaText: string;
  selectedBrand: string;
  selectedLine: string;
}

interface MaterialItem {
  productName: string;
  totalQuantity: number;
  unit: string;
  inStock?: boolean;
  stockStatus?: 'available' | 'low' | 'out'; // New field for 3-state status
  matchedProduct?: {
    id: string;
    name: string;
    displayName?: string;
    confidence: number;
    currentStock?: number;
    unitType?: string;
  };
  matchType?: 'exact' | 'partial' | 'fuzzy' | 'none';
  requiresToneSelection?: boolean; // Placeholder items that need a real SKU
}

export const MaterialsSummaryCard: React.FC<MaterialsSummaryCardProps> = ({
  formulationData,
  formulaText,
  selectedBrand,
  selectedLine,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const { saveProductMapping, incrementMappingUsage, products } = useInventoryStore();
  const { currentUser } = useAuthStore();
  const [pendingConfirmations, setPendingConfirmations] = useState<{
    [key: string]: boolean;
  }>({});

  // Extract materials from structured data or parse from text
  const [materials, setMaterials] = useState<MaterialItem[]>([]);
  const [isLoadingMatches, setIsLoadingMatches] = useState(false);
  const [mappingModal, setMappingModal] = useState<{ visible: boolean; aiName: string }>({
    visible: false,
    aiName: '',
  });
  // grouping by session (Sesión 1 / Sesión 2 ...)

  // Process materials and check inventory asynchronously
  useEffect(() => {
    const processMaterials = async () => {
      const materialMap = new Map<string, MaterialItem>();

      // Return empty array if no formula text
      if (!formulaText) {
        setMaterials([]);
        return;
      }

      setIsLoadingMatches(true);

      if (formulationData && formulationData.steps) {
        // Use structured data
        formulationData.steps.forEach(step => {
          const title = (step.stepTitle || '').toLowerCase();
          const sessionMatch = title.match(/sesión\s*(\d+)/i);
          const sessionIndex = sessionMatch ? parseInt(sessionMatch[1]) || 1 : 1;
          if (step.mix) {
            step.mix.forEach(product => {
              const key = `${product.productName}__s${sessionIndex}`;
              const existing = materialMap.get(key);

              if (existing) {
                existing.totalQuantity += product.quantity;
              } else {
                materialMap.set(key, {
                  productName: product.productName,
                  totalQuantity: product.quantity,
                  unit: product.unit,
                  inStock: false,
                  stockStatus: 'out',
                  matchType: 'none',
                  requiresToneSelection: /\[seleccionar tono\]/i.test(product.productName),
                  sessionIndex,
                });
              }
            });
          }
        });
      }

      // Si no hay materiales estructurados, intentar parsear el texto
      if (materialMap.size === 0 && formulaText) {
        // Use the unified parser
        const products = parseFormulaTextToProducts(formulaText);

        products.forEach(product => {
          const key = product.name;
          const existing = materialMap.get(key);

          if (existing) {
            existing.totalQuantity += product.amount;
          } else {
            materialMap.set(key, {
              productName: product.name,
              totalQuantity: product.amount,
              unit: product.unit,
              inStock: false,
              stockStatus: 'out',
              matchType: 'none',
            });
          }
        });
      }

      // Check stock availability and match products asynchronously
      const materialsArray = Array.from(materialMap.values());

      // Process all materials in parallel to avoid await-in-loop warning
      const materialPromises = materialsArray.map(async material => {
        try {
          // Skip matching if requires tone selection
          if (material.requiresToneSelection) {
            return material;
          }

          // Try structured matching first if we have the data
          let matches: ProductMatch[] = [];

          if (formulationData && formulationData.steps) {
            // Extract structured data from the material
            const structuredData = formulationData.steps
              .flatMap(step => step.mix || [])
              .find(p => p.productName === material.productName);

            if (structuredData) {
              matches = await InventoryConsumptionService.findMatchingProductsStructured({
                brand: structuredData.brand,
                line: structuredData.line,
                type: structuredData.type,
                shade: structuredData.shade,
                name: material.productName,
              });
            }
          }

          // Fallback to name-based matching
          if (matches.length === 0) {
            matches = await InventoryConsumptionService.findMatchingProducts(material.productName);
          }

          const bestMatch = matches[0];

          // Strict mode: only consider EXACT matches as available in inventory
          if (bestMatch && bestMatch.matchType === 'exact') {
            const product = bestMatch.product;
            material.inStock = product.currentStock >= material.totalQuantity;

            // Determine 3-state stock status (only for exact matches)
            if (product.currentStock >= material.totalQuantity) {
              material.stockStatus = 'available';
            } else if (product.currentStock > 0) {
              material.stockStatus = 'low';
            } else {
              material.stockStatus = 'out';
            }

            material.matchedProduct = {
              id: product.id,
              name: product.name,
              displayName: product.displayName,
              confidence: bestMatch.confidence || bestMatch.matchScore,
              currentStock: product.currentStock,
              unitType: product.unitType,
            };
            material.matchType = bestMatch.matchType;
          } else {
            material.inStock = false;
            material.stockStatus = 'out';
            material.matchType = 'none';
          }

          return material;
        } catch (error) {
          logger.error('Error matching product:', material.productName, error);
          material.inStock = false;
          material.stockStatus = 'out';
          material.matchType = 'none';
          return material;
        }
      });

      // Wait for all materials to be processed
      const processedMaterials = await Promise.all(materialPromises);

      setMaterials(processedMaterials);
      setIsLoadingMatches(false);
    };

    processMaterials();
  }, [formulationData, formulaText]);

  const copyToClipboard = async () => {
    const visible = materials.filter(m => (m.sessionIndex || 1) === 1);
    const text = visible.map(m => `• ${m.productName}: ${m.totalQuantity}${m.unit}`).join('\n');

    const fullText = `Lista de Materiales (Sesión actual) - ${selectedBrand} ${selectedLine}\n\n${text}`;

    await Clipboard.setStringAsync(fullText);
    Alert.alert('Copiado', 'Lista de materiales copiada al portapapeles');
  };

  const navigateToInventory = () => {
    router.push('/inventory');
  };

  const currentSessionMaterials = materials.filter(m => (m.sessionIndex || 1) === 1);
  const totalItems = currentSessionMaterials.length;
  const itemsInStock = currentSessionMaterials.filter(m => m.inStock).length;
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshStock = async () => {
    setIsRefreshing(true);
    // Force re-processing of materials by triggering the useEffect
    // This will re-run the async matching logic
    setMaterials([]); // Clear current materials to trigger re-processing
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsRefreshing(false);
  };

  // Unused resolver functions - keeping as placeholders for future tone selection feature
  // const _openResolver = (index: number) => {
  //   setResolverIndex(index);
  //   setResolverVisible(true);
  // };

  // const _resolveToneWithProduct = (product: Product) => {
  //   if (resolverIndex === null) return;
  //   setMaterials(prev => {
  //     const next = [...prev];
  //     const item = next[resolverIndex!];
  //     if (item) {
  //       item.productName =
  //         product.displayName ||
  //         product.name ||
  //         `${product.brand} ${product.line || ''} ${product.shade || ''}`.trim();
  //       item.matchedProduct = {
  //         id: product.id,
  //         name: product.name || product.displayName,
  //         displayName: product.displayName,
  //         confidence: 100,
  //         currentStock: product.currentStock,
  //         unitType: product.unitType,
  //       };
  //       item.matchType = 'exact';
  //       item.requiresToneSelection = false;
  //       item.inStock = product.currentStock >= item.totalQuantity;
  //       item.stockStatus = item.inStock ? 'available' : product.currentStock > 0 ? 'low' : 'out';
  //     }
  //     return next;
  //   });
  //   setResolverVisible(false);
  //   setResolverIndex(null);
  // };

  const handleConfirmMatch = async (
    iaProductName: string,
    matchedProduct: MaterialItem['matchedProduct']
  ) => {
    if (!currentUser?.salon_id) return;

    // Marcar como confirmado en UI
    setPendingConfirmations(prev => ({ ...prev, [iaProductName]: true }));

    // Guardar mapping localmente
    saveProductMapping(iaProductName, matchedProduct.id, matchedProduct.confidence);

    // Sincronizar con Supabase
    try {
      await supabase.from('product_mappings').upsert(
        {
          salon_id: currentUser.salon_id,
          ai_product_name: iaProductName,
          inventory_product_id: matchedProduct.id,
          confidence: Math.min(matchedProduct.confidence + 5, 100), // Aumentar confianza por confirmación
          usage_count: 1,
        },
        {
          onConflict: 'salon_id,ai_product_name',
        }
      );

      // Incrementar uso si ya existía
      incrementMappingUsage(iaProductName);

      Alert.alert(
        'Producto confirmado',
        'El producto se ha asociado correctamente. La próxima vez se reconocerá automáticamente.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      logger.error('Error saving product mapping:', error);
    }
  };

  return (
    <BeautyCard
      variant="default"
      style={styles.container}
      testID="materials-summary-card"
      accessibilityLabel="Lista de materiales necesarios"
    >
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
        accessibilityLabel={isExpanded ? 'Contraer lista' : 'Expandir lista'}
        accessibilityRole="button"
      >
        <View style={styles.headerLeft}>
          <Package size={20} color={BeautyMinimalTheme.semantic.interactive.professional.default} />
          <View style={styles.headerText}>
            <Text style={styles.title}>Lista de Materiales</Text>
            <Text style={styles.subtitle}>
              {isLoadingMatches
                ? `${totalItems} productos • Verificando disponibilidad...`
                : `${totalItems} productos • ${itemsInStock}/${totalItems} en stock`}
            </Text>
          </View>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={handleRefreshStock}
            disabled={isRefreshing}
            style={styles.refreshButton}
            accessibilityLabel="Actualizar stock"
          >
            <RefreshCw
              size={18}
              color={
                isRefreshing
                  ? BeautyMinimalTheme.semantic.text.tertiary
                  : BeautyMinimalTheme.semantic.interactive.professional.default
              }
              style={isRefreshing ? styles.rotating : undefined}
            />
          </TouchableOpacity>
          {isExpanded ? (
            <ChevronUp size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
          ) : (
            <ChevronDown size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          {/* Ya no pedimos seleccionar tonos ahora; se decidirán en su sesión correspondiente */}
          <View style={styles.materialsList}>
            {/* Current session */}
            {materials
              .filter(m => (m.sessionIndex || 1) === 1)
              .map((material, index) => (
                <View
                  key={index}
                  style={[styles.materialItem, index % 2 === 0 && styles.materialItemEven]}
                >
                  <View style={styles.materialItemContent}>
                    <View style={styles.materialProductInfo}>
                      <Text
                        style={[
                          styles.materialName,
                          !material.matchedProduct && styles.materialNameMissing,
                        ]}
                      >
                        {material.productName}
                      </Text>
                      <Text style={styles.materialQuantity}>
                        {material.totalQuantity}
                        {material.unit}
                      </Text>
                      <Text
                        style={[
                          styles.availabilityText,
                          material.matchedProduct && material.matchType === 'exact'
                            ? material.stockStatus === 'available'
                              ? styles.availabilitySuccess
                              : material.stockStatus === 'low'
                                ? styles.availabilityWarning
                                : styles.availabilityError
                            : styles.availabilityError,
                        ]}
                      >
                        {material.matchedProduct && material.matchType === 'exact'
                          ? material.stockStatus === 'available'
                            ? 'Disponible'
                            : material.stockStatus === 'low'
                              ? 'Stock bajo'
                              : 'Sin stock'
                          : 'No en inventario'}
                      </Text>

                      {/* Para productos de coloración, solo mostrar info si NO es match exacto */}
                      {material.matchedProduct &&
                        material.matchType === 'exact' &&
                        material.matchedProduct.confidence < 100 &&
                        !pendingConfirmations[material.productName] && (
                          <View style={styles.matchInfo}>
                            {/* Detectar si es producto de coloración */}
                            {material.productName.toLowerCase().includes('color') ||
                            material.productName.toLowerCase().includes('tinte') ||
                            material.productName.toLowerCase().includes('illumina') ||
                            material.productName.toLowerCase().includes('koleston') ||
                            material.productName.toLowerCase().includes('majirel') ? (
                              <>
                                <Text style={styles.matchInfoText}>
                                  En inventario:{' '}
                                  {material.matchedProduct.displayName ||
                                    material.matchedProduct.name}
                                </Text>
                                <Text style={styles.matchWarning}>
                                  ⚠️ Tono diferente - No es el producto exacto
                                </Text>
                              </>
                            ) : (
                              <>
                                <Text style={styles.matchInfoText}>
                                  Identificado como:{' '}
                                  {material.matchedProduct.displayName ||
                                    material.matchedProduct.name}
                                </Text>
                                <Text style={styles.matchConfidence}>
                                  Confianza: {Math.round(material.matchedProduct.confidence)}%
                                </Text>
                              </>
                            )}
                          </View>
                        )}
                    </View>

                    <View style={styles.materialStatusContainer}>
                      {/* Acciones a la derecha (estado visual se muestra debajo del nombre) */}
                      {material.matchedProduct && material.matchType === 'exact' ? (
                        <>
                          {material.matchedProduct.confidence < 100 &&
                            !pendingConfirmations[material.productName] &&
                            !(
                              material.productName.toLowerCase().includes('color') ||
                              material.productName.toLowerCase().includes('tinte') ||
                              material.productName.toLowerCase().includes('illumina') ||
                              material.productName.toLowerCase().includes('koleston') ||
                              material.productName.toLowerCase().includes('majirel')
                            ) && (
                              <TouchableOpacity
                                style={styles.confirmButton}
                                onPress={() =>
                                  handleConfirmMatch(material.productName, material.matchedProduct)
                                }
                                accessibilityLabel="Confirmar coincidencia de producto"
                              >
                                <CheckCircle
                                  size={14}
                                  color={
                                    BeautyMinimalTheme.semantic.interactive.professional.default
                                  }
                                />
                                <Text style={styles.confirmButtonText}>Confirmar</Text>
                              </TouchableOpacity>
                            )}
                        </>
                      ) : (
                        <TouchableOpacity
                          style={styles.mapButton}
                          onPress={() =>
                            setMappingModal({ visible: true, aiName: material.productName })
                          }
                          accessibilityLabel="Asociar producto manualmente"
                        >
                          <Text style={styles.mapButtonText}>Asociar producto</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              ))}
          </View>

          {itemsInStock < totalItems && (
            <View style={styles.warningContainer}>
              <AlertTriangle size={16} color={BeautyMinimalTheme.semantic.status.warning} />
              <Text style={styles.warningText}>
                Se descontarán solo los productos encontrados en inventario ({itemsInStock} de{' '}
                {totalItems})
              </Text>
            </View>
          )}

          {/* No mostramos materiales de futuras sesiones para evitar confusión */}

          <TouchableOpacity
            style={styles.copyButton}
            onPress={copyToClipboard}
            accessibilityLabel="Copiar lista de materiales"
          >
            <Copy size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
            <Text style={styles.copyButtonText}>Copiar lista</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.inventoryButton}
            onPress={navigateToInventory}
            accessibilityLabel="Abrir inventario completo"
          >
            <Package
              size={16}
              color={BeautyMinimalTheme.semantic.interactive.professional.default}
            />
            <Text style={styles.inventoryButtonText}>Ver inventario completo</Text>
          </TouchableOpacity>
        </View>
      )}
      {/* Modal para asociar producto manualmente */}
      <ProductMappingModal
        visible={mappingModal.visible}
        onClose={() => setMappingModal({ visible: false, aiName: '' })}
        aiProductName={mappingModal.aiName}
        suggestedProduct={null}
        confidence={0}
        onConfirm={(productId: string) => {
          const product = products.find(p => p.id === productId);
          if (!product) {
            setMappingModal({ visible: false, aiName: '' });
            return;
          }
          const matched = {
            id: product.id,
            name: product.name,
            displayName: product.displayName,
            confidence: 100,
            currentStock: product.currentStock,
            unitType: product.unitType,
          } as MaterialItem['matchedProduct'];
          // Reutilizamos el flujo existente de confirmación
          // Esto guarda el mapping (local + Supabase) y muestra feedback
          // Además, actualizamos la UI localmente
          (async () => {
            await handleConfirmMatch(mappingModal.aiName, matched);
            setMaterials(prev =>
              prev.map(m => {
                if (m.productName !== mappingModal.aiName) return m;
                const inStock = (product.currentStock || 0) >= m.totalQuantity;
                return {
                  ...m,
                  matchedProduct: matched,
                  matchType: 'exact',
                  inStock,
                  stockStatus: inStock
                    ? 'available'
                    : (product.currentStock || 0) > 0
                      ? 'low'
                      : 'out',
                };
              })
            );
            setMappingModal({ visible: false, aiName: '' });
          })();
        }}
        onCreateNew={() => setMappingModal({ visible: false, aiName: '' })}
      />
    </BeautyCard>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: BeautyMinimalTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: BeautyMinimalTheme.spacing.md,
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  subtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },
  content: {
    paddingTop: 0,
  },
  materialsList: {
    borderRadius: BeautyMinimalTheme.radius.md,
    overflow: 'hidden',
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  materialItem: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle + '40',
  },
  materialItemEven: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
  },
  materialItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  materialProductInfo: {
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.md,
  },
  materialName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: 2,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  materialNameMissing: {
    color: BeautyMinimalTheme.semantic.text.tertiary,
  },
  materialQuantity: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  materialStatusContainer: {
    flexShrink: 0,
  },

  warningContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '10',
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    marginTop: BeautyMinimalTheme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  warningText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.warning,
    flex: 1,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
    borderRadius: BeautyMinimalTheme.radius.md,
    gap: BeautyMinimalTheme.spacing.sm,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.minimum,
  },
  availabilityText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    marginTop: 2,
  },
  availabilitySuccess: {
    color: BeautyMinimalTheme.semantic.status.success,
  },
  availabilityWarning: {
    color: BeautyMinimalTheme.semantic.status.warning,
  },
  availabilityError: {
    color: BeautyMinimalTheme.semantic.status.error,
  },
  copyButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  inventoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderRadius: BeautyMinimalTheme.radius.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.interactive.professional.default,
    gap: BeautyMinimalTheme.spacing.sm,
    minHeight: BeautyMinimalTheme.spacing.touchTarget.minimum,
  },
  inventoryButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  refreshButton: {
    padding: BeautyMinimalTheme.spacing.xs,
  },
  rotating: {
    transform: [{ rotate: '360deg' }],
  },
  matchInfo: {
    marginTop: BeautyMinimalTheme.spacing.xs,
    paddingTop: BeautyMinimalTheme.spacing.xs,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  matchInfoText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontStyle: 'italic',
  },
  matchConfidence: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    marginTop: 2,
  },
  matchWarning: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.status.warning,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    marginTop: 2,
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.interactive.professional.default,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.professional.hover,
    gap: BeautyMinimalTheme.spacing.xs / 2,
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  confirmButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  mapButton: {
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
    borderRadius: BeautyMinimalTheme.radius.full,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    backgroundColor: 'transparent',
  },
  mapButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
});
