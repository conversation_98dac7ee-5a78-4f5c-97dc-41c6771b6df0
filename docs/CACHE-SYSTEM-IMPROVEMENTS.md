# Sistema de Caché <PERSON> - Determinístico y Eficiente

## 📈 Mejoras Implementadas

### 1. **Normalización Determinística**

#### ✅ Normalización de Valores

- **Strings**: Convertidos a `lowercase` y `trim()`
- **Números**: Redondeados a 2 decimales para consistencia
- **Arrays**: Ordenados alfabéticamente después de normalizar cada elemento
- **Objetos**: Keys ordenadas alfabéticamente, valores normalizados recursivamente
- **Null/undefined**: Tratados consistentemente como `null`

#### ✅ Filtrado Inteligente de Campos

```typescript
// Solo campos relevantes para el output de IA
const CACHE_RELEVANT_FIELDS = {
  diagnose_image: ['imageHash', 'imageUrl', 'analysis_depth', 'language'],
  analyze_desired_look: ['imageHash', 'imageUrl', 'currentLevel', 'diagnosis', 'language'],
  generate_formula: ['diagnosis', 'desiredResult', 'brand', 'line', 'language'],
  // ...
};

// Campos excluidos (metadata irrelevante)
const CACHE_EXCLUDED_FIELDS = [
  'timestamp',
  'created_at',
  'updated_at',
  'request_id',
  'session_id',
  'user_id',
  'salon_id',
  'device_info',
  'app_version',
  'ip_address',
];
```

### 2. **Sistema de Categorías de Caché**

#### ✅ TTL Inteligente por Categoría

- **exact_match**: 30 días (casos idénticos con todos los campos)
- **similar_case**: 7 días (casos parcialmente similares)
- **template**: 3 días (casos genéricos)

#### ✅ Hash con Prefijo de Categoría

```typescript
// Ejemplo: "exact_match:base64hash..." o "similar_case:base64hash..."
const cacheKey = `${category}:${hash}`;
```

### 3. **Búsqueda de Cache Mejorada**

#### ✅ Búsqueda en Múltiples Niveles

1. **Exact match**: Coincidencia perfecta de hash
2. **Similar search**: Busca en la misma categoría para casos similares
3. **Logging inteligente**: Métricas de edad, categoría y tokens ahorrados

#### ✅ Manejo de Imágenes Consistente

```typescript
// Para imágenes, crea hash consistente:
if (payload.imageBase64) {
  const imageData = payload.imageBase64;
  extracted.imageHash = `${imageData.substring(0, 200)}_len:${imageData.length}`;
  delete extracted.imageUrl; // Prefiere hash sobre URL
}
```

### 4. **Funciones de Análisis y Mantenimiento**

#### ✅ Estadísticas de Cache

```typescript
// Nuevo endpoint: /salonier-assistant con task="cache_stats"
{
  totalEntries: number,
  byTask: { [task: string]: number },
  byCategory: { [category: string]: number },
  totalSavings: { tokens: number, cost: number },
  oldestEntry: number // minutos
}
```

#### ✅ Limpieza Automática

- Limpieza probabilística (5% chance en cada save)
- Eliminación de entries expiradas por salón
- Logging de operaciones de limpieza

#### ✅ Algoritmo de Similitud (Preparado)

- Implementado Levenshtein distance para fuzzy matching
- Base para futuras mejoras de búsqueda similar

## 🎯 Impacto Esperado

### Determinismo Mejorado

- **Antes**: Casos idénticos podían generar diferentes cache keys
- **Después**: Casos idénticos SIEMPRE generan el mismo cache key

### Eficiencia Mejorada

- **Cache hit rate**: Se espera incremento del 40% → 60%+
- **Consistencia**: Casos similares pueden reutilizar respuestas
- **TTL inteligente**: Cache más relevante permanece más tiempo

### Ejemplos de Casos que Ahora Funcionan

#### ✅ Casos que antes fallaban:

```typescript
// Estos dos requests ahora generan el MISMO cache key:
const request1 = {
  diagnosis: '  Cabello Castaño Nivel 4  ',
  desiredResult: 'rubio medio',
  brand: "L'ORÉAL",
};

const request2 = {
  brand: "l'oréal",
  diagnosis: 'cabello castaño nivel 4',
  desiredResult: 'Rubio Medio',
  timestamp: '2024-08-21T10:30:00Z', // ← Este campo se ignora
};
```

#### ✅ Imágenes Consistentes:

```typescript
// Misma imagen = mismo hash, independientemente de metadata
const image1 = { imageBase64: 'data:image/jpeg;base64,/9j/...', uploadTime: '...' };
const image2 = { imageBase64: 'data:image/jpeg;base64,/9j/...' };
// → Ambos generan identical cache key
```

## 🔧 Uso del Sistema

### Para Desarrolladores

#### Obtener Estadísticas de Cache

```typescript
const response = await fetch('/salonier-assistant', {
  method: 'POST',
  body: JSON.stringify({
    task: 'cache_stats',
  }),
});
```

#### Forzar Limpieza de Cache

```typescript
// Llama automáticamente con 5% probabilidad
// O manualmente tras muchas operaciones
```

### Para Monitoreo

#### Métricas Clave a Observar

- **Cache hit rate** por task type
- **Tokens ahorrados** por categoría
- **Distribución** de entries por TTL
- **Eficiencia** de limpieza automática

## 🚨 Compatibilidad

### ✅ Totalmente Retrocompatible

- Cache existente sigue funcionando
- Gradual migración a nuevo sistema
- Sin breaking changes en API

### ✅ Fallback Robusto

```typescript
// Si falla la normalización:
try {
  return generateSmartCacheKey();
} catch (error) {
  logger.warn('Using fallback cache key');
  return generateSimpleCacheKey();
}
```

## 📊 Testing y Validación

### Test Cases Implementados

1. **Normalización**: Strings con diferentes casos → mismo resultado
2. **Campos irrelevantes**: Metadata ignorada correctamente
3. **Imágenes**: Mismo contenido → mismo hash
4. **TTL**: Categorías diferentes → TTL diferentes
5. **Limpieza**: Entries expiradas eliminadas correctamente

### Métricas de Éxito

- [ ] Cache hit rate > 60%
- [ ] Tiempo de respuesta < 200ms para cache hits
- [ ] 0 cache keys duplicados para casos idénticos
- [ ] Limpieza automática < 1% CPU usage

---

**Resumen**: Sistema de caché completamente reescrito con normalización determinística, categorización inteligente, y funciones avanzadas de análisis. Casos idénticos ahora SIEMPRE reutilizan cache, y casos similares pueden beneficiarse de búsqueda inteligente.
