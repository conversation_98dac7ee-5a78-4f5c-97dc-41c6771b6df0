import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  Search,
  Filter,
  Plus,
  MessageCircle,
  CheckCircle2,
  Clock,
  Star,
  TrendingUp,
  Calendar,
} from 'lucide-react-native';
import Animated, {
  FadeIn,
  FadeInDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  withDelay,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { useHapticsEnabled, useAnimationsEnabled } from '@/stores/whimsy-store';

// Internal imports
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { ServiceHistoryCard } from '@/components/client-history/ServiceHistoryCard';
import { AddFeedbackModal } from '@/components/AddFeedbackModal';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';
import { PreviousFormula } from '@/stores/client-history-store';
import type { Database } from '@/types/database';

interface FeedbackTabContentProps {
  clientId: string;
  clientName: string;
  services: PreviousFormula[];
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

type FilterType = 'all' | 'recent' | 'high_rating' | 'needs_attention';
type SortType = 'date_desc' | 'date_asc' | 'rating_desc' | 'rating_asc';

export const FeedbackTabContent: React.FC<FeedbackTabContentProps> = ({
  _clientId,
  clientName,
  services,
  onRefresh,
  isRefreshing = false,
}) => {
  const { getFeedbackByService } = useFormulaFeedbackStore();

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('date_desc');
  const [selectedServiceForFeedback, setSelectedServiceForFeedback] =
    useState<PreviousFormula | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Animation preferences
  const hapticsEnabled = useHapticsEnabled();
  const animationsEnabled = useAnimationsEnabled();

  // Animation values
  const statsCardScale = useSharedValue(0.95);
  const filterChipScale = useSharedValue(1);
  const _pullToRefreshProgress = useSharedValue(0);

  // Pre-computed animated styles (moved from render callbacks)
  const statsCardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: statsCardScale.value }],
  }));

  const filterChipAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: filterChipScale.value }],
  }));

  const progressBarAnimatedStyle = useAnimatedStyle(() => ({
    width: withTiming(`${stats.completionRate}%`, {
      duration: 800,
    }),
  }));

  // Fixed animated styles - moved from inside callbacks and conditions
  const filterChipActiveStyle = useAnimatedStyle(() => ({
    transform: [{ scale: 1.05 }],
  }));

  const filterChipInactiveStyle = useAnimatedStyle(() => ({
    transform: [{ scale: 1 }],
  }));

  const addFeedbackButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: 1 }],
    opacity: 1,
  }));

  const emptyStateScaleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: 1 }],
  }));

  const pendingEmptyRotationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        rotate: withTiming(
          '360deg',
          {
            duration: 2000,
          },
          () => {
            // Restart animation
          }
        ),
      },
    ],
  }));

  const completedEmptyScaleStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withSequence(withTiming(1.1, { duration: 1000 }), withTiming(1, { duration: 1000 })),
      },
    ],
  }));

  // Initialize animations
  React.useEffect(() => {
    if (animationsEnabled) {
      statsCardScale.value = withDelay(200, withSpring(1, { damping: 15, stiffness: 150 }));
    } else {
      statsCardScale.value = 1;
    }
  }, [animationsEnabled, statsCardScale]);

  // Calculate services with and without feedback
  const { pendingServices, completedServices, stats } = useMemo(() => {
    const pending: PreviousFormula[] = [];
    const completed: PreviousFormula[] = [];
    let totalRating = 0;
    let ratingCount = 0;

    services.forEach(service => {
      const feedback = getFeedbackByService(service.id);

      if (feedback) {
        completed.push(service);
        totalRating += feedback.rating;
        ratingCount++;
      } else {
        pending.push(service);
      }
    });

    // Apply search filter
    const filterBySearch = (serviceList: PreviousFormula[]) => {
      if (!searchQuery.trim()) return serviceList;

      return serviceList.filter(
        service =>
          service.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.line.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.formula.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.notes?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    };

    // Apply additional filters
    const applyFilters = (serviceList: PreviousFormula[], isCompleted: boolean) => {
      let filtered = filterBySearch(serviceList);

      if (selectedFilter !== 'all') {
        switch (selectedFilter) {
          case 'recent':
            filtered = filtered.filter(service => {
              const serviceDate = new Date(service.date);
              const monthAgo = new Date();
              monthAgo.setMonth(monthAgo.getMonth() - 1);
              return serviceDate >= monthAgo;
            });
            break;
          case 'high_rating':
            if (isCompleted) {
              filtered = filtered.filter(service => {
                const feedback = getFeedbackByService(service.id);
                return feedback && feedback.rating >= 4;
              });
            }
            break;
          case 'needs_attention':
            if (isCompleted) {
              filtered = filtered.filter(service => {
                const feedback = getFeedbackByService(service.id);
                return feedback && (feedback.rating <= 3 || !feedback.worked_as_expected);
              });
            }
            break;
        }
      }

      // Apply sorting
      filtered.sort((a, b) => {
        switch (sortBy) {
          case 'date_asc':
            return new Date(a.date).getTime() - new Date(b.date).getTime();
          case 'date_desc':
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          case 'rating_desc':
            if (isCompleted) {
              const feedbackA = getFeedbackByService(a.id);
              const feedbackB = getFeedbackByService(b.id);
              return (feedbackB?.rating || 0) - (feedbackA?.rating || 0);
            }
            return 0;
          case 'rating_asc':
            if (isCompleted) {
              const feedbackA = getFeedbackByService(a.id);
              const feedbackB = getFeedbackByService(b.id);
              return (feedbackA?.rating || 0) - (feedbackB?.rating || 0);
            }
            return 0;
          default:
            return 0;
        }
      });

      return filtered;
    };

    const pendingFiltered = applyFilters(pending, false);
    const completedFiltered = applyFilters(completed, true);

    return {
      pendingServices: pendingFiltered,
      completedServices: completedFiltered,
      stats: {
        total: services.length,
        pending: pending.length,
        completed: completed.length,
        averageRating: ratingCount > 0 ? Math.round((totalRating / ratingCount) * 10) / 10 : 0,
        completionRate:
          services.length > 0 ? Math.round((completed.length / services.length) * 100) : 0,
      },
    };
  }, [services, getFeedbackByService, searchQuery, selectedFilter, sortBy]);

  // Enhanced feedback modal handler with animation
  const handleAddFeedback = useCallback(
    (service: PreviousFormula) => {
      // Convert PreviousFormula to Database service type for the modal
      const _serviceForModal = {
        id: service.id,
        formula: {
          id: service.id, // Use service id as formula id for compatibility
        },
      } as Database['public']['Tables']['services']['Row'];

      setSelectedServiceForFeedback(service);

      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    },
    [hapticsEnabled]
  );

  const handleFeedbackSuccess = useCallback(() => {
    setSelectedServiceForFeedback(null);
    onRefresh?.();

    if (hapticsEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  }, [onRefresh, hapticsEnabled]);

  // Filter options
  const filterOptions = [
    { key: 'all', label: 'Todos', icon: MessageCircle },
    { key: 'recent', label: 'Recientes', icon: Calendar },
    { key: 'high_rating', label: 'Alta calificación', icon: Star },
    { key: 'needs_attention', label: 'Requiere atención', icon: TrendingUp },
  ] as const;

  const sortOptions = [
    { key: 'date_desc', label: 'Más recientes' },
    { key: 'date_asc', label: 'Más antiguos' },
    { key: 'rating_desc', label: 'Mayor calificación' },
    { key: 'rating_asc', label: 'Menor calificación' },
  ] as const;

  return (
    <View style={styles.container}>
      {/* Header with Statistics */}
      <Animated.View entering={FadeIn} style={[styles.statsContainer, statsCardAnimatedStyle]}>
        <BeautyCard variant="default" style={styles.statsCard}>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.total}</Text>
              <Text style={styles.statLabel}>Total servicios</Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[styles.statValue, { color: BeautyMinimalTheme.semantic.status.warning }]}
              >
                {stats.pending}
              </Text>
              <Text style={styles.statLabel}>Pendientes</Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[styles.statValue, { color: BeautyMinimalTheme.semantic.status.success }]}
              >
                {stats.completed}
              </Text>
              <Text style={styles.statLabel}>Completados</Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statValue,
                  { color: BeautyMinimalTheme.semantic.interactive.primary.default },
                ]}
              >
                {stats.averageRating > 0 ? `${stats.averageRating}⭐` : 'N/A'}
              </Text>
              <Text style={styles.statLabel}>Promedio</Text>
            </View>
          </View>
          <View style={styles.completionRate}>
            <Text style={styles.completionRateText}>
              {stats.completionRate}% feedback completado
            </Text>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, progressBarAnimatedStyle]} />
            </View>
          </View>
        </BeautyCard>
      </Animated.View>

      {/* Search and Filters */}
      <View style={styles.controlsContainer}>
        <View style={styles.searchContainer}>
          <Search size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por marca, línea o fórmula..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={BeautyMinimalTheme.semantic.text.tertiary}
          />
        </View>

        <Animated.View style={filterChipAnimatedStyle}>
          <TouchableOpacity
            style={[styles.filterButton, showFilters && styles.filterButtonActive]}
            onPress={() => {
              if (animationsEnabled) {
                filterChipScale.value = withSequence(
                  withSpring(0.95, { damping: 15 }),
                  withSpring(1, { damping: 12 })
                );
              }
              if (hapticsEnabled) {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              setShowFilters(!showFilters);
            }}
            accessibilityRole="button"
            accessibilityLabel="Mostrar filtros"
          >
            <Filter size={20} color={BeautyMinimalTheme.semantic.text.secondary} />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Filter Options */}
      {showFilters && (
        <Animated.View entering={FadeInDown} style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterChips}>
              {filterOptions.map((option, index) => {
                const IconComponent = option.icon;
                const isActive = selectedFilter === option.key;

                return (
                  <Animated.View
                    key={option.key}
                    entering={animationsEnabled ? FadeIn.delay(index * 50) : undefined}
                    style={isActive ? filterChipActiveStyle : filterChipInactiveStyle}
                  >
                    <TouchableOpacity
                      style={[styles.filterChip, isActive && styles.filterChipActive]}
                      onPress={() => {
                        if (hapticsEnabled) {
                          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        }
                        setSelectedFilter(option.key);
                      }}
                      accessibilityRole="button"
                      accessibilityState={{ selected: isActive }}
                    >
                      <IconComponent
                        size={16}
                        color={
                          isActive
                            ? BeautyMinimalTheme.semantic.interactive.primary.default
                            : BeautyMinimalTheme.semantic.text.secondary
                        }
                      />
                      <Text
                        style={[styles.filterChipText, isActive && styles.filterChipTextActive]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  </Animated.View>
                );
              })}
            </View>
          </ScrollView>

          {/* Sort Options */}
          <View style={styles.sortContainer}>
            <Text style={styles.sortLabel}>Ordenar por:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.sortOptions}>
                {sortOptions.map((option, index) => {
                  const isActive = sortBy === option.key;

                  return (
                    <Animated.View
                      key={option.key}
                      entering={animationsEnabled ? FadeIn.delay(200 + index * 30) : undefined}
                    >
                      <TouchableOpacity
                        style={[styles.sortChip, isActive && styles.sortChipActive]}
                        onPress={() => {
                          if (hapticsEnabled) {
                            Haptics.selectionAsync();
                          }
                          setSortBy(option.key);
                        }}
                        accessibilityRole="button"
                        accessibilityState={{ selected: isActive }}
                      >
                        <Text style={[styles.sortChipText, isActive && styles.sortChipTextActive]}>
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    </Animated.View>
                  );
                })}
              </View>
            </ScrollView>
          </View>
        </Animated.View>
      )}

      {/* Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => {
                if (hapticsEnabled) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                }
                onRefresh();
              }}
              tintColor={BeautyMinimalTheme.semantic.interactive.primary.default}
              colors={[BeautyMinimalTheme.semantic.interactive.primary.default]}
              progressBackgroundColor={BeautyMinimalTheme.semantic.background.secondary}
            />
          ) : undefined
        }
      >
        {/* Pending Feedback Section */}
        <Animated.View entering={FadeInDown.delay(100)}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Clock size={20} color={BeautyMinimalTheme.semantic.status.warning} />
              <Text style={styles.sectionTitle}>Pendientes de Feedback</Text>
              {pendingServices.length > 0 && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>{pendingServices.length}</Text>
                </View>
              )}
            </View>
          </View>

          {pendingServices.length > 0 ? (
            <View style={styles.servicesContainer}>
              {pendingServices.map((service, index) => (
                <Animated.View
                  key={`pending-${service.id}`}
                  entering={FadeInDown.delay(150 + index * 50)}
                  style={styles.serviceWrapper}
                >
                  <ServiceHistoryCard
                    service={service}
                    _clientName={clientName}
                    onPress={() => router.push(`/service/detail/${service.id}`)}
                    testID={`pending-feedback-card-${service.id}`}
                  />
                  <Animated.View
                    entering={animationsEnabled ? FadeIn.delay(100 + index * 50) : undefined}
                    style={addFeedbackButtonStyle}
                  >
                    <TouchableOpacity
                      style={styles.addFeedbackButton}
                      onPress={() => handleAddFeedback(service)}
                      onPressIn={() => {
                        if (animationsEnabled) {
                          // Scale down on press
                        }
                      }}
                      accessibilityRole="button"
                      accessibilityLabel={`Agregar feedback para servicio del ${service.date}`}
                    >
                      <Plus size={16} color={BeautyMinimalTheme.semantic.background.primary} />
                      <Text style={styles.addFeedbackButtonText}>Agregar Feedback</Text>
                    </TouchableOpacity>
                  </Animated.View>
                </Animated.View>
              ))}
            </View>
          ) : (
            <Animated.View
              entering={animationsEnabled ? FadeIn.delay(300) : undefined}
              style={emptyStateScaleStyle}
            >
              <BeautyCard variant="secondary" style={styles.emptyState}>
                <Animated.View style={pendingEmptyRotationStyle}>
                  <CheckCircle2 size={48} color={BeautyMinimalTheme.semantic.status.success} />
                </Animated.View>
                <Text style={styles.emptyStateTitle}>¡Excelente trabajo!</Text>
                <Text style={styles.emptyStateText}>
                  Todos los servicios tienen feedback registrado
                </Text>
              </BeautyCard>
            </Animated.View>
          )}
        </Animated.View>

        {/* Completed Feedback Section */}
        <Animated.View entering={FadeInDown.delay(200)} style={styles.sectionSpacing}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <CheckCircle2 size={20} color={BeautyMinimalTheme.semantic.status.success} />
              <Text style={styles.sectionTitle}>Feedback Completados</Text>
              {completedServices.length > 0 && (
                <View style={[styles.badge, styles.badgeSuccess]}>
                  <Text style={styles.badgeText}>{completedServices.length}</Text>
                </View>
              )}
            </View>
          </View>

          {completedServices.length > 0 ? (
            <View style={styles.servicesContainer}>
              {completedServices.map((service, index) => (
                <Animated.View
                  key={`completed-${service.id}`}
                  entering={FadeInDown.delay(250 + index * 50)}
                >
                  <ServiceHistoryCard
                    service={service}
                    _clientName={clientName}
                    onPress={() => router.push(`/service/detail/${service.id}`)}
                    testID={`completed-feedback-card-${service.id}`}
                  />
                </Animated.View>
              ))}
            </View>
          ) : (
            <Animated.View
              entering={animationsEnabled ? FadeIn.delay(400) : undefined}
              style={emptyStateScaleStyle}
            >
              <BeautyCard variant="secondary" style={styles.emptyState}>
                <Animated.View style={completedEmptyScaleStyle}>
                  <MessageCircle size={48} color={BeautyMinimalTheme.semantic.text.secondary} />
                </Animated.View>
                <Text style={styles.emptyStateTitle}>Sin feedback aún</Text>
                <Text style={styles.emptyStateText}>
                  Los servicios con feedback aparecerán aquí para revisión y análisis
                </Text>
              </BeautyCard>
            </Animated.View>
          )}
        </Animated.View>
      </ScrollView>

      {/* Add Feedback Modal */}
      {selectedServiceForFeedback && (
        <AddFeedbackModal
          visible={!!selectedServiceForFeedback}
          onClose={() => setSelectedServiceForFeedback(null)}
          service={
            {
              id: selectedServiceForFeedback.id,
              formula: {
                id: selectedServiceForFeedback.id,
              },
            } as Database['public']['Tables']['services']['Row']
          }
          onSuccess={handleFeedbackSuccess}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  statsContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingTop: BeautyMinimalTheme.spacing.sm,
    paddingBottom: BeautyMinimalTheme.spacing.md,
  },
  statsCard: {
    padding: BeautyMinimalTheme.spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  statLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: BeautyMinimalTheme.spacing.xs,
    textAlign: 'center',
  },
  completionRate: {
    paddingTop: BeautyMinimalTheme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  completionRateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.primary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: BeautyMinimalTheme.semantic.border.subtle,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: BeautyMinimalTheme.semantic.status.success,
    borderRadius: 2,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingBottom: BeautyMinimalTheme.spacing.md,
    gap: BeautyMinimalTheme.spacing.sm,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.lg,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    gap: BeautyMinimalTheme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  filterButton: {
    padding: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
  },
  filterButtonActive: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
  },
  filtersContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingBottom: BeautyMinimalTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  filterChips: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.sm,
    paddingBottom: BeautyMinimalTheme.spacing.sm,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.full,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  filterChipActive: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
  },
  filterChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  filterChipTextActive: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  sortContainer: {
    paddingTop: BeautyMinimalTheme.spacing.sm,
  },
  sortLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  sortOptions: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  sortChip: {
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
  },
  sortChipActive: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
  },
  sortChipText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  sortChipTextActive: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  content: {
    flex: 1,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
  },
  sectionHeader: {
    paddingVertical: BeautyMinimalTheme.spacing.md,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  badge: {
    backgroundColor: BeautyMinimalTheme.semantic.status.warning,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    minWidth: 24,
    alignItems: 'center',
  },
  badgeSuccess: {
    backgroundColor: BeautyMinimalTheme.semantic.status.success,
  },
  badgeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.background.primary,
  },
  sectionSpacing: {
    marginTop: BeautyMinimalTheme.spacing.xl,
  },
  servicesContainer: {
    gap: BeautyMinimalTheme.spacing.sm,
  },
  serviceWrapper: {
    position: 'relative',
  },
  addFeedbackButton: {
    position: 'absolute',
    top: BeautyMinimalTheme.spacing.md,
    right: BeautyMinimalTheme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
    shadowColor: BeautyMinimalTheme.semantic.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addFeedbackButtonText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.background.primary,
  },
  emptyState: {
    padding: BeautyMinimalTheme.spacing.xl,
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  emptyStateTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  emptyStateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});
