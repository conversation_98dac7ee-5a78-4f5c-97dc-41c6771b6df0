# CLAUDE-troubleshooting.md

Common issues and solutions for Salonier development.

## 🔧 Troubleshooting Common Issues

### Expo/React Native Issues

#### Development Server Problems

- Clear cache: `npx expo start --clear`
- Reset Metro: `npx react-native start --reset-cache`
- Clear watchman: `watchman watch-del-all`

#### Platform-Specific Issues

- **iOS build issues**: `cd ios && pod install`
- **Android issues**: Check Android Studio and SDK versions
- **Network connectivity**: Use `npm run mobile:tunnel` for remote testing

#### Performance Issues

- Check for memory leaks in components
- Optimize images and assets
- Use React DevTools Profiler
- Monitor bundle size with `npx expo export --dump-assetmap`

### Supabase Connection Issues

#### Authentication Problems

- Check status: `npx supabase status`
- Verify env vars: `SUPABASE_URL` and `SUPABASE_ANON_KEY`
- Test connection: Check network tab in browser
- Verify RLS policies are not blocking access

#### Edge Function Issues

- Check logs: `mcp__supabase__get_logs: "edge-function"`
- Verify function deployment status
- Test locally with `supabase functions serve`
- Check CORS settings for web requests

#### Database Issues

- Migration conflicts: `npx supabase db reset`
- Type generation: `mcp__supabase__generate_typescript_types`
- Performance: Use `mcp__supabase__get_advisors: "performance"`

### TypeScript Errors

#### Common Patterns

- Strict mode is enabled - all types required
- Run `npm run lint:fix` for auto-fixes
- Check `tsconfig.json` for configuration
- Use `mcp__ide__getDiagnostics` for detailed error analysis

#### Import/Export Issues

- Check relative path imports
- Verify module resolution in `tsconfig.json`
- Use absolute imports with `@/` prefix

### AI Integration Issues

#### OpenAI API Problems

- Verify API key in environment variables
- Check rate limits and quotas
- Monitor token usage and costs
- Test with simpler prompts first

#### Prompt Engineering

- Keep prompts under 300 characters when possible
- Use JSON mode for structured output
- Implement fallback responses
- Cache common responses

### State Management (Zustand)

#### Offline Sync Issues

- Check network connectivity detection
- Verify queue implementation
- Test offline/online transitions
- Monitor sync conflicts

#### Store Performance

- Avoid excessive subscriptions
- Use selectors for specific state slices
- Implement proper cleanup in components

### Testing Issues

#### Test Failures

- Clear Jest cache: `npm test -- --clearCache`
- Check test environment setup
- Verify mock implementations
- Run tests in isolation

#### Coverage Issues

- Target >80% coverage
- Focus on critical business logic
- Mock external dependencies properly
- Test error scenarios

## 🚨 Emergency Procedures

### Production Issues

1. Check logs: `mcp__supabase__get_logs`
2. Use `debug-specialist` agent for investigation
3. Consider rollback if critical
4. Document incident for post-mortem

### Database Emergencies

1. Check advisor warnings: `mcp__supabase__get_advisors`
2. Use `database-architect` agent for immediate analysis
3. Consider read-only mode if needed
4. Plan migration or fix strategy

### Security Incidents

1. Use `security-privacy-auditor` agent immediately
2. Check for data exposure
3. Verify RLS policies
4. Review access logs
