# Quickstart: File Structure Reorganization

## Overview

This quickstart guide demonstrates how to reorganize the Salonier project from its current disorganized state to a clean, maintainable structure that supports React Native multi-platform development.

## Prerequisites

- [ ] Current Salonier project accessible
- [ ] Git repository with clean working directory
- [ ] Node.js and npm/yarn installed
- [ ] React Native/Expo development environment set up

## Quick Validation Steps

### Step 1: Analyze Current Structure (5 minutes)

```bash
# Navigate to project root
cd /path/to/salonier-project

# Count current files and directories
find . -type f -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | wc -l
find . -type d | head -20

# Identify potential issues
echo "Files in root directory:"
ls -la | grep -E "\.(ts|tsx|js|jsx)$"

echo "Deeply nested files (>4 levels):"
find . -type f -name "*.ts*" -o -name "*.js*" | awk -F'/' 'NF>5 {print NF-1 ": " $0}' | head -10
```

**Expected Output**: Should show current file count and identify disorganization patterns

### Step 2: Create Target Structure (10 minutes)

```bash
# Create new directory structure (dry run - don't move files yet)
mkdir -p target-structure/{src,assets,supabase,docs,scripts}
mkdir -p target-structure/src/{components,screens,stores,services,utils,hooks,types,constants,navigation}
mkdir -p target-structure/src/components/{ui,forms}
mkdir -p target-structure/assets/{images,fonts,icons}
mkdir -p target-structure/supabase/{functions,migrations}
mkdir -p target-structure/__tests__/{components,screens,stores,services,utils,integration}

# Show target structure
tree target-structure/ || find target-structure/ -type d
```

**Expected Output**: Clean directory tree showing organized structure

### Step 3: File Classification Test (5 minutes)

```bash
# Classify current files by type
echo "React Components (.tsx files):"
find . -name "*.tsx" | head -10

echo "Stores (state management):"
find . -name "*store*" -o -name "*Store*" | head -10

echo "Services (API/business logic):"
find . -name "*service*" -o -name "*Service*" -o -name "*api*" | head -10

echo "Utilities:"
find . -name "*util*" -o -name "*helper*" -o -name "*Util*" | head -10
```

**Expected Output**: Lists showing where different types of files currently exist

### Step 4: Identify Migration Complexity (5 minutes)

```bash
# Check import statements to understand dependencies
echo "Import analysis - most imported files:"
grep -r "import.*from.*['\"]\./" . --include="*.ts" --include="*.tsx" | \
  sed "s/.*from ['\"]//; s/['\"].*//" | sort | uniq -c | sort -nr | head -10

echo "Relative import depth:"
grep -r "import.*from.*['\"]\.\./" . --include="*.ts" --include="*.tsx" | \
  grep -o "\.\.\/" | wc -l
```

**Expected Output**: Shows which files are heavily imported and complexity of current imports

### Step 5: Validate Development Environment (3 minutes)

```bash
# Ensure project builds before reorganization
npm install
npm run lint || echo "Linting errors found - will need to fix post-migration"
npm test -- --passWithNoTests || echo "Test issues found"

# Check if Expo/React Native works
npm run mobile --help || echo "Mobile command not available"
```

**Expected Output**: Current project state and any existing build issues

## Success Criteria Validation

### Developer Onboarding Test

**Goal**: New developer should understand structure in 15 minutes
**Test**: Have someone unfamiliar look at the target structure and answer:

- [ ] Where would they find UI components?
- [ ] Where would they add a new screen?
- [ ] Where would they find API integration code?
- [ ] Where would they add new state management?

### Code Location Test

**Goal**: Developers can find specific components quickly
**Test**: Time how long it takes to locate:

- [ ] Authentication-related components
- [ ] Inventory management logic
- [ ] AI integration services
- [ ] Color formulation features

### Growth Scalability Test

**Goal**: Structure supports adding new features
**Test**: Plan where these would go in new structure:

- [ ] New user role management
- [ ] Additional AI models integration
- [ ] Reporting/analytics features
- [ ] Multi-language support

### Refactoring Support Test

**Goal**: Dependencies are clear from organization
**Test**: Identify dependencies for:

- [ ] Authentication components
- [ ] State management stores
- [ ] API service layers
- [ ] Shared utility functions

## Common Issues and Solutions

### Issue: Import Path Updates

**Symptom**: TypeScript/ESLint errors after moving files
**Solution**:

```bash
# Use search and replace for import path updates
find . -name "*.ts" -o -name "*.tsx" | xargs sed -i 's|from.*old/path|from ./new/path|g'
```

### Issue: Asset Loading Problems

**Symptom**: Images or fonts not loading after reorganization
**Solution**: Verify assets are in correct Expo-expected locations and update references

### Issue: Build Configuration Conflicts

**Symptom**: Metro bundler or Expo build errors
**Solution**: Update `metro.config.js` and `babel.config.js` if custom paths are used

### Issue: Test File Imports

**Symptom**: Tests can't find moved files
**Solution**: Update test imports and verify `__tests__` directory mirrors source structure

## Performance Validation

After reorganization, verify:

- [ ] Build time hasn't increased significantly
- [ ] App startup time remains under performance goals
- [ ] Development server reload time is reasonable
- [ ] Test execution time is acceptable

## Rollback Plan

If issues arise:

```bash
# Revert to previous state
git reset --hard HEAD~1  # If changes were committed
# or
git stash  # If changes weren't committed yet
```

## Next Steps

1. **Execute migration plan** using generated tasks
2. **Update documentation** to reflect new structure
3. **Update team guidelines** for future file placement
4. **Set up linting rules** to enforce organization
5. **Create templates** for common file types

## Timeline

- **Analysis**: 5 minutes
- **Structure Creation**: 10 minutes
- **Classification**: 5 minutes
- **Complexity Assessment**: 5 minutes
- **Environment Validation**: 3 minutes
- **Total**: ~30 minutes for validation

This quickstart should complete in under 30 minutes and provide clear confidence that the reorganization approach is viable for the Salonier project.
