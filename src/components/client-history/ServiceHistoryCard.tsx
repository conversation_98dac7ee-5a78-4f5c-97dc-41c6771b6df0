import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Pressable, TouchableOpacity } from 'react-native';
import { Star, Calendar, Palette, CheckCircle, AlertTriangle, Edit2 } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
} from 'react-native-reanimated';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { ColorConstants } from '@/styles/colors';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { useFormulaFeedbackStore, FormulaFeedback } from '@/stores/formula-feedback-store';
import { PreviousFormula } from '@/stores/client-history-store';
import { FeedbackEditModal } from '@/components/FeedbackEditModal';
import { CompactFormulaDisplay } from './CompactFormulaDisplay';
import * as Haptics from 'expo-haptics';
import { useHapticsEnabled, useAnimationsEnabled } from '@/stores/whimsy-store';
import { logger } from '@/utils/logger';

interface ServiceHistoryCardProps {
  service: PreviousFormula;
  _clientName: string;
  onPress?: () => void;
  testID?: string;
  onAddFeedback?: (service: PreviousFormula) => void;
  compact?: boolean;
}

export const ServiceHistoryCard: React.FC<ServiceHistoryCardProps> = ({
  service,
  _clientName,
  onPress,
  testID,
  onAddFeedback,
  compact = false,
}) => {
  const { getFeedbackByService, updateFeedback } = useFormulaFeedbackStore();
  const [showEditFeedbackModal, setShowEditFeedbackModal] = useState(false);

  // Animation and preferences
  const hapticsEnabled = useHapticsEnabled();
  const animationsEnabled = useAnimationsEnabled();

  // Animation values
  const cardScale = useSharedValue(1);
  const editButtonScale = useSharedValue(0);
  const editButtonOpacity = useSharedValue(0);
  const starsOpacity = useSharedValue(0);
  const badgesScale = useSharedValue(0);

  // Get feedback for this service (if service has a service_id)
  const feedback: FormulaFeedback | undefined = service.id
    ? getFeedbackByService(service.id)
    : undefined;

  // Card entrance animation
  useEffect(() => {
    if (animationsEnabled) {
      // Enhanced staggered entrance animation with better timing
      const cardDelay = Math.random() * 150; // Reduced randomness for smoother stagger

      cardScale.value = withDelay(
        cardDelay,
        withSpring(1, { damping: 15, stiffness: 200 }) // Slightly more responsive
      );

      starsOpacity.value = withDelay(cardDelay + 200, withTiming(1, { duration: 500 }));

      badgesScale.value = withDelay(
        cardDelay + 300,
        withSpring(1, { damping: 12, stiffness: 180 })
      );

      // Show edit button with delay if feedback exists
      if (feedback) {
        editButtonOpacity.value = withDelay(cardDelay + 400, withTiming(1, { duration: 400 }));
        editButtonScale.value = withDelay(
          cardDelay + 400,
          withSpring(1, { damping: 15, stiffness: 200 })
        );
      }
    } else {
      // Instant appearance if animations disabled
      cardScale.value = 1;
      starsOpacity.value = 1;
      badgesScale.value = 1;
      editButtonOpacity.value = feedback ? 1 : 0;
      editButtonScale.value = 1;
    }
  }, [
    animationsEnabled,
    feedback,
    badgesScale,
    cardScale,
    editButtonOpacity,
    editButtonScale,
    starsOpacity,
  ]);

  // Calculate visual indicators
  const getResultColor = (result: string) => {
    switch (result) {
      case 'excelente':
        return BeautyMinimalTheme.semantic.status.success;
      case 'bueno':
        return BeautyMinimalTheme.semantic.interactive.primary.default;
      case 'regular':
        return BeautyMinimalTheme.semantic.status.warning;
      case 'malo':
        return BeautyMinimalTheme.semantic.status.error;
      default:
        return BeautyMinimalTheme.semantic.text.secondary;
    }
  };

  const getResultIcon = (result: string) => {
    switch (result) {
      case 'excelente':
        return CheckCircle;
      case 'bueno':
        return CheckCircle;
      case 'regular':
        return AlertTriangle;
      case 'malo':
        return AlertTriangle;
      default:
        return Palette;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const ResultIcon = getResultIcon(service.result);
  const resultColor = getResultColor(service.result);

  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }],
  }));

  const editButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: editButtonScale.value }],
    opacity: editButtonOpacity.value,
  }));

  const starsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: starsOpacity.value,
  }));

  const badgesAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: badgesScale.value }],
  }));

  // Press handlers with enhanced feedback
  const handleCardPressIn = () => {
    if (animationsEnabled) {
      cardScale.value = withSpring(0.98, { damping: 15 });
    }
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleCardPressOut = () => {
    if (animationsEnabled) {
      cardScale.value = withSpring(1, { damping: 12 });
    }
  };

  const handleEditPressIn = () => {
    if (animationsEnabled) {
      editButtonScale.value = withSpring(0.95, { damping: 20, stiffness: 400 });
    }
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleEditPressOut = () => {
    if (animationsEnabled) {
      editButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
    }
  };

  // Ultra-compact render for new design
  if (compact) {
    const hasFeedback = getFeedbackByService(service.id);
    const ResultIcon = getResultIcon(service.result);
    const resultColor = getResultColor(service.result);

    return (
      <Animated.View style={cardAnimatedStyle}>
        <View style={[styles.compactContainer, !hasFeedback && styles.compactContainerPending]}>
          <TouchableOpacity
            onPress={onPress}
            onPressIn={handleCardPressIn}
            onPressOut={handleCardPressOut}
            testID={testID}
            activeOpacity={0.8}
            style={styles.compactTouchableContainer}
          >
            {/* Línea 1: Contexto */}
            <View style={styles.compactHeader}>
              <View style={styles.compactDateBrand}>
                <Text style={styles.compactDate}>{formatDate(service.date)}</Text>
                <Text style={styles.compactSeparator}>•</Text>
                <Text style={styles.compactBrand}>{service.brand}</Text>
                <Text style={styles.compactSeparator}>•</Text>
                <View style={styles.compactRating}>
                  <Star
                    size={12}
                    color={BeautyMinimalTheme.beautyColors.amber}
                    fill={BeautyMinimalTheme.beautyColors.amber}
                  />
                  <Text style={styles.compactRatingText}>{service.satisfaction}</Text>
                </View>
              </View>
              <View
                style={[
                  styles.compactStatusDot,
                  {
                    backgroundColor: hasFeedback
                      ? BeautyMinimalTheme.semantic.status.success
                      : BeautyMinimalTheme.semantic.status.warning,
                  },
                ]}
              />
            </View>

            {/* Línea 2: Transformación */}
            <View style={styles.compactTransformation}>
              <Text style={styles.compactTransformationText} numberOfLines={1}>
                {service.currentColor || 'Color base'} → {service.desiredColor || service.line}
              </Text>
            </View>
          </TouchableOpacity>

          {/* Línea 3: Acción requerida */}
          <View style={styles.compactAction}>
            {!hasFeedback ? (
              <TouchableOpacity
                style={styles.compactFeedbackButton}
                onPress={() => {
                  if (__DEV__) {
                    logger.debug('Feedback button pressed for service', 'ServiceHistoryCard', {
                      serviceId: service.id,
                    });
                  }
                  if (onAddFeedback) {
                    onAddFeedback(service);
                  } else {
                    if (__DEV__) {
                      logger.warn('onAddFeedback not provided', 'ServiceHistoryCard');
                    }
                  }
                }}
                hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                activeOpacity={0.7}
              >
                <Text style={styles.compactActionText}>⚠️ Pendiente feedback</Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.compactSuccess}>
                <ResultIcon size={12} color={resultColor} />
                <Text style={[styles.compactSuccessText, { color: resultColor }]}>
                  {service.result.charAt(0).toUpperCase() + service.result.slice(1)}
                </Text>
              </View>
            )}
          </View>
        </View>
      </Animated.View>
    );
  }

  // Original full card for non-compact mode
  return (
    <Animated.View style={cardAnimatedStyle}>
      <BeautyCard
        variant="elevated"
        style={styles.container}
        testID={testID}
        accessibilityLabel={`Servicio del ${formatDate(service.date)} con ${service.brand} ${service.line}`}
      >
        <Pressable
          style={styles.content}
          onPress={onPress}
          onPressIn={handleCardPressIn}
          onPressOut={handleCardPressOut}
          accessibilityRole="button"
          accessibilityHint="Tocar para ver detalles del servicio"
        >
          {/* Header with date and result */}
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <Calendar size={16} color={BeautyMinimalTheme.semantic.text.secondary} />
              <Text style={styles.dateText}>{formatDate(service.date)}</Text>
            </View>
            <View style={[styles.resultBadge, { backgroundColor: resultColor + '15' }]}>
              <ResultIcon size={14} color={resultColor} />
              <Text style={[styles.resultText, { color: resultColor }]}>
                {service.result.toUpperCase()}
              </Text>
            </View>
          </View>

          {/* Brand and Line */}
          <View style={styles.brandContainer}>
            <Palette
              size={18}
              color={BeautyMinimalTheme.semantic.interactive.professional.default}
            />
            <View style={styles.brandText}>
              <Text style={styles.brandName}>{service.brand}</Text>
              <Text style={styles.lineName}>{service.line}</Text>
            </View>
          </View>

          {/* Enhanced Formula Display */}
          <CompactFormulaDisplay
            service={service}
            variant="compact"
            showConfidence={true}
            style={styles.formulaDisplay}
          />

          {/* Rating Section */}
          <View style={styles.ratingSection}>
            <Animated.View style={[styles.starsContainer, starsAnimatedStyle]}>
              {Array.from({ length: 5 }, (_, index) => (
                <Star
                  key={index}
                  size={16}
                  color={
                    index < service.satisfaction
                      ? BeautyMinimalTheme.semantic.status.warning
                      : BeautyMinimalTheme.semantic.border.subtle
                  }
                  fill={
                    index < service.satisfaction
                      ? BeautyMinimalTheme.semantic.status.warning
                      : 'transparent'
                  }
                />
              ))}
              <Text style={styles.ratingText}>{service.satisfaction}/5</Text>
            </Animated.View>

            {/* Simplified feedback info with inline edit action */}
            {feedback && (
              <Animated.View style={[styles.feedbackSection, badgesAnimatedStyle]}>
                <View style={styles.feedbackSummary}>
                  <View style={styles.feedbackStatus}>
                    <CheckCircle size={12} color={BeautyMinimalTheme.semantic.status.success} />
                    <Text style={styles.feedbackStatusText}>
                      {feedback.rating >= 4 ? 'Exitosa' : feedback.rating >= 3 ? 'OK' : 'Mejorable'}
                    </Text>
                  </View>

                  <View style={styles.feedbackActions}>
                    <Animated.View style={editButtonAnimatedStyle}>
                      <Pressable
                        style={styles.inlineEditButton}
                        onPress={e => {
                          e.stopPropagation();
                          if (hapticsEnabled) {
                            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                          }
                          setShowEditFeedbackModal(true);
                        }}
                        onPressIn={handleEditPressIn}
                        onPressOut={handleEditPressOut}
                        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                        accessibilityRole="button"
                        accessibilityLabel="Editar feedback"
                        accessibilityHint="Tocar para editar el feedback de este servicio"
                      >
                        <Edit2 size={12} color={BeautyMinimalTheme.semantic.text.secondary} />
                        <Text style={styles.inlineEditText}>Editar</Text>
                      </Pressable>
                    </Animated.View>
                  </View>
                </View>

                {/* Minimal feedback indicators */}
                <View style={styles.feedbackIndicators}>
                  <Text
                    style={[
                      styles.feedbackDot,
                      {
                        color: feedback.worked_as_expected
                          ? BeautyMinimalTheme.semantic.status.success
                          : BeautyMinimalTheme.semantic.status.error,
                      },
                    ]}
                  >
                    ●
                  </Text>
                  <Text style={styles.feedbackLabel}>
                    {feedback.worked_as_expected
                      ? 'Funcionó como esperado'
                      : 'No funcionó como esperado'}
                  </Text>
                </View>
              </Animated.View>
            )}
          </View>

          {/* Notes if available */}
          {service.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Notas</Text>
              <Text style={styles.notesText} numberOfLines={2}>
                {service.notes}
              </Text>
            </View>
          )}

          {/* Detailed feedback preview if available */}
          {feedback && (feedback.actual_result || feedback.adjustments_made) && (
            <View style={styles.feedbackPreview}>
              {feedback.actual_result && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackLabel}>Resultado:</Text>
                  <Text style={styles.feedbackValue} numberOfLines={1}>
                    {feedback.actual_result}
                  </Text>
                </View>
              )}
              {feedback.adjustments_made && (
                <View style={styles.feedbackDetail}>
                  <Text style={styles.feedbackLabel}>Ajustes:</Text>
                  <Text style={styles.feedbackValue} numberOfLines={1}>
                    {feedback.adjustments_made}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Sync status indicator for offline-first feedback */}
          {service._syncStatus && service._syncStatus === 'pending' && (
            <View style={styles.syncIndicator}>
              <View style={styles.syncDot} />
              <Text style={styles.syncText}>Sincronizando...</Text>
            </View>
          )}
        </Pressable>

        {/* Feedback Edit Modal */}
        {feedback && (
          <FeedbackEditModal
            visible={showEditFeedbackModal}
            onClose={() => setShowEditFeedbackModal(false)}
            feedback={feedback}
            onSave={async updates => {
              await updateFeedback(feedback.id, updates);
              setShowEditFeedbackModal(false);
            }}
          />
        )}
      </BeautyCard>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // ULTRA-COMPACT STYLES
  compactContainer: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  compactContainerPending: {
    borderColor: BeautyMinimalTheme.semantic.status.warning,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '05',
  },
  compactTouchableContainer: {
    flex: 1,
  },
  compactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  compactDateBrand: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactDate: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  compactBrand: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  compactSeparator: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    marginHorizontal: BeautyMinimalTheme.spacing.xs,
  },
  compactRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  compactRatingText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  compactStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  compactTransformation: {
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  compactTransformationText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  compactAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactFeedbackButton: {
    flex: 1,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.sm,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning + '10',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.status.warning + '30',
  },
  compactActionText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.status.warning,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    textAlign: 'center',
  },
  compactSuccess: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  compactSuccessText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  // ORIGINAL STYLES
  container: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    // Añadir un borde sutil pero más visible para mayor diferenciación
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.default,
    // Fondo ligeramente contrastado para mejor definición
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
  },
  content: {
    padding: BeautyMinimalTheme.spacing.md,
    // Añadir un fondo interno más sutil para crear capas visuales
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: BeautyMinimalTheme.radius.md,
    margin: BeautyMinimalTheme.spacing.xs,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
    paddingBottom: BeautyMinimalTheme.spacing.sm,
    // Añadir un borde inferior sutil para separar el header del contenido
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  dateText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  resultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
    // Añadir una sombra muy sutil para destacar el badge
    ...BeautyMinimalTheme.shadows.subtle,
    // Borde sutil para mayor definición
    borderWidth: 1,
    borderColor: ColorConstants.TRANSPARENT_BLACK_05,
  },
  resultText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  brandContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  brandText: {
    flex: 1,
  },
  brandName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  lineName: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  formulaDisplay: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  ratingSection: {
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
    paddingTop: BeautyMinimalTheme.spacing.md,
    marginTop: BeautyMinimalTheme.spacing.sm,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    // Añadir un fondo muy sutil para la sección de rating
    backgroundColor: BeautyMinimalTheme.neutrals.pearl,
    marginHorizontal: -BeautyMinimalTheme.spacing.xs,
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  ratingText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginLeft: BeautyMinimalTheme.spacing.sm,
  },
  feedbackSection: {
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  feedbackSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  feedbackStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  feedbackStatusText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.status.success,
  },
  feedbackActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inlineEditButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  inlineEditText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
  feedbackIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  feedbackDot: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
  },
  feedbackLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  notesContainer: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  notesLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    textTransform: 'uppercase',
  },
  notesText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight: 18,
  },
  feedbackPreview: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
    borderRadius: BeautyMinimalTheme.radius.sm,
    padding: BeautyMinimalTheme.spacing.sm,
    marginTop: BeautyMinimalTheme.spacing.xs,
  },
  feedbackDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  feedbackLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    minWidth: 60,
    marginRight: BeautyMinimalTheme.spacing.xs,
  },
  feedbackValue: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    flex: 1,
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
    paddingTop: BeautyMinimalTheme.spacing.xs,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  syncDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: BeautyMinimalTheme.semantic.status.warning,
  },
  syncText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    fontStyle: 'italic',
  },
});
