import { useState } from 'react';
import {
  BrandConversionState,
  createInitialBrandConversionState,
  createConversionContext,
  resetBrandConversionState,
  validateBrandConversion,
} from '../utils/brandConversionUtils';

/**
 * Hook for managing brand conversion functionality
 */
export const useBrandConversion = () => {
  const [brandConversionState, setBrandConversionState] = useState<BrandConversionState>(
    createInitialBrandConversionState()
  );

  const setShowBrandModal = (show: boolean) => {
    setBrandConversionState(prev => ({ ...prev, showBrandModal: show }));
  };

  const setBrandModalType = (type: 'main' | 'conversion') => {
    setBrandConversionState(prev => ({ ...prev, brandModalType: type }));
  };

  const setConversionMode = (mode: boolean) => {
    setBrandConversionState(prev => ({ ...prev, conversionMode: mode }));
  };

  const setOriginalBrand = (brand: string) => {
    setBrandConversionState(prev => ({ ...prev, originalBrand: brand }));
  };

  const setOriginalLine = (line: string) => {
    setBrandConversionState(prev => ({ ...prev, originalLine: line }));
  };

  const setOriginalFormula = (formula: string) => {
    setBrandConversionState(prev => ({ ...prev, originalFormula: formula }));
  };

  const resetBrandConversion = () => {
    setBrandConversionState(prev => ({ ...prev, ...resetBrandConversionState() }));
  };

  const getConversionContext = () => {
    return createConversionContext(brandConversionState);
  };

  const isValidConversion = () => {
    return validateBrandConversion(brandConversionState);
  };

  return {
    // State
    showBrandModal: brandConversionState.showBrandModal,
    brandModalType: brandConversionState.brandModalType,
    conversionMode: brandConversionState.conversionMode,
    originalBrand: brandConversionState.originalBrand,
    originalLine: brandConversionState.originalLine,
    originalFormula: brandConversionState.originalFormula,

    // Actions
    setShowBrandModal,
    setBrandModalType,
    setConversionMode,
    setOriginalBrand,
    setOriginalLine,
    setOriginalFormula,
    resetBrandConversion,

    // Computed
    getConversionContext,
    isValidConversion,
  };
};
