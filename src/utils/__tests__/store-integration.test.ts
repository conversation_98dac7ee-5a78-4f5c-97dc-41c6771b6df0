/**
 * Integration test to verify the Event Bus fixes circular dependencies
 * Tests the actual auth-store and salon-config-store interaction
 */

import { storeEventBus, STORE_EVENTS } from '../store-event-bus';

// Mock the stores to avoid actual Supabase calls
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      onAuthStateChange: jest.fn(),
      getSession: jest.fn(),
    },
  },
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('Store Integration - Event Bus Fix', () => {
  beforeEach(() => {
    storeEventBus.clear();
    jest.clearAllMocks();
  });

  afterEach(() => {
    storeEventBus.clear();
  });

  describe('Auth Store to Salon Config Store Communication', () => {
    it('should emit SYNC_REQUIRED event when auth store syncs', async () => {
      const mockSyncHandler = jest.fn();
      
      // Setup listener (simulating salon-config-store)
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, mockSyncHandler);
      
      // Simulate auth store emitting sync event
      await storeEventBus.emit(
        STORE_EVENTS.SYNC_REQUIRED, 
        { source: 'auth' }, 
        'auth-store'
      );
      
      expect(mockSyncHandler).toHaveBeenCalledWith({ source: 'auth' });
    });

    it('should handle multiple sync events without interference', async () => {
      const authSyncHandler = jest.fn();
      const inventorySyncHandler = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, authSyncHandler);
      storeEventBus.on(STORE_EVENTS.INVENTORY_UPDATED, inventorySyncHandler);
      
      // Emit different events
      await storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { source: 'auth' }, 'auth-store');
      await storeEventBus.emit(STORE_EVENTS.INVENTORY_UPDATED, { productId: '123' }, 'inventory-store');
      
      expect(authSyncHandler).toHaveBeenCalledWith({ source: 'auth' });
      expect(inventorySyncHandler).toHaveBeenCalledWith({ productId: '123' });
    });

    it('should verify no circular dependency imports exist', () => {
      // This test ensures we can import both stores without circular dependency errors
      expect(() => {
        // These imports should not cause circular dependency issues
        require('../../stores/auth-store');
        require('../../stores/salon-config-store');
      }).not.toThrow();
    });
  });

  describe('Event Bus Performance', () => {
    it('should handle high-frequency events efficiently', async () => {
      const handler = jest.fn();
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler);
      
      const startTime = Date.now();
      
      // Emit 100 events
      const promises = Array.from({ length: 100 }, (_, i) =>
        storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { index: i }, 'test')
      );
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(handler).toHaveBeenCalledTimes(100);
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
    });

    it('should cleanup listeners properly to prevent memory leaks', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      const unsubscribe1 = storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler1);
      const unsubscribe2 = storeEventBus.on(STORE_EVENTS.USER_SIGNED_IN, handler2);
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(1);
      expect(storeEventBus.getListenerCount(STORE_EVENTS.USER_SIGNED_IN)).toBe(1);
      
      unsubscribe1();
      unsubscribe2();
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(0);
      expect(storeEventBus.getListenerCount(STORE_EVENTS.USER_SIGNED_IN)).toBe(0);
    });
  });

  describe('Error Isolation', () => {
    it('should isolate errors between different event handlers', async () => {
      const errorHandler = jest.fn(() => {
        throw new Error('Handler error');
      });
      const goodHandler = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, errorHandler);
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, goodHandler);
      
      // Should not throw despite error in first handler
      await expect(
        storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { test: 'data' }, 'test')
      ).resolves.not.toThrow();
      
      expect(errorHandler).toHaveBeenCalled();
      expect(goodHandler).toHaveBeenCalled();
    });
  });

  describe('Type Safety', () => {
    it('should provide type-safe event constants', () => {
      // Verify all expected event types exist
      expect(STORE_EVENTS.USER_SIGNED_IN).toBe('user:signed_in');
      expect(STORE_EVENTS.USER_SIGNED_OUT).toBe('user:signed_out');
      expect(STORE_EVENTS.SYNC_REQUIRED).toBe('sync:required');
      expect(STORE_EVENTS.SALON_CONFIG_UPDATED).toBe('salon:config_updated');
      expect(STORE_EVENTS.INVENTORY_UPDATED).toBe('inventory:updated');
    });
  });
});
