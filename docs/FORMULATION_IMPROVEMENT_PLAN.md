# Plan de Mejora de Formulación (P0)

Este documento resume los cambios acordados para aumentar la coherencia y la confianza del asistente de formulación sin bloquear por inventario ni modificar la calculadora de "Adaptar fórmula de otra marca".

## Objetivos

- Proporciones y cantidades siempre consistentes por paso (p. ej., 1:2 → 30 g + 60 ml).
- Coherencia de sesiones en todas las vistas (misma frase y rango recomendado).
- FOA (Fondo de Aclaración) visible y conectado con la tonalización.
- Inventario no bloqueante: chips En stock / Sin stock / Pte. precio.
- Costes: cuando falte precio o stock, mostrar "—" o "Pte. precio/stock" (nunca 0,00 € ficticio).

## Entregables (P0)

1. SessionPlanner (determinista)

- Servicio que calcula `min/recommended/max` sesiones a partir de: nivel actual, objetivo, cabello teñido/natural, porosidad, resistencia y técnica.
- Se usa para unificar el texto "Plan recomendado: N sesiones (rango A–B)".

2. MixingEngine (determinista)

- Normaliza cada paso de la fórmula:
  - Detecta línea/brand del paso a partir de los productos.
  - Obtiene el ratio de mezcla (por línea y contexto: color, tonalizar, decolorar) y el oxidante recomendado.
  - Calcula y corrige las cantidades del oxidante por "partes".
  - Expone un `mixRatioLabel` (p. ej., "Mezcla 1:2 → 30 g + 60 ml").
- Fuente de datos: reglas de BD (si están disponibles) y, en su defecto, `brand-packs` locales.

3. FOA y copy

- En Diagnóstico por zonas: mostrar "Fondo esperado al aclarar" y "Neutralizar con …".
- Evitar "neutralizar: no requiere" en objetivos 1→8/9.

4. Inventario y costes

- Chips de estado por producto: En stock / Sin stock / Pte. precio.
- En costes, mostrar "—" o "Pte. precio/stock" cuando falte información.

## Alcance de esta rama

- Nuevos servicios:
  - `services/formulation/session-planner.ts`
  - `services/formulation/mixing-engine.ts`
  - `services/rulesService.ts` (skeleton; fallback a brand-packs)
- Integración inicial en `useFormulation.ts` para normalizar pasos con `MixingEngine`.
- Pequeño soporte UI en `StepDetailCard` para mostrar la etiqueta de mezcla si está disponible.

## Fuentes de verdad

- En runtime, la fuente de verdad de números (ratios, ml, vol, tiempos) es **determinista** (MixingEngine + reglas).
- La IA sugiere estructura/tonos; el compilado final valida y normaliza.

## Siguientes fases (P1+)

- Usar reglas de Supabase como principal (y generar brand-packs desde BD en build).
- Pipeline de ingestión IA→staging con evidencia y validación ligera.
- "Proven formulas" y métricas de confianza.
