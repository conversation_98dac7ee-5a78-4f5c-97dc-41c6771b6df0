/**
 * Edge Function Integration Tests - Direct API Testing
 * Tests the salonier-assistant Edge Function endpoints directly
 *
 * This test suite focuses on:
 * 1. Direct Edge Function API calls
 * 2. Authentication flow
 * 3. Detailed logging validation
 * 4. Error handling and edge cases
 * 5. Performance benchmarking
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL =
  process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://wekqsxzjhhiuxahxpfin.supabase.co';
const SUPABASE_ANON_KEY =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indla3FzeHpqaGhpdXhhaHhwZmluIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyOTI0MDksImV4cCI6MjA0OTg2ODQwOX0.LoV7pFBz7N2ZNGEJIFf0AKfPY3CqwJpCO5NeUH0_qeg';

interface TestAuthUser {
  email: string;
  password: string;
  token?: string;
  userId?: string;
}

interface APITestCase {
  name: string;
  task: string;
  payload: any;
  expectedFields: string[];
  expectedDuration?: number; // milliseconds
  shouldSucceed: boolean;
}

describe('Edge Function Integration Tests', () => {
  let supabase: SupabaseClient;
  let testUser: TestAuthUser;
  let functionUrl: string;

  beforeAll(async () => {
    supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    functionUrl = `${SUPABASE_URL}/functions/v1/salonier-assistant`;

    // Setup test user authentication
    testUser = {
      email: '<EMAIL>',
      password: 'integrationtest123',
    };

    await setupTestAuthentication();
  });

  async function setupTestAuthentication() {
    console.log('Setting up test authentication...');

    // Try to sign in with existing test user
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password,
    });

    if (signInError) {
      console.log('Test user does not exist, creating...');

      // Create new test user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: testUser.email,
        password: testUser.password,
        options: {
          data: {
            full_name: 'Integration Test User',
            salon_name: 'Integration Test Salon',
          },
        },
      });

      if (signUpError) {
        throw new Error(`Failed to create test user: ${signUpError.message}`);
      }

      signInData = signUpData;
    }

    testUser.token = signInData.session?.access_token;
    testUser.userId = signInData.user?.id;

    expect(testUser.token).toBeTruthy();
    console.log('Authentication setup complete');
  }

  async function makeAPICall(
    task: string,
    payload: any,
    timeout = 30000
  ): Promise<{
    success: boolean;
    data?: any;
    error?: any;
    duration: number;
  }> {
    const startTime = Date.now();

    try {
      const response = (await Promise.race([
        fetch(functionUrl, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${testUser.token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            task,
            payload,
          }),
        }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Request timeout')), timeout)),
      ])) as Response;

      const duration = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: {
            status: response.status,
            statusText: response.statusText,
            body: errorText,
          },
          duration,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        error,
        duration,
      };
    }
  }

  describe('Authentication and Basic Connectivity', () => {
    test('should have valid authentication token', () => {
      expect(testUser.token).toBeTruthy();
      expect(testUser.token?.length).toBeGreaterThan(100);
    });

    test('should connect to Edge Function endpoint', async () => {
      const result = await makeAPICall(
        'parse_product_text',
        {
          text: 'tubo de tinte rubio ceniza 8.1',
        },
        10000
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.duration).toBeLessThan(10000);
    });
  });

  describe('Hair Diagnosis Analysis (diagnose_image)', () => {
    const testCases: APITestCase[] = [
      {
        name: 'Virgin dark blonde hair analysis',
        task: 'diagnose_image',
        payload: {
          imageUrl: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=800&q=80',
        },
        expectedFields: [
          'averageLevel',
          'overallTone',
          'overallReflect',
          'overallCondition',
          'zoneAnalysis',
          'bucketInfo',
          'overallConfidence',
        ],
        expectedDuration: 25000,
        shouldSucceed: true,
      },
      {
        name: 'Damaged colored hair analysis',
        task: 'diagnose_image',
        payload: {
          imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616c24ca249?w=800&q=80',
        },
        expectedFields: ['averageLevel', 'overallTone', 'overallCondition', 'zoneAnalysis'],
        expectedDuration: 25000,
        shouldSucceed: true,
      },
      {
        name: 'Invalid image URL should fail gracefully',
        task: 'diagnose_image',
        payload: {
          imageUrl: 'https://invalid-url-that-does-not-exist.com/image.jpg',
        },
        expectedFields: [],
        shouldSucceed: false,
      },
    ];

    test.each(testCases)('$name', async testCase => {
      const result = await makeAPICall(testCase.task, testCase.payload, 30000);

      if (testCase.shouldSucceed) {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();

        // Validate required fields
        testCase.expectedFields.forEach(field => {
          expect(result.data).toHaveProperty(field);
        });

        // Validate specific diagnosis fields
        if (result.data.averageLevel) {
          expect(result.data.averageLevel).toBeGreaterThan(1);
          expect(result.data.averageLevel).toBeLessThanOrEqual(10);
        }

        if (result.data.overallConfidence) {
          expect(result.data.overallConfidence).toBeGreaterThan(0);
          expect(result.data.overallConfidence).toBeLessThanOrEqual(1);
        }

        if (result.data.zoneAnalysis) {
          expect(result.data.zoneAnalysis).toHaveProperty('roots');
          expect(result.data.zoneAnalysis).toHaveProperty('mids');
          expect(result.data.zoneAnalysis).toHaveProperty('ends');
        }

        // Performance validation
        if (testCase.expectedDuration) {
          expect(result.duration).toBeLessThan(testCase.expectedDuration);
        }

        console.log(`✅ ${testCase.name} completed in ${result.duration}ms`);
        console.log(`📊 Analysis Result:`, {
          level: result.data.averageLevel,
          tone: result.data.overallTone,
          condition: result.data.overallCondition,
          confidence: result.data.overallConfidence,
        });
      } else {
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();

        console.log(`❌ ${testCase.name} failed as expected:`, result.error);
      }
    });
  });

  describe('Formula Generation (generate_formula)', () => {
    let sampleDiagnosis: any;

    beforeAll(async () => {
      // Get a sample diagnosis for formula generation tests
      const diagnosisResult = await makeAPICall('diagnose_image', {
        imageUrl: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=800&q=80',
      });

      expect(diagnosisResult.success).toBe(true);
      sampleDiagnosis = diagnosisResult.data;
    });

    const formulaTestCases: APITestCase[] = [
      {
        name: 'Generate formula for level lift with Wella',
        task: 'generate_formula',
        payload: {
          diagnosis: null, // Will be set dynamically
          desiredResult: {
            targetLevel: 8,
            targetTone: 'neutral',
            viability: 'feasible',
          },
          brand: 'Wella Professionals',
          line: 'Koleston Perfect',
          clientHistory: 'Virgin hair, first coloration',
        },
        expectedFields: ['formula', 'products', 'technique', 'processingTime'],
        expectedDuration: 35000,
        shouldSucceed: true,
      },
      {
        name: 'Generate formula for color correction with LOréal',
        task: 'generate_formula',
        payload: {
          diagnosis: null, // Will be set dynamically
          desiredResult: {
            targetLevel: 7,
            targetTone: 'ash',
            viability: 'challenging',
          },
          brand: "L'Oréal Professionnel",
          line: 'Majirel',
          clientHistory: 'Previously colored 6 months ago, some damage',
        },
        expectedFields: ['formula', 'products', 'technique', 'processingTime'],
        expectedDuration: 35000,
        shouldSucceed: true,
      },
    ];

    test.each(formulaTestCases)('$name', async testCase => {
      // Set diagnosis data
      testCase.payload.diagnosis = sampleDiagnosis;

      const result = await makeAPICall(testCase.task, testCase.payload, 45000);

      if (testCase.shouldSucceed) {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();

        // Validate required fields
        testCase.expectedFields.forEach(field => {
          expect(result.data).toHaveProperty(field);
        });

        // Validate formula content
        expect(result.data.formula).toContain('ml'); // Should contain measurements
        expect(result.data.products).toBeInstanceOf(Array);
        expect(result.data.products.length).toBeGreaterThan(0);

        // Validate processing time format
        expect(result.data.processingTime).toMatch(/\d+.*min/i);

        // Performance validation
        if (testCase.expectedDuration) {
          expect(result.duration).toBeLessThan(testCase.expectedDuration);
        }

        console.log(`✅ ${testCase.name} completed in ${result.duration}ms`);
        console.log(`🧪 Formula Overview:`, {
          brand: testCase.payload.brand,
          productsCount: result.data.products.length,
          hasFormula: !!result.data.formula,
          processingTime: result.data.processingTime,
        });
      } else {
        expect(result.success).toBe(false);
        console.log(`❌ ${testCase.name} failed as expected`);
      }
    });
  });

  describe('Desired Look Analysis (analyze_desired_look)', () => {
    const desiredLookTestCases: APITestCase[] = [
      {
        name: 'Analyze blonde desired look',
        task: 'analyze_desired_look',
        payload: {
          imageUrl: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800&q=80',
          currentLevel: 6,
        },
        expectedFields: ['targetLevel', 'targetTone', 'viability', 'technique'],
        expectedDuration: 20000,
        shouldSucceed: true,
      },
      {
        name: 'Analyze red desired look',
        task: 'analyze_desired_look',
        payload: {
          imageUrl: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&q=80',
          currentLevel: 5,
        },
        expectedFields: ['targetLevel', 'targetTone', 'viability'],
        expectedDuration: 20000,
        shouldSucceed: true,
      },
    ];

    test.each(desiredLookTestCases)('$name', async testCase => {
      const result = await makeAPICall(testCase.task, testCase.payload, 25000);

      if (testCase.shouldSucceed) {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();

        // Validate required fields
        testCase.expectedFields.forEach(field => {
          expect(result.data).toHaveProperty(field);
        });

        // Validate target level
        if (result.data.targetLevel) {
          expect(result.data.targetLevel).toBeGreaterThan(1);
          expect(result.data.targetLevel).toBeLessThanOrEqual(10);
        }

        // Validate viability
        if (result.data.viability) {
          expect(['feasible', 'challenging', 'risky', 'impossible']).toContain(
            result.data.viability
          );
        }

        console.log(`✅ ${testCase.name} completed in ${result.duration}ms`);
        console.log(`🎯 Desired Look Analysis:`, {
          targetLevel: result.data.targetLevel,
          targetTone: result.data.targetTone,
          viability: result.data.viability,
          technique: result.data.technique,
        });
      } else {
        expect(result.success).toBe(false);
      }
    });
  });

  describe('Product Text Parsing (parse_product_text)', () => {
    const parseTestCases: APITestCase[] = [
      {
        name: 'Parse Spanish product description',
        task: 'parse_product_text',
        payload: {
          text: 'caja de 12 tubos de tinte Wella Koleston 8/1 de 60ml',
        },
        expectedFields: ['brand', 'line', 'shade', 'quantity'],
        shouldSucceed: true,
      },
      {
        name: 'Parse professional product with developer',
        task: 'parse_product_text',
        payload: {
          text: '6 tubos LOreal Majirel 7.1 + oxidante 20vol 1000ml',
        },
        expectedFields: ['products'],
        shouldSucceed: true,
      },
      {
        name: 'Parse complex formula description',
        task: 'parse_product_text',
        payload: {
          text: 'Formula: 50ml Koleston 8/0 + 25ml 8/1 + 75ml ox 6% tiempo 30min',
        },
        expectedFields: ['formula', 'processingTime'],
        shouldSucceed: true,
      },
    ];

    test.each(parseTestCases)('$name', async testCase => {
      const result = await makeAPICall(testCase.task, testCase.payload, 15000);

      if (testCase.shouldSucceed) {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();

        console.log(`✅ ${testCase.name} completed in ${result.duration}ms`);
        console.log(`📝 Parsed Result:`, result.data);

        // Should have parsed some useful information
        const hasValidData = testCase.expectedFields.some(
          field => result.data[field] !== undefined && result.data[field] !== null
        );
        expect(hasValidData).toBe(true);
      } else {
        expect(result.success).toBe(false);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle missing task parameter', async () => {
      const result = await makeAPICall('', {}, 5000);
      expect(result.success).toBe(false);
    });

    test('should handle invalid task parameter', async () => {
      const result = await makeAPICall('invalid_task_name', {}, 5000);
      expect(result.success).toBe(false);
    });

    test('should handle malformed payload', async () => {
      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${testUser.token}`,
          'Content-Type': 'application/json',
        },
        body: '{"invalid_json": ',
      });

      expect(response.ok).toBe(false);
    });

    test('should handle unauthorized requests', async () => {
      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          Authorization: 'Bearer invalid_token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          task: 'parse_product_text',
          payload: { text: 'test' },
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);
    });
  });

  describe('Performance Benchmarking', () => {
    test('should meet performance targets for common operations', async () => {
      const benchmarks = [
        { task: 'parse_product_text', maxDuration: 3000 },
        { task: 'diagnose_image', maxDuration: 25000 },
        { task: 'analyze_desired_look', maxDuration: 20000 },
        { task: 'generate_formula', maxDuration: 35000 },
      ];

      for (const benchmark of benchmarks) {
        let payload = { text: 'test product' };

        if (benchmark.task === 'diagnose_image' || benchmark.task === 'analyze_desired_look') {
          payload = {
            imageUrl: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=400',
          };
        }

        if (benchmark.task === 'analyze_desired_look') {
          payload = { ...payload, currentLevel: 6 };
        }

        if (benchmark.task === 'generate_formula') {
          payload = {
            diagnosis: { averageLevel: 6, overallTone: 'neutral' },
            desiredResult: { targetLevel: 8 },
            brand: 'Wella',
            line: 'Koleston',
          };
        }

        const result = await makeAPICall(benchmark.task, payload);

        if (result.success) {
          expect(result.duration).toBeLessThan(benchmark.maxDuration);
          console.log(
            `⚡ ${benchmark.task}: ${result.duration}ms (target: <${benchmark.maxDuration}ms)`
          );
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up test authentication
    if (testUser.token) {
      await supabase.auth.signOut();
    }
  });
});
