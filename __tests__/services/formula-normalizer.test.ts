import { normalizeFormulaSteps } from '@/services/formula-normalizer';
import type { FormulationStep } from '@/types/formulation';

describe('Formula Normalizer', () => {
  it('scales toner step by hair length and fixes ratio label', () => {
    const steps: FormulationStep[] = [
      {
        stepNumber: 1,
        stepTitle: 'Sesión 2: Depositar/tonalizar',
        instructions: 'Tonalizar global',
        mix: [
          {
            productId: 't1',
            productName: 'Marca Toner X (mezcla 1:2)',
            quantity: 30,
            unit: 'gr',
          },
          {
            productId: 'd1',
            productName: 'Marca Oxidante 10 vol',
            quantity: 30,
            unit: 'ml',
          },
        ],
        processingTime: 20,
      },
    ];

    const out = normalizeFormulaSteps(steps, 'Generic', 'Toner', {
      hairLengthCm: 45,
      hairDensity: 'Media',
      hairThickness: 'Medio',
    });
    const s = out![0];
    const color = s.mix!.find(m => m.unit === 'gr')!;
    const dev = s.mix!.find(m => m.unit === 'ml')!;
    expect(color.quantity).toBeGreaterThanOrEqual(55); // 60g expected
    expect(dev.quantity).toBeGreaterThanOrEqual(110); // 120ml expected
    expect(color.productName).toMatch(/\(mezcla 1:2\)/);
    expect(dev.productName).toMatch(/\(mezcla 1:2\)/);
  });

  it('adds developer if missing and updates label', () => {
    const steps: FormulationStep[] = [
      {
        stepNumber: 1,
        stepTitle: 'Aplicación global',
        instructions: 'Color permanente global',
        mix: [{ productId: 'c1', productName: 'Marca Color 7/1', quantity: 40, unit: 'gr' }],
        processingTime: 35,
      },
    ];
    const out = normalizeFormulaSteps(steps, 'Generic', 'Permanent', { hairLengthCm: 35 });
    const s = out![0];
    expect(s.mix!.some(m => m.unit === 'ml')).toBe(true);
    const labelInName = s.mix!.every(m => /(mezcla)/i.test(m.productName));
    expect(labelInName).toBe(true);
  });
});
