import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { X, Search, Check, ChevronRight } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useAuthStore } from '@/stores/auth-store';
import {
  professionalHairColorBrands,
  Brand,
  ProductLine,
  getColorLinesByBrandId,
  getBrandsWithFormulableLines,
} from '@/services/brandService';

interface BrandsModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function BrandsModal({ visible, onClose }: BrandsModalProps) {
  const { preferredBrandLines, addBrandLineSelection, removeBrandLineSelection } = useAuthStore();

  // Modal states
  const [selectedBrandForModal, setSelectedBrandForModal] = useState<Brand | null>(null);
  const [tempSelectedLines, setTempSelectedLines] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCountryFilter, setSelectedCountryFilter] = useState<string>('');
  const [filteredBrands, setFilteredBrands] = useState<Brand[]>(getBrandsWithFormulableLines());
  const [isSelectedBrandsExpanded, setIsSelectedBrandsExpanded] = useState(false);

  // Search and filter effect
  useEffect(() => {
    // Solo buscar entre marcas con líneas formulables
    const brandsWithLines = getBrandsWithFormulableLines();
    let results = brandsWithLines;

    // Aplicar filtro de búsqueda
    if (searchQuery.trim()) {
      const lowercaseQuery = searchQuery.toLowerCase();
      results = brandsWithLines.filter(
        brand =>
          brand.name.toLowerCase().includes(lowercaseQuery) ||
          brand.country.toLowerCase().includes(lowercaseQuery) ||
          brand.lines.some(
            line => line.isColorLine && line.name.toLowerCase().includes(lowercaseQuery)
          )
      );
    }

    // Aplicar filtro de país
    if (selectedCountryFilter) {
      results = results.filter(brand => brand.country === selectedCountryFilter);
    }

    setFilteredBrands(results);
  }, [searchQuery, selectedCountryFilter]);

  // Get selected brands with their lines
  const getSelectedBrandLines = () => {
    const result: Array<{ brand: Brand; selectedLines: ProductLine[] }> = [];

    preferredBrandLines.forEach(bl => {
      const brand = professionalHairColorBrands.find(b => b.id === bl.brandId);
      if (brand) {
        // Only include color lines in the selection
        const colorLines = brand.lines.filter(line => line.isColorLine === true);
        const selectedLines = colorLines.filter(line => bl.selectedLines.includes(line.id));
        result.push({
          brand,
          selectedLines,
        });
      }
    });

    return result;
  };

  const openBrandModal = (brand?: Brand) => {
    if (brand) {
      setSelectedBrandForModal(brand);
      // Load existing selections for this brand
      const existing = preferredBrandLines.find(bl => bl.brandId === brand.id);
      setTempSelectedLines(existing?.selectedLines || []);
    } else {
      setSelectedBrandForModal(null);
      setTempSelectedLines([]);
    }
  };

  const toggleLineSelection = (lineId: string) => {
    setTempSelectedLines(prev =>
      prev.includes(lineId) ? prev.filter(id => id !== lineId) : [...prev, lineId]
    );
  };

  const saveBrandLineSelection = async () => {
    if (selectedBrandForModal) {
      if (tempSelectedLines.length > 0) {
        await addBrandLineSelection(selectedBrandForModal.id, tempSelectedLines);
      } else {
        await removeBrandLineSelection(selectedBrandForModal.id);
      }
    }
    setSelectedBrandForModal(null);
    setTempSelectedLines([]);
  };

  const removeBrandSelection = async (brandId: string) => {
    await removeBrandLineSelection(brandId);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSelectedCountryFilter('');
  };

  const handleClose = () => {
    setSearchQuery('');
    setSelectedCountryFilter('');
    setSelectedBrandForModal(null);
    setTempSelectedLines([]);
    onClose();
  };

  // Get unique countries for filter - solo de marcas con líneas formulables
  const countries = Array.from(
    new Set(getBrandsWithFormulableLines().map(brand => brand.country))
  ).sort();

  if (selectedBrandForModal) {
    // Brand detail view - select lines
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => setSelectedBrandForModal(null)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.title}>{selectedBrandForModal.name}</Text>
            <TouchableOpacity onPress={saveBrandLineSelection}>
              <Text style={styles.saveText}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.brandDetailHeader}>
              <Text style={styles.brandDetailName}>{selectedBrandForModal.name}</Text>
              <Text style={styles.brandDetailCountry}>{selectedBrandForModal.country}</Text>
              {selectedBrandForModal.description && (
                <Text style={styles.brandDetailDescription}>
                  {selectedBrandForModal.description}
                </Text>
              )}
            </View>

            <Text style={styles.linesTitle}>Selecciona las líneas de coloración que utilizas:</Text>

            {getColorLinesByBrandId(selectedBrandForModal.id).map(line => (
              <TouchableOpacity
                key={line.id}
                style={[
                  styles.lineItem,
                  tempSelectedLines.includes(line.id) && styles.lineItemSelected,
                ]}
                onPress={() => toggleLineSelection(line.id)}
              >
                <View style={styles.lineItemContent}>
                  <Text
                    style={[
                      styles.lineItemName,
                      tempSelectedLines.includes(line.id) && styles.lineItemNameSelected,
                    ]}
                  >
                    {line.name}
                  </Text>
                  {line.description && (
                    <Text style={styles.lineItemDescription}>{line.description}</Text>
                  )}
                </View>
                {tempSelectedLines.includes(line.id) && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal>
    );
  }

  // Main brands list view
  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Marcas Preferidas</Text>
          <View style={styles.placeholderView} />
        </View>

        {/* Fixed Search Section - Always visible */}
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar marcas de coloración..."
              placeholderTextColor={Colors.light.gray}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                <X size={16} color={Colors.light.gray} />
              </TouchableOpacity>
            )}
          </View>

          {/* Country Filter */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.countryFilterContainer}
            contentContainerStyle={styles.countryFilterContent}
          >
            <TouchableOpacity
              style={[
                styles.countryFilterChip,
                !selectedCountryFilter && styles.countryFilterChipSelected,
              ]}
              onPress={() => setSelectedCountryFilter('')}
            >
              <Text
                style={[
                  styles.countryFilterText,
                  !selectedCountryFilter && styles.countryFilterTextSelected,
                ]}
              >
                Todos
              </Text>
            </TouchableOpacity>
            {countries.map(country => (
              <TouchableOpacity
                key={country}
                style={[
                  styles.countryFilterChip,
                  selectedCountryFilter === country && styles.countryFilterChipSelected,
                ]}
                onPress={() => setSelectedCountryFilter(country)}
              >
                <Text
                  style={[
                    styles.countryFilterText,
                    selectedCountryFilter === country && styles.countryFilterTextSelected,
                  ]}
                >
                  {country}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Scrollable Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Selected Brands Summary */}
          {getSelectedBrandLines().length > 0 && (
            <View style={styles.selectedBrandsContainer}>
              <TouchableOpacity
                style={styles.selectedBrandsHeader}
                onPress={() => setIsSelectedBrandsExpanded(!isSelectedBrandsExpanded)}
              >
                <View>
                  <Text style={styles.sectionTitle}>Marcas Seleccionadas</Text>
                  {!isSelectedBrandsExpanded && (
                    <Text style={styles.selectedBrandsSummary}>
                      {getSelectedBrandLines().length} marca
                      {getSelectedBrandLines().length > 1 ? 's' : ''} seleccionada
                      {getSelectedBrandLines().length > 1 ? 's' : ''}
                    </Text>
                  )}
                </View>
                <ChevronRight
                  size={20}
                  color={Colors.light.gray}
                  style={[
                    styles.chevronIcon,
                    isSelectedBrandsExpanded && styles.chevronIconExpanded,
                  ]}
                />
              </TouchableOpacity>

              {isSelectedBrandsExpanded &&
                getSelectedBrandLines().map((item, index) => (
                  <View key={index} style={styles.selectedBrandCard}>
                    <View style={styles.selectedBrandHeader}>
                      <View style={styles.selectedBrandInfo}>
                        <Text style={styles.selectedBrandName}>{item.brand.name}</Text>
                        <Text style={styles.selectedBrandCountry}>{item.brand.country}</Text>
                      </View>
                      <TouchableOpacity onPress={() => removeBrandSelection(item.brand.id)}>
                        <X size={16} color={Colors.light.gray} />
                      </TouchableOpacity>
                    </View>
                    <View style={styles.selectedLinesContainer}>
                      {item.selectedLines.map((line, lineIndex) => (
                        <View key={lineIndex} style={styles.selectedLineTag}>
                          <Text style={styles.selectedLineText}>{line.name}</Text>
                        </View>
                      ))}
                    </View>
                    <TouchableOpacity
                      style={styles.editButton}
                      onPress={() => openBrandModal(item.brand)}
                    >
                      <Text style={styles.editButtonText}>Editar líneas</Text>
                    </TouchableOpacity>
                  </View>
                ))}
            </View>
          )}

          {/* Results Header */}
          <View style={styles.resultsHeader}>
            <Text style={styles.resultsText}>{filteredBrands.length} marcas encontradas</Text>
          </View>

          {/* Brands List */}
          <View style={styles.brandsList}>
            {filteredBrands.map(brand => {
              const isSelected = preferredBrandLines.some(bl => bl.brandId === brand.id);
              return (
                <TouchableOpacity
                  key={brand.id}
                  style={styles.brandItem}
                  onPress={() => openBrandModal(brand)}
                >
                  <View style={styles.brandItemContent}>
                    <Text style={styles.brandItemName}>{brand.name}</Text>
                    <Text style={styles.brandItemCountry}>{brand.country}</Text>
                    <Text style={styles.brandItemLines}>
                      {getColorLinesByBrandId(brand.id).length} líneas de coloración disponibles
                    </Text>
                    {brand.description && (
                      <Text style={styles.brandItemDescription}>{brand.description}</Text>
                    )}
                  </View>
                  {isSelected ? (
                    <View style={styles.selectedIndicator}>
                      <Check size={16} color={Colors.light.primary} />
                    </View>
                  ) : (
                    <ChevronRight size={20} color={Colors.light.gray} />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>

          {filteredBrands.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                No se encontraron marcas con los filtros seleccionados
              </Text>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  saveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  selectedBrandsContainer: {
    padding: spacing.lg,
    backgroundColor: Colors.light.surface,
  },
  selectedBrandsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  selectedBrandsSummary: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginTop: spacing.xs,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  selectedBrandCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  selectedBrandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  selectedBrandInfo: {
    flex: 1,
  },
  selectedBrandName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 2,
  },
  selectedBrandCountry: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  selectedLinesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginBottom: spacing.sm,
  },
  selectedLineTag: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: radius.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  selectedLineText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  editButton: {
    alignSelf: 'flex-start',
  },
  editButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  searchSection: {
    padding: spacing.lg,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  clearButton: {
    padding: spacing.xs,
  },
  countryFilterContainer: {
    maxHeight: 50,
  },
  countryFilterContent: {
    paddingRight: spacing.lg,
  },
  countryFilterChip: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.full,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  countryFilterChipSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  countryFilterText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    fontWeight: typography.weights.medium,
  },
  countryFilterTextSelected: {
    color: Colors.light.textLight,
  },
  resultsHeader: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  brandsList: {
    paddingVertical: spacing.sm,
  },
  brandItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  brandItemContent: {
    flex: 1,
  },
  brandItemName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 2,
  },
  brandItemCountry: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginBottom: 2,
  },
  brandItemLines: {
    fontSize: typography.sizes.xs,
    color: Colors.light.accent,
    marginBottom: 4,
  },
  brandItemDescription: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
    lineHeight: 16,
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    padding: spacing.xl * 2,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: typography.sizes.base,
    color: Colors.light.gray,
    textAlign: 'center',
  },
  brandDetailHeader: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  brandDetailName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  brandDetailCountry: {
    fontSize: typography.sizes.base,
    color: Colors.light.gray,
    marginBottom: spacing.sm,
  },
  brandDetailDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  linesTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  lineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  lineItemSelected: {
    backgroundColor: Colors.light.primary + '05',
  },
  lineItemContent: {
    flex: 1,
  },
  lineItemName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  lineItemNameSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  lineItemDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    lineHeight: 18,
  },
  placeholderView: {
    width: 24,
  },
  chevronIcon: {
    transform: [{ rotate: '0deg' }],
  },
  chevronIconExpanded: {
    transform: [{ rotate: '90deg' }],
  },
});
