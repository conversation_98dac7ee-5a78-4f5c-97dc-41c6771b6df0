import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import {
  ClientAllergy,
  PatchTest,
  ConsentRecord,
  PreviousFormula,
  SupabaseService,
  SupabaseFormula,
  SupabaseConsent,
} from '@/types/client-history.types';

// Funciones de extracción y normalización de datos de fórmulas

export const extractBrandLine = (formulaData: any): { brand?: string; line?: string } => {
  try {
    const steps = (formulaData?.steps as any[]) || [];
    for (const s of steps) {
      const mix = (s?.mix as any[]) || [];
      const color = mix.find(m => (m?.type || '').toLowerCase() === 'color');
      if (color?.brand || color?.line) {
        return { brand: color.brand, line: color.line };
      }
    }
    for (const s of steps) {
      const mix = (s?.mix as any[]) || [];
      const anyItem = mix.find(m => m?.brand || m?.line);
      if (anyItem) {
        return { brand: anyItem.brand, line: anyItem.line };
      }
    }
  } catch {
    // Ignore errors in data extraction
  }
  return {};
};

export const extractColorItems = (
  formulaData: any
): Array<{ brand?: string; line?: string; shade?: string }> => {
  const items: Array<{ brand?: string; line?: string; shade?: string }> = [];
  try {
    const steps = (formulaData?.steps as any[]) || [];
    for (const s of steps) {
      const mix = (s?.mix as any[]) || [];
      for (const m of mix) {
        if ((m?.type || '').toLowerCase() === 'color') {
          items.push({ brand: m.brand, line: m.line, shade: m.shade });
        }
      }
    }
  } catch {
    // Ignore errors in data extraction
  }
  return items;
};

export const normalizeShade = (shade?: string | null): string | null => {
  if (!shade) return null;
  return String(shade).trim();
};

// Funciones de conversión entre tipos de Supabase y locales

export function convertSupabaseServiceToFormula(
  service: SupabaseService,
  formula?: SupabaseFormula
): PreviousFormula | null {
  const baseResult = (
    service.satisfaction_score >= 4
      ? 'excelente'
      : service.satisfaction_score >= 3
        ? 'bueno'
        : service.satisfaction_score >= 2
          ? 'regular'
          : 'malo'
  ) as PreviousFormula['result'];

  if (!formula || !formula.formula_text) {
    return {
      id: service.id,
      serviceId: service.id,
      date: service.service_date,
      formula: service.notes || 'Servicio sin fórmula registrada',
      brand: '',
      line: '—',
      result: baseResult,
      satisfaction: service.satisfaction_score || 3,
      notes: service.notes || '',
      processingTime: service.duration_minutes || 30,
      oxidantVolume: '0',
      _syncStatus: 'synced',
    };
  }

  return {
    id: formula.id,
    serviceId: service.id,
    date: service.service_date,
    formula: formula.formula_text,
    brand: formula.brand || '',
    line: formula.line || '',
    result: baseResult,
    satisfaction: service.satisfaction_score || 3,
    notes: service.notes || '',
    processingTime: formula.processing_time || 35,
    oxidantVolume: formula.developer_volume?.toString() || '20',
    _syncStatus: 'synced',
  };
}

export function convertSupabaseConsentToLocal(consent: SupabaseConsent): ConsentRecord {
  return {
    id: consent.id,
    date: consent.created_at,
    consentItems: consent.consent_data?.items || [],
    signature: consent.signature_data || '',
    safetyChecklist: consent.safety_checklist || [],
    ipAddress: consent.ip_address || '',
    userAgent: consent.user_agent || '',
    skipSafetyVerification: consent.skip_safety || false,
    _syncStatus: 'synced',
  };
}

// Lógica de negocio para evaluación de riesgos y advertencias

export function checkAllergies(allergies: ClientAllergy[]): string[] {
  const warnings: string[] = [];
  allergies.forEach(allergy => {
    if (allergy.severity === 'severa') {
      warnings.push(`⚠️ ALERGIA SEVERA: ${allergy.substance}`);
    } else if (allergy.severity === 'moderada') {
      warnings.push(`⚠️ Alergia moderada: ${allergy.substance}`);
    }
  });
  return warnings;
}

export function checkPatchTests(patchTests: PatchTest[]): string[] {
  const warnings: string[] = [];
  const pendingTests = patchTests.filter(t => t.result === 'pendiente');
  if (pendingTests.length > 0) {
    warnings.push(`⚠️ Test de parche pendiente desde ${pendingTests[0].date}`);
  }

  const recentPositiveTests = patchTests.filter(t => {
    const daysSince = Math.floor((Date.now() - new Date(t.date).getTime()) / (1000 * 60 * 60 * 24));
    return t.result === 'positivo' && daysSince <= 30;
  });
  if (recentPositiveTests.length > 0) {
    warnings.push(`⚠️ Test de parche POSITIVO reciente - NO PROCEDER`);
  }
  return warnings;
}

export function checkConsent(lastConsent: ConsentRecord | null): string[] {
  const warnings: string[] = [];
  if (lastConsent) {
    const daysSince = Math.floor(
      (Date.now() - new Date(lastConsent.date).getTime()) / (1000 * 60 * 60 * 24)
    );
    if (daysSince > 365) {
      warnings.push(`⚠️ Consentimiento vencido - Renovar antes de proceder`);
    }
  }
  return warnings;
}

// Función de evaluación de catálogo (depende de Supabase)

export async function evaluateCatalogStatusIfNeeded(
  catalogStatusFromAI: string | undefined,
  formulaData: any
): Promise<{
  catalogStatus: 'unverified' | 'matched' | 'mismatch' | 'partial' | 'unknown_brand';
  catalogIssues: string[];
} | null> {
  try {
    const needsEval = !catalogStatusFromAI || catalogStatusFromAI === 'unverified';
    if (!needsEval) return null;

    const colors = extractColorItems(formulaData);
    if (!colors.length) {
      return { catalogStatus: 'matched', catalogIssues: [] };
    }

    let total = 0;
    let matched = 0;
    const issues: string[] = [];

    for (const c of colors) {
      total += 1;
      const brandName = (c.brand || '').trim();
      const lineName = (c.line || '').trim();
      const shade = normalizeShade(c.shade);

      if (!brandName || !lineName) {
        issues.push('Falta brand/line en ítem de color');
        continue;
      }

      const { data: bRows, error: bErr } = await supabase
        .from('brands')
        .select('id, name')
        .ilike('name', brandName)
        .limit(1);
      if (bErr || !bRows?.length) {
        issues.push(`Marca desconocida: ${brandName}`);
        continue;
      }
      const brandId = bRows[0].id as string;

      const { data: lRows, error: lErr } = await supabase
        .from('product_lines')
        .select('id, name')
        .eq('brand_id', brandId)
        .ilike('name', lineName)
        .limit(1);
      if (lErr || !lRows?.length) {
        issues.push(`Línea desconocida para la marca: ${brandName} / ${lineName}`);
        continue;
      }
      const lineId = lRows[0].id as string;

      if (!shade) {
        matched += 1;
        continue;
      }

      const { data: sRows, error: sErr } = await supabase
        .from('brand_line_shades')
        .select('id')
        .eq('brand_id', brandId)
        .eq('line_id', lineId)
        .ilike('shade_code', shade)
        .limit(1);
      if (!sErr && sRows && sRows.length) {
        matched += 1;
      } else {
        issues.push(`Tono no encontrado en catálogo: ${brandName} / ${lineName} ${shade}`);
      }
    }

    if (issues.some(i => i.startsWith('Marca desconocida') || i.startsWith('Línea desconocida'))) {
      return { catalogStatus: 'unknown_brand', catalogIssues: issues };
    }
    if (matched === 0) {
      return { catalogStatus: 'mismatch', catalogIssues: issues };
    }
    if (matched < total) {
      return { catalogStatus: 'partial', catalogIssues: issues };
    }
    return { catalogStatus: 'matched', catalogIssues: issues };
  } catch (e) {
    logger.warn('Catalog fallback evaluation failed', e as any);
    return null;
  }
}
