import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import * as Haptics from 'expo-haptics';
import { Check } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface MaterialItem {
  id: string;
  name: string;
  type?: string;
  zone?: string;
}

interface MaterialsListProps {
  items: MaterialItem[];
  checkedItems: string[];
  onToggleItem: (id: string) => void;
  groupBy?: 'type' | 'zone' | null;
  title?: string;
  typeColors?: Record<string, string>;
  testID?: string;
}

const defaultTypeColors = {
  color: Colors.light.primary,
  developer: Colors.light.accent,
  additive: Colors.light.warning,
  material: Colors.light.secondary,
};

const defaultTypeLabels = {
  color: 'Tintes',
  developer: 'Oxidantes',
  additive: 'Aditivos',
  material: 'Materiales',
};

export const MaterialsList: React.FC<MaterialsListProps> = ({
  items,
  checkedItems,
  onToggleItem,
  groupBy = null,
  title,
  typeColors = defaultTypeColors,
  testID = 'materials-list',
}) => {
  const handleToggleItem = async (id: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onToggleItem(id);
  };

  const renderItem = (item: MaterialItem) => {
    const isChecked = checkedItems.includes(item.id);
    const itemTypeColor = typeColors[item.type || 'material'] || Colors.light.secondary;

    return (
      <TouchableOpacity
        key={item.id}
        style={styles.checkItem}
        onPress={() => handleToggleItem(item.id)}
        testID={`${testID}-item-${item.id}`}
      >
        <View style={[styles.checkbox, isChecked && styles.checkboxChecked]}>
          {isChecked && (
            <Animated.View
              style={[
                styles.animatedCheckmark,
                {
                  transform: [{ scale: isChecked ? 1 : 0 }],
                },
              ]}
            >
              <Check size={16} color="white" />
            </Animated.View>
          )}
        </View>

        <View style={styles.checkItemInfo}>
          <Text style={styles.checkItemName}>{item.name}</Text>
          {item.zone && <Text style={styles.checkItemZone}>{item.zone}</Text>}
        </View>

        {item.type && <View style={[styles.typeIndicator, { backgroundColor: itemTypeColor }]} />}
      </TouchableOpacity>
    );
  };

  const renderGroupedItems = () => {
    if (!groupBy) {
      return items.map(renderItem);
    }

    const grouped = items.reduce(
      (groups, item) => {
        const key = groupBy === 'type' ? item.type || 'material' : item.zone || 'general';
        if (!groups[key]) groups[key] = [];
        groups[key].push(item);
        return groups;
      },
      {} as Record<string, MaterialItem[]>
    );

    return Object.entries(grouped).map(([groupKey, groupItems]) => {
      if (groupItems.length === 0) return null;

      const groupColor =
        groupBy === 'type' ? typeColors[groupKey] || Colors.light.secondary : Colors.light.accent;

      const groupLabel =
        groupBy === 'type'
          ? defaultTypeLabels[groupKey as keyof typeof defaultTypeLabels] || groupKey
          : groupKey;

      return (
        <View key={groupKey} style={styles.productGroup}>
          <View style={styles.productGroupHeader}>
            <View style={[styles.productGroupIndicator, { backgroundColor: groupColor }]} />
            <Text style={styles.productGroupTitle}>
              {groupLabel} ({groupItems.length})
            </Text>
          </View>
          {groupItems.map(renderItem)}
        </View>
      );
    });
  };

  return (
    <View style={styles.container} testID={testID}>
      {title && (
        <Text style={styles.sectionTitle} testID={`${testID}-title`}>
          {title}
        </Text>
      )}
      {renderGroupedItems()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
    paddingHorizontal: 4,
  },
  productGroup: {
    marginBottom: 20,
  },
  productGroupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  productGroupIndicator: {
    width: 4,
    height: 16,
    borderRadius: 2,
    marginRight: 12,
  },
  productGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 1,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.textTertiary,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  animatedCheckmark: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkItemInfo: {
    flex: 1,
  },
  checkItemName: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500',
  },
  checkItemZone: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  typeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
