import React, { useMemo } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { Filter, X, ChevronDown, Package, AlertCircle, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';

interface FilterChipProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
  icon?: React.ReactNode;
  count?: number;
}

const FilterChip: React.FC<FilterChipProps> = ({ label, isActive, onPress, icon, count }) => (
  <TouchableOpacity style={[styles.chip, isActive && styles.chipActive]} onPress={onPress}>
    {icon && <View style={styles.chipIcon}>{icon}</View>}
    <Text style={[styles.chipText, isActive && styles.chipTextActive]}>{label}</Text>
    {count !== undefined && count > 0 && (
      <View style={[styles.chipBadge, isActive && styles.chipBadgeActive]}>
        <Text style={[styles.chipBadgeText, isActive && styles.chipBadgeTextActive]}>{count}</Text>
      </View>
    )}
  </TouchableOpacity>
);

export const InventoryFilters: React.FC = () => {
  const {
    products,
    activeFilters,
    sortBy,
    groupBy,
    setFilter,
    setSortBy: _setSortBy,
    setGroupBy: _setGroupBy,
    resetFilters,
    getFilteredAndSortedProducts,
  } = useInventoryStore();

  const { colorTerm, developerTerm } = useRegionalUnits();

  // Calculate counts for each filter
  const filterCounts = useMemo(() => {
    const lowStock = products.filter(
      p => p.currentStock <= p.minStock && p.currentStock > 0
    ).length;
    const outOfStock = products.filter(p => p.currentStock === 0).length;
    const inStock = products.filter(p => p.currentStock > p.minStock).length;

    // Get unique brands and categories
    const brands = new Set<string>();
    const categories = new Set<string>();

    products.forEach(p => {
      if (p.brand) brands.add(p.brand);
      if (p.category) categories.add(p.category);
    });

    return {
      lowStock,
      outOfStock,
      inStock,
      brands: Array.from(brands).sort(),
      categories: Array.from(categories).sort(),
    };
  }, [products]);

  // Check if any filters are active
  const hasActiveFilters =
    activeFilters.stockStatus !== 'all' ||
    activeFilters.categories.length > 0 ||
    activeFilters.brands.length > 0;

  const handleStockStatusChange = (status: typeof activeFilters.stockStatus) => {
    setFilter('stockStatus', activeFilters.stockStatus === status ? 'all' : status);
  };

  const handleCategoryToggle = (category: string) => {
    const newCategories = activeFilters.categories.includes(category)
      ? activeFilters.categories.filter(c => c !== category)
      : [...activeFilters.categories, category];
    setFilter('categories', newCategories);
  };

  const handleBrandToggle = (brand: string) => {
    const newBrands = activeFilters.brands.includes(brand)
      ? activeFilters.brands.filter(b => b !== brand)
      : [...activeFilters.brands, brand];
    setFilter('brands', newBrands);
  };

  const categoryLabels: Record<string, string> = {
    tinte: colorTerm.charAt(0).toUpperCase() + colorTerm.slice(1),
    oxidante: developerTerm.charAt(0).toUpperCase() + developerTerm.slice(1),
    decolorante: 'Decolorante',
    tratamiento: 'Tratamiento',
    otro: 'Otro',
  };

  return (
    <View style={styles.container}>
      {/* Filter Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Filter size={18} color={Colors.light.primary} />
          <Text style={styles.headerTitle}>Filtros</Text>
          {hasActiveFilters && (
            <TouchableOpacity onPress={resetFilters} style={styles.clearButton}>
              <Text style={styles.clearButtonText}>Limpiar</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Sort and Group Controls */}
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.controlButton}>
            <Text style={styles.controlLabel}>Ordenar: </Text>
            <Text style={styles.controlValue}>
              {sortBy === 'name' && 'Nombre'}
              {sortBy === 'stock' && 'Stock'}
              {sortBy === 'price' && 'Precio'}
              {sortBy === 'brand' && 'Marca'}
              {sortBy === 'usage' && 'Uso'}
            </Text>
            <ChevronDown size={16} color={Colors.light.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlButton}>
            <Text style={styles.controlLabel}>Agrupar: </Text>
            <Text style={styles.controlValue}>
              {groupBy === 'none' && 'No'}
              {groupBy === 'brand' && 'Marca'}
              {groupBy === 'line' && 'Línea'}
              {groupBy === 'category' && 'Categoría'}
              {groupBy === 'type' && 'Tipo'}
            </Text>
            <ChevronDown size={16} color={Colors.light.gray} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Chips */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
        {/* Stock Status Filters */}
        <FilterChip
          label="Stock bajo"
          isActive={activeFilters.stockStatus === 'low'}
          onPress={() => handleStockStatusChange('low')}
          icon={
            <AlertCircle
              size={16}
              color={activeFilters.stockStatus === 'low' ? 'white' : Colors.light.warning}
            />
          }
          count={filterCounts.lowStock}
        />

        <FilterChip
          label="Sin stock"
          isActive={activeFilters.stockStatus === 'out'}
          onPress={() => handleStockStatusChange('out')}
          icon={
            <X
              size={16}
              color={activeFilters.stockStatus === 'out' ? 'white' : Colors.light.error}
            />
          }
          count={filterCounts.outOfStock}
        />

        <FilterChip
          label="En stock"
          isActive={activeFilters.stockStatus === 'ok'}
          onPress={() => handleStockStatusChange('ok')}
          icon={
            <CheckCircle
              size={16}
              color={activeFilters.stockStatus === 'ok' ? 'white' : Colors.light.success}
            />
          }
          count={filterCounts.inStock}
        />

        {/* Category Filters */}
        {filterCounts.categories.map(category => (
          <FilterChip
            key={category}
            label={categoryLabels[category] || category}
            isActive={activeFilters.categories.includes(category)}
            onPress={() => handleCategoryToggle(category)}
          />
        ))}

        {/* Show "More brands" if there are many */}
        {filterCounts.brands.length > 5 ? (
          <FilterChip
            label={`${activeFilters.brands.length} marcas seleccionadas`}
            isActive={activeFilters.brands.length > 0}
            onPress={() => {
              /* Open brand selector modal */
            }}
            icon={
              <Package
                size={16}
                color={activeFilters.brands.length > 0 ? 'white' : Colors.light.primary}
              />
            }
          />
        ) : (
          /* Show individual brand chips if there are few */
          filterCounts.brands.map(brand => (
            <FilterChip
              key={brand}
              label={brand}
              isActive={activeFilters.brands.includes(brand)}
              onPress={() => handleBrandToggle(brand)}
            />
          ))
        )}
      </ScrollView>

      {/* Results Count */}
      <View style={styles.resultsBar}>
        <Text style={styles.resultsText}>
          {getFilteredAndSortedProducts().length} de {products.length} productos
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingBottom: spacing.sm,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.sm,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  clearButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: Colors.light.primary + '10',
    borderRadius: radius.sm,
  },
  clearButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  headerRight: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  controlLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  controlValue: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  chipsContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    marginRight: spacing.sm,
    gap: spacing.xs,
  },
  chipActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  chipIcon: {
    marginRight: 2,
  },
  chipText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  chipTextActive: {
    color: Colors.light.textLight,
  },
  chipBadge: {
    backgroundColor: Colors.light.gray + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.sm,
    marginLeft: spacing.xs,
  },
  chipBadgeActive: {
    backgroundColor: Colors.light.backgroundOpacity20,
  },
  chipBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold,
    color: Colors.light.gray,
  },
  chipBadgeTextActive: {
    color: Colors.light.textLight,
  },
  resultsBar: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xs,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
});
