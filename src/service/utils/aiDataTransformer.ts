import { logger } from '@/utils/logger';

// Recommendation interface removed - no longer needed without ClientHistoryPanel

// Safe AI data transformation helper
interface RawZoneData {
  level?: number;
  tone?: string;
  condition?: string;
  confidence?: number;
  grayPercentage?: number;
  [key: string]: unknown;
}

export const safeTransformAIZoneData = (zoneData: RawZoneData, zoneName: string) => {
  // ENHANCED DEBUGGING: Log the exact structure of incoming zone data
  logger.debug(`Processing zone data for ${zoneName}`, 'DiagnosisStep', {
    hasZoneData: !!zoneData,
    zoneDataType: typeof zoneData,
    zoneDataKeys: zoneData ? Object.keys(zoneData) : [],
    rawZoneData: zoneData,
    // Log specific fields we're looking for
    tone: zoneData?.tone,
    tono: zoneData?.tono,
    pigmentAccumulation: zoneData?.pigmentAccumulation,
    acumulacionDePigmentos: zoneData?.['acumulación de pigmentos'],
    acumulacionPigmentos: zoneData?.acumulacionPigmentos,
    damage: zoneData?.damage,
    daño: zoneData?.daño,
    level: zoneData?.level,
    nivel: zoneData?.nivel,
    depth: zoneData?.depth,
    profundidad: zoneData?.profundidad,
  });

  if (!zoneData) {
    logger.warn(`Missing zone data for ${zoneName}, using defaults`, 'DiagnosisStep');
    return {
      level: 5,
      tone: undefined,
      reflect: undefined,
      state: undefined,
      grayPercentage: 0,
      grayType: undefined,
      grayPattern: undefined,
      unwantedTone: undefined,
      cuticleState: undefined,
      damage: undefined,
      porosity: undefined,
      elasticity: undefined,
      resistance: undefined,
      pigmentAccumulation: undefined,
    };
  }

  // Validate tone values against comprehensive professional types
  const validNaturalTones = [
    // Negros (Niveles 1-2)
    'Negro',
    'Negro Castaño',

    // Castaños Oscuros (Niveles 3-4)
    'Castaño Oscuro',
    'Castaño Oscuro Ceniza',
    'Castaño Oscuro Dorado',

    // Castaños Medios (Nivel 5)
    'Castaño Medio',
    'Castaño Medio Ceniza',
    'Castaño Medio Dorado',
    'Castaño Medio Cobrizo',

    // Castaños Claros (Nivel 6)
    'Castaño Claro',
    'Castaño Claro Ceniza',
    'Castaño Claro Dorado',
    'Castaño Claro Cobrizo',

    // Rubios Oscuros (Nivel 7)
    'Rubio Oscuro',
    'Rubio Oscuro Ceniza',
    'Rubio Oscuro Dorado',
    'Rubio Oscuro Beige',

    // Rubios Medios (Nivel 8)
    'Rubio Medio',
    'Rubio Medio Ceniza', // ← THE CRITICAL MISSING ONE!
    'Rubio Ceniza', // ← Direct alias for AI results
    'Rubio Medio Dorado',
    'Rubio Medio Beige',

    // Rubios Claros (Nivel 9)
    'Rubio Claro',
    'Rubio Claro Ceniza',
    'Rubio Claro Dorado',
    'Rubio Claro Perla',

    // Rubios Muy Claros (Nivel 10)
    'Rubio Muy Claro',
    'Rubio Muy Claro Ceniza',
    'Rubio Muy Claro Dorado',
    'Rubio Platino',

    // Rojos Naturales
    'Rojo',
    'Caoba',
    'Rojo Cobrizo',
    'Rojo Caoba',

    // Legacy compatibility
    'Castaño',
    'Rubio',
    'Gris',
    'Canoso',
    'Blanco',

    // Legacy case variations for backward compatibility
    'Castaño oscuro',
    'Castaño medio',
    'Castaño claro',
    'Rubio oscuro',
    'Rubio medio',
    'Rubio claro',
    'Rubio muy claro',
    'Rubio platino',
  ];
  const validUndertones = ['Frío', 'Cálido', 'Neutro'];
  const validReflects = [
    // Cold reflects (.1, .2, .7, .8)
    'Cenizo', // .1 - Ash/Blue
    'Violeta', // .2 - Violet
    'Perla', // .8 - Pearl (Blue-Violet)
    'Irisado', // Iridescent special
    'Mate', // .7 - Matte/Green (neutralizes red)

    // Neutral reflects (.0)
    'Natural', // .0 - Natural

    // Warm reflects (.3, .4, .5, .6)
    'Dorado', // .3 - Golden/Yellow
    'Cobrizo', // .4 - Copper/Orange
    'Caoba', // .5 - Mahogany (Red-Violet)
    'Rojizo', // .6 - Red

    // Special professional reflects
    'Beige', // Beige naturals
    'Chocolate', // Chocolate browns
    'Miel', // Honey golds
    'Arena', // Sand beiges
  ];
  const validDamageTypes = ['Bajo', 'Medio', 'Alto'];
  const validPigmentTypes = ['Baja', 'Media', 'Alta'];

  // ENHANCED: Try multiple field name variations for tone with intelligent mapping
  const extractTone = () => {
    const possibleTones = [
      zoneData.tone,
      zoneData.tono,
      zoneData.naturalTone,
      zoneData.dominantTone,
    ].filter(Boolean);

    // First try direct match
    let validTone = possibleTones.find(t => validNaturalTones.includes(t));

    // If no direct match, try intelligent mapping
    if (!validTone && possibleTones.length > 0) {
      const tone = possibleTones[0];
      const lowerTone = tone?.toLowerCase() || '';

      // CRITICAL FIX: Map AI variations to comprehensive valid tone names

      // Direct mappings first
      if (tone === 'Castaño') validTone = 'Castaño Medio';
      else if (tone === 'Rubio') validTone = 'Rubio Medio';
      else if (tone === 'Negro') validTone = 'Negro';
      // CRITICAL: Handle "Rubio Ceniza" - the main issue!
      else if (tone === 'Rubio Ceniza' || tone === 'rubio ceniza') validTone = 'Rubio Medio Ceniza';
      // Gray hair mapping
      else if (
        lowerTone.includes('gris') ||
        lowerTone.includes('gray') ||
        lowerTone.includes('cano')
      )
        validTone = 'Gris';
      else if (lowerTone.includes('blanco') || lowerTone.includes('white')) validTone = 'Blanco';
      // Comprehensive brown hair mapping
      else if (lowerTone.includes('castaño')) {
        if (tone.includes('ceniza') || tone.includes('ash')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Ceniza';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Castaño Oscuro Ceniza';
          else validTone = 'Castaño Medio Ceniza';
        } else if (tone.includes('dorado') || tone.includes('golden')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Dorado';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Castaño Oscuro Dorado';
          else validTone = 'Castaño Medio Dorado';
        } else if (tone.includes('cobrizo') || tone.includes('copper')) {
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro Cobrizo';
          else validTone = 'Castaño Medio Cobrizo';
        } else {
          // Standard brown tones
          if (tone.includes('claro') || tone.includes('light')) validTone = 'Castaño Claro';
          else if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Castaño Oscuro';
          else validTone = 'Castaño Medio';
        }
      }

      // Comprehensive blonde hair mapping
      else if (lowerTone.includes('rubio') || lowerTone.includes('blonde')) {
        if (tone.includes('ceniza') || tone.includes('ash')) {
          if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro Ceniza';
          else if (tone.includes('claro') || tone.includes('light'))
            validTone = 'Rubio Claro Ceniza';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Rubio Oscuro Ceniza';
          else validTone = 'Rubio Medio Ceniza'; // ← DEFAULT FOR "RUBIO CENIZA"
        } else if (tone.includes('dorado') || tone.includes('golden')) {
          if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro Dorado';
          else if (tone.includes('claro') || tone.includes('light'))
            validTone = 'Rubio Claro Dorado';
          else if (tone.includes('oscuro') || tone.includes('dark'))
            validTone = 'Rubio Oscuro Dorado';
          else validTone = 'Rubio Medio Dorado';
        } else if (tone.includes('beige')) {
          if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Rubio Oscuro Beige';
          else validTone = 'Rubio Medio Beige';
        } else if (tone.includes('perla') || tone.includes('pearl')) {
          validTone = 'Rubio Claro Perla';
        } else {
          // Standard blonde tones
          if (tone.includes('platino') || tone.includes('platinum')) validTone = 'Rubio Platino';
          else if (tone.includes('muy claro') || tone.includes('very light'))
            validTone = 'Rubio Muy Claro';
          else if (tone.includes('claro') || tone.includes('light')) validTone = 'Rubio Claro';
          else if (tone.includes('oscuro') || tone.includes('dark')) validTone = 'Rubio Oscuro';
          else validTone = 'Rubio Medio';
        }
      }
    }

    logger.debug(`Extracting tone for ${zoneName}`, 'DiagnosisStep', {
      possibleTones,
      validTone,
      matched: !!validTone,
      appliedMapping: validTone !== possibleTones.find(t => validNaturalTones.includes(t)),
    });
    return validTone;
  };

  // ENHANCED: Try multiple field name variations for pigment accumulation with intelligent mapping
  const extractPigmentAccumulation = () => {
    const possibleValues = [
      zoneData.pigmentAccumulation,
      zoneData['acumulación de pigmentos'],
      zoneData.acumulacionDePigmentos,
      zoneData.acumulacionPigmentos,
      zoneData.pigmentLoad,
      zoneData.colorAccumulation,
    ].filter(Boolean);

    // First try direct match
    let validValue = possibleValues.find(v => validPigmentTypes.includes(v));

    // If no direct match, try intelligent mapping
    if (!validValue && possibleValues.length > 0) {
      const value = possibleValues[0];
      // Map AI variations to valid pigment accumulation levels
      if (value === 'Leve' || value === 'Ligera' || value === 'Bajo') validValue = 'Baja';
      else if (value === 'Moderada' || value === 'Medio' || value === 'Intermedia')
        validValue = 'Media';
      else if (value === 'Fuerte' || value === 'Intensa' || value === 'Alto' || value === 'Severa')
        validValue = 'Alta';
    }

    logger.debug(`Extracting pigment accumulation for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      validValue,
      matched: !!validValue,
      appliedMapping: validValue !== possibleValues.find(v => validPigmentTypes.includes(v)),
    });
    return validValue;
  };

  // ENHANCED: Try multiple field name variations for damage with intelligent mapping
  const extractDamage = () => {
    const possibleValues = [
      zoneData.damage,
      zoneData.daño,
      zoneData.damageLevel,
      zoneData.nivelDaño,
      zoneData.hairDamage,
    ].filter(Boolean);

    // First try direct match
    let validValue = possibleValues.find(v => validDamageTypes.includes(v));

    // If no direct match, try intelligent mapping
    if (!validValue && possibleValues.length > 0) {
      const value = possibleValues[0];
      // Map AI variations to valid damage levels
      if (value === 'Leve' || value === 'Ligero' || value === 'Mínimo') validValue = 'Bajo';
      else if (value === 'Moderado' || value === 'Intermedio' || value === 'Regular')
        validValue = 'Medio';
      else if (value === 'Severo' || value === 'Fuerte' || value === 'Intenso' || value === 'Grave')
        validValue = 'Alto';
    }

    logger.debug(`Extracting damage for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      validValue,
      matched: !!validValue,
      appliedMapping: validValue !== possibleValues.find(v => validDamageTypes.includes(v)),
    });
    return validValue;
  };

  // ENHANCED: Try multiple field name variations for level
  const extractLevel = () => {
    const possibleValues = [
      zoneData.level,
      zoneData.nivel,
      zoneData.depth,
      zoneData.profundidad,
      zoneData.colorLevel,
    ].filter(v => v !== undefined);

    const numericValue = possibleValues.find(v => !isNaN(Number(v)));
    const level = numericValue ? Number(numericValue) : 5;
    logger.debug(`Extracting level for ${zoneName}`, 'DiagnosisStep', {
      possibleValues,
      numericValue,
      finalLevel: level,
    });
    return level;
  };

  const result = {
    level: extractLevel(),
    tone: extractTone(),
    reflect: (() => {
      // Try multiple field name variations for reflect
      const possibleReflects = [
        zoneData.reflect,
        zoneData.undertone,
        zoneData.reflejo,
        zoneData.matiz,
        zoneData.reflections,
      ].filter(Boolean);

      // First try direct match with Spanish reflect values
      let validReflect = possibleReflects.find(r => validReflects.includes(r));

      // If no direct match with reflects, try undertones as fallback
      if (!validReflect) {
        validReflect = possibleReflects.find(r => validUndertones.includes(r));
      }

      // If still no match, try intelligent mapping
      if (!validReflect && possibleReflects.length > 0) {
        const reflect = possibleReflects[0];
        const lowerReflect = reflect?.toLowerCase() || '';

        // Map AI variations to valid reflect names
        if (lowerReflect.includes('natural') || lowerReflect.includes('neutro'))
          validReflect = 'Natural';
        else if (
          lowerReflect.includes('cenizo') ||
          lowerReflect.includes('ash') ||
          lowerReflect.includes('gris')
        )
          validReflect = 'Cenizo';
        else if (
          lowerReflect.includes('dorado') ||
          lowerReflect.includes('golden') ||
          lowerReflect.includes('cálido')
        )
          validReflect = 'Dorado';
        else if (lowerReflect.includes('cobrizo') || lowerReflect.includes('copper'))
          validReflect = 'Cobrizo';
        else if (lowerReflect.includes('rojizo') || lowerReflect.includes('red'))
          validReflect = 'Rojizo';
        else if (lowerReflect.includes('violeta') || lowerReflect.includes('violet'))
          validReflect = 'Violeta';
        else if (lowerReflect.includes('caoba') || lowerReflect.includes('mahogany'))
          validReflect = 'Caoba';
        else if (lowerReflect.includes('beige')) validReflect = 'Beige';
        else if (lowerReflect.includes('irisado') || lowerReflect.includes('iridescent'))
          validReflect = 'Irisado';
        // Fallback to undertone mapping
        else if (lowerReflect.includes('frío') || lowerReflect.includes('cool'))
          validReflect = 'Frío';
        else if (lowerReflect.includes('cálido') || lowerReflect.includes('warm'))
          validReflect = 'Cálido';
      }

      logger.debug(`Extracting reflect for ${zoneName}`, 'DiagnosisStep', {
        possibleReflects,
        validReflect,
        matched: !!validReflect,
        appliedMapping: validReflect !== possibleReflects.find(r => validReflects.includes(r)),
      });

      return validReflect;
    })(),
    state: zoneData.state || zoneData.estado || undefined,
    grayPercentage: Math.max(
      0,
      Math.min(
        100,
        Number(zoneData.grayPercentage || zoneData.canasPercentage || zoneData.porcentajeCanas) || 0
      )
    ),
    grayType: zoneData.grayType || zoneData.tiposCanas || undefined,
    grayPattern: zoneData.grayPattern || zoneData.patronCanas || undefined,
    unwantedTone: zoneData.unwantedTone || zoneData.tonoIndeseado || undefined,
    cuticleState: zoneData.cuticleState || zoneData.estadoCuticula || undefined,
    damage: extractDamage(),
    porosity: zoneData.porosity || zoneData.porosidad || undefined,
    elasticity: zoneData.elasticity || zoneData.elasticidad || undefined,
    resistance: zoneData.resistance || zoneData.resistencia || undefined,
    pigmentAccumulation: extractPigmentAccumulation(),
  };

  // ENHANCED DEBUGGING: Log final result with field-by-field analysis
  logger.debug(`Zone transformation result for ${zoneName}`, 'DiagnosisStep', {
    result,
    fieldsPopulated: {
      level: result.level !== 5, // 5 is default
      tone: !!result.tone,
      reflect: !!result.reflect,
      damage: !!result.damage,
      pigmentAccumulation: !!result.pigmentAccumulation,
      porosity: !!result.porosity,
      elasticity: !!result.elasticity,
      resistance: !!result.resistance,
    },
    criticalFieldsForForm: {
      hasTone: !!result.tone,
      hasPigmentAccumulation: !!result.pigmentAccumulation,
      hasDamage: !!result.damage,
    },
  });

  return result;
};
