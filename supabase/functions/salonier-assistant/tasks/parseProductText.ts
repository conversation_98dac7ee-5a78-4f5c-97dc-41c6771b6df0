// supabase/functions/salonier-assistant/tasks/parseProductText.ts
import { AIResponse } from '../types.ts';
import { logger } from '../src/utils/security.ts';
import { retryWithBackoff, calculateCost } from '../src/utils/helpers.ts';
import { generateCacheKey, saveToCache } from '../src/utils/caching.ts';

export async function parseProductText(payload: any, salonId: string, supabase: any, openaiApiKey: string): Promise<AIResponse> {
  const { text } = payload;

  const prompt = `Analiza este texto sobre un producto de peluquería y extrae la información estructurada:
  
  Texto: "${text}"
  
  Devuelve un JSON con:
  {
    "brand": "marca detectada",
    "name": "nombre del producto",
    "line": "línea si se menciona",
    "type": "color|developer|treatment|shampoo|conditioner|styling|other",
    "size": { "value": número, "unit": "ml|g|oz" },
    "quantity": número de unidades,
    "details": { "cualquier detalle adicional" }
  }`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 300,
          temperature: 0,
          top_p: 1,
          seed: 42,
          response_format: { type: 'json_object' },
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Product text parsing OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = JSON.parse(data.choices[0].message.content);
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-3.5-turbo', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('parse_product_text', payload);
    await saveToCache(
      supabase,
      salonId,
      'parse_product_text',
      inputHash,
      payload,
      result,
      'gpt-3.5-turbo',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Product text parsing failed
    return { success: false, error: error.message };
  }
}