import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { ColorCorrectionService } from '@/services/colorCorrectionService';
import { getBrandPack } from '@/data/brand-packs';
import { COLOR_TECHNIQUES } from '@/types/desired-photo';
import { FormulationDebugger } from '@/service/utils/formulationDebugger';
import { logger } from '@/utils/logger';
import { Formulation, FormulationStep } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis, UnwantedTone } from '@/types/hair-diagnosis';
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';
import {
  enforceToningSafety,
  ensureDeveloperPresent,
  ensurePreLighteningStep,
  ensurePostTreatmentStep,
  applyBrandDefaults,
} from '../utils/formulationHelpers';

interface GenerationContext {
  clientId?: string;
  adjustmentContext?: string;
  clientContext?: {
    hairLengthCm?: number;
    hairDensity?: string;
    hairThickness?: string;
  };
}

interface FormulationError {
  type: 'network' | 'auth' | 'validation' | 'ai' | 'mapping' | 'unknown';
  message: string;
  details?: any;
}

/**
 * Hook for AI-powered formula generation
 */
export const useFormulaGeneration = () => {
  const [isGeneratingFormula, setIsGeneratingFormula] = useState(false);
  const [formulationError, setFormulationError] = useState<FormulationError | null>(null);

  const { getCompatibleFormulas, getRecommendationsForClient } = useClientHistoryStore();

  const generateFormulaWithAI = useCallback(
    async (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
      selectedBrand: string,
      selectedLine: string,
      conversionContext?: { originalBrand: string; originalLine: string; originalFormula: string },
      context?: GenerationContext
    ): Promise<{ formula: string; formulation: Formulation | null }> => {
      if (!analysisResult && !desiredAnalysisResult) {
        Alert.alert(
          'Error',
          'Necesitas completar el diagnóstico y el análisis del color deseado antes de generar la fórmula'
        );
        throw new Error('Missing analysis data');
      }

      setIsGeneratingFormula(true);

      try {
        // Get client history for better formula generation
        let historyContext = '';
        if (context?.clientId) {
          const compatibleFormulas = getCompatibleFormulas(context.clientId);
          const recommendations = getRecommendationsForClient(context.clientId);

          if (compatibleFormulas.length > 0) {
            historyContext += `\nFórmulas exitosas anteriores:\n${compatibleFormulas
              .slice(0, 2)
              .map(f => `- ${f.formula} (Satisfacción: ${f.satisfaction}/5)`)
              .join('\n')}`;
          }

          if (recommendations.length > 0) {
            historyContext += `\nRecomendaciones basadas en historial:\n${recommendations
              .slice(0, 3)
              .map(r => `- ${r}`)
              .join('\n')}`;
          }
        }

        // Analyze color correction needs
        const unwantedTones: Partial<Record<HairZone, UnwantedTone>> = {};
        Object.values(zoneColorAnalysis).forEach(zone => {
          if (zone.unwantedTone && zone.zone) {
            unwantedTones[zone.zone] = zone.unwantedTone;
          }
        });

        const _correctionAnalysis = ColorCorrectionService.analyzeColorCorrection(
          zoneColorAnalysis as Record<HairZone, ZoneColorAnalysis>,
          desiredAnalysisResult!,
          unwantedTones,
          selectedBrand,
          selectedLine
        );

        // Check if we should use the regional formulation service
        const salonConfig = useSalonConfigStore.getState();

        // Clear previous errors
        setFormulationError(null);
        FormulationDebugger.startDebugging({
          hasAnalysis: !!analysisResult,
          hasDesiredResult: !!desiredAnalysisResult,
          selectedBrand,
          selectedLine,
          productMappings: [],
        });

        logger.debug('Generating formula with AI - START', 'useFormulaGeneration', {
          hasAnalysisResult: !!analysisResult,
          hasDesiredAnalysisResult: !!desiredAnalysisResult,
          selectedBrand,
          selectedLine,
          hasRegionalConfig: !!salonConfig.regionalConfig,
          conversionMode: !!conversionContext,
          clientContext: context?.clientContext,
        });

        if (analysisResult || desiredAnalysisResult) {
          logger.debug('Calling Edge Function for formula generation', 'useFormulaGeneration');
          const formulaContext = {
            currentDiagnosis: analysisResult,
            desiredResult: desiredAnalysisResult,
            brand: selectedBrand,
            line: selectedLine,
            regionalConfig: salonConfig.regionalConfig,
            clientHistory: historyContext,
            conversionMode: conversionContext,
          };

          // Call Supabase Edge Function
          logger.debug('Edge Function call parameters', 'useFormulaGeneration', {
            task: 'generate_formula',
            brand: formulaContext.brand,
            line: formulaContext.line,
          });

          // Check authentication before calling Edge Function
          const {
            data: { session },
          } = await supabase.auth.getSession();
          if (!session) {
            logger.warn('No active session - Edge Function call may fail', 'useFormulaGeneration');
          }

          // Invoke Edge Function with simple retry and auth refresh on 401
          // NOTE: Sequential processing required - must try first call, then refresh auth if needed
          let data: any | null = null;
          let error: any | null = null;

          for (let attempt = 1; attempt <= 2; attempt++) {
            // eslint-disable-next-line no-await-in-loop -- Edge Function call required in retry loop
            const resp = await supabase.functions.invoke('salonier-assistant', {
              body: {
                task: 'generate_formula',
                payload: {
                  diagnosis: formulaContext.currentDiagnosis,
                  desiredResult: formulaContext.desiredResult,
                  brand: formulaContext.brand,
                  line: formulaContext.line,
                  clientHistory: formulaContext.clientHistory,
                  regionalConfig: salonConfig.regionalConfig,
                  adjustmentContext: context?.adjustmentContext || undefined,
                },
              },
            });
            data = resp.data;
            error = resp.error as any;
            if (!error) break;

            // If first attempt failed due to auth, refresh and retry once
            const maybeStatus = (error?.context as any)?.status || error?.status || 0;
            if (attempt === 1 && (maybeStatus === 401 || /invalid token/i.test(String(error)))) {
              try {
                logger.warn(
                  'Refreshing session after 401 in useFormulaGeneration',
                  'useFormulaGeneration'
                );
                // eslint-disable-next-line no-await-in-loop -- Auth refresh required in retry loop
                await supabase.auth.refreshSession();
                continue;
              } catch {
                // ignore and exit loop
              }
            }
            break;
          }

          logger.debug('Edge Function response received', 'useFormulaGeneration', { data, error });

          if (error) {
            // Log error details to help debugging
            const errorStatus = (error?.context as any)?.status || error?.status;
            const originalError = error?.message || String(error);

            logger.error('Edge Function error (generate_formula)', 'useFormulaGeneration', {
              status: errorStatus,
              message: originalError,
              hasSession: !!session,
              attempt: 'final',
            });

            // Provide more specific error messages with error tracking
            const errorType =
              errorStatus === 401
                ? 'auth'
                : errorStatus === 429
                  ? 'network'
                  : errorStatus >= 500
                    ? 'network'
                    : 'ai';

            const errorMessage =
              errorStatus === 401
                ? 'Error de autenticación. Reinicia la sesión.'
                : errorStatus === 429
                  ? 'Límite de solicitudes excedido. Intenta en unos minutos.'
                  : errorStatus >= 500
                    ? 'Error del servidor. Intenta más tarde.'
                    : 'Error al generar la fórmula con IA';

            setFormulationError({
              type: errorType,
              message: errorMessage,
              details: { status: errorStatus, originalError },
            });

            throw new Error(errorMessage);
          }

          // Try multiple response structures
          let generatedFormula = null;
          let structuredFormula = null;

          if (data && data.success && data.data) {
            generatedFormula = data.data.formulaText;
            structuredFormula = data.data.formulationData;
            try {
              // Attach hashes and catalog evaluation for later persistence/UX
              if (structuredFormula) {
                if (data.data.scenarioHash)
                  (structuredFormula as any).scenarioHash = data.data.scenarioHash;
                if (data.data.contentHash)
                  (structuredFormula as any).contentHash = data.data.contentHash;
                if (data.data.catalogEvaluation) {
                  (structuredFormula as any).catalogStatus = data.data.catalogEvaluation.status;
                  (structuredFormula as any).catalogIssues =
                    data.data.catalogEvaluation.issues || [];
                }
              }
            } catch {}
          } else if (data && data.formulaText) {
            generatedFormula = data.formulaText;
            structuredFormula = data.formulationData;
            try {
              if (structuredFormula && (data as any).scenarioHash)
                (structuredFormula as any).scenarioHash = (data as any).scenarioHash;
              if (structuredFormula && (data as any).contentHash)
                (structuredFormula as any).contentHash = (data as any).contentHash;
              if (structuredFormula && (data as any).catalogEvaluation) {
                (structuredFormula as any).catalogStatus = (data as any).catalogEvaluation.status;
                (structuredFormula as any).catalogIssues =
                  (data as any).catalogEvaluation.issues || [];
              }
            } catch {}
          } else if (data && data.result) {
            generatedFormula = data.result.formulaText;
            structuredFormula = data.result.formulationData;
            try {
              if (structuredFormula && data.result.scenarioHash)
                (structuredFormula as any).scenarioHash = data.result.scenarioHash;
              if (structuredFormula && data.result.contentHash)
                (structuredFormula as any).contentHash = data.result.contentHash;
              if (structuredFormula && data.result.catalogEvaluation) {
                (structuredFormula as any).catalogStatus = data.result.catalogEvaluation.status;
                (structuredFormula as any).catalogIssues =
                  data.result.catalogEvaluation.issues || [];
              }
            } catch {}
          } else if (data && typeof data === 'string') {
            generatedFormula = data;
          }

          if (!generatedFormula) {
            setFormulationError({
              type: 'ai',
              message: 'Respuesta inválida del servidor',
              details: { responseData: data },
            });
            throw new Error('Respuesta inválida del servidor');
          }

          logger.debug('Formula generated successfully', 'useFormulaGeneration', {
            formulaLength: generatedFormula.length,
            hasStructuredData: !!structuredFormula,
            structuredSteps: structuredFormula?.steps?.length || 0,
          });

          // Apply brand pack defaults and enhancements
          let processedFormulation = structuredFormula as Formulation | null;

          try {
            if (processedFormulation?.steps && processedFormulation.steps.length > 0) {
              // Apply brand defaults
              processedFormulation = applyBrandDefaults(
                processedFormulation,
                selectedBrand,
                selectedLine
              );

              // Enforce toning safety
              processedFormulation =
                enforceToningSafety(processedFormulation, selectedBrand, selectedLine) ||
                processedFormulation;

              // Ensure developer is present
              processedFormulation = ensureDeveloperPresent(
                processedFormulation,
                selectedBrand,
                selectedLine
              );

              // Calculate levels for pre-lightening check
              const currentLevel =
                analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
              const targetLevel =
                parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;

              // Ensure pre-lightening step when needed
              processedFormulation = ensurePreLighteningStep(
                processedFormulation,
                selectedBrand,
                selectedLine,
                zoneColorAnalysis,
                currentLevel,
                targetLevel
              );

              // Ensure post-treatment step
              processedFormulation = ensurePostTreatmentStep(processedFormulation, selectedBrand);

              // Normalize quantities by hair length/density before setting state
              try {
                const { normalizeFormulaSteps } = await import('@/services/formula-normalizer');
                const normalized = normalizeFormulaSteps(
                  (processedFormulation.steps || []) as FormulationStep[],
                  selectedBrand,
                  selectedLine,
                  {
                    hairLengthCm: context?.clientContext?.hairLengthCm,
                    hairDensity: context?.clientContext?.hairDensity,
                    hairThickness: context?.clientContext?.hairThickness,
                  }
                );
                if (normalized)
                  processedFormulation = { ...processedFormulation, steps: normalized };
              } catch {
                // no-op normalization
              }

              // Log product mapping for debugging
              if (processedFormulation?.steps && processedFormulation.steps.length > 0) {
                const products = processedFormulation.steps.flatMap(step => step.mix || []);
                FormulationDebugger.logProductMappingIssues(products);
              }
            } else {
              const safe = enforceToningSafety(structuredFormula, selectedBrand, selectedLine);
              processedFormulation = (safe || structuredFormula) as Formulation | null;

              // Normalize quantities by hair length/density
              try {
                const { normalizeFormulaSteps } = await import('@/services/formula-normalizer');
                const normalized = normalizeFormulaSteps(
                  (processedFormulation?.steps || []) as FormulationStep[],
                  selectedBrand,
                  selectedLine,
                  {
                    hairLengthCm: context?.clientContext?.hairLengthCm,
                    hairDensity: context?.clientContext?.hairDensity,
                    hairThickness: context?.clientContext?.hairThickness,
                  }
                );
                if (processedFormulation && normalized)
                  processedFormulation = { ...processedFormulation, steps: normalized };
              } catch {
                // no-op
              }
            }
          } catch {
            processedFormulation = structuredFormula as Formulation | null;
          }

          // Log warnings if received from Edge Function
          if (structuredFormula?.warnings && structuredFormula.warnings.length > 0) {
            logger.warn('Warnings received from Edge Function', 'useFormulaGeneration', {
              warningCount: structuredFormula.warnings.length,
              warnings: structuredFormula.warnings,
              formulaTitle: structuredFormula.formulaTitle,
            });
          } else {
            logger.debug('No warnings from Edge Function', 'useFormulaGeneration', {
              hasFormulationData: !!structuredFormula,
              formulaTitle: structuredFormula?.formulaTitle,
            });
          }

          return {
            formula: generatedFormula,
            formulation: processedFormulation,
          };
        } else {
          // No analysis data available - generate basic fallback
          logger.warn('No analysis data available for formula generation', 'useFormulaGeneration');
          setFormulationError({
            type: 'validation',
            message: 'Datos de análisis insuficientes',
            details: {
              hasAnalysisResult: !!analysisResult,
              hasDesiredAnalysisResult: !!desiredAnalysisResult,
            },
          });
          throw new Error('Datos de análisis insuficientes');
        }
      } catch (error) {
        // Formula generation failed
        logger.error('Error generating formula with AI - FINAL CATCH', 'useFormulaGeneration', {
          error,
          errorType: typeof error,
          errorMessage: error instanceof Error ? error.message : String(error),
          hasFormulationError: !!formulationError,
        });

        // Set error if not already set
        if (!formulationError) {
          setFormulationError({
            type: 'unknown',
            message: error instanceof Error ? error.message : 'Error desconocido',
            details: { originalError: error },
          });
        }

        throw error;
      } finally {
        setIsGeneratingFormula(false);
        logger.debug('Formula generation completed', 'useFormulaGeneration', {
          hasError: !!formulationError,
          isGenerating: false,
        });
      }
    },
    [getCompatibleFormulas, getRecommendationsForClient, formulationError]
  );

  const generateFallbackFormula = useCallback(
    async (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      selectedBrand: string,
      selectedLine: string
    ): Promise<string> => {
      // Generate fallback formula
      const currentLevel = analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
      const targetLevel =
        parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
      const levelDifference = Math.abs(targetLevel - currentLevel);
      const selectedTechnique = desiredAnalysisResult?.general?.technique || 'full_color';

      // Fallback con conocimiento de marca/línea
      const pack = getBrandPack(selectedBrand, selectedLine);
      const dev = levelDifference > 2 ? 30 : pack.defaultDeveloper || 20;
      const ratio = pack.defaultMixRatio || '1:1';
      const time =
        pack.techniqueTimes?.fullColor ||
        pack.techniqueTimes?.toner ||
        pack.techniqueTimes?.highlift ||
        35;

      const fallbackFormula = `Fórmula Base (${selectedBrand} • ${selectedLine})
 - Mezcla: ${ratio}
 - Oxidante: ${dev} vol
 - Objetivo: nivel ${targetLevel}

 Aplicación estándar:
 1. Dividir el cabello en secciones
 2. Técnica: ${COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name || 'Seleccionada'}
 3. Procesar ${time} minutos
 4. Enjuagar y acondicionar`;

      logger.debug('Fallback formula generated', 'useFormulaGeneration', {
        fallbackFormulaLength: fallbackFormula.length,
        isFromAI: false,
      });

      return fallbackFormula;
    },
    []
  );

  return {
    isGeneratingFormula,
    formulationError,
    setFormulationError,
    generateFormulaWithAI,
    generateFallbackFormula,
  };
};
