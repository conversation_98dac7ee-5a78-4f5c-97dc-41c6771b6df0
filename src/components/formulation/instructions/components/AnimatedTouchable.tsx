import React, { useRef } from 'react';
import { TouchableOpacity, Animated } from 'react-native';

interface AnimatedTouchableProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: Record<string, unknown>;
  disabled?: boolean;
  activeOpacity?: number;
}

export const AnimatedTouchable = ({
  children,
  onPress,
  style,
  ...props
}: AnimatedTouchableProps) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      {...props}
    >
      <Animated.View style={[style, { transform: [{ scale: scaleAnim }] }]}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};
