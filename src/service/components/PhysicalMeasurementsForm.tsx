import React, { memo } from 'react';
import { Text, StyleSheet } from 'react-native';
import { BeautyCard } from '@/components/beauty';
import DiagnosisTextInput from '@/components/DiagnosisTextInput';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useDiagnosisContext } from '@/service/contexts/DiagnosisContext';

const PhysicalMeasurementsFormComponent: React.FC = () => {
  const { data, onUpdate, isDataFromAI } = useDiagnosisContext();
  return (
    <BeautyCard variant="default" style={styles.section}>
      <Text style={styles.sectionTitle}>Mediciones Físicas</Text>

      <DiagnosisTextInput
        label="Longitud total del cabello (cm)"
        value={data.hairLength?.toString() || ''}
        onChangeText={value => onUpdate({ hairLength: parseFloat(value) || 0 })}
        placeholder="0.0 cm"
        keyboardType="numeric"
        isFromAI={isDataFromAI}
      />

      <DiagnosisTextInput
        label="Crecimiento mensual (cm)"
        value={data.monthlyGrowth?.toString() || ''}
        onChangeText={value => onUpdate({ monthlyGrowth: parseFloat(value) || 0 })}
        placeholder="1.25"
        keyboardType="numeric"
        isFromAI={isDataFromAI}
      />
    </BeautyCard>
  );
};

// Export memoized component
export const PhysicalMeasurementsForm = memo(PhysicalMeasurementsFormComponent);

// Display name for debugging
PhysicalMeasurementsForm.displayName = 'PhysicalMeasurementsForm';

const styles = StyleSheet.create({
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
});
