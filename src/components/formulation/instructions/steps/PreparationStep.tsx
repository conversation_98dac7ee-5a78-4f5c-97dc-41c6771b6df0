import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Animated } from 'react-native';
import { Package, Check } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { VisualFormulationData } from '@/types/visual-formulation';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';
import { commonStyles } from '@/styles/commonStyles';
import Colors from '@/constants/colors';
import { ColorConstants } from '@/styles/colors';
import { instructionStyles } from '../styles';

interface PreparationStepProps {
  formulaData: VisualFormulationData;
  checkedItems: string[];
  setCheckedItems: (items: string[]) => void;
}

interface Ingredient {
  id: string;
  name: string;
  amount: number | string;
  unit: string;
  type?: string;
  zone?: string;
}

interface Zone {
  zone: string;
  ingredients: Ingredient[];
}

export function PreparationStep({
  formulaData,
  checkedItems,
  setCheckedItems,
}: PreparationStepProps) {
  // Crear datos mock completos si no vienen todos los productos
  const mockProducts = [
    {
      id: 'color-1',
      name: 'Illumina Color 8/69 (60g)',
      type: 'color',
      zone: 'General',
    },
    {
      id: 'color-2',
      name: 'Illumina Color 8/36 (30g)',
      type: 'color',
      zone: 'General',
    },
    {
      id: 'developer-1',
      name: 'Welloxon Perfect 20vol (135ml)',
      type: 'developer',
      zone: 'General',
    },
    {
      id: 'additive-1',
      name: 'Color Fresh 0/68 (5g)',
      type: 'additive',
      zone: 'General',
    },
  ];

  // Extraer TODOS los productos de la fórmula, incluyendo developers y additives
  let products = [];

  if (formulaData.zones && formulaData.zones.length > 0) {
    // Extraer productos de las zonas
    products = formulaData.zones.flatMap((zone: Zone) =>
      zone.ingredients.map((ing: Ingredient, index: number) => ({
        id: `${zone.zone}-${ing.name}-${index}`,
        name: `${ing.name} (${ing.amount}${ing.unit})`,
        type: ing.type || 'color',
        zone:
          zone.zone === HairZone.ROOTS
            ? HairZoneDisplay[HairZone.ROOTS]
            : zone.zone === HairZone.MIDS
              ? HairZoneDisplay[HairZone.MIDS]
              : zone.zone === HairZone.ENDS
                ? HairZoneDisplay[HairZone.ENDS]
                : 'General',
      }))
    );

    // Añadir developer si existe
    if (formulaData.mixingProportions?.developer) {
      const dev = formulaData.mixingProportions.developer;
      products.push({
        id: 'developer-main',
        name: `${dev.name} ${dev.volume}vol (${dev.amount || '135'}ml)`,
        type: 'developer',
        zone: 'General',
      });
    }

    // Añadir additives si existen
    if (formulaData.additives && formulaData.additives.length > 0) {
      formulaData.additives.forEach((add: Ingredient, index: number) => {
        products.push({
          id: `additive-${index}`,
          name: `${add.name} (${add.amount}${add.unit || 'g'})`,
          type: 'additive',
          zone: 'General',
        });
      });
    }
  }

  // Si no hay productos, usar mock
  if (products.length === 0) {
    products = mockProducts;
  }

  const materials = [
    { id: 'bowl', name: 'Bowl de mezcla', type: 'material' },
    { id: 'brush', name: 'Pincel aplicador', type: 'material' },
    { id: 'gloves', name: 'Guantes protectores', type: 'material' },
    { id: 'cape', name: 'Capa de cliente', type: 'material' },
    { id: 'towel', name: 'Toallas', type: 'material' },
  ];

  const toggleItem = async (id: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCheckedItems(
      checkedItems.includes(id) ? checkedItems.filter(i => i !== id) : [...checkedItems, id]
    );
  };

  const allItems = [...products, ...materials];
  const progress = (checkedItems.length / allItems.length) * 100;

  const typeLabels = {
    color: 'Tintes',
    developer: 'Oxidantes',
    additive: 'Aditivos',
  };

  const typeColors = {
    color: Colors.light.primary,
    developer: Colors.light.accent,
    additive: Colors.light.warning,
  };

  return (
    <ScrollView style={instructionStyles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={[instructionStyles.checklistHeader, { backgroundColor: Colors.light.primary }]}>
        <Package size={48} color={ColorConstants.WHITE} />
        <Text style={instructionStyles.checklistHeaderTitle}>Verifica que tienes todo listo</Text>
        <View style={instructionStyles.progressBar}>
          <Animated.View
            style={[
              instructionStyles.progressFill,
              { width: `${progress}%`, backgroundColor: Colors.light.background },
            ]}
          />
        </View>
        <Text style={instructionStyles.progressText}>{Math.round(progress)}% Completo</Text>
      </View>

      <View style={instructionStyles.checklistContent}>
        <Text style={instructionStyles.sectionTitle}>Productos de la Fórmula</Text>

        {/* Agrupar productos por tipo */}
        {['color', 'developer', 'additive'].map(type => {
          const productsOfType = products.filter((p: Ingredient) => p.type === type);
          if (productsOfType.length === 0) return null;

          return (
            <View key={type} style={instructionStyles.productGroup}>
              <View style={instructionStyles.productGroupHeader}>
                <View
                  style={[
                    instructionStyles.productGroupIndicator,
                    {
                      backgroundColor: typeColors[type as keyof typeof typeColors],
                    },
                  ]}
                />
                <Text style={instructionStyles.productGroupTitle}>
                  {typeLabels[type as keyof typeof typeLabels]} ({productsOfType.length})
                </Text>
              </View>
              {productsOfType.map((item: Ingredient) => (
                <TouchableOpacity
                  key={item.id}
                  style={instructionStyles.checkItem}
                  onPress={() => toggleItem(item.id)}
                >
                  <View
                    style={[
                      instructionStyles.checkbox,
                      checkedItems.includes(item.id) && instructionStyles.checkboxChecked,
                    ]}
                  >
                    {checkedItems.includes(item.id) && (
                      <Animated.View
                        style={[
                          instructionStyles.animatedCheckmark,
                          {
                            transform: [
                              {
                                scale: checkedItems.includes(item.id) ? 1 : 0,
                              },
                            ],
                          },
                        ]}
                      >
                        <Check size={16} color={ColorConstants.WHITE} />
                      </Animated.View>
                    )}
                  </View>
                  <View style={instructionStyles.checkItemInfo}>
                    <Text style={instructionStyles.checkItemName}>{item.name}</Text>
                    <Text style={instructionStyles.checkItemZone}>{item.zone}</Text>
                  </View>
                  <View
                    style={[
                      instructionStyles.typeIndicator,
                      {
                        backgroundColor: typeColors[type as keyof typeof typeColors],
                      },
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>
          );
        })}

        <Text style={[instructionStyles.sectionTitle, commonStyles.marginTop20]}>
          Materiales de Trabajo
        </Text>
        {materials.map(item => (
          <TouchableOpacity
            key={item.id}
            style={instructionStyles.checkItem}
            onPress={() => toggleItem(item.id)}
          >
            <View
              style={[
                instructionStyles.checkbox,
                checkedItems.includes(item.id) && instructionStyles.checkboxChecked,
              ]}
            >
              {checkedItems.includes(item.id) && (
                <Animated.View
                  style={[
                    instructionStyles.animatedCheckmark,
                    {
                      transform: [
                        {
                          scale: checkedItems.includes(item.id) ? 1 : 0,
                        },
                      ],
                    },
                  ]}
                >
                  <Check size={16} color={ColorConstants.WHITE} />
                </Animated.View>
              )}
            </View>
            <Text style={instructionStyles.checkItemName}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}
