import { planSessions } from '@/services/session-planner';

describe('Session Planner', () => {
  it('recommends 1 session for small changes', () => {
    const res = planSessions({ currentLevel: 6, targetLevel: 7 });
    expect(res.recommended).toBeGreaterThanOrEqual(1);
    expect(res.min).toBe(1);
  });

  it('recommends ≥2 when roots are dyed and lifting', () => {
    const res = planSessions({ currentLevel: 5, targetLevel: 7, dyedRoots: true });
    expect(res.recommended).toBeGreaterThanOrEqual(2);
  });

  it('recommends ≥3 for high damage scenarios', () => {
    const res = planSessions({ currentLevel: 4, targetLevel: 8, damage: 'Alto' });
    expect(res.recommended).toBeGreaterThanOrEqual(3);
  });
});
