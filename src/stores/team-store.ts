import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { TeamMember, TeamStore } from '@/types/team';
import { Permission } from '@/types/permissions';
import { supabase } from '@/lib/supabase';
// Remove circular dependency - get auth-store at runtime when needed
import { logger } from '@/utils/logger';

async function hashPassword(password: string): Promise<string> {
  const hash = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, password);
  return hash;
}

export const useTeamStore = create<TeamStore>()(
  persist(
    (set, get) => ({
      members: [],

      addMember: async memberData => {
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (!user?.salonId) throw new Error('No salon ID found');

        // Optimistic update
        const newMember = {
          ...memberData,
          id: Date.now().toString(),
          joinedDate: new Date().toISOString(),
          salonId: user.salonId,
        };

        set(state => ({
          members: [...state.members, newMember],
        }));

        try {
          // Create auth user
          const { data: authData, error: authError } = await supabase.auth.admin.createUser({
            email: memberData.email,
            password: memberData.passwordHash, // This should be the actual password, not hash
            email_confirm: true,
            user_metadata: {
              full_name: memberData.name,
            },
          });

          if (authError) throw authError;

          // Create profile for team member
          const { data: _profile, error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: authData.user.id,
              salon_id: user.salonId,
              email: memberData.email,
              full_name: memberData.name,
              role: memberData.role,
              permissions: memberData.permissions,
              is_active: memberData.status === 'active',
            })
            .select()
            .single();

          if (profileError) throw profileError;

          // Update local state with real ID
          set(state => ({
            members: state.members.map(m =>
              m.id === newMember.id ? { ...m, id: authData.user.id } : m
            ),
          }));
        } catch (error) {
          // Revert on error
          set(state => ({
            members: state.members.filter(m => m.id !== newMember.id),
          }));

          logger.error('Error adding team member', 'TeamStore', error);
          throw error;
        }
      },

      updateMember: async (id, updates) => {
        // Optimistic update
        set(state => ({
          members: state.members.map(member =>
            member.id === id ? { ...member, ...updates } : member
          ),
        }));

        try {
          const { error } = await supabase
            .from('profiles')
            .update({
              full_name: updates.name,
              role: updates.role,
              permissions: updates.permissions,
              is_active: updates.status === 'active',
            })
            .eq('id', id);

          if (error) throw error;
        } catch (error) {
          // Revert on error
          await get().syncWithSupabase();
          logger.error('Error updating team member', 'TeamStore', error);
          throw error;
        }
      },

      removeMember: async id => {
        // Store previous state for rollback
        const previousMembers = get().members;

        // Optimistic update
        set(state => ({
          members: state.members.filter(member => member.id !== id),
        }));

        try {
          // Soft delete by deactivating
          const { error } = await supabase
            .from('profiles')
            .update({ is_active: false })
            .eq('id', id);

          if (error) throw error;
        } catch (error) {
          // Revert on error
          set({ members: previousMembers });
          logger.error('Error removing team member', 'TeamStore', error);
          throw error;
        }
      },

      toggleMemberStatus: async id => {
        // Optimistic update
        set(state => ({
          members: state.members.map(member =>
            member.id === id
              ? {
                  ...member,
                  status: member.status === 'active' ? 'inactive' : 'active',
                }
              : member
          ),
        }));

        try {
          const member = get().members.find(m => m.id === id);
          if (!member) throw new Error('Member not found');

          const { error } = await supabase
            .from('profiles')
            .update({ is_active: member.status === 'active' })
            .eq('id', id);

          if (error) throw error;
        } catch (error) {
          // Revert on error
          await get().syncWithSupabase();
          logger.error('Error toggling member status', 'TeamStore', error);
          throw error;
        }
      },

      getMemberByEmail: (email: string) => {
        const state = get();
        return state.members.find(member => member.email.toLowerCase() === email.toLowerCase());
      },

      getMembersBySalon: (salonId: string) => {
        const state = get();
        return state.members.filter(member => member.salonId === salonId);
      },

      verifyPassword: async (email: string, password: string) => {
        // In Supabase, password verification is handled by auth
        // This is kept for backward compatibility
        try {
          const { data: _data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) return false;

          // Sign out immediately as we're just verifying
          await supabase.auth.signOut();

          return true;
        } catch {
          return false;
        }
      },

      hashPassword: hashPassword,

      updatePassword: async (memberId: string, newPassword: string) => {
        try {
          // In Supabase, we need to use admin API to update password
          const { error } = await supabase.auth.admin.updateUserById(memberId, {
            password: newPassword,
          });

          if (error) throw error;

          // Update local hash for reference
          const newPasswordHash = await hashPassword(newPassword);
          set(state => ({
            members: state.members.map(member =>
              member.id === memberId ? { ...member, passwordHash: newPasswordHash } : member
            ),
          }));
        } catch (error) {
          logger.error('Error updating password', 'TeamStore', error);
          throw error;
        }
      },

      syncWithSupabase: async () => {
        // Import at runtime to avoid circular dependency
        const { useAuthStore } = await import('./auth-store');
        const user = useAuthStore.getState().user;
        if (!user?.salonId) return;

        try {
          const { data: profiles, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('salon_id', user.salonId)
            .neq('id', user.id); // Exclude current user

          if (error) throw error;

          const members: TeamMember[] = profiles.map(profile => ({
            id: profile.id,
            name: profile.full_name || profile.email,
            email: profile.email,
            role: profile.role as TeamMember['role'],
            permissions: (profile.permissions || []) as Permission[],
            status: profile.is_active ? 'active' : 'inactive',
            salonId: profile.salon_id || '',
            joinedDate: profile.created_at || new Date().toISOString(),
            passwordHash: '', // Not stored locally for security
          }));

          set({ members });
        } catch (error) {
          logger.error('Error syncing team members', 'TeamStore', error);
          throw error;
        }
      },
    }),
    {
      name: 'team-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
