import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import {
  ChevronLeft,
  Edit,
  Package,
  AlertCircle,
  TrendingUp,
  Clock,
  Plus,
  Minus,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { Product, StockMovement } from '@/types/inventory';
import StockMovementsModal from '@/components/inventory/StockMovementsModal';

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [consumption, setConsumption] = useState<{
    dailyAverage: number;
    weeklyAverage: number;
    monthlyTotal: number;
  } | null>(null);
  const [showMovementsModal, setShowMovementsModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const { getProduct, getStockMovements, getConsumptionAnalysis, getActiveAlerts } =
    useInventoryStore();
  const { formatCurrency } = useSalonConfigStore();

  const loadProductData = useCallback(() => {
    setIsLoading(true);
    if (id) {
      const productData = getProduct(id);
      if (productData) {
        setProduct(productData);
        setMovements(getStockMovements(id, 10));
        setConsumption(getConsumptionAnalysis(id, 'monthly'));
      } else {
        Alert.alert('Error', 'Producto no encontrado', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      }
    }
    setIsLoading(false);
  }, [id, getProduct, getStockMovements, getConsumptionAnalysis]);

  useEffect(() => {
    loadProductData();
  }, [loadProductData]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
      </View>
    );
  }

  if (!product) {
    return null;
  }

  const alerts = getActiveAlerts().filter(a => a.productId === product.id);
  const stockPercentage = product.maxStock ? (product.currentStock / product.maxStock) * 100 : 100;
  const isLowStock = product.currentStock <= product.minStock;

  const getStockStatusColor = () => {
    if (product.currentStock === 0) return Colors.light.error;
    if (isLowStock) return Colors.light.warning;
    return Colors.light.success;
  };

  const formatMovementType = (type: StockMovement['type']) => {
    const types: Record<string, string> = {
      entrada: 'Entrada',
      salida: 'Salida',
      ajuste: 'Ajuste',
      consumo: 'Consumo',
      use: 'Uso',
      purchase: 'Compra',
      adjustment: 'Ajuste',
      return: 'Devolución',
      waste: 'Desperdicio',
    };
    return types[type] || type;
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalle del Producto</Text>
        <TouchableOpacity onPress={() => router.push(`/inventory/edit/${product.id}`)}>
          <Edit size={24} color={Colors.light.primary} />
        </TouchableOpacity>
      </View>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <View style={styles.alertsSection}>
          {alerts.map(alert => (
            <View key={alert.id} style={styles.alertCard}>
              <AlertCircle size={20} color={Colors.light.warning} />
              <Text style={styles.alertText}>{alert.message}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Product Info Card */}
      <View style={styles.card}>
        <View style={styles.productHeader}>
          <View>
            <Text style={styles.productName}>{product.name}</Text>
            <Text style={styles.productBrand}>{product.brand}</Text>
            <Text style={styles.productCategory}>
              {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
            </Text>
          </View>
          <Package size={48} color={Colors.light.lightGray} />
        </View>

        <View style={styles.divider} />

        {/* Stock Status */}
        <View style={styles.stockSection}>
          <Text style={styles.sectionTitle}>Estado del Stock</Text>
          <View style={styles.stockInfo}>
            <View style={styles.stockRow}>
              <Text style={styles.stockLabel}>Stock Actual:</Text>
              <Text style={[styles.stockValue, { color: getStockStatusColor() }]}>
                {product.currentStock} {product.unitType}
              </Text>
            </View>
            <View style={styles.stockRow}>
              <Text style={styles.stockLabel}>Stock Mínimo:</Text>
              <Text style={styles.stockValue}>
                {product.minStock} {product.unitType}
              </Text>
            </View>
            {product.maxStock && (
              <View style={styles.stockRow}>
                <Text style={styles.stockLabel}>Stock Máximo:</Text>
                <Text style={styles.stockValue}>
                  {product.maxStock} {product.unitType}
                </Text>
              </View>
            )}
          </View>

          {/* Stock Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${Math.min(stockPercentage, 100)}%`,
                    backgroundColor: getStockStatusColor(),
                  },
                ]}
              />
            </View>
          </View>
        </View>

        <View style={styles.divider} />

        {/* Pricing Info */}
        <View style={styles.pricingSection}>
          <Text style={styles.sectionTitle}>Información de Costos</Text>
          <View style={styles.pricingRow}>
            <Text style={styles.pricingLabel}>Precio de Compra:</Text>
            <Text style={styles.pricingValue}>{formatCurrency(product.purchasePrice)}</Text>
          </View>

          {/* Enhanced Cost Calculation */}
          {(() => {
            const price = product.purchasePrice || 0;
            let total = 0;

            // Enhanced logic: Parse packages from product name
            const packageMatch = product.name?.match(/(\d+)x(\d+)(ml|g)?/i);
            if (packageMatch) {
              const packages = parseInt(packageMatch[1]);
              const sizePerPackage = parseInt(packageMatch[2]);
              total = packages * sizePerPackage;
            }
            // Fallback: Use unitSize if available
            else if (product.unitSize && product.unitSize > 0) {
              total = product.unitSize;
            }
            // Fallback: Use stock if nothing else works
            else if (product.currentStock && product.currentStock > 0) {
              total = product.currentStock;
            } else {
            }

            const costPerUnit = price && total ? price / total : 0;

            if (costPerUnit > 0) {
              return (
                <View style={styles.pricingRow}>
                  <Text style={styles.pricingLabel}>Costo por {product.unitType}:</Text>
                  <Text style={styles.pricingValue}>{formatCurrency(costPerUnit)}</Text>
                </View>
              );
            }
            return null;
          })()}

          <View style={styles.pricingRow}>
            <Text style={styles.pricingLabel}>Valor en Stock:</Text>
            <Text style={styles.pricingValue}>
              {(() => {
                const price = product.purchasePrice || 0;
                let total = 0;

                const packageMatch = product.name?.match(/(\d+)x(\d+)(ml|g)?/i);
                if (packageMatch) {
                  const packages = parseInt(packageMatch[1]);
                  const sizePerPackage = parseInt(packageMatch[2]);
                  total = packages * sizePerPackage;
                } else if (product.unitSize && product.unitSize > 0) {
                  total = product.unitSize;
                } else if (product.currentStock && product.currentStock > 0) {
                  total = product.currentStock;
                }

                const costPerUnit = price && total ? price / total : product.costPerUnit;
                return formatCurrency(product.currentStock * costPerUnit);
              })()}
            </Text>
          </View>
        </View>
      </View>

      {/* Stock Actions */}
      <View style={styles.actionsCard}>
        <Text style={styles.sectionTitle}>Gestionar Stock</Text>
        <View style={styles.actionsRow}>
          <TouchableOpacity
            style={[styles.actionButton, styles.addButton]}
            onPress={() => setShowMovementsModal(true)}
          >
            <Plus size={20} color={Colors.light.textLight} />
            <Text style={styles.actionButtonText}>Entrada</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => setShowMovementsModal(true)}
          >
            <Minus size={20} color={Colors.light.textLight} />
            <Text style={styles.actionButtonText}>Salida</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Consumption Analysis */}
      {consumption && (
        <View style={styles.card}>
          <View style={styles.analysisHeader}>
            <TrendingUp size={20} color={Colors.light.primary} />
            <Text style={styles.sectionTitle}>Análisis de Consumo (30 días)</Text>
          </View>
          <View style={styles.analysisContent}>
            <View style={styles.analysisRow}>
              <Text style={styles.analysisLabel}>Total Consumido:</Text>
              <Text style={styles.analysisValue}>
                {consumption.totalConsumed} {product.unitType}
              </Text>
            </View>
            <View style={styles.analysisRow}>
              <Text style={styles.analysisLabel}>Costo Total:</Text>
              <Text style={styles.analysisValue}>{formatCurrency(consumption.totalCost)}</Text>
            </View>
            <View style={styles.analysisRow}>
              <Text style={styles.analysisLabel}>Promedio por Servicio:</Text>
              <Text style={styles.analysisValue}>
                {(consumption.avgConsumption || 0).toFixed(1)} {product.unitType}
              </Text>
            </View>
            <View style={styles.analysisRow}>
              <Text style={styles.analysisLabel}>Servicios Realizados:</Text>
              <Text style={styles.analysisValue}>{consumption.servicesCount}</Text>
            </View>
          </View>
        </View>
      )}

      {/* Recent Movements */}
      <View style={styles.card}>
        <View style={styles.movementsHeader}>
          <Clock size={20} color={Colors.light.primary} />
          <Text style={styles.sectionTitle}>Movimientos Recientes</Text>
        </View>
        {movements.length > 0 ? (
          movements.map(movement => (
            <View key={movement.id} style={styles.movementRow}>
              <View style={styles.movementInfo}>
                <Text style={styles.movementType}>{formatMovementType(movement.type)}</Text>
                <Text style={styles.movementReason}>{movement.notes}</Text>
                <Text style={styles.movementDate}>
                  {new Date(movement.date).toLocaleDateString()}
                </Text>
              </View>
              <Text
                style={[
                  styles.movementQuantity,
                  movement.quantity > 0 ? styles.positiveQuantity : styles.negativeQuantity,
                ]}
              >
                {movement.quantity > 0 ? '+' : ''}
                {movement.quantity} {product.unitType}
              </Text>
            </View>
          ))
        ) : (
          <Text style={styles.noMovementsText}>No hay movimientos registrados</Text>
        )}
      </View>

      {/* Additional Info */}
      {(product.barcode || product.notes) && (
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Información Adicional</Text>
          {product.barcode && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Código de Barras:</Text>
              <Text style={styles.infoValue}>{product.barcode}</Text>
            </View>
          )}
          {product.notes && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Notas:</Text>
              <Text style={styles.infoValue}>{product.notes}</Text>
            </View>
          )}
        </View>
      )}

      {/* Stock Movements Modal */}
      {product && (
        <StockMovementsModal
          visible={showMovementsModal}
          onClose={() => setShowMovementsModal(false)}
          product={product}
          onComplete={() => {
            loadProductData();
            setShowMovementsModal(false);
          }}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  card: {
    backgroundColor: Colors.light.background,
    margin: 15,
    marginBottom: 0,
    borderRadius: 12,
    padding: 15,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  productName: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  productCategory: {
    fontSize: 14,
    color: Colors.light.primary,
    textTransform: 'capitalize',
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  stockSection: {
    marginBottom: 5,
  },
  stockInfo: {
    marginBottom: 10,
  },
  stockRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  stockLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  stockValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginTop: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.light.lightGray,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  pricingSection: {
    marginTop: 5,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  pricingLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  pricingValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  alertsSection: {
    margin: 15,
    marginBottom: 0,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning + '20',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  alertText: {
    fontSize: 14,
    color: Colors.light.warning,
    marginLeft: 8,
    flex: 1,
  },
  actionsCard: {
    backgroundColor: Colors.light.background,
    margin: 15,
    marginBottom: 0,
    borderRadius: 12,
    padding: 15,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionsRow: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButton: {
    backgroundColor: Colors.light.success,
  },
  removeButton: {
    backgroundColor: Colors.light.error,
  },
  actionButtonText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    fontSize: 14,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 10,
  },
  analysisContent: {
    marginTop: 10,
  },
  analysisRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  analysisValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  movementsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 10,
  },
  movementRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  movementInfo: {
    flex: 1,
  },
  movementType: {
    fontSize: 14,
    fontWeight: '500',
  },
  movementReason: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  movementDate: {
    fontSize: 11,
    color: Colors.light.lightGray,
    marginTop: 2,
  },
  movementQuantity: {
    fontSize: 14,
    fontWeight: '600',
  },
  positiveQuantity: {
    color: Colors.light.success,
  },
  negativeQuantity: {
    color: Colors.light.error,
  },
  noMovementsText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: 'center',
    paddingVertical: 20,
  },
  infoRow: {
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
  },
});
