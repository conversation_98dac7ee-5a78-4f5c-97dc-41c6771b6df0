/**
 * File Category Enum
 * Categorizes files by their role in the project
 */

export enum FileCategory {
  SOURCE_CODE = 'SOURCE_CODE',
  COMPONENT = 'COMPONENT',
  SCREEN = 'SCREEN',
  STORE = 'STORE',
  SERVICE = 'SERVICE',
  UTILITY = 'UTILITY',
  HOOK = 'HOOK',
  TYPE = 'TYPE',
  CONSTANT = 'CONSTANT',
  ASSET = 'ASSET',
  CONFIGURATION = 'CONFIGURATION',
  DOCUMENTATION = 'DOCUMENTATION',
  TEST = 'TEST',
  BUILD_ARTIFACT = 'BUILD_ARTIFACT',
}

export const FILE_CATEGORY_DESCRIPTIONS: Record<FileCategory, string> = {
  [FileCategory.SOURCE_CODE]: 'Application source code',
  [FileCategory.COMPONENT]: 'React components',
  [FileCategory.SCREEN]: 'Screen-level components',
  [FileCategory.STORE]: 'State management files',
  [FileCategory.SERVICE]: 'Business logic and API calls',
  [FileCategory.UTILITY]: 'Pure utility functions',
  [FileCategory.HOOK]: 'Custom React hooks',
  [FileCategory.TYPE]: 'TypeScript type definitions',
  [FileCategory.CONSTANT]: 'Configuration constants',
  [FileCategory.ASSET]: 'Images, fonts, icons',
  [FileCategory.CONFIGURATION]: 'Build and environment config',
  [FileCategory.DOCUMENTATION]: 'README, guides, specs',
  [FileCategory.TEST]: 'Test files',
  [FileCategory.BUILD_ARTIFACT]: 'Generated/compiled files',
};
