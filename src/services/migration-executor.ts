#!/usr/bin/env npx tsx

/**
 * Migration Executor - Practical implementation for Salonier file reorganization
 * This script safely migrates files from the current messy structure to the organized one
 */

import { promises as fs } from 'fs';
import { join, dirname, basename } from 'path';

interface MigrationAction {
  id: string;
  type: 'MOVE' | 'CREATE_DIRECTORY' | 'MOVE_WITH_RENAME';
  source: string;
  target: string;
  reason: string;
}

export class MigrationExecutor {
  private readonly projectRoot: string;
  private readonly dryRun: boolean;

  constructor(projectRoot: string = process.cwd(), dryRun = false) {
    this.projectRoot = projectRoot;
    this.dryRun = dryRun;
  }

  /**
   * Execute the main migration plan for Salonier
   */
  async executeSalonierMigration(): Promise<void> {
    console.info('🚀 Starting Salonier file structure migration...\n');

    const actions = await this.generateMigrationPlan();

    if (this.dryRun) {
      console.log('📋 DRY RUN - Actions that would be executed:\n');
      actions.forEach(action => {
        console.log(`${action.type}: ${action.source} → ${action.target}`);
        console.log(`   Reason: ${action.reason}\n`);
      });
      return;
    }

    // Create target directories first
    await this.createTargetDirectories();

    // Execute migrations in safe order
    let completed = 0;
    for (const action of actions) {
      try {
        await this.executeAction(action);
        completed++;
        console.log(
          `✅ [${completed}/${actions.length}] ${action.type}: ${basename(action.source)}`
        );
      } catch (error) {
        console.error(`❌ Failed to execute ${action.type} for ${action.source}:`, error);
        // Continue with other actions - don't fail entire migration
      }
    }

    console.log(`\n🎉 Migration completed! ${completed}/${actions.length} actions successful.`);
  }

  private async generateMigrationPlan(): Promise<MigrationAction[]> {
    const actions: MigrationAction[] = [];

    // 1. Move root markdown files to docs/
    const rootMarkdownFiles = await this.findFiles('.', /\.md$/, { maxDepth: 1 });
    for (const file of rootMarkdownFiles) {
      if (!file.startsWith('README.md') && !file.startsWith('package')) {
        actions.push({
          id: `docs-${basename(file)}`,
          type: 'MOVE',
          source: file,
          target: `docs/${basename(file)}`,
          reason: 'Root markdown files should be in docs/ directory',
        });
      }
    }

    // 2. Move root JS config files to appropriate locations
    const configFiles = [
      'babel.config.js',
      'metro.config.js',
      'tailwind.config.js',
      'jest.config.js',
      'eslint.config.js',
      '.eslintrc.js',
    ];

    for (const configFile of configFiles) {
      if (await this.fileExists(configFile)) {
        // Keep these at root for now - they need to be at root for tooling
        console.log(`ℹ️  Keeping ${configFile} at root (required by tooling)`);
      }
    }

    // 3. Move test scripts to scripts/
    const testScripts = [
      'create_demo_users.js',
      'debug-payload-test.js',
      'functional_test.js',
      'security-audit.js',
      'test_auth_config.js',
      'test_auth_fixed.js',
      'verification_test.js',
    ];

    for (const script of testScripts) {
      if (await this.fileExists(script)) {
        actions.push({
          id: `script-${script}`,
          type: 'MOVE',
          source: script,
          target: `scripts/legacy/${script}`,
          reason: 'Legacy test scripts should be in scripts/legacy/',
        });
      }
    }

    // 4. Already organized folders - check what's properly placed
    const wellOrganizedPaths = [
      'stores/',
      'components/',
      'hooks/',
      'utils/',
      'types/',
      'services/',
      'constants/',
      'supabase/',
    ];

    console.log('✅ Well organized folders that will stay:');
    for (const path of wellOrganizedPaths) {
      if (await this.directoryExists(path)) {
        console.log(`   ${path}`);
      }
    }

    // 5. Move frontend/ legacy files if they exist
    if (await this.directoryExists('frontend/')) {
      actions.push({
        id: 'frontend-legacy',
        type: 'MOVE',
        source: 'frontend/',
        target: 'archive/frontend-legacy/',
        reason: 'Move legacy frontend structure to archive',
      });
    }

    return actions;
  }

  private async createTargetDirectories(): Promise<void> {
    const directories = [
      'src/components',
      'src/components/ui',
      'src/components/forms',
      'src/screens',
      'src/services',
      'src/stores',
      'src/utils',
      'src/hooks',
      'src/types',
      'src/constants',
      'src/navigation',
      'assets/images',
      'assets/fonts',
      'assets/icons',
      '__tests__/components',
      '__tests__/screens',
      '__tests__/services',
      '__tests__/stores',
      '__tests__/utils',
      '__tests__/integration',
      'docs',
      'scripts/legacy',
      'archive',
    ];

    for (const dir of directories) {
      await this.ensureDirectory(dir);
    }
  }

  private async executeAction(action: MigrationAction): Promise<void> {
    switch (action.type) {
      case 'MOVE':
        await this.moveFile(action.source, action.target);
        break;
      case 'CREATE_DIRECTORY':
        await this.ensureDirectory(action.target);
        break;
      case 'MOVE_WITH_RENAME':
        await this.moveFile(action.source, action.target);
        break;
    }
  }

  private async moveFile(source: string, target: string): Promise<void> {
    const sourcePath = join(this.projectRoot, source);
    const targetPath = join(this.projectRoot, target);

    // Ensure target directory exists
    await this.ensureDirectory(dirname(target));

    // Check if source exists
    if (!(await this.fileExists(source))) {
      throw new Error(`Source file does not exist: ${source}`);
    }

    // Check if target already exists
    if (await this.fileExists(target)) {
      console.log(`⚠️  Target already exists, skipping: ${target}`);
      return;
    }

    await fs.rename(sourcePath, targetPath);
  }

  private async ensureDirectory(dirPath: string): Promise<void> {
    const fullPath = join(this.projectRoot, dirPath);
    try {
      await fs.access(fullPath);
    } catch {
      await fs.mkdir(fullPath, { recursive: true });
    }
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(join(this.projectRoot, filePath));
      return true;
    } catch {
      return false;
    }
  }

  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stat = await fs.stat(join(this.projectRoot, dirPath));
      return stat.isDirectory();
    } catch {
      return false;
    }
  }

  private async findFiles(
    dir: string,
    pattern: RegExp,
    options: { maxDepth?: number } = {}
  ): Promise<string[]> {
    const files: string[] = [];
    const { maxDepth = Infinity } = options;

    async function scan(currentDir: string, depth: number): Promise<void> {
      if (depth > maxDepth) return;

      const entries = await fs.readdir(join(process.cwd(), currentDir), { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(currentDir, entry.name);

        if (entry.isFile() && pattern.test(entry.name)) {
          files.push(fullPath);
        } else if (entry.isDirectory() && !['node_modules', '.git', '.expo'].includes(entry.name)) {
          await scan(fullPath, depth + 1);
        }
      }
    }

    await scan(dir, 0);
    return files;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const executor = new MigrationExecutor(process.cwd(), dryRun);

  executor.executeSalonierMigration().catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}
