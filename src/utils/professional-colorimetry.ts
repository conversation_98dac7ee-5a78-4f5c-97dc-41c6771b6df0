/**
 * Sistema Profesional de Colorimetría Capilar
 * Basado en estándares internacionales de la industria
 *
 * Este módulo implementa las leyes fundamentales de colorimetría
 * para garantizar fórmulas técnicamente correctas y viables.
 */

/**
 * Sistema Internacional de Niveles de Profundidad
 * Escala 1-11 con niveles intermedios críticos
 */
export const PROFESSIONAL_LEVEL_SYSTEM: Record<string, number> = {
  // Niveles base (1-10)
  negro: 1,
  'negro castaño': 2, // Nivel crítico frecuentemente omitido
  'castaño muy oscuro': 3,
  'castaño oscuro': 4,
  'castaño medio': 5,
  'castaño claro': 6,
  'rubio oscuro': 6.5, // Nivel intermedio importante
  'rubio medio': 7,
  rubio: 8,
  'rubio claro': 9,
  'rubio muy claro': 9.5,
  'rubio platino': 10,
  // Niveles especiales
  decolorante: 11, // Para decolorantes especiales
  'high lift': 12, // Aclarantes especiales
};

/**
 * Sistema Internacional de Reflejos/Matices
 * Numeración 0-9 estándar con neutralización específica
 */
export const PROFESSIONAL_REFLECT_SYSTEM = {
  '0': {
    name: 'Natural',
    keywords: ['natural', 'neutro', 'base'],
    neutralizes: 'ninguno',
    pigment: 'neutro',
  },
  '1': {
    name: 'Ceniza',
    keywords: ['ceniza', 'ash', 'gris', 'mate'],
    neutralizes: 'amarillo/naranja',
    pigment: 'azul-violeta',
  },
  '2': {
    name: 'Irisado/Violeta',
    keywords: ['irisado', 'violeta', 'morado', 'pearl'],
    neutralizes: 'amarillo',
    pigment: 'violeta',
  },
  '3': {
    name: 'Dorado',
    keywords: ['dorado', 'gold', 'cálido', 'warm'],
    neutralizes: 'verde',
    pigment: 'amarillo-dorado',
  },
  '4': {
    name: 'Cobrizo',
    keywords: ['cobrizo', 'cobre', 'copper', 'orange'],
    neutralizes: 'verde',
    pigment: 'naranja-rojo',
  },
  '5': {
    name: 'Caoba',
    keywords: ['caoba', 'mahogany', 'burgundy'],
    neutralizes: 'verde',
    pigment: 'rojo-violeta',
  },
  '6': {
    name: 'Rojizo',
    keywords: ['rojizo', 'rojo', 'red', 'ruby'],
    neutralizes: 'verde',
    pigment: 'rojo',
  },
  '7': {
    name: 'Mate/Verde',
    keywords: ['mate', 'verde', 'khaki', 'olive'],
    neutralizes: 'rojo/naranja',
    pigment: 'verde',
  },
  '8': {
    name: 'Perla',
    keywords: ['perla', 'pearl', 'nácar'],
    neutralizes: 'amarillo suave',
    pigment: 'azul-rosado',
  },
  '9': {
    name: 'Ceniza Suave',
    keywords: ['ceniza suave', 'soft ash', 'beige'],
    neutralizes: 'amarillo leve',
    pigment: 'azul suave',
  },
};

/**
 * Leyes Fundamentales de Colorimetría
 */
export const COLORIMETRY_LAWS = {
  /**
   * Ley 1: Límites de Elevación
   * Define cuántos niveles se pueden aclarar con cada técnica
   */
  ELEVATION_LIMITS: {
    permanentTint: 2, // Tinte permanente estándar
    demiPermanent: 0, // Sin elevación
    semiPermanent: 0, // Sin elevación
    highLiftTint: 4, // Súper aclarantes
    bleachRequired: (current: number, target: number) => target - current > 4,

    // Cálculo de oxidante según elevación deseada
    getDeveloperVolume: (levelsToLift: number): number => {
      if (levelsToLift <= 0) return 10; // Depositar solo
      if (levelsToLift <= 1) return 20; // 1 nivel
      if (levelsToLift <= 2) return 30; // 2 niveles
      if (levelsToLift <= 3) return 40; // 3 niveles (con high lift)
      return 40; // Máximo, requiere decoloración para más
    },
  },

  /**
   * Ley 2: Neutralización Obligatoria
   * Pigmentos subyacentes que deben neutralizarse por nivel
   */
  NEUTRALIZATION_MAP: {
    1: {
      underlying: 'negro',
      neutralizer: 'no requiere',
      pigment: 'rojo profundo',
    },
    2: {
      underlying: 'negro-rojo',
      neutralizer: 'no requiere',
      pigment: 'rojo',
    },
    3: {
      underlying: 'rojo profundo',
      neutralizer: '.7 (verde) o .1',
      pigment: 'rojo',
    },
    4: {
      underlying: 'rojo',
      neutralizer: '.2 (violeta) o .1 fuerte',
      pigment: 'rojo',
    },
    5: {
      underlying: 'rojo-naranja',
      neutralizer: '.1 o .2',
      pigment: 'rojo-naranja',
    },
    6: {
      underlying: 'naranja',
      neutralizer: '.1 (ceniza)',
      pigment: 'naranja',
    },
    7: {
      underlying: 'naranja-amarillo',
      neutralizer: '.1 suave',
      pigment: 'naranja-amarillo',
    },
    8: {
      underlying: 'amarillo-naranja',
      neutralizer: '.2 (violeta)',
      pigment: 'amarillo',
    },
    9: {
      underlying: 'amarillo',
      neutralizer: '.2 (violeta)',
      pigment: 'amarillo pálido',
    },
    10: {
      underlying: 'amarillo pálido',
      neutralizer: '.2 suave',
      pigment: 'mínimo',
    },
  },

  /**
   * Ley 3: Cobertura de Canas
   * Proporciones de mezcla según porcentaje
   */
  GRAY_COVERAGE: (
    grayPercent: number
  ): { natural: number; reflect: number; prepigmentation: boolean } => {
    if (grayPercent >= 100) {
      return { natural: 100, reflect: 0, prepigmentation: true };
    }
    if (grayPercent > 70) {
      return { natural: 100, reflect: 0, prepigmentation: false };
    }
    if (grayPercent > 50) {
      return { natural: 75, reflect: 25, prepigmentation: false };
    }
    if (grayPercent > 30) {
      return { natural: 50, reflect: 50, prepigmentation: false };
    }
    return { natural: 25, reflect: 75, prepigmentation: false };
  },

  /**
   * Ley 4: Tiempos de Procesamiento
   * Basados en técnica y tipo de producto
   */
  PROCESSING_TIMES: {
    permanentColor: {
      virgin: 35, // Cabello virgen
      retouch: 30, // Retoque de raíz
      refresh: 10, // Refresco de medios y puntas
    },
    highLift: {
      virgin: 45,
      retouch: 40,
    },
    bleach: {
      onScalp: 30, // En cuero cabelludo
      offScalp: 45, // Fuera del cuero (mechas, balayage)
      checkEvery: 10, // Revisar cada X minutos
    },
    toner: {
      standard: 20,
      express: 10,
    },
  },
};

/**
 * Validador de Fórmulas
 * Verifica que una fórmula sea técnicamente viable
 */
export const validateFormula = (
  currentLevel: number,
  targetLevel: number,
  technique = 'permanentColor'
): {
  isViable: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  const levelDifference = targetLevel - currentLevel;

  // Validar límites de elevación
  if (technique === 'permanentColor' && levelDifference > 2) {
    issues.push(`Tinte permanente solo puede elevar 2 niveles (intentando ${levelDifference})`);
    recommendations.push('Usar súper aclarante o decoloración previa');
  }

  if (technique === 'highLift' && levelDifference > 4) {
    issues.push(`Súper aclarante solo puede elevar 4 niveles (intentando ${levelDifference})`);
    recommendations.push('Requiere decoloración previa');
  }

  // Validar neutralización
  const neutralization =
    COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
      Math.floor(targetLevel) as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
    ];
  if (neutralization && levelDifference > 0) {
    recommendations.push(
      `Neutralizar pigmento ${neutralization.underlying} con reflejo ${neutralization.neutralizer}`
    );
  }

  // Validar oxidante
  const requiredDeveloper = COLORIMETRY_LAWS.ELEVATION_LIMITS.getDeveloperVolume(levelDifference);
  if (requiredDeveloper > 40) {
    issues.push('Elevación requiere más de 40 vol, considerar decoloración');
  }

  return {
    isViable: issues.length === 0,
    issues,
    recommendations,
  };
};

/**
 * Analizador de Tono Natural vs Con Reflejo
 */
export const analyzeTone = (
  toneName: string
): {
  level: number;
  reflect: string;
  isNatural: boolean;
} => {
  // Limpiar y normalizar el nombre
  const normalized = toneName.toLowerCase().trim();

  // Detectar nivel
  let level = 0;
  for (const [name, value] of Object.entries(PROFESSIONAL_LEVEL_SYSTEM)) {
    if (normalized.includes(name)) {
      level = value;
      break;
    }
  }

  // Si no encontramos nivel por nombre, buscar por número
  const levelMatch = normalized.match(/(\d+(?:\.\d+)?)/);
  if (levelMatch && !level) {
    level = parseFloat(levelMatch[1]);
  }

  // Detectar reflejo
  let reflect = '0'; // Natural por defecto

  // Buscar reflejo por número (ej: "8.34" → reflejo 34)
  const reflectMatch = normalized.match(/\d+[.,](\d+)/);
  if (reflectMatch) {
    reflect = reflectMatch[1];
  } else {
    // Buscar reflejo por palabra clave
    for (const [code, info] of Object.entries(PROFESSIONAL_REFLECT_SYSTEM)) {
      for (const keyword of info.keywords) {
        if (normalized.includes(keyword)) {
          reflect = code;
          break;
        }
      }
    }
  }

  // Determinar si es natural
  const isNatural =
    reflect === '0' ||
    normalized.includes('natural') ||
    normalized.includes('base') ||
    (!reflectMatch && !normalized.match(/ceniza|dorado|cobrizo|caoba|rojizo|violeta|mate|perla/));

  return { level, reflect, isNatural };
};

/**
 * Calculador de Sesiones Necesarias
 * Basado en principios profesionales de colorimetría
 */
export const calculateSessions = (
  currentLevel: number,
  targetLevel: number,
  hasChemicalProcess = false,
  grayPercentage = 0
): {
  sessions: number;
  process: string[];
  warnings: string[];
} => {
  const levelDifference = targetLevel - currentLevel;
  const process: string[] = [];
  const warnings: string[] = [];
  let sessions = 1;

  // Aclarar
  if (levelDifference > 0) {
    if (levelDifference <= 2) {
      sessions = 1;
      process.push('Tinte permanente con oxidante apropiado');
    } else if (levelDifference <= 4) {
      if (currentLevel >= 6) {
        sessions = 1;
        process.push('Súper aclarante o high lift');
      } else {
        sessions = 2;
        process.push('Sesión 1: Decoloración parcial');
        process.push('Sesión 2: Tinte al tono deseado');
      }
    } else if (levelDifference <= 6) {
      sessions = 2;
      process.push('Sesión 1: Decoloración controlada');
      process.push('Sesión 2: Tonalización o tinte');
      warnings.push('Proceso delicado, evaluar salud del cabello');
    } else {
      sessions = 3;
      process.push('Sesión 1: Primera decoloración');
      process.push('Sesión 2: Segunda decoloración');
      process.push('Sesión 3: Tonalización final');
      warnings.push('Alto riesgo de daño, considerar técnicas alternativas');
    }
  }

  // Oscurecer
  if (levelDifference < 0) {
    const darkness = Math.abs(levelDifference);
    if (darkness <= 3) {
      sessions = 1;
      process.push('Tinte directo al tono deseado');
    } else {
      sessions = 2;
      process.push('Sesión 1: Pre-pigmentación');
      process.push('Sesión 2: Tinte al tono final');
      if (darkness > 5) {
        warnings.push('Diferencia extrema, resultado puede requerir ajustes');
      }
    }
  }

  // Ajustar por canas
  if (grayPercentage > 70 && sessions === 1) {
    process[0] = `${process[0]} con fórmula 100% base natural`;
  }

  // Ajustar por proceso químico previo
  if (hasChemicalProcess && levelDifference > 2) {
    warnings.push('Cabello procesado requiere evaluación previa');
  }

  return { sessions, process, warnings };
};

/**
 * Mapea un nivel numérico a su nombre de tono profesional correspondiente
 * Basado en el sistema internacional de colorimetría capilar
 *
 * @param level - Nivel de profundidad (1-12, acepta decimales)
 * @returns Nombre profesional del tono
 *
 * @example
 * getToneNameFromLevel(5.5) // Returns "Castaño medio"
 * getToneNameFromLevel(8) // Returns "Rubio medio"
 */
export const getToneNameFromLevel = (level: number): string => {
  // Validar entrada
  if (level < 1) return 'Negro';
  if (level > 12) return 'Súper aclarante';

  // Mapeo preciso según estándares profesionales
  if (level >= 1 && level < 2.5) return 'Negro';
  if (level >= 2.5 && level < 4) return 'Castaño oscuro';
  if (level >= 4 && level < 5.5) return 'Castaño medio';
  if (level >= 5.5 && level < 6.5) return 'Castaño claro';
  if (level >= 6.5 && level < 7.5) return 'Rubio oscuro';
  if (level >= 7.5 && level < 8.5) return 'Rubio medio';
  if (level >= 8.5 && level < 9.5) return 'Rubio claro';
  if (level >= 9.5 && level < 10.5) return 'Rubio muy claro';
  if (level >= 10.5 && level < 11.5) return 'Rubio platino';

  return 'Súper aclarante'; // Niveles 11.5+
};

/**
 * Tipo para información completa de color
 */
export interface ColorInfo {
  level: number;
  levelName: string;
  toneName: string;
  reflect: string;
  reflectName: string;
  isNatural: boolean;
  technicalName: string;
  colorFamily: 'warm' | 'cool' | 'neutral';
}

/**
 * Combina información de nivel y tono para obtener datos completos del color
 * Proporciona mapeo técnico preciso para evitar errores de visualización
 *
 * @param level - Nivel numérico (1-12)
 * @param reflect - Código de reflejo ('0'-'9') o cadena vacía para natural
 * @returns Información completa del color con datos técnicos
 *
 * @example
 * getColorFromLevelAndTone(5.5, '0')
 * // Returns: {
 * //   level: 5.5,
 * //   levelName: "Castaño medio",
 * //   toneName: "Castaño medio natural",
 * //   reflect: "0",
 * //   reflectName: "Natural",
 * //   isNatural: true,
 * //   technicalName: "5.5 Castaño medio natural",
 * //   colorFamily: "neutral"
 * // }
 */
export const getColorFromLevelAndTone = (level: number, reflect = '0'): ColorInfo => {
  const levelName = getToneNameFromLevel(level);
  const reflectCode = reflect || '0';

  // Obtener información del reflejo
  const reflectInfo =
    PROFESSIONAL_REFLECT_SYSTEM[reflectCode as keyof typeof PROFESSIONAL_REFLECT_SYSTEM] ||
    PROFESSIONAL_REFLECT_SYSTEM['0'];

  const isNatural = reflectCode === '0' || reflectCode === '';

  // Construir nombre técnico completo
  let toneName: string;
  if (isNatural) {
    toneName = `${levelName} natural`;
  } else {
    toneName = `${levelName} ${reflectInfo.name.toLowerCase()}`;
  }

  const technicalName = `${level} ${toneName}`;

  // Determinar familia de color para mapeo de UI
  let colorFamily: 'warm' | 'cool' | 'neutral' = 'neutral';

  if (isNatural) {
    colorFamily = 'neutral';
  } else {
    // Clasificar según pigmentos del reflejo
    const warmReflects = ['3', '4', '5', '6']; // Dorado, Cobre, Caoba, Rojo
    const coolReflects = ['1', '2', '7', '8', '9']; // Ceniza, Violeta, Mate, Perla, Ceniza suave

    if (warmReflects.includes(reflectCode)) {
      colorFamily = 'warm';
    } else if (coolReflects.includes(reflectCode)) {
      colorFamily = 'cool';
    }
  }

  return {
    level,
    levelName,
    toneName,
    reflect: reflectCode,
    reflectName: reflectInfo.name,
    isNatural,
    technicalName,
    colorFamily,
  };
};
