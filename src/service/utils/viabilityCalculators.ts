import { ViabilityAnalysis } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import {
  HairZone,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  HairState,
} from '@/types/hair-diagnosis';
import { planSessions } from '@/services/session-planner';
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';

/**
 * Analyze viability of a color service based on current and desired state
 */
export const analyzeViability = (
  analysisResult: AIAnalysisResult | null,
  desiredAnalysisResult: DesiredColorAnalysisResult | null,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
): ViabilityAnalysis => {
  if (!analysisResult || !desiredAnalysisResult) {
    return {
      score: 'caution' as const,
      factors: {
        levelDifference: 0,
        hairHealth: 'good' as const,
        chemicalHistory: [],
        estimatedSessions: 1,
      },
      recommendations: [],
      warnings: [],
    };
  }

  const factorsList: string[] = [];
  const recommendations: string[] = [];
  const warnings: string[] = [];
  let overall: 'low' | 'medium' | 'high' = 'medium';
  let _riskLevel: 'low' | 'medium' | 'high' = 'medium';
  let sessionsNeeded = 1;

  // Analyze level difference with deterministic session planner
  const currentLevel = analysisResult.averageLevel || analysisResult.averageDepthLevel || 5;
  const targetLevel =
    parseInt(desiredAnalysisResult.general.overallLevel?.split('/')[0] || '7') || 7;
  const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
  const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as HairState | undefined;
  const damage = ((rootsZone as Partial<ZonePhysicalAnalysis>)?.damage || 'Bajo') as
    | 'Bajo'
    | 'Medio'
    | 'Alto';
  const sessionPlan = planSessions({
    currentLevel,
    targetLevel,
    dyedRoots: !!(rootsState && rootsState !== HairState.NATURAL),
    damage,
  });
  sessionsNeeded = sessionPlan.recommended;
  const levelDifference = Math.abs(targetLevel - currentLevel);
  factorsList.push(
    levelDifference > 1 ? 'Cambio de nivel moderado/significativo' : 'Cambio de nivel mínimo'
  );

  // Enforce professional rule: "color no levanta color" (dyed hair cannot be lightened reliably with tint)
  try {
    const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
    const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as HairState | undefined;
    const goingLighter = targetLevel > currentLevel;
    if (goingLighter && rootsState && rootsState !== HairState.NATURAL) {
      warnings.push(
        'Color no levanta color: para aclarar sobre cabello teñido, planifica decoloración/arrastre de color.'
      );
      recommendations.push(
        'Divide en sesiones: 1) Decoloración controlada o remover color 2) Tonalizar/Depositar.'
      );
      overall = 'high';
      _riskLevel = 'high';
    }
  } catch {
    // non-fatal
  }

  // Analyze hair condition
  const rootsPhysical = zoneColorAnalysis[HairZone.ROOTS];
  if (rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio') {
    warnings.push('Cabello con daño severo - considerar tratamiento previo');
    _riskLevel = 'high';
    recommendations.push('Aplicar tratamiento reconstructor antes del color');
  }

  // Analyze porosity
  const rootsPhysicalAnalysis = rootsPhysical as Partial<ZonePhysicalAnalysis>;
  if (rootsPhysicalAnalysis?.porosity === 'Alta') {
    factorsList.push('Porosidad alta detectada');
    recommendations.push('Usar productos de baja alcalinidad');
    recommendations.push('Reducir tiempo de procesamiento');
  }

  // Time estimation
  let _timeEstimate = '1-2 horas';
  if (levelDifference > 2) _timeEstimate = '2-3 horas';
  if (levelDifference > 4) _timeEstimate = '3-4 horas';
  if (sessionsNeeded > 1) _timeEstimate = `${sessionsNeeded} sesiones de 2-3 horas cada una`;

  return {
    score: overall === 'low' ? 'safe' : overall === 'medium' ? 'caution' : 'risky',
    factors: {
      levelDifference,
      hairHealth:
        rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio'
          ? 'poor'
          : rootsPhysical?.damage === 'Bajo'
            ? 'good'
            : 'fair',
      chemicalHistory: factorsList,
      estimatedSessions: sessionsNeeded,
    },
    recommendations,
    warnings,
  };
};
