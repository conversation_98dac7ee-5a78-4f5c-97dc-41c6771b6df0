import { useEffect, useMemo, useCallback } from 'react';
import { useChatStore, ChatMessage } from '@/stores/chat-store';
import { useSafeAsync } from '@/utils/memory-cleanup';

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface UseChatInitializationProps {
  propConversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
  isTablet: boolean;
}

interface UseChatInitializationReturn {
  currentMessages: ChatMessage[];
  conversationStarters: string[];
  activeConversationId: string | null;
  isSending: boolean;
  streamingMessage: {
    conversationId: string;
    messageId: string;
    content: string;
    isComplete: boolean;
    currentIndex?: number;
  } | null;
  typingStatus: 'idle' | 'thinking' | 'analyzing' | 'writing';
  error: Error | null;
  handleSelectConversation: (id: string) => Promise<void>;
  handleCreateNewConversation: () => Promise<void>;
}

export const useChatInitialization = ({
  propConversationId,
  contextType,
  contextId,
  contextData,
  // isTablet, // isTablet is not directly used inside this hook, but passed as prop
}: UseChatInitializationProps): UseChatInitializationReturn => {
  const { execute: safeExecute } = useSafeAsync();

  // Selectors from chat store
  const messages = useChatStore(state => state.messages);
  const activeConversationId = useChatStore(state => state.activeConversationId);
  const isSending = useChatStore(state => state.isSending);
  const error = useChatStore(state => state.error);
  const streamingMessage = useChatStore(state => state.streamingMessage);
  const typingStatus = useChatStore(state => state.typingStatus);

  // Context stores - keeping references for potential future context-aware features
  // const { clients: _clients } = useClientStore(); // Keep for potential future use in context logic
  // const { configuration: _configuration } = useSalonConfigStore(); // Keep for potential future use in context logic

  // Make currentMessages reactive to messages and activeConversationId changes
  const currentMessages = useMemo(() => {
    const result = activeConversationId ? messages[activeConversationId] || [] : [];
    // Recalculated current messages for active conversation
    return result;
  }, [messages, activeConversationId]);

  // Initialize conversation - NUCLEAR FIX: Remove all function dependencies to prevent infinite loop
  useEffect(() => {
    const initializeChat = async () => {
      const chatStore = useChatStore.getState();

      await chatStore.loadConversations();

      const updatedStore = useChatStore.getState();
      const updatedConversations = updatedStore.conversations;

      if (propConversationId) {
        updatedStore.setActiveConversation(propConversationId);
        await updatedStore.loadMessages(propConversationId);
      } else if (contextType && contextId) {
        const existingConv = updatedConversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          updatedStore.setActiveConversation(existingConv.id);
          await updatedStore.loadMessages(existingConv.id);
        } else {
          let title = 'Nueva conversación';
          if (contextType === 'client' && contextData?.name) {
            title = `Consulta sobre ${contextData.name}`;
          } else if (contextType === 'service') {
            title = 'Consulta sobre servicio';
          } else if (contextType === 'formula') {
            title = 'Consulta sobre fórmula';
          } else if (contextType === 'inventory') {
            title = 'Consulta de inventario';
          }

          const newConv = await updatedStore.createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            updatedStore.setActiveConversation(newConv.id);
          }
        }
      } else if (!updatedStore.activeConversationId && updatedConversations.length > 0) {
        const mostRecent = updatedConversations[0];
        updatedStore.setActiveConversation(mostRecent.id);
        await updatedStore.loadMessages(mostRecent.id);
      }
    };

    initializeChat();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propConversationId, contextType, contextId, contextData?.name]);

  const conversationStarters = useMemo(() => {
    const starters = [];
    if (contextType === 'client' && contextData?.name) {
      starters.push(`¿Qué técnica recomiendas para ${contextData.name}?`);
    } else {
      starters.push('¿Cómo corrijo un color naranja?');
      starters.push('Fórmula para rubio ceniza nivel 8');
    }
    return starters.slice(0, 2);
  }, [contextType, contextData?.name]);

  const handleSelectConversation = useCallback(
    async (id: string) => {
      const chatStore = useChatStore.getState();
      chatStore.cleanupConversationMemory(id);
      chatStore.setActiveConversation(id);
      await safeExecute(() => chatStore.loadMessages(id));
    },
    [safeExecute]
  );

  const handleCreateNewConversation = useCallback(async () => {
    const chatStore = useChatStore.getState();
    const title = 'Nueva conversación';
    const newConv = await chatStore.createConversation({ title });
    if (newConv) {
      chatStore.setActiveConversation(newConv.id);
    }
  }, []);

  return {
    currentMessages,
    conversationStarters,
    activeConversationId,
    isSending,
    streamingMessage,
    typingStatus,
    error,
    handleSelectConversation,
    handleCreateNewConversation,
  };
};
