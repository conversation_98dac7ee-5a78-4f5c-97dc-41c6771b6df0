/**
 * Security Headers Configuration
 *
 * Comprehensive security headers to protect against various attacks:
 * - XSS (Cross-Site Scripting)
 * - CSRF (Cross-Site Request Forgery)
 * - Clickjacking
 * - MIME sniffing
 * - Information disclosure
 *
 * OWASP Security Headers Implementation
 */

export interface SecurityHeadersConfig {
  environment: 'development' | 'production' | 'test';
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  maxAge?: number;
}

export class SecurityHeaders {
  private static readonly DEFAULT_ALLOWED_ORIGINS = {
    production: ['https://salonier.app', 'https://www.salonier.app'],
    development: ['http://localhost:*', 'https://localhost:*'],
    test: ['http://localhost:*', 'https://localhost:*'],
  };

  private static readonly DEFAULT_ALLOWED_METHODS = [
    'GET',
    'POST',
    'PUT',
    'DELETE',
    'OPTIONS',
    'HEAD',
  ];

  private static readonly DEFAULT_ALLOWED_HEADERS = [
    'authorization',
    'x-client-info',
    'apikey',
    'content-type',
    'x-requested-with',
    'accept',
    'origin',
    'user-agent',
    'cache-control',
  ];

  /**
   * Get comprehensive security headers for HTTP responses
   */
  static getSecurityHeaders(config: SecurityHeadersConfig): Record<string, string> {
    const {
      environment = 'production',
      allowedOrigins = this.DEFAULT_ALLOWED_ORIGINS[environment],
      allowedMethods = this.DEFAULT_ALLOWED_METHODS,
      allowedHeaders = this.DEFAULT_ALLOWED_HEADERS,
      maxAge = 86400, // 24 hours
    } = config;

    return {
      // CORS Headers
      'Access-Control-Allow-Origin': this.getCORSOrigin(allowedOrigins, environment),
      'Access-Control-Allow-Methods': allowedMethods.join(', '),
      'Access-Control-Allow-Headers': allowedHeaders.join(', '),
      'Access-Control-Max-Age': maxAge.toString(),
      'Access-Control-Allow-Credentials': 'true',

      // Security Headers
      ...this.getSecurityOnlyHeaders(environment),
    };
  }

  /**
   * Get security-only headers (no CORS)
   */
  static getSecurityOnlyHeaders(environment = 'production'): Record<string, string> {
    return {
      // HSTS - Force HTTPS
      'Strict-Transport-Security':
        environment === 'production'
          ? 'max-age=63072000; includeSubDomains; preload'
          : 'max-age=3600',

      // Prevent MIME sniffing
      'X-Content-Type-Options': 'nosniff',

      // Prevent clickjacking
      'X-Frame-Options': 'DENY',

      // XSS Protection (legacy browsers)
      'X-XSS-Protection': '1; mode=block',

      // Referrer Policy
      'Referrer-Policy': 'strict-origin-when-cross-origin',

      // Permissions Policy (formerly Feature Policy)
      'Permissions-Policy': this.getPermissionsPolicy(),

      // Content Security Policy
      'Content-Security-Policy': this.getContentSecurityPolicy(environment),

      // Additional security headers
      'X-Permitted-Cross-Domain-Policies': 'none',
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-origin',

      // Cache control for sensitive endpoints
      'Cache-Control': 'no-cache, no-store, must-revalidate, private',
      Pragma: 'no-cache',
      Expires: '0',
    };
  }

  /**
   * Get CORS origin header value
   */
  private static getCORSOrigin(allowedOrigins: string[], environment: string): string {
    if (environment === 'development' || environment === 'test') {
      return '*'; // Allow all origins in development
    }

    // In production, be more restrictive
    return allowedOrigins.length === 1 ? allowedOrigins[0] : allowedOrigins[0];
  }

  /**
   * Get Content Security Policy
   */
  private static getContentSecurityPolicy(environment: string): string {
    const policies = [
      // Default fallback
      "default-src 'self'",

      // Scripts - very restrictive
      environment === 'production'
        ? "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net"
        : "script-src 'self' 'unsafe-inline' 'unsafe-eval'",

      // Styles
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",

      // Images - allow data URIs for base64 images and external HTTPS
      "img-src 'self' data: https: blob:",

      // Fonts
      "font-src 'self' https://fonts.gstatic.com",

      // Connect to APIs
      environment === 'production'
        ? "connect-src 'self' https://*.supabase.co https://api.openai.com"
        : "connect-src 'self' https://*.supabase.co http://localhost:* https://api.openai.com",

      // Media
      "media-src 'self' data: blob:",

      // Objects and embeds
      "object-src 'none'",
      "embed-src 'none'",

      // Base URI
      "base-uri 'self'",

      // Forms
      "form-action 'self'",

      // Frame ancestors (prevent clickjacking)
      "frame-ancestors 'none'",

      // Upgrade insecure requests in production
      environment === 'production' ? 'upgrade-insecure-requests' : '',
    ];

    return policies.filter(Boolean).join('; ');
  }

  /**
   * Get Permissions Policy
   */
  private static getPermissionsPolicy(): string {
    const permissions = [
      'camera=self', // Allow camera for photo capture
      'microphone=()', // No microphone access needed
      'geolocation=()', // No location access needed
      'payment=()', // No payment API access
      'usb=()', // No USB access
      'magnetometer=()', // No magnetometer
      'gyroscope=()', // No gyroscope
      'accelerometer=()', // No accelerometer
      'ambient-light-sensor=()', // No light sensor
      'autoplay=()', // No autoplay
      'battery=()', // No battery API
      'display-capture=()', // No screen capture
      'document-domain=()', // No document.domain
      'encrypted-media=()', // No DRM content
      'fullscreen=self', // Allow fullscreen on same origin
      'midi=()', // No MIDI access
      'notifications=()', // No notifications for now
      'persistent-storage=()', // No persistent storage quota
      'picture-in-picture=()', // No PIP
      'publickey-credentials-get=()', // No WebAuthn for now
      'screen-wake-lock=()', // No wake lock
      'web-share=()', // No Web Share API
      'xr-spatial-tracking=()', // No VR/AR
    ];

    return permissions.join(', ');
  }

  /**
   * Get headers for API responses (JSON content)
   */
  static getAPIHeaders(config: SecurityHeadersConfig): Record<string, string> {
    return {
      ...this.getSecurityHeaders(config),
      'Content-Type': 'application/json',
    };
  }

  /**
   * Get headers for Edge Functions
   */
  static getEdgeFunctionHeaders(environment = 'production'): Record<string, string> {
    return {
      // CORS for Edge Functions
      'Access-Control-Allow-Origin': environment === 'production' ? 'https://salonier.app' : '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      'Access-Control-Max-Age': '86400',

      // Security headers
      ...this.getSecurityOnlyHeaders(environment),

      // Content type
      'Content-Type': 'application/json',
    };
  }

  /**
   * Check if request origin is allowed
   */
  static isOriginAllowed(origin: string | null, config: SecurityHeadersConfig): boolean {
    if (!origin) return true; // Allow requests without Origin header

    const {
      environment = 'production',
      allowedOrigins = this.DEFAULT_ALLOWED_ORIGINS[environment],
    } = config;

    // In development, allow all origins
    if (environment === 'development' || environment === 'test') {
      return true;
    }

    // Check against allowed origins
    return allowedOrigins.some(allowed => {
      // Exact match
      if (allowed === origin) return true;

      // Wildcard match (e.g., https://*.example.com)
      if (allowed.includes('*')) {
        const pattern = allowed.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(origin);
      }

      return false;
    });
  }

  /**
   * Validate request headers for security issues
   */
  static validateRequestHeaders(headers: Record<string, string>): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check for suspicious headers
    const suspiciousHeaders = [
      'x-forwarded-proto',
      'x-forwarded-host',
      'x-original-url',
      'x-rewrite-url',
    ];

    for (const header of suspiciousHeaders) {
      if (headers[header]) {
        issues.push(`Suspicious header detected: ${header}`);
      }
    }

    // Check User-Agent (should exist for legitimate requests)
    if (!headers['user-agent'] && !headers['User-Agent']) {
      issues.push('Missing User-Agent header');
    }

    // Check for overly long headers (potential attack)
    for (const [key, value] of Object.entries(headers)) {
      if (value.length > 8192) {
        // 8KB limit
        issues.push(`Header ${key} is too long (${value.length} characters)`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  /**
   * Get security headers for file downloads
   */
  static getDownloadHeaders(filename: string, contentType: string): Record<string, string> {
    return {
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${filename}"`,
      'X-Content-Type-Options': 'nosniff',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
    };
  }
}

// Export convenience functions
export const getSecurityHeaders = SecurityHeaders.getSecurityHeaders.bind(SecurityHeaders);
export const getAPIHeaders = SecurityHeaders.getAPIHeaders.bind(SecurityHeaders);
export const getEdgeFunctionHeaders = SecurityHeaders.getEdgeFunctionHeaders.bind(SecurityHeaders);
export const isOriginAllowed = SecurityHeaders.isOriginAllowed.bind(SecurityHeaders);
export const validateRequestHeaders = SecurityHeaders.validateRequestHeaders.bind(SecurityHeaders);
