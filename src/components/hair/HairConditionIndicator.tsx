import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
  withSequence,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import {
  getHairConditionIcon,
  _DAMAGE_LEVEL_ICONS,
  _POROSITY_ICONS,
  _ELASTICITY_ICONS,
  HairConditionProperty,
} from '@/constants/hair-iconography';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface HairConditionIndicatorProps {
  property: HairConditionProperty;
  value: string;
  showLabel?: boolean;
  showDescription?: boolean;
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
  interactive?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
}

/**
 * Professional Hair Condition Indicator
 *
 * Visual indicator for hair condition properties (damage, porosity, elasticity).
 * Uses professional iconography and color coding for immediate recognition.
 */
export const HairConditionIndicator: React.FC<HairConditionIndicatorProps> = ({
  property,
  value,
  showLabel = true,
  showDescription = false,
  size = 'medium',
  animated = true,
  interactive = false,
  onPress,
  style,
}) => {
  const scaleAnim = useSharedValue(0);
  const pulseAnim = useSharedValue(1);
  const glowAnim = useSharedValue(0);

  React.useEffect(() => {
    if (animated) {
      scaleAnim.value = withSpring(1, { damping: 15, stiffness: 150 });

      // Add subtle pulse for critical conditions
      if (value === 'Alto' || value === 'Pobre') {
        pulseAnim.value = withSequence(
          withTiming(1.05, { duration: 800 }),
          withTiming(1, { duration: 800 })
        );

        // Trigger haptic feedback for critical conditions
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      // Add glow effect for excellent conditions
      if (value === 'Bajo' || value === 'Buena') {
        glowAnim.value = withTiming(1, { duration: 1000 });
      }
    } else {
      scaleAnim.value = 1;
      pulseAnim.value = 1;
      glowAnim.value = value === 'Bajo' || value === 'Buena' ? 1 : 0;
    }
  }, [value, animated, glowAnim, pulseAnim, scaleAnim]);

  // Define animated styles before any early returns (Rules of Hooks)
  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value * pulseAnim.value }],
  }));

  const animatedGlowStyle = useAnimatedStyle(() => ({
    shadowOpacity: interpolate(glowAnim.value, [0, 1], [0.1, 0.3]),
  }));

  const conditionData = getHairConditionIcon(property, value);

  if (!conditionData) {
    return null;
  }

  const IconComponent = conditionData.icon;

  // Size configurations
  const sizeConfigs = {
    small: {
      containerSize: 36,
      iconSize: 18,
      fontSize: 11,
      descriptionSize: 10,
      padding: 8,
    },
    medium: {
      containerSize: 48,
      iconSize: 24,
      fontSize: 13,
      descriptionSize: 12,
      padding: 12,
    },
    large: {
      containerSize: 60,
      iconSize: 30,
      fontSize: 15,
      descriptionSize: 13,
      padding: 16,
    },
  };

  const config = sizeConfigs[size];

  const handlePress = () => {
    if (interactive && onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  // Get property display name
  const getPropertyLabel = () => {
    switch (property) {
      case 'damage':
        return 'Nivel de Daño';
      case 'porosity':
        return 'Porosidad';
      case 'elasticity':
        return 'Elasticidad';
      default:
        return property;
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          width: config.containerSize + (showDescription ? 120 : 0),
          minHeight: config.containerSize,
        },
        animatedContainerStyle,
        style,
      ]}
    >
      {/* Icon Container with Professional Styling */}
      <Animated.View
        style={[
          styles.iconContainer,
          {
            width: config.containerSize,
            height: config.containerSize,
            borderRadius: config.containerSize / 2,
            backgroundColor: conditionData.color + '15',
            borderColor: conditionData.color + '40',
            padding: config.padding,
          },
          animatedGlowStyle,
          interactive && styles.interactiveContainer,
        ]}
        onTouchEnd={handlePress}
      >
        <IconComponent size={config.iconSize} color={conditionData.color} />
      </Animated.View>

      {/* Labels and Description */}
      {(showLabel || showDescription) && (
        <View style={styles.textContainer}>
          {showLabel && (
            <View>
              <Text style={[styles.propertyLabel, { fontSize: config.fontSize - 1 }]}>
                {getPropertyLabel()}
              </Text>
              <Text
                style={[
                  styles.valueLabel,
                  { fontSize: config.fontSize, color: conditionData.color },
                ]}
              >
                {value}
              </Text>
            </View>
          )}

          {showDescription && (
            <Text style={[styles.description, { fontSize: config.descriptionSize }]}>
              {conditionData.description}
            </Text>
          )}
        </View>
      )}
    </Animated.View>
  );
};

/**
 * Hair Condition Summary Component
 *
 * Shows complete hair condition analysis with all properties
 */
interface HairConditionSummaryProps {
  damage?: string;
  porosity?: string;
  elasticity?: string;
  showDescriptions?: boolean;
  onPropertyPress?: (property: HairConditionProperty, value: string) => void;
  style?: ViewStyle;
}

export const HairConditionSummary: React.FC<HairConditionSummaryProps> = ({
  damage,
  porosity,
  elasticity,
  showDescriptions = false,
  onPropertyPress,
  style,
}) => {
  const conditions = [
    { property: 'damage' as HairConditionProperty, value: damage },
    { property: 'porosity' as HairConditionProperty, value: porosity },
    { property: 'elasticity' as HairConditionProperty, value: elasticity },
  ].filter(item => item.value);

  if (conditions.length === 0) {
    return (
      <View style={[styles.summaryContainer, style]}>
        <Text style={styles.noDataText}>Análisis de condición capilar pendiente</Text>
      </View>
    );
  }

  // Calculate overall hair health score
  const getHealthScore = () => {
    let score = 0;
    let count = 0;

    conditions.forEach(({ property, value }) => {
      if (!value) return;

      count++;
      switch (property) {
        case 'damage':
          if (value === 'Bajo') score += 100;
          else if (value === 'Medio') score += 60;
          else score += 20;
          break;
        case 'porosity':
          if (value === 'Media') score += 100;
          else if (value === 'Baja') score += 80;
          else score += 40;
          break;
        case 'elasticity':
          if (value === 'Buena') score += 100;
          else if (value === 'Media') score += 70;
          else score += 30;
          break;
      }
    });

    return count > 0 ? Math.round(score / count) : 0;
  };

  const healthScore = getHealthScore();
  const getHealthLevel = (score: number) => {
    if (score >= 85)
      return { level: 'Excelente', color: BeautyMinimalTheme.semantic.status.success };
    if (score >= 70) return { level: 'Buena', color: BeautyMinimalTheme.beautyColors.sage[500] };
    if (score >= 50) return { level: 'Regular', color: BeautyMinimalTheme.beautyColors.amber };
    return { level: 'Comprometida', color: BeautyMinimalTheme.semantic.status.error };
  };

  const healthData = getHealthLevel(healthScore);

  return (
    <View style={[styles.summaryContainer, style]}>
      <View style={styles.summaryHeader}>
        <Text style={styles.summaryTitle}>Condición Capilar Profesional</Text>
        <View style={[styles.healthBadge, { backgroundColor: healthData.color + '20' }]}>
          <Text style={[styles.healthScore, { color: healthData.color }]}>
            {healthData.level} ({healthScore}%)
          </Text>
        </View>
      </View>

      <View style={styles.conditionsGrid}>
        {conditions.map(({ property, value }) => (
          <HairConditionIndicator
            key={property}
            property={property}
            value={value!}
            size="large"
            showLabel={true}
            showDescription={showDescriptions}
            interactive={!!onPropertyPress}
            onPress={() => onPropertyPress?.(property, value!)}
            style={styles.conditionItem}
          />
        ))}
      </View>

      {/* Professional Recommendations */}
      {healthScore < 70 && (
        <View style={styles.recommendationsContainer}>
          <Text style={styles.recommendationsTitle}>Recomendaciones Profesionales</Text>
          <View style={styles.recommendationsList}>
            {damage === 'Alto' && (
              <Text style={styles.recommendationItem}>
                • Tratamiento reconstructor antes de cualquier proceso químico
              </Text>
            )}
            {porosity === 'Alta' && (
              <Text style={styles.recommendationItem}>
                • Uso de productos selladores de cutícula
              </Text>
            )}
            {elasticity === 'Pobre' && (
              <Text style={styles.recommendationItem}>
                • Evitar tensión mecánica, riesgo de rotura elevado
              </Text>
            )}
            {healthScore < 50 && (
              <Text style={[styles.recommendationItem, styles.criticalRecommendation]}>
                ⚠️ Considerar postponer procesos químicos hasta mejorar condición
              </Text>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // Single Indicator
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    elevation: 2,
    shadowColor: BeautyMinimalTheme.neutrals.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  interactiveContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
  },
  textContainer: {
    flex: 1,
    gap: BeautyMinimalTheme.spacing.xs,
  },
  propertyLabel: {
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  valueLabel: {
    fontWeight: '700',
  },
  description: {
    color: BeautyMinimalTheme.semantic.text.tertiary,
    lineHeight: 16,
    fontStyle: 'italic',
  },

  // Summary Component
  summaryContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
    gap: BeautyMinimalTheme.spacing.md,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  healthBadge: {
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.md,
  },
  healthScore: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
  },
  conditionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    gap: BeautyMinimalTheme.spacing.sm,
  },
  conditionItem: {
    flex: 1,
    minWidth: 140,
  },
  noDataText: {
    textAlign: 'center',
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontStyle: 'italic',
    padding: BeautyMinimalTheme.spacing.lg,
  },

  // Recommendations
  recommendationsContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: BeautyMinimalTheme.beautyColors.amber,
  },
  recommendationsTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  recommendationsList: {
    gap: BeautyMinimalTheme.spacing.xs,
  },
  recommendationItem: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight: 18,
  },
  criticalRecommendation: {
    color: BeautyMinimalTheme.semantic.status.error,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
});

export default HairConditionIndicator;
