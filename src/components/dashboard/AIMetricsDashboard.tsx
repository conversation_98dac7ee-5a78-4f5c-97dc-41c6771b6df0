import React, { useState, useEffect, useCallback } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Zap,
  Target,
  AlertCircle,
  CheckCircle,
  BarChart3,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { supabase } from '@/lib/supabase';

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  changeLabel: string;
  icon: React.ReactNode;
  status: 'success' | 'warning' | 'error' | 'neutral';
}

interface AIMetrics {
  latency: {
    current: number;
    target: number;
    trend: number;
  };
  cost: {
    daily: number;
    monthly: number;
    perService: number;
    saved: number;
  };
  accuracy: {
    current: number;
    target: number;
    byModel: {
      'gpt-3.5-turbo': number;
      'gpt-4o-mini': number;
      'gpt-4o': number;
    };
  };
  cache: {
    hitRate: number;
    entriesCount: number;
    tokensSaved: number;
    costSaved: number;
  };
  usage: {
    totalRequests: number;
    successRate: number;
    errorRate: number;
    modelDistribution: {
      'gpt-3.5-turbo': number;
      'gpt-4o-mini': number;
      'gpt-4o': number;
    };
  };
}

export const AIMetricsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<AIMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('today');

  useEffect(() => {
    loadMetrics();
  }, [timeRange, loadMetrics]);

  const loadMetrics = useCallback(async () => {
    try {
      setLoading(true);

      // Get metrics from database
      const { data: metricsData } = await supabase
        .from('ai_metrics_simple')
        .select('*')
        .order('metric_date', { ascending: false })
        .limit(timeRange === 'today' ? 1 : timeRange === 'week' ? 7 : 30);

      // Get cache analytics
      const { data: cacheData } = await supabase
        .from('ai_cache_analytics')
        .select('*')
        .limit(1)
        .single();

      // Calculate aggregated metrics
      if (metricsData && metricsData.length > 0) {
        const aggregated = calculateAggregatedMetrics(metricsData, cacheData);
        setMetrics(aggregated);
      }
    } catch (error) {
      logger.error('Error loading metrics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [timeRange]);

  const calculateAggregatedMetrics = (
    data: Record<string, unknown>[],
    cacheData: Record<string, unknown>
  ): AIMetrics => {
    // Mock calculation - would be replaced with real data processing
    const totalRequests = data.reduce((sum, d) => sum + (Number(d.total_requests) || 0), 0);
    const totalCost = data.reduce((sum, d) => sum + (Number(d.total_cost) || 0), 0);
    const cacheHits = data.reduce((sum, d) => sum + (Number(d.cache_hits) || 0), 0);

    return {
      latency: {
        current: 2.8, // seconds
        target: 3.0,
        trend: -79, // % improvement
      },
      cost: {
        daily: totalCost,
        monthly: totalCost * 30,
        perService: totalRequests > 0 ? totalCost / totalRequests : 0.025,
        saved: data.reduce((sum, d) => sum + (d.cost_saved || 0), 0),
      },
      accuracy: {
        current: 93,
        target: 95,
        byModel: {
          'gpt-3.5-turbo': 85,
          'gpt-4o-mini': 92,
          'gpt-4o': 98,
        },
      },
      cache: {
        hitRate:
          cacheData?.cache_hit_rate || (totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0),
        entriesCount: cacheData?.total_cached || 0,
        tokensSaved: cacheData?.total_tokens_saved || 0,
        costSaved: cacheData?.total_cost_saved || 0,
      },
      usage: {
        totalRequests,
        successRate: 98,
        errorRate: 2,
        modelDistribution: {
          'gpt-3.5-turbo': 60,
          'gpt-4o-mini': 30,
          'gpt-4o': 10,
        },
      },
    };
  };

  const getMetricCards = (): MetricCard[] => {
    if (!metrics) return [];

    return [
      {
        title: 'Latencia Promedio',
        value: `${metrics.latency.current}s`,
        change: metrics.latency.trend,
        changeLabel: 'vs anterior',
        icon: <Clock size={20} color={Colors.light.primary} />,
        status: metrics.latency.current <= metrics.latency.target ? 'success' : 'warning',
      },
      {
        title: 'Costo por Servicio',
        value: `$${metrics.cost.perService.toFixed(3)}`,
        change: metrics.cost.trend || -74,
        changeLabel: 'ahorro',
        icon: <DollarSign size={20} color={Colors.light.success} />,
        status: 'success',
      },
      {
        title: 'Precisión IA',
        value: `${metrics.accuracy.current}%`,
        change: metrics.accuracy.trend || 8,
        changeLabel: 'mejora',
        icon: <Target size={20} color={Colors.light.primary} />,
        status: metrics.accuracy.current >= metrics.accuracy.target ? 'success' : 'warning',
      },
      {
        title: 'Cache Hit Rate',
        value: `${metrics.cache.hitRate.toFixed(1)}%`,
        change: metrics.cache.hitRate,
        changeLabel: 'objetivo 40%',
        icon: <Zap size={20} color={Colors.light.warning} />,
        status: metrics.cache.hitRate >= 40 ? 'success' : 'warning',
      },
    ];
  };

  const renderMetricCard = (metric: MetricCard) => (
    <View key={metric.title} style={[styles.metricCard, styles[`card${metric.status}`]]}>
      <View style={styles.metricHeader}>
        {metric.icon}
        <Text style={styles.metricTitle}>{metric.title}</Text>
      </View>

      <Text style={styles.metricValue}>{metric.value}</Text>

      <View style={styles.metricChange}>
        {metric.change > 0 ? (
          <TrendingUp size={16} color={Colors.light.success} />
        ) : metric.change < 0 ? (
          <TrendingDown size={16} color={Colors.light.error} />
        ) : null}
        <Text
          style={[
            styles.metricChangeText,
            metric.change > 0 ? styles.changePositive : styles.changeNegative,
          ]}
        >
          {Math.abs(metric.change)}% {metric.changeLabel}
        </Text>
      </View>
    </View>
  );

  const renderModelDistribution = () => (
    <View style={styles.distributionCard}>
      <Text style={styles.sectionTitle}>Distribución de Modelos</Text>

      {Object.entries(metrics?.usage.modelDistribution || {}).map(([model, percentage]) => (
        <View key={model} style={styles.distributionRow}>
          <Text style={styles.modelName}>{model}</Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${percentage}%` }]} />
          </View>
          <Text style={styles.percentage}>{percentage}%</Text>
        </View>
      ))}
    </View>
  );

  const renderCostSavings = () => (
    <View style={styles.savingsCard}>
      <View style={styles.savingsHeader}>
        <DollarSign size={24} color={Colors.light.success} />
        <Text style={styles.savingsTitle}>Ahorros Totales</Text>
      </View>

      <View style={styles.savingsGrid}>
        <View style={styles.savingItem}>
          <Text style={styles.savingLabel}>Hoy</Text>
          <Text style={styles.savingValue}>${metrics?.cost.saved.toFixed(2) || '0.00'}</Text>
        </View>

        <View style={styles.savingItem}>
          <Text style={styles.savingLabel}>Mes</Text>
          <Text style={styles.savingValue}>${((metrics?.cost.saved || 0) * 30).toFixed(2)}</Text>
        </View>

        <View style={styles.savingItem}>
          <Text style={styles.savingLabel}>Tokens</Text>
          <Text style={styles.savingValue}>
            {(metrics?.cache.tokensSaved || 0).toLocaleString()}
          </Text>
        </View>
      </View>
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={() => {
            setRefreshing(true);
            loadMetrics();
          }}
        />
      }
    >
      {/* Time range selector */}
      <View style={styles.timeRangeContainer}>
        {(['today', 'week', 'month'] as const).map(range => (
          <TouchableOpacity
            key={range}
            style={[styles.timeRangeButton, timeRange === range && styles.timeRangeButtonActive]}
            onPress={() => setTimeRange(range)}
          >
            <Text style={[styles.timeRangeText, timeRange === range && styles.timeRangeTextActive]}>
              {range === 'today' ? 'Hoy' : range === 'week' ? 'Semana' : 'Mes'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Main metrics grid */}
      <View style={styles.metricsGrid}>{getMetricCards().map(renderMetricCard)}</View>

      {/* Model distribution */}
      {renderModelDistribution()}

      {/* Cost savings */}
      {renderCostSavings()}

      {/* Status indicators */}
      <View style={styles.statusContainer}>
        <View style={styles.statusItem}>
          <CheckCircle size={20} color={Colors.light.success} />
          <Text style={styles.statusText}>Sistema Funcionando Correctamente</Text>
        </View>

        {metrics && metrics.cache.hitRate < 40 && (
          <View style={styles.statusItem}>
            <AlertCircle size={20} color={Colors.light.warning} />
            <Text style={styles.statusText}>Optimizando rendimiento</Text>
          </View>
        )}
      </View>

      {/* Chart placeholder */}
      <View style={styles.chartCard}>
        <View style={styles.chartHeader}>
          <BarChart3 size={20} color={Colors.light.primary} />
          <Text style={styles.chartTitle}>Tendencia Semanal</Text>
        </View>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.chartPlaceholderText}>Gráfico de tendencias próximamente</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.backgroundLight,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: Colors.backgroundLight,
    alignItems: 'center',
  },
  timeRangeButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  timeRangeText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  timeRangeTextActive: {
    color: Colors.light.textLight,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
  },
  metricCard: {
    width: '48%',
    margin: '1%',
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.backgroundLight,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 12,
    color: Colors.textLight,
    marginLeft: 8,
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricChangeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  changePositive: {
    color: Colors.light.success,
  },
  changeNegative: {
    color: Colors.light.error,
  },
  distributionCard: {
    backgroundColor: Colors.light.background,
    margin: 8,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  distributionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  modelName: {
    fontSize: 12,
    color: Colors.text,
    width: 100,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.backgroundLight,
    borderRadius: 4,
    marginHorizontal: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 4,
  },
  percentage: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    width: 40,
    textAlign: 'right',
  },
  savingsCard: {
    backgroundColor: Colors.light.success + '10',
    margin: 8,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.success + '30',
  },
  savingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  savingsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  savingsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  savingItem: {
    alignItems: 'center',
  },
  savingLabel: {
    fontSize: 12,
    color: Colors.textLight,
    marginBottom: 4,
  },
  savingValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.success,
  },
  statusContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.light.background,
    margin: 8,
    borderRadius: 12,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  statusText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
  },
  chartCard: {
    backgroundColor: Colors.light.background,
    margin: 8,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  chartHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  chartPlaceholder: {
    height: 200,
    backgroundColor: Colors.backgroundLight,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartPlaceholderText: {
    color: Colors.textLight,
    fontSize: 14,
  },
});

export default AIMetricsDashboard;
