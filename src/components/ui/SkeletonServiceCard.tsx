import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useAnimationsEnabled } from '@/stores/whimsy-store';

interface SkeletonServiceCardProps {
  testID?: string;
}

export function SkeletonServiceCard({
  testID = 'skeleton-service-card',
}: SkeletonServiceCardProps) {
  const animationsEnabled = useAnimationsEnabled();
  const shimmerValue = useSharedValue(0);

  React.useEffect(() => {
    if (animationsEnabled) {
      shimmerValue.value = withRepeat(
        withSequence(withTiming(1, { duration: 1200 }), withTiming(0, { duration: 1200 })),
        -1,
        false
      );
    } else {
      shimmerValue.value = 0.7;
    }
  }, [shimmerValue, animationsEnabled]);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(shimmerValue.value, [0, 1], [0.3, 0.7]);
    return {
      opacity,
    };
  });

  const createSkeletonElement = (width: number | string, height: number, style?: ViewStyle) => (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: BeautyMinimalTheme.semantic.border.subtle,
          borderRadius: BeautyMinimalTheme.radius.sm,
        },
        shimmerStyle,
        style,
      ]}
    />
  );

  return (
    <View style={styles.container} testID={testID}>
      {/* Header Section */}
      <View style={styles.header}>
        {/* Date skeleton */}
        {createSkeletonElement(80, 16)}

        {/* Status badge skeleton */}
        {createSkeletonElement(60, 20, { borderRadius: BeautyMinimalTheme.radius.full })}
      </View>

      {/* Content Section */}
      <View style={styles.content}>
        {/* Service type skeleton */}
        {createSkeletonElement('70%', 18, { marginBottom: BeautyMinimalTheme.spacing.xs })}

        {/* Formula details skeleton */}
        {createSkeletonElement('90%', 14, { marginBottom: BeautyMinimalTheme.spacing.xs })}
        {createSkeletonElement('60%', 14, { marginBottom: BeautyMinimalTheme.spacing.sm })}

        {/* Tags skeleton */}
        <View style={styles.tagsRow}>
          {createSkeletonElement(45, 24, { borderRadius: BeautyMinimalTheme.radius.full })}
          {createSkeletonElement(55, 24, { borderRadius: BeautyMinimalTheme.radius.full })}
          {createSkeletonElement(35, 24, { borderRadius: BeautyMinimalTheme.radius.full })}
        </View>
      </View>

      {/* Footer Section */}
      <View style={styles.footer}>
        {/* Duration and price skeleton */}
        {createSkeletonElement(50, 14)}
        {createSkeletonElement(40, 14)}
      </View>

      {/* Feedback button skeleton */}
      <View style={styles.feedbackButtonContainer}>
        {createSkeletonElement(100, 28, { borderRadius: BeautyMinimalTheme.radius.full })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    ...BeautyMinimalTheme.shadows.sm,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  content: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  tagsRow: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.xs,
    flexWrap: 'wrap',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  feedbackButtonContainer: {
    alignItems: 'flex-end',
  },
});
