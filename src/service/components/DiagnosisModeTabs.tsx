import React, { memo } from 'react';
import { Text, TouchableOpacity, StyleSheet } from 'react-native';
import { getDiagnosisPhaseIcon } from '@/constants/hair-iconography';
import { BeautyCard } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useDiagnosisContext } from '@/service/contexts/DiagnosisContext';

const DiagnosisModeTabsComponent: React.FC = () => {
  const { diagnosisMethod, onUpdate } = useDiagnosisContext();
  return (
    <BeautyCard variant="default" style={styles.tabsContainer}>
      <TouchableOpacity
        style={[styles.tab, diagnosisMethod === 'ai' && styles.activeTab]}
        onPress={() => onUpdate({ diagnosisMethod: 'ai' })}
      >
        {(() => {
          const aiIcon = getDiagnosisPhaseIcon('ai-analyzing');
          const IconComponent = aiIcon.icon;
          return (
            <IconComponent
              size={16}
              color={
                diagnosisMethod === 'ai'
                  ? BeautyMinimalTheme.semantic.interactive.primary.default
                  : BeautyMinimalTheme.semantic.text.secondary
              }
            />
          );
        })()}
        <Text style={[styles.tabText, diagnosisMethod === 'ai' && styles.activeTabText]}>
          Análisis IA ✨
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, diagnosisMethod === 'manual' && styles.activeTab]}
        onPress={() => onUpdate({ diagnosisMethod: 'manual' })}
      >
        {(() => {
          const manualIcon = getDiagnosisPhaseIcon('damage-assessment');
          const IconComponent = manualIcon.icon;
          return (
            <IconComponent
              size={16}
              color={
                diagnosisMethod === 'manual'
                  ? BeautyMinimalTheme.semantic.interactive.primary.default
                  : BeautyMinimalTheme.semantic.text.secondary
              }
            />
          );
        })()}
        <Text style={[styles.tabText, diagnosisMethod === 'manual' && styles.activeTabText]}>
          Diagnóstico Manual
        </Text>
      </TouchableOpacity>
    </BeautyCard>
  );
};

// Export memoized component
export const DiagnosisModeTabs = memo(DiagnosisModeTabsComponent);

// Display name for debugging
DiagnosisModeTabs.displayName = 'DiagnosisModeTabs';

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    padding: BeautyMinimalTheme.spacing.xs,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: BeautyMinimalTheme.spacing.md,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: BeautyMinimalTheme.spacing.sm,
    borderRadius: BeautyMinimalTheme.radius.md,
  },
  activeTab: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    ...BeautyMinimalTheme.shadows.soft,
  },
  tabText: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  activeTabText: {
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
});
