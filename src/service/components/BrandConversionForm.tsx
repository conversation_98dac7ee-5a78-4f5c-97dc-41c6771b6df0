import React from 'react';
import { View, Text, StyleSheet, Switch, TextInput, TouchableOpacity } from 'react-native';
import { ChevronRight, Zap } from 'lucide-react-native';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import Colors from '@/constants/colors';
import { getTypographyStyle } from '@/constants/typography-system';

interface BrandConversionFormProps {
  conversionMode: boolean;
  onConversionModeChange: (mode: boolean) => void;
  originalFormula: string;
  onOriginalFormulaChange: (formula: string) => void;
  originalBrand: string;
  originalLine: string;
  selectedBrand: string;
  selectedLine: string;
  onSelectOriginalBrand: () => void;
  isGenerating: boolean;
  onGenerate: () => void;
}

export const BrandConversionForm: React.FC<BrandConversionFormProps> = ({
  conversionMode,
  onConversionModeChange,
  originalFormula,
  onOriginalFormulaChange,
  originalBrand,
  originalLine,
  selectedBrand,
  selectedLine,
  onSelectOriginalBrand,
  isGenerating,
  onGenerate,
}) => {
  return (
    <>
      <View style={[styles.conversionSection, conversionMode && styles.conversionSectionActive]}>
        <TouchableOpacity
          style={styles.conversionHeader}
          activeOpacity={0.7}
          onPress={() => onConversionModeChange(!conversionMode)}
        >
          <View style={styles.conversionTitleContainer}>
            <Text style={styles.conversionTitle}>Adaptar fórmula de otra marca</Text>
            <Text style={styles.conversionSubtitle}>Obtén el equivalente en tu marca</Text>
          </View>
          <Switch
            trackColor={{
              false: Colors.light.lightGray,
              true: Colors.light.primary,
            }}
            thumbColor={conversionMode ? Colors.light.primary : Colors.light.gray}
            ios_backgroundColor={Colors.light.lightGray}
            onValueChange={onConversionModeChange}
            value={conversionMode}
            style={styles.conversionSwitch}
          />
        </TouchableOpacity>

        {conversionMode && (
          <BeautyCard variant="default" style={styles.conversionContent}>
            {/* Formula Input First */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>¿Cuál es la fórmula?</Text>
              <TextInput
                style={[styles.input, styles.textArea, styles.conversionFormulaInput]}
                value={originalFormula}
                onChangeText={onOriginalFormulaChange}
                placeholder="Ej: 7.31 + 8.34 (2:1) • Oxidante 20 vol • 35 min"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <TouchableOpacity
              style={styles.conversionBrandContainer}
              onPress={onSelectOriginalBrand}
            >
              <Text style={styles.conversionLabel}>¿De qué marca es la fórmula?</Text>
              <View style={styles.conversionSelectContainer}>
                <Text style={styles.selectText}>{originalBrand || 'Seleccionar marca...'}</Text>
                <ChevronRight size={16} color={Colors.light.gray} />
              </View>
            </TouchableOpacity>

            {originalBrand && (
              <TouchableOpacity
                style={styles.conversionBrandContainer}
                onPress={onSelectOriginalBrand}
              >
                <Text style={styles.conversionLabel}>¿Qué línea o producto?</Text>
                <View style={styles.conversionSelectContainer}>
                  <Text style={styles.selectText}>{originalLine || 'Seleccionar línea...'}</Text>
                  <ChevronRight size={16} color={Colors.light.gray} />
                </View>
              </TouchableOpacity>
            )}

            {originalBrand && originalLine && (
              <View style={styles.conversionInfoBox}>
                <Text style={styles.conversionInfoText}>
                  ✨ Se adaptará a {selectedBrand || 'marca seleccionada'}{' '}
                  {selectedLine || 'línea seleccionada'}
                </Text>
                <Text style={styles.conversionInfoSubtext}>
                  Manteniendo el mismo resultado de color
                </Text>
              </View>
            )}
          </BeautyCard>
        )}
      </View>
      <BeautyButton
        title={
          conversionMode && originalBrand && originalLine
            ? 'Convertir y Generar Fórmula'
            : 'Calcular Fórmula Personalizada'
        }
        onPress={onGenerate}
        disabled={isGenerating}
        loading={isGenerating}
        variant="primary"
        size="lg"
        icon={Zap}
        fullWidth
        style={styles.aiButton}
      />
    </>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  label: {
    ...getTypographyStyle('bodyEmphasis'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  selectText: {
    fontSize: 15,
  },
  conversionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  conversionSectionActive: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  conversionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    minHeight: 60,
  },
  conversionTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  conversionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  conversionSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  conversionSwitch: {
    transform: [{ scale: 0.8 }],
  },
  conversionContent: {
    marginTop: 8,
  },
  conversionBrandContainer: {
    marginBottom: 16,
  },
  conversionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
  },
  conversionSelectContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  conversionFormulaInput: {
    minHeight: 140,
    paddingTop: 12,
  },
  conversionInfoBox: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary + '20',
  },
  conversionInfoText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  conversionInfoSubtext: {
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  aiButton: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
    shadowRadius: 8,
    elevation: 4,
  },
});
