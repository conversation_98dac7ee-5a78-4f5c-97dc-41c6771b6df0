import { estimateColorGrams } from '@/services/consumption-estimator';

describe('Consumption Estimator', () => {
  it('scales grams by length for toner_global', () => {
    expect(estimateColorGrams({ application: 'toner_global', hairLengthCm: 18 })).toBe(30);
    expect(estimateColorGrams({ application: 'toner_global', hairLengthCm: 30 })).toBe(45);
    expect(estimateColorGrams({ application: 'toner_global', hairLengthCm: 45 })).toBe(60);
    expect(estimateColorGrams({ application: 'toner_global', hairLengthCm: 60 })).toBe(75);
  });

  it('applies density and thickness multipliers', () => {
    const base = estimateColorGrams({ application: 'full', hairLengthCm: 35 }); // 60g base
    const highDenseThick = estimateColorGrams({
      application: 'full',
      hairLengthCm: 35,
      density: 'Alta',
      thickness: 'Grueso',
    });
    expect(highDenseThick).toBeGreaterThanOrEqual(base);
  });
});
