/**
 * Comprehensive tests for AI Analysis Store
 * Testing core functionality, error handling, and edge cases
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAIAnalysisStore } from '../../src/stores/ai-analysis-store';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { ImageProcessor } from '@/utils/image-processor';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';
import { useAuthStore } from '../../src/stores/auth-store';

// Mock dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      refreshSession: jest.fn(),
    },
    functions: {
      invoke: jest.fn(),
    },
  },
  getCurrentSalonId: jest.fn(),
}));
jest.mock('@/utils/image-processor');
jest.mock('@/utils/secure-image-upload');
jest.mock('../auth-store');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockImageProcessor = ImageProcessor as jest.Mocked<typeof ImageProcessor>;
const mockUploadAndAnonymizeImage = uploadAndAnonymizeImage as jest.MockedFunction<
  typeof uploadAndAnonymizeImage
>;
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;

// Mock getCurrentSalonId (already imported above)
const mockGetCurrentSalonId = getCurrentSalonId as jest.MockedFunction<typeof getCurrentSalonId>;

// Mock data
const mockAIAnalysisResult = {
  hairThickness: 'Medio',
  hairDensity: 'Alta',
  overallTone: 'Castaño',
  overallReflect: 'Dorado',
  averageLevel: 5,
  zoneAnalysis: {
    roots: {
      zone: 'roots',
      level: 4,
      tone: 'Castaño',
      reflect: 'Dorado',
      state: 'Natural',
      porosity: 'Media',
      elasticity: 'Buena',
      resistance: 'Alta',
      damage: 'Bajo',
      confidence: 0.92,
    },
    mids: {
      zone: 'mids',
      level: 5,
      tone: 'Castaño',
      reflect: 'Dorado',
      state: 'Natural',
      porosity: 'Media',
      elasticity: 'Buena',
      resistance: 'Media',
      damage: 'Bajo',
      confidence: 0.88,
    },
    ends: {
      zone: 'ends',
      level: 6,
      tone: 'Castaño',
      reflect: 'Dorado',
      state: 'Natural',
      porosity: 'Alta',
      elasticity: 'Regular',
      resistance: 'Media',
      damage: 'Medio',
      confidence: 0.85,
    },
  },
  overallCondition: 'Bueno',
  recommendations: ['Hidratar puntas', 'Mantener tratamiento'],
  overallConfidence: 0.88,
  analysisTimestamp: Date.now(),
};

const mockDesiredPhotoAnalysis = {
  photoId: 'photo-123',
  detectedLevel: 8,
  detectedTone: 'Rubio',
  detectedTechnique: 'balayage',
  detectedTones: ['Rubio', 'Dorado'],
  viabilityScore: 0.75,
  estimatedSessions: 2,
  requiredProcesses: ['decoloración', 'matizado'],
  confidence: 0.82,
};

describe('AI Analysis Store', () => {
  // Setup and teardown
  beforeEach(() => {
    // Reset store state
    useAIAnalysisStore.setState({
      isAnalyzing: false,
      analysisResult: null,
      analysisHistory: [],
      isAnalyzingDesiredPhoto: false,
      analyzingPhotoId: null,
      desiredPhotoAnalyses: {},
      privacyMode: true,
      settings: {
        autoFaceBlur: true,
        imageQualityThreshold: 60,
        privacyMode: true,
        saveAnalysisHistory: false,
      },
    });

    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mocks
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { access_token: 'test-token', user: { id: 'user-123' } } },
      error: null,
    });

    mockUseAuthStore.mockReturnValue({
      user: { id: 'user-123', salonId: 'salon-123' },
    } as any);

    mockGetCurrentSalonId.mockResolvedValue('salon-123');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('analyzeImage', () => {
    it('should analyze image successfully with AI', async () => {
      // Setup mocks
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          data: mockAIAnalysisResult,
        },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      // Execute
      await store.analyzeImage('test-image-uri');

      // Assertions
      const state = useAIAnalysisStore.getState();
      expect(state.isAnalyzing).toBe(false);
      expect(state.analysisResult).toEqual(
        expect.objectContaining({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          analysisTimestamp: expect.any(Number),
        })
      );
    });

    it('should handle image validation errors', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: false,
        issues: [{ severity: 'error', message: 'Imagen muy borrosa' }],
        suggestions: ['Tomar nueva foto con mejor iluminación'],
      });

      const store = useAIAnalysisStore.getState();

      // Use a local URI (not http/https) to trigger validation path
      await expect(store.analyzeImage('file:///bad-image-uri')).rejects.toThrow(
        /Error al procesar la imagen/
      );

      const state = useAIAnalysisStore.getState();
      expect(state.isAnalyzing).toBe(false);
      expect(state.analysisResult).toBeNull();
    });

    it('should handle network errors gracefully', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      mockSupabase.functions.invoke.mockRejectedValue(new Error('Network error'));

      const store = useAIAnalysisStore.getState();

      await expect(store.analyzeImage('test-image')).rejects.toThrow(/Error al procesar la imagen/);
    });

    it('should handle timeout errors', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      // Mock timeout
      const abortError = new Error('Request aborted');
      abortError.name = 'AbortError';
      mockSupabase.functions.invoke.mockRejectedValue(abortError);

      const store = useAIAnalysisStore.getState();

      await expect(store.analyzeImage('test-image')).rejects.toThrow(/tardó demasiado/);
    });

    it('should retry on OpenAI response errors', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      // First call fails, second succeeds
      mockSupabase.functions.invoke
        .mockResolvedValueOnce({
          data: { success: false, error: 'Invalid response from OpenAI' },
          error: null,
        })
        .mockResolvedValueOnce({
          data: { success: true, data: mockAIAnalysisResult },
          error: null,
        });

      const store = useAIAnalysisStore.getState();

      await store.analyzeImage('test-image');

      expect(mockSupabase.functions.invoke).toHaveBeenCalledTimes(2);

      const state = useAIAnalysisStore.getState();
      expect(state.analysisResult).toBeTruthy();
    });

    it('should handle session refresh on 401 error', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      // Mock 401 error first, then success
      mockSupabase.functions.invoke
        .mockResolvedValueOnce({
          data: null,
          error: { message: 'HTTP 401: Invalid token' },
        })
        .mockResolvedValueOnce({
          data: { success: true, data: mockAIAnalysisResult },
          error: null,
        });

      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: { session: { access_token: 'new-token' } },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      await store.analyzeImage('test-image');

      expect(mockSupabase.auth.refreshSession).toHaveBeenCalled();
      expect(mockSupabase.functions.invoke).toHaveBeenCalledTimes(2);
    });
  });

  describe('analyzeDesiredPhoto', () => {
    it('should analyze desired photo successfully', async () => {
      const clientId = 'client-123';
      const diagnosis = { averageLevel: 5, hairThickness: 'Medio' };

      mockUploadAndAnonymizeImage.mockResolvedValue({
        success: true,
        publicUrl: 'https://example.com/uploaded-image.jpg',
        filePath: 'path/to/image.jpg',
      });

      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          data: mockDesiredPhotoAnalysis,
        },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      const result = await store.analyzeDesiredPhoto(
        'photo-123',
        'local-image-uri',
        diagnosis,
        clientId
      );

      expect(result).toEqual(mockDesiredPhotoAnalysis);
      expect(mockUploadAndAnonymizeImage).toHaveBeenCalledWith('local-image-uri', {
        clientId,
        photoType: 'desired',
        onProgress: expect.any(Function),
      });

      const state = useAIAnalysisStore.getState();
      expect(state.desiredPhotoAnalyses['photo-123']).toEqual(mockDesiredPhotoAnalysis);
    });

    it('should handle public URL images without upload', async () => {
      const diagnosis = { averageLevel: 5 };
      const publicUrl = 'https://example.com/existing-image.jpg';

      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: mockDesiredPhotoAnalysis },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      const result = await store.analyzeDesiredPhoto('photo-123', publicUrl, diagnosis);

      expect(result).toEqual(mockDesiredPhotoAnalysis);
      expect(mockUploadAndAnonymizeImage).not.toHaveBeenCalled();
    });

    it('should handle upload failures gracefully', async () => {
      mockUploadAndAnonymizeImage.mockResolvedValue({
        success: false,
        error: 'Upload failed',
      });

      const store = useAIAnalysisStore.getState();

      const result = await store.analyzeDesiredPhoto('photo-123', 'local-uri', {}, 'client-123');

      expect(result).toBeNull();

      const state = useAIAnalysisStore.getState();
      expect(state.isAnalyzingDesiredPhoto).toBe(false);
    });

    it('should maintain backward compatibility with number diagnosis', async () => {
      const numericalDiagnosis = 5; // Old format

      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: mockDesiredPhotoAnalysis },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      await store.analyzeDesiredPhoto(
        'photo-123',
        'https://example.com/image.jpg',
        numericalDiagnosis
      );

      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('salonier-assistant', {
        body: {
          task: 'analyze_desired_look',
          payload: expect.objectContaining({
            currentLevel: 5,
          }),
        },
        headers: expect.any(Object),
        signal: expect.any(AbortSignal),
      });
    });
  });

  describe('Settings Management', () => {
    it('should update settings and persist to storage', async () => {
      const newSettings = {
        autoFaceBlur: false,
        imageQualityThreshold: 80,
        saveAnalysisHistory: true,
      };

      const store = useAIAnalysisStore.getState();

      await store.updateSettings(newSettings);

      const state = useAIAnalysisStore.getState();
      expect(state.settings).toEqual(expect.objectContaining(newSettings));
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'salonier-ai-settings',
        expect.stringContaining('false') // Should contain the updated autoFaceBlur setting
      );
    });

    it('should handle storage errors gracefully', async () => {
      (AsyncStorage.setItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

      const store = useAIAnalysisStore.getState();

      // Should not throw
      await expect(store.updateSettings({ autoFaceBlur: false })).resolves.toBeUndefined();

      // Settings should still be updated in memory
      const state = useAIAnalysisStore.getState();
      expect(state.settings.autoFaceBlur).toBe(false);
    });
  });

  describe('Analysis History', () => {
    it('should save analysis to history when enabled', async () => {
      // Enable history saving
      const store = useAIAnalysisStore.getState();
      await store.updateSettings({ saveAnalysisHistory: true });

      // Setup successful analysis
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');
      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: mockAIAnalysisResult },
        error: null,
      });

      await store.analyzeImage('test-image');

      const state = useAIAnalysisStore.getState();
      expect(state.analysisHistory).toHaveLength(1);
      expect(state.analysisHistory[0]).toEqual(
        expect.objectContaining({
          hairThickness: 'Medio',
          analysisTimestamp: expect.any(Number),
        })
      );
    });

    it('should not save to history when disabled', async () => {
      // Ensure history is disabled
      const store = useAIAnalysisStore.getState();
      await store.updateSettings({ saveAnalysisHistory: false });

      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');
      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: mockAIAnalysisResult },
        error: null,
      });

      await store.analyzeImage('test-image');

      const state = useAIAnalysisStore.getState();
      expect(state.analysisHistory).toHaveLength(0);
    });

    it('should clear analysis history', async () => {
      // Set some history first
      useAIAnalysisStore.setState({
        analysisHistory: [mockAIAnalysisResult],
      });

      const store = useAIAnalysisStore.getState();
      await store.clearAnalysisHistory();

      const state = useAIAnalysisStore.getState();
      expect(state.analysisHistory).toHaveLength(0);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('salonier-analysis-history');
    });

    it('should get analysis history', () => {
      const testHistory = [mockAIAnalysisResult];
      useAIAnalysisStore.setState({ analysisHistory: testHistory });

      const store = useAIAnalysisStore.getState();
      const history = store.getAnalysisHistory();

      expect(history).toEqual(testHistory);
    });
  });

  describe('State Management', () => {
    it('should clear analysis data', () => {
      // Set some data first
      useAIAnalysisStore.setState({
        analysisResult: mockAIAnalysisResult,
        desiredPhotoAnalyses: { 'photo-1': mockDesiredPhotoAnalysis },
        analyzingPhotoId: 'photo-1',
      });

      const store = useAIAnalysisStore.getState();
      store.clearAnalysis();

      const state = useAIAnalysisStore.getState();
      expect(state.analysisResult).toBeNull();
      expect(state.desiredPhotoAnalyses).toEqual({});
      expect(state.analyzingPhotoId).toBeNull();
    });

    it('should set privacy mode', () => {
      const store = useAIAnalysisStore.getState();
      store.setPrivacyMode(false);

      const state = useAIAnalysisStore.getState();
      expect(state.privacyMode).toBe(false);
    });

    it('should initialize with default settings', () => {
      const state = useAIAnalysisStore.getState();
      expect(state.settings).toEqual({
        autoFaceBlur: true,
        imageQualityThreshold: 60,
        privacyMode: true,
        saveAnalysisHistory: false,
      });
    });
  });

  describe('Error Edge Cases', () => {
    it('should handle empty analysis response', async () => {
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: null },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      // Use local URI to trigger validation path
      await expect(store.analyzeImage('file:///test-image')).rejects.toThrow(
        /Error al procesar la imagen/
      );
    });

    it('should handle incomplete analysis data with defaults', async () => {
      const incompleteResult = {
        hairThickness: 'Medio',
        // Missing some fields, but function should fill with defaults
      };

      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, data: incompleteResult },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      await store.analyzeImage('test-image');

      const state = useAIAnalysisStore.getState();
      expect(state.analysisResult).toBeTruthy();
      expect(state.analysisResult?.hairThickness).toBe('Medio');
      expect(state.analysisResult?.averageLevel).toBe(6); // Default value
    });

    it('should handle no session error', async () => {
      // Need to setup the mocks first
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      // Then override session to return null
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      // Use local URI to trigger validation path
      await expect(store.analyzeImage('file:///test-image')).rejects.toThrow(
        /Error al procesar la imagen/
      );
    });

    it('should handle missing salon ID', async () => {
      // Setup basic mocks first
      mockImageProcessor.validateQuality.mockResolvedValue({
        valid: true,
        issues: [],
        suggestions: [],
      });
      mockImageProcessor.compressForAI.mockResolvedValue('base64-data');

      // Mock session with valid session but no salon ID
      mockSupabase.auth.getSession.mockResolvedValue({
        data: {
          session: {
            access_token: 'test-token',
            user: { id: 'user-123' },
          },
        },
        error: null,
      });

      // Mock auth store to return user without salon ID
      mockUseAuthStore.mockReturnValue({
        user: { id: 'user-123', user_metadata: {} },
        initializeAuth: jest.fn().mockResolvedValue(undefined),
      } as any);

      // Mock getCurrentSalonId to return null both times
      mockGetCurrentSalonId
        .mockResolvedValueOnce(null) // First call
        .mockResolvedValueOnce(null); // Second call after repair attempt

      const store = useAIAnalysisStore.getState();

      // Use local URI to trigger validation path
      await expect(store.analyzeImage('file:///test-image')).rejects.toThrow(
        /Error al procesar la imagen/
      );
    });
  });

  describe('URL Management', () => {
    it('should handle public URL errors gracefully', async () => {
      const publicUrl = 'https://example.com/image.jpg';

      // Mock failed analysis for public URL
      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: false, error: 'Analysis failed' },
        error: null,
      });

      const store = useAIAnalysisStore.getState();

      await expect(store.analyzeImage(publicUrl)).rejects.toThrow(/Error al procesar la imagen/);

      const state = useAIAnalysisStore.getState();
      expect(state.isAnalyzing).toBe(false);
    });
  });
});
