# Tasks: Auditoría Completa de Código y Corrección de Errores

**Input**: Design documents from `/specs/003-quiero-hacer-una/`
**Prerequisites**: plan.md (required), research.md, data-model.md, contracts/

## Execution Flow (main)

```
1. Load plan.md from feature directory
   → Extract: TypeScript 5.9.2, React Native with Expo 54.0.0, ESLint 9.32.0
   → Structure: React Native mobile app with existing codebase
2. Load design documents:
   → data-model.md: AuditMetrics, AuditConfig, AuditReport entities
   → contracts/: audit-api.yaml with 5 endpoints
   → research.md: ESLint optimization, TypeScript safety decisions
3. Generate tasks by category:
   → Setup: audit infrastructure, baseline measurement
   → Tests: contract tests for audit API, integration tests
   → Core: audit services, metrics collection, fix application
   → Integration: reporting, configuration management
   → Polish: performance validation, documentation
4. Apply task rules:
   → Different files = mark [P] for parallel execution
   → Manual fixes = sequential to avoid conflicts
   → Tests before implementation (TDD)
5. Number tasks sequentially (T001-T035)
6. Target: Resolve 111 ESLint warnings, achieve code quality goals
```

## Format: `[ID] [P?] Description`

- **[P]**: Can run in parallel (different files, no dependencies)
- Include exact file paths in descriptions

## Path Conventions

- **React Native project**: root level configuration, `/components`, `/services`, `/hooks`
- Configuration files: `.eslintrc.js`, `.prettierrc`, `tsconfig.json`
- Test files: `__tests__/`, `/tests/`

## Phase 3.1: Setup and Baseline Assessment

- [x] **T001** Create audit infrastructure directory `scripts/audit/` for audit utilities
- [ ] **T002** [P] Install additional audit dependencies (npm audit-ci, @typescript-eslint/parser latest)
- [x] **T003** [P] Create baseline metrics collection script in `scripts/audit/collect-baseline.ts`
- [ ] **T004** [P] Create audit configuration manager in `scripts/audit/audit-config.ts`
- [x] **T005** Run comprehensive baseline audit and save results to `audit-reports/baseline-$(date).json`

## Phase 3.2: Tests First (TDD) ⚠️ MUST COMPLETE BEFORE 3.3

**CRITICAL: These tests MUST be written and MUST FAIL before ANY implementation**

- [ ] **T006** [P] Contract test for audit scan endpoint in `__tests__/audit/audit-scan.test.ts`
- [ ] **T007** [P] Contract test for audit fix endpoint in `__tests__/audit/audit-fix.test.ts`
- [ ] **T008** [P] Contract test for audit report retrieval in `__tests__/audit/audit-report.test.ts`
- [ ] **T009** [P] Contract test for metrics endpoint in `__tests__/audit/audit-metrics.test.ts`
- [ ] **T010** [P] Contract test for dependency scan in `__tests__/audit/dependency-audit.test.ts`
- [ ] **T011** [P] Integration test for ESLint fix workflow in `__tests__/integration/eslint-fix.test.ts`
- [ ] **T012** [P] Integration test for TypeScript type fixes in `__tests__/integration/typescript-fix.test.ts`
- [ ] **T013** [P] Integration test for style extraction in `__tests__/integration/style-extraction.test.ts`

## Phase 3.3: Core Implementation (ONLY after tests are failing)

### Data Models and Types

- [ ] **T014** [P] AuditMetrics type definitions in `types/audit.ts`
- [ ] **T015** [P] AuditConfig interface in `types/audit-config.ts`
- [ ] **T016** [P] AuditReport and IssueResolution types in `types/audit-report.ts`

### Core Audit Services

- [ ] **T017** [P] ESLint scanner service in `services/audit/eslint-scanner.ts`
- [ ] **T018** [P] Prettier formatter service in `services/audit/prettier-service.ts`
- [ ] **T019** [P] TypeScript analyzer service in `services/audit/typescript-analyzer.ts`
- [ ] **T020** [P] React Hooks dependency analyzer in `services/audit/hooks-analyzer.ts`
- [ ] **T021** [P] Style extraction service in `services/audit/style-extractor.ts`
- [ ] **T022** [P] Security vulnerability scanner in `services/audit/security-scanner.ts`

### Fix Application Services

- [ ] **T023** ESLint auto-fix service in `services/audit/eslint-fixer.ts` (sequential - modifies files)
- [ ] **T024** TypeScript type replacement service in `services/audit/type-fixer.ts` (sequential - modifies files)
- [ ] **T025** Hooks dependency fix service in `services/audit/hooks-fixer.ts` (sequential - modifies files)
- [ ] **T026** Console statement removal service in `services/audit/console-cleaner.ts` (sequential - modifies files)

## Phase 3.4: Integration and Orchestration

- [ ] **T027** Main audit orchestrator in `services/audit/audit-orchestrator.ts`
- [ ] **T028** Audit report generator in `services/audit/report-generator.ts`
- [ ] **T029** Configuration validator in `services/audit/config-validator.ts`
- [ ] **T030** File backup and restore utility in `services/audit/backup-manager.ts`

## Phase 3.5: Manual Code Quality Fixes

### High-Priority Manual Fixes (Based on lint-output.txt analysis)

- [x] **T031** Fix React Hooks exhaustive-deps warnings in `hooks/useStepTimer.ts` and `hooks/useStepValidation.ts`
- [ ] **T032** Replace TypeScript 'any' types in `components/formulation/instructions/steps/ApplicationStep.tsx`
- [x] **T033** Extract inline styles in `components/hair/HairLevelIndicator.tsx` to StyleSheet
- [ ] **T034** Fix React Hooks dependencies in `examples/RefactoredInstructionsFlow.tsx`
- [ ] **T035** Remove console statements from production code in `components/formulation/InstructionsFlowWrapper.tsx`

## Phase 3.6: Validation and Polish

- [ ] **T036** [P] Performance impact validation script in `scripts/audit/performance-validator.ts`
- [ ] **T037** [P] Bundle size analysis in `scripts/audit/bundle-analyzer.ts`
- [ ] **T038** [P] Test coverage validation in `scripts/audit/coverage-validator.ts`
- [ ] **T039** Run complete audit suite and generate final report
- [ ] **T040** [P] Update project documentation in `docs/code-quality-standards.md`
- [ ] **T041** [P] Create audit automation script in `scripts/run-quality-audit.sh`
- [ ] **T042** Validate Husky pre-commit hooks still function correctly

## Dependencies

**Setup Dependencies**:

- T001-T005 must complete before any other phases

**Test Dependencies**:

- T006-T013 must complete and FAIL before T014-T035
- All contract tests (T006-T010) can run in parallel
- All integration tests (T011-T013) can run in parallel

**Implementation Dependencies**:

- T014-T016 (types) before T017-T022 (services)
- T017-T022 (scanners) before T023-T026 (fixers)
- T023-T026 (fixers) must run sequentially (file conflicts)
- T027-T030 (orchestration) after T017-T026

**Manual Fix Dependencies**:

- T031-T035 must run sequentially to avoid merge conflicts
- T031-T035 after T023-T026 (automated fixes)

**Validation Dependencies**:

- T036-T042 after all implementation tasks complete

## Parallel Execution Examples

### Contract Tests (Phase 3.2)

```bash
# Launch T006-T010 together:
Task: "Contract test for audit scan endpoint in __tests__/audit/audit-scan.test.ts"
Task: "Contract test for audit fix endpoint in __tests__/audit/audit-fix.test.ts"
Task: "Contract test for audit report retrieval in __tests__/audit/audit-report.test.ts"
Task: "Contract test for metrics endpoint in __tests__/audit/audit-metrics.test.ts"
Task: "Contract test for dependency scan in __tests__/audit/dependency-audit.test.ts"
```

### Core Services (Phase 3.3)

```bash
# Launch T017-T022 together:
Task: "ESLint scanner service in services/audit/eslint-scanner.ts"
Task: "Prettier formatter service in services/audit/prettier-service.ts"
Task: "TypeScript analyzer service in services/audit/typescript-analyzer.ts"
Task: "React Hooks dependency analyzer in services/audit/hooks-analyzer.ts"
Task: "Style extraction service in services/audit/style-extractor.ts"
Task: "Security vulnerability scanner in services/audit/security-scanner.ts"
```

### Validation Tasks (Phase 3.6)

```bash
# Launch T036-T038, T040-T041 together:
Task: "Performance impact validation script in scripts/audit/performance-validator.ts"
Task: "Bundle size analysis in scripts/audit/bundle-analyzer.ts"
Task: "Test coverage validation in scripts/audit/coverage-validator.ts"
Task: "Update project documentation in docs/code-quality-standards.md"
Task: "Create audit automation script in scripts/run-quality-audit.sh"
```

## Success Criteria Validation

### Quantitative Targets

- [ ] ESLint warnings reduced from 111 to ≤10
- [ ] Zero ESLint errors maintained
- [ ] 100% Prettier compliance achieved
- [ ] TypeScript 'any' usage ≤5% of total annotations
- [ ] Zero console.log statements in production code
- [ ] Bundle size increase ≤5%
- [ ] Test coverage maintained at current levels

### Quality Gates

- [ ] All automated tests pass
- [ ] TypeScript compilation succeeds without errors
- [ ] Husky pre-commit hooks execute successfully
- [ ] Application functionality preserved (no regressions)
- [ ] Performance benchmarks within acceptable ranges

## Notes

- [P] tasks = different files, no dependencies, can run in parallel
- Manual fixes (T031-T035) must be sequential to avoid conflicts
- Verify all tests fail before implementing corresponding services
- Commit after each task completion
- Create backup before running T031-T035 (manual fixes)
- Target: Transform 111 warnings into clean, maintainable code

## Task Generation Rules Applied

1. **From Contracts (audit-api.yaml)**:
   - 5 endpoints → 5 contract test tasks (T006-T010) [P]
   - Each endpoint → corresponding service implementation

2. **From Data Model**:
   - 6 entities → 3 type definition tasks (T014-T016) [P]
   - Relationships → orchestration service tasks (T027-T030)

3. **From Research Decisions**:
   - ESLint optimization → scanner + fixer services (T017, T023)
   - TypeScript safety → analyzer + fixer services (T019, T024)
   - Style management → extractor service (T021)

4. **From Baseline Analysis**:
   - 111 lint warnings → specific manual fix tasks (T031-T035)
   - Performance concerns → validation tasks (T036-T038) [P]

## Validation Checklist

_Verified before task execution_

- [x] All contracts have corresponding tests (T006-T010)
- [x] All entities have type definition tasks (T014-T016)
- [x] All tests come before implementation (T006-T013 → T014+)
- [x] Parallel tasks truly independent (different files)
- [x] Each task specifies exact file path
- [x] No [P] task modifies same file as another [P] task
