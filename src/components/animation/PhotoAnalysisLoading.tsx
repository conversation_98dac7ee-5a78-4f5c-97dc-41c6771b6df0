/**
 * PHOTO ANALYSIS LOADING COMPONENT
 *
 * Specialized loading animation for hair photo analysis
 * Shows professional camera and analysis imagery
 * Integrates with BeautyMinimalTheme and haptic feedback
 */

import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  withDelay,
  interpolate,
  interpolateColor,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Camera, Focus, Zap, CheckCircle } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { MicroInteractions } from '@/constants/micro-interactions';

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

export type PhotoAnalysisStage =
  | 'capturing'
  | 'processing'
  | 'analyzing-color'
  | 'analyzing-texture'
  | 'finalizing'
  | 'complete';

interface PhotoAnalysisLoadingProps {
  stage: PhotoAnalysisStage;
  progress?: number; // 0-100
  onStageComplete?: (stage: PhotoAnalysisStage) => void;
  showProgressText?: boolean;
}

const STAGE_CONFIG = {
  capturing: {
    message: 'Capturando imagen capilar...',
    icon: Camera,
    color: BeautyMinimalTheme.beautyColors.sage[500],
    duration: 2000,
  },
  processing: {
    message: 'Procesando imagen...',
    icon: Focus,
    color: BeautyMinimalTheme.beautyColors.amethyst[600],
    duration: 1500,
  },
  'analyzing-color': {
    message: 'Analizando pigmentación y tono...',
    icon: Focus,
    color: BeautyMinimalTheme.beautyColors.rose[500],
    duration: 2500,
  },
  'analyzing-texture': {
    message: 'Evaluando estructura y porosidad...',
    icon: Focus,
    color: BeautyMinimalTheme.beautyColors.sage[600],
    duration: 2000,
  },
  finalizing: {
    message: 'Generando diagnóstico profesional...',
    icon: Zap,
    color: BeautyMinimalTheme.beautyColors.amethyst[500],
    duration: 1800,
  },
  complete: {
    message: 'Análisis completado',
    icon: CheckCircle,
    color: BeautyMinimalTheme.beautyColors.mint,
    duration: 1000,
  },
} as const;

/**
 * CAMERA FOCUS SCANNING ANIMATION
 * Simulates professional camera autofocus scanning
 */
const CameraFocusScanning: React.FC<{
  stage: PhotoAnalysisStage;
  size: number;
}> = ({ stage, size }) => {
  const focusBox1 = useSharedValue(0);
  const focusBox2 = useSharedValue(0);
  const scanLine = useSharedValue(0);
  const flashEffect = useSharedValue(0);

  const stageConfig = STAGE_CONFIG[stage];

  useEffect(() => {
    // Reset animations when stage changes
    focusBox1.value = 0;
    focusBox2.value = 0;
    scanLine.value = 0;
    flashEffect.value = 0;

    if (stage === 'capturing') {
      // Camera capture focus boxes
      focusBox1.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 800, easing: Easing.out(Easing.quad) }),
          withTiming(0.3, { duration: 400 })
        ),
        3, // Limited repeats for capturing
        false
      );

      focusBox2.value = withRepeat(
        withDelay(
          400,
          withSequence(
            withTiming(1, { duration: 600, easing: Easing.out(Easing.quad) }),
            withTiming(0.3, { duration: 300 })
          )
        ),
        3,
        false
      );

      // Flash effect when capturing
      flashEffect.value = withDelay(
        1500,
        withSequence(withTiming(1, { duration: 100 }), withTiming(0, { duration: 200 }))
      );
    } else if (
      stage === 'processing' ||
      stage === 'analyzing-color' ||
      stage === 'analyzing-texture'
    ) {
      // Continuous scanning for analysis stages
      scanLine.value = withRepeat(
        withTiming(1, {
          duration: stageConfig.duration / 2,
          easing: Easing.inOut(Easing.quad),
        }),
        -1,
        true
      );

      focusBox1.value = withRepeat(
        withSequence(withTiming(1, { duration: 1000 }), withTiming(0.5, { duration: 500 })),
        -1,
        false
      );
    } else if (stage === 'finalizing') {
      // Finalizing with gentle pulse
      focusBox1.value = withRepeat(withSpring(1, MicroInteractions.springs.gentle), -1, true);
    } else if (stage === 'complete') {
      // Success animation
      focusBox1.value = withSequence(
        withSpring(1.2, MicroInteractions.springs.bouncy),
        withSpring(1, MicroInteractions.springs.tight)
      );
    }
  }, [stage, stageConfig.duration, focusBox1, focusBox2, scanLine, flashEffect]);

  const focusBox1Style = useAnimatedStyle(() => ({
    opacity: focusBox1.value,
    transform: [{ scale: interpolate(focusBox1.value, [0, 1], [0.8, 1.1]) }],
    borderColor: interpolateColor(
      focusBox1.value,
      [0, 1],
      [BeautyMinimalTheme.neutrals.mist, stageConfig.color]
    ),
  }));

  const focusBox2Style = useAnimatedStyle(() => ({
    opacity: focusBox2.value,
    transform: [{ scale: interpolate(focusBox2.value, [0, 1], [1.0, 1.3]) }],
  }));

  const scanLineStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: interpolate(scanLine.value, [0, 1], [-size * 0.4, size * 0.4]),
      },
    ],
    opacity: interpolate(scanLine.value, [0, 0.2, 0.8, 1], [0, 1, 1, 0]),
  }));

  const flashStyle = useAnimatedStyle(() => ({
    opacity: flashEffect.value,
  }));

  const IconComponent = stageConfig.icon;

  return (
    <View style={[styles.cameraContainer, { width: size, height: size }]}>
      {/* Focus boxes */}
      <Animated.View style={[styles.focusBox, focusBox1Style]} />
      {stage === 'capturing' && <Animated.View style={[styles.focusBoxOuter, focusBox2Style]} />}

      {/* Scanning line for analysis stages */}
      {(stage === 'processing' || stage.startsWith('analyzing')) && (
        <Animated.View
          style={[styles.scanLine, { backgroundColor: stageConfig.color }, scanLineStyle]}
        />
      )}

      {/* Center icon */}
      <View style={styles.iconContainer}>
        <IconComponent size={size * 0.3} color={stageConfig.color} />
      </View>

      {/* Flash effect */}
      <Animated.View style={[styles.flashOverlay, flashStyle]} />
    </View>
  );
};

/**
 * PROGRESS RING ANIMATION
 * Professional circular progress indicator
 */
const ProgressRing: React.FC<{
  progress: number;
  size: number;
  color: string;
}> = ({ progress, size, color }) => {
  const progressValue = useSharedValue(0);

  useEffect(() => {
    progressValue.value = withTiming(progress / 100, {
      duration: MicroInteractions.durations.normal,
      easing: MicroInteractions.easing.gentle,
    });
  }, [progress, progressValue]);

  const progressStyle = useAnimatedStyle(() => {
    const strokeDasharray = size * Math.PI;
    const strokeDashoffset = strokeDasharray * (1 - progressValue.value);

    return {
      strokeDashoffset,
      stroke: color,
    };
  });

  return (
    <View style={[styles.progressRingContainer, { width: size, height: size }]}>
      {}
      <Animated.View
        style={[
          styles.progressRing,
          progressStyle,
          styles.progressRingDynamic,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: BeautyMinimalTheme.neutrals.mist,
          },
        ]}
      />
    </View>
  );
};

/**
 * MAIN PHOTO ANALYSIS LOADING COMPONENT
 */
export const PhotoAnalysisLoading: React.FC<PhotoAnalysisLoadingProps> = ({
  stage,
  progress,
  onStageComplete,
  showProgressText = true,
}) => {
  const fadeIn = useSharedValue(0);
  const stageConfig = STAGE_CONFIG[stage];
  const hapticTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Fade in animation
    fadeIn.value = withSpring(1, MicroInteractions.springs.gentle);

    // Haptic feedback on stage change
    if (stage === 'capturing') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } else if (stage === 'complete') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Set up stage completion timeout
    if (onStageComplete && stage !== 'complete') {
      hapticTimeoutRef.current = setTimeout(() => {
        onStageComplete(stage);
      }, stageConfig.duration);
    }

    return () => {
      if (hapticTimeoutRef.current) {
        clearTimeout(hapticTimeoutRef.current);
      }
    };
  }, [stage, onStageComplete, stageConfig.duration, fadeIn]);

  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ scale: fadeIn.value }],
  }));

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      {/* Main animation */}
      <CameraFocusScanning stage={stage} size={80} />

      {/* Progress ring if progress is provided */}
      {typeof progress === 'number' && (
        <View style={styles.progressContainer}>
          <ProgressRing progress={progress} size={100} color={stageConfig.color} />
        </View>
      )}

      {/* Stage message */}
      <Text style={styles.stageMessage}>{stageConfig.message}</Text>

      {/* Progress text */}
      {showProgressText && typeof progress === 'number' && (
        <Text style={styles.progressText}>{Math.round(progress)}% completado</Text>
      )}

      {/* Professional subtitle */}
      <Text style={styles.subtitle}>✨ Análisis capilar profesional con IA</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.xl,
  },

  // Camera focus animation
  cameraContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  focusBox: {
    position: 'absolute',
    width: '80%',
    height: '80%',
    borderWidth: 2,
    borderRadius: 8,
    backgroundColor: BeautyMinimalTheme.semantic.transparency.neutral.overlay,
  },
  focusBoxOuter: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
    borderRadius: 12,
    backgroundColor: BeautyMinimalTheme.semantic.transparency.neutral.overlay,
  },
  scanLine: {
    position: 'absolute',
    width: '90%',
    height: 2,
    borderRadius: 1,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  flashOverlay: {
    position: 'absolute',
    width: '120%',
    height: '120%',
    borderRadius: 50,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    zIndex: 1,
  },

  // Progress ring
  progressContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRing: {
    backgroundColor: BeautyMinimalTheme.semantic.transparency.neutral.overlay,
  },
  progressRingDynamic: {
    borderWidth: 3,
  },

  // Text styles
  stageMessage: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginTop: BeautyMinimalTheme.spacing.md,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  progressText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  subtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default PhotoAnalysisLoading;
