import { FormulationStep, ProductMix, Formulation } from '@/types/formulation';
import { getBrandPack } from '@/data/brand-packs';
import { COLORIMETRY_LAWS } from '@/utils/professional-colorimetry';
import { HairZone, HairState, ZoneColorAnalysis } from '@/types/hair-diagnosis';

/**
 * Build a plain-text material list from structured steps (for cost parsing)
 */
export const stringifySteps = (steps?: FormulationStep[] | null): string => {
  if (!steps || steps.length === 0) return '';
  const lines: string[] = [];
  steps.forEach(step => {
    if (step.mix && step.mix.length > 0) {
      step.mix.forEach(m => {
        if (!m || !m.productName) return;
        const qty = typeof m.quantity === 'number' ? m.quantity : Number(m.quantity) || 0;
        const unit = m.unit || 'ml';
        lines.push(`- ${m.productName} (${qty}${unit})`);
      });
    }
  });
  return lines.join('\n');
};

/**
 * Enforce safer developer for toning steps (10 vol or demi)
 */
export const enforceToningSafety = (
  form: Formulation | null,
  _brand: string,
  _line: string
): Formulation | null => {
  if (!form || !form.steps) return form;
  const toned = { ...form, steps: form.steps.map(s => ({ ...s })) } as Formulation;
  toned.steps.forEach(step => {
    const text = `${step.stepTitle || ''} ${step.instructions || ''}`.toLowerCase();
    const isToning =
      text.includes('tonaliz') ||
      text.includes(' tono ') ||
      text.includes('depositar') ||
      text.includes('gloss');
    if (isToning) {
      if (step.mix && step.mix.length > 0) {
        step.mix = step.mix.map(m => {
          const lname = m.productName.toLowerCase();
          if (
            lname.includes('oxidante') ||
            lname.includes('developer') ||
            lname.includes('welloxon')
          ) {
            let newName = m.productName.replace(/\b(10|13|15|18|20|30|40)\s*vol\b/gi, '10 vol');
            if (!/vol/i.test(newName)) newName = `${m.productName} 10 vol`;
            return { ...m, productName: newName };
          }
          return m;
        });
      }
    }
  });
  return toned;
};

/**
 * Ensure developer is present according to default ratio when missing
 */
export const ensureDeveloperPresent = (
  formulation: Formulation,
  selectedBrand: string,
  selectedLine: string
): Formulation => {
  try {
    const packRatio = (getBrandPack(selectedBrand, selectedLine).defaultMixRatio || '1:1')
      .toString()
      .trim();
    const [left, right] = packRatio.split(':').map(v => parseFloat(v || '1'));
    const ratio = right / (left || 1);

    const updatedSteps = (formulation.steps || []).map(step => {
      if (!step.mix || step.mix.length === 0) return step;
      const hasDeveloper = step.mix.some(m =>
        /oxidante|developer|welloxon|peróxido|peroxido/i.test(m.productName)
      );
      const colorItems = step.mix.filter(
        m => !/oxidante|developer|welloxon|peróxido|peroxido/i.test(m.productName)
      );
      if (!hasDeveloper && colorItems.length > 0) {
        const colorTotal = colorItems.reduce((a, b) => a + (b.quantity || 0), 0);
        const devAmount = Math.round(colorTotal * ratio);
        const devName = `${selectedBrand} Oxidante ${getBrandPack(selectedBrand, selectedLine).defaultDeveloper || 20} vol`;
        const developerItem: ProductMix = {
          productId: `auto-dev-${Date.now()}`,
          productName: devName,
          quantity: devAmount > 0 ? devAmount : Math.max(30, Math.round(30 * ratio)),
          unit: 'ml',
        };
        return { ...step, mix: [...step.mix, developerItem] };
      }
      return step;
    });

    return { ...formulation, steps: updatedSteps };
  } catch {
    return formulation;
  }
};

/**
 * Ensure pre-lightening step when needed (dyed hair going lighter without a bleach step)
 */
export const ensurePreLighteningStep = (
  formulation: Formulation,
  selectedBrand: string,
  selectedLine: string,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
  currentLevel: number,
  targetLevel: number
): Formulation => {
  try {
    const rootsZone = zoneColorAnalysis[HairZone.ROOTS];
    const rootsState = (rootsZone as Partial<ZoneColorAnalysis>)?.state as HairState | undefined;
    const goingLighter = targetLevel > currentLevel;
    const hasBleachStep = (formulation.steps || []).some(s =>
      `${s.stepTitle} ${s.instructions}`.toLowerCase().match(/decolor|aclar(a|ado)|bleach|lighten/)
    );

    if (goingLighter && rootsState && rootsState !== HairState.NATURAL && !hasBleachStep) {
      const pack = getBrandPack(selectedBrand, selectedLine);
      const liftHint = targetLevel - currentLevel;
      const devVol = liftHint >= 3 ? 30 : 20;
      const preStep = {
        stepNumber: 1,
        stepTitle: 'Sesión 1: Decoloración controlada',
        instructions:
          'Preparar polvo decolorante con oxidante. Aplicar con control en particiones finas. Revisar cada 10 minutos. No exceder el nivel seguro para la fibra.',
        mix: [
          {
            productId: `lightener-${Date.now()}`,
            productName: `${selectedBrand} Aclarante/Polvo decolorante`,
            quantity: 30,
            unit: 'gr',
          },
          {
            productId: `dev-pre-${Date.now()}`,
            productName: `${selectedBrand} Oxidante ${devVol} vol`,
            quantity: 45,
            unit: 'ml',
          },
        ],
        processingTime: pack.techniqueTimes?.highlift || 45,
      } as FormulationStep;

      const renumbered = [preStep, ...formulation.steps].map((s, i) => ({
        ...s,
        stepNumber: i + 1,
        stepTitle: s.stepTitle?.toLowerCase().includes('sesión')
          ? s.stepTitle
          : i === 0
            ? s.stepTitle
            : `Sesión 1: ${s.stepTitle}`,
      }));

      return {
        ...formulation,
        steps: renumbered,
        warnings: Array.from(
          new Set([
            ...(formulation.warnings || []),
            'Color no levanta color: se añadió fase de decoloración',
          ])
        ),
      };
    }
  } catch {
    // no-op
  }
  return formulation;
};

/**
 * Ensure a consistent post-treatment step exists at the end
 */
export const ensurePostTreatmentStep = (
  formulation: Formulation,
  selectedBrand: string
): Formulation => {
  try {
    const hasPost = (formulation.steps || []).some(s =>
      `${s.stepTitle}`.toLowerCase().match(/post(\s|-)tratamiento|tratamiento final|sellado/)
    );
    if (!hasPost) {
      const brandLc = selectedBrand.toLowerCase();
      const brandHints = brandLc.includes('wella')
        ? 'Sugerencias: WellaPlex Nº2 o ColorMotion+ Structure+ Mask.'
        : brandLc.includes('schwarzkopf')
          ? 'Sugerencias: Fibreplex Nº2 o BC pH 4.5 Color Freeze Treatment.'
          : brandLc.includes("l'oreal") || brandLc.includes("l'oréal")
            ? 'Sugerencias: Metal Detox Mask o Serie Expert Vitamino Color.'
            : brandLc.includes('goldwell')
              ? 'Sugerencias: BondPro+ Step 2 o Dualsenses Color 60s Treatment.'
              : 'Sugerencias: tratamiento reconstructor + sellador ácido pH 3.5–4.5.';
      const postStep = {
        stepNumber: (formulation.steps?.length || 0) + 1,
        stepTitle: 'Post‑tratamiento y sellado',
        instructions: `Enjuagar con agua templada. Aplicar tratamiento reconstructor y sellador ácido de pH. Enfriar 2–3 min y enjuagar. Secar y evaluar brillo/igualación. ${brandHints}`,
        processingTime: 10,
      } as FormulationStep;
      return { ...formulation, steps: [...(formulation.steps || []), postStep] };
    }
  } catch {
    // no-op
  }
  return formulation;
};

/**
 * Apply brand pack defaults if structured data misses times
 */
export const applyBrandDefaults = (
  formulation: Formulation | null,
  selectedBrand: string,
  selectedLine: string
): Formulation | null => {
  if (!formulation?.steps || formulation.steps.length === 0) return formulation;

  const pack = getBrandPack(selectedBrand, selectedLine);
  const defaultTime =
    pack.techniqueTimes?.fullColor ||
    pack.techniqueTimes?.toner ||
    pack.techniqueTimes?.highlift ||
    35;

  return {
    ...formulation,
    steps: formulation.steps.map(s => ({
      ...s,
      processingTime: s.processingTime ?? defaultTime,
    })),
  };
};

/**
 * Compute interval days between sessions
 */
export const computeIntervalDays = (
  targetLevel: number,
  currentLevel: number,
  goingLighter: boolean,
  damage: 'Bajo' | 'Medio' | 'Alto',
  rootsState?: HairState
): number => {
  const lift = Math.max(0, targetLevel - currentLevel);
  const base = 7;
  const liftDays = goingLighter ? Math.max(0, Math.ceil(Math.max(0, lift - 1)) * 3) : 0;
  const damageDays = damage === 'Alto' ? 10 : damage === 'Medio' ? 7 : 0;
  const dyedDays = goingLighter && rootsState && rootsState !== HairState.NATURAL ? 4 : 0;
  const days = base + liftDays + damageDays + dyedDays;
  return Math.max(7, Math.min(28, days));
};

/**
 * Get toner line recommendation by brand
 */
export const getTonerLineByBrand = (selectedBrand: string): string => {
  const b = selectedBrand.toLowerCase();
  if (b.includes('wella')) return 'Color Touch';
  if (b.includes('schwarzkopf')) return 'IGORA VIBRANCE';
  if (b.includes("l'oreal") || b.includes("l'oréal")) return 'Dia Light';
  if (b.includes('goldwell')) return 'Colorance';
  return 'Demi/Toner';
};

/**
 * Get neutralization hints for target level
 */
export const getNeutralizationHints = (targetLevel: number) => {
  const neutralInfo =
    COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
      Math.max(
        1,
        Math.min(10, Math.round(targetLevel))
      ) as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
    ];
  const neutralHint = neutralInfo?.neutralizer
    ? ` Sugerencia: reflejo ${neutralInfo.neutralizer} según fondo ${neutralInfo.underlying}.`
    : '';
  const foaText = neutralInfo?.underlying
    ? `Fondo esperado al aclarar (auto): ${neutralInfo.underlying}. `
    : '';
  return { neutralHint, foaText };
};

/**
 * Get brand-specific treatment hints
 */
export const getBrandTreatmentHints = (selectedBrand: string): string => {
  const brandLc = selectedBrand.toLowerCase();
  return brandLc.includes('wella')
    ? 'Sugerencias: WellaPlex Nº2 o ColorMotion+ Structure+ Mask.'
    : brandLc.includes('schwarzkopf')
      ? 'Sugerencias: Fibreplex Nº2 o BC pH 4.5 Color Freeze Treatment.'
      : brandLc.includes("l'oreal") || brandLc.includes("l'oréal")
        ? 'Sugerencias: Metal Detox Mask o Serie Expert Vitamino Color.'
        : brandLc.includes('goldwell')
          ? 'Sugerencias: BondPro+ Step 2 o Dualsenses Color 60s Treatment.'
          : 'Sugerencias: tratamiento reconstructor + sellador ácido pH 3.5–4.5.';
};
