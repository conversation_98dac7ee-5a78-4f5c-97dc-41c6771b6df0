openapi: 3.0.3
info:
  title: Code Quality Audit API
  description: API contracts for comprehensive code audit and quality improvement system
  version: 1.0.0

paths:
  /audit/scan:
    post:
      summary: Initiate comprehensive code quality scan
      description: Starts a full codebase audit including ESLint, Prettier, TypeScript, and security checks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditScanRequest'
      responses:
        '200':
          description: Audit scan initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditScanResponse'
        '400':
          description: Invalid scan configuration
        '500':
          description: Internal server error

  /audit/fix:
    post:
      summary: Apply automated fixes to identified issues
      description: Executes automated fixes for ESLint, Prettier, and other resolvable issues
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditFixRequest'
      responses:
        '200':
          description: Fixes applied successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditFixResponse'
        '400':
          description: Invalid fix configuration
        '409':
          description: Conflicts detected, manual intervention required

  /audit/report/{reportId}:
    get:
      summary: Retrieve audit report by ID
      description: Gets comprehensive audit report with before/after metrics
      parameters:
        - name: reportId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Audit report retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditReport'
        '404':
          description: Report not found

  /audit/metrics:
    get:
      summary: Get current code quality metrics
      description: Returns real-time code quality metrics without running full audit
      responses:
        '200':
          description: Current metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditMetrics'

  /audit/dependencies:
    get:
      summary: Scan dependencies for security vulnerabilities
      description: Checks npm packages for known security vulnerabilities
      responses:
        '200':
          description: Dependency scan completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DependencyAuditResponse'

components:
  schemas:
    AuditScanRequest:
      type: object
      required:
        - targetDirectories
      properties:
        targetDirectories:
          type: array
          items:
            type: string
          example: ['app/', 'components/', 'services/']
        excludePatterns:
          type: array
          items:
            type: string
          example: ['node_modules/', '*.test.ts']
        auditTypes:
          type: array
          items:
            type: string
            enum: [eslint, prettier, typescript, hooks, styles, security]
        autoFix:
          type: boolean
          default: false

    AuditScanResponse:
      type: object
      properties:
        reportId:
          type: string
          format: uuid
        status:
          type: string
          enum: [initiated, scanning, completed, failed]
        estimatedDuration:
          type: integer
          description: Estimated completion time in seconds
        scanStartTime:
          type: string
          format: date-time

    AuditFixRequest:
      type: object
      required:
        - reportId
        - issueTypes
      properties:
        reportId:
          type: string
          format: uuid
        issueTypes:
          type: array
          items:
            type: string
            enum: [eslint, prettier, typescript, hooks, styles, console]
        dryRun:
          type: boolean
          default: false
        backupFiles:
          type: boolean
          default: true

    AuditFixResponse:
      type: object
      properties:
        fixedIssueCount:
          type: integer
        remainingIssueCount:
          type: integer
        modifiedFiles:
          type: array
          items:
            type: string
        executionTime:
          type: integer
          description: Fix execution time in milliseconds
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/FixConflict'

    AuditReport:
      type: object
      properties:
        reportId:
          type: string
          format: uuid
        executionDate:
          type: string
          format: date-time
        beforeMetrics:
          $ref: '#/components/schemas/AuditMetrics'
        afterMetrics:
          $ref: '#/components/schemas/AuditMetrics'
        fixedIssues:
          type: array
          items:
            $ref: '#/components/schemas/IssueResolution'
        remainingIssues:
          type: array
          items:
            $ref: '#/components/schemas/UnresolvedIssue'
        executionTime:
          type: integer
        status:
          type: string
          enum: [completed, partial, failed]

    AuditMetrics:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        eslintWarnings:
          type: integer
          minimum: 0
        eslintErrors:
          type: integer
          minimum: 0
        prettierViolations:
          type: integer
          minimum: 0
        typescriptAnyCount:
          type: integer
          minimum: 0
        hooksDependencyIssues:
          type: integer
          minimum: 0
        inlineStyleCount:
          type: integer
          minimum: 0
        consoleStatementCount:
          type: integer
          minimum: 0
        securityVulnerabilities:
          $ref: '#/components/schemas/SecuritySummary'
        testCoverage:
          type: number
          minimum: 0
          maximum: 100
        bundleSize:
          type: integer
          minimum: 0

    IssueResolution:
      type: object
      properties:
        issueType:
          type: string
          enum: [eslint, prettier, typescript, hooks, style, console, security]
        filePath:
          type: string
        lineNumber:
          type: integer
          minimum: 1
        originalCode:
          type: string
        fixedCode:
          type: string
        ruleId:
          type: string
        severity:
          type: string
          enum: [error, warning, info]
        autoFixed:
          type: boolean

    UnresolvedIssue:
      type: object
      properties:
        issueType:
          type: string
          enum: [eslint, prettier, typescript, hooks, style, console, security]
        filePath:
          type: string
        lineNumber:
          type: integer
        description:
          type: string
        ruleId:
          type: string
        severity:
          type: string
          enum: [error, warning, info]
        reason:
          type: string
          description: Why the issue could not be automatically resolved

    FixConflict:
      type: object
      properties:
        filePath:
          type: string
        conflictType:
          type: string
          enum: [eslint-prettier, type-inference, breaking-change]
        description:
          type: string
        suggestedResolution:
          type: string

    SecuritySummary:
      type: object
      properties:
        critical:
          type: integer
          minimum: 0
        high:
          type: integer
          minimum: 0
        medium:
          type: integer
          minimum: 0
        low:
          type: integer
          minimum: 0
        info:
          type: integer
          minimum: 0

    DependencyAuditResponse:
      type: object
      properties:
        totalPackages:
          type: integer
        vulnerablePackages:
          type: integer
        vulnerabilities:
          type: array
          items:
            $ref: '#/components/schemas/DependencyVulnerability'
        recommendations:
          type: array
          items:
            $ref: '#/components/schemas/UpdateRecommendation'

    DependencyVulnerability:
      type: object
      properties:
        packageName:
          type: string
        currentVersion:
          type: string
        vulnerabilityId:
          type: string
        severity:
          type: string
          enum: [critical, high, medium, low, info]
        description:
          type: string
        patchedVersion:
          type: string

    UpdateRecommendation:
      type: object
      properties:
        packageName:
          type: string
        currentVersion:
          type: string
        recommendedVersion:
          type: string
        updateType:
          type: string
          enum: [patch, minor, major]
        breakingChanges:
          type: boolean
        securityFix:
          type: boolean
