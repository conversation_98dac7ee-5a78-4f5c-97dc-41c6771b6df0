import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { X, Plus, Info, Check, Euro } from 'lucide-react-native';
import { router } from 'expo-router';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { Product } from '@/types/inventory';
import { BeautyCard } from '@/components/beauty/BeautyCard';
import { BeautyButton } from '@/components/beauty/BeautyButton';

interface PricingSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: () => void;
}

interface ProductTemplate {
  name: string;
  brand: string;
  category: Product['category'];
  unitType: Product['unitType'];
  defaultSize: number;
  suggestedPrice: number;
}

const DEFAULT_PRODUCTS: ProductTemplate[] = [
  // Oxidantes
  {
    name: 'Oxidante 10 Vol (3%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 20 Vol (6%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 30 Vol (9%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 40 Vol (12%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },

  // Decolorantes
  {
    name: 'Polvo Decolorante Azul',
    brand: 'Genérico',
    category: 'decolorante',
    unitType: 'g',
    defaultSize: 500,
    suggestedPrice: 15,
  },
  {
    name: 'Polvo Decolorante Blanco',
    brand: 'Genérico',
    category: 'decolorante',
    unitType: 'g',
    defaultSize: 500,
    suggestedPrice: 15,
  },

  // Tratamientos
  {
    name: 'Olaplex No.1',
    brand: 'Olaplex',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 100,
    suggestedPrice: 80,
  },
  {
    name: 'Olaplex No.2',
    brand: 'Olaplex',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 500,
    suggestedPrice: 120,
  },
  {
    name: 'Protector de Cuero Cabelludo',
    brand: 'Genérico',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 250,
    suggestedPrice: 12,
  },
];

export default function PricingSetupModal({
  visible,
  onClose,
  onComplete,
}: PricingSetupModalProps) {
  const { addProduct, initializeWithDefaults: _initializeWithDefaults } = useInventoryStore();
  const { formatCurrency } = useSalonConfigStore();

  const [step, setStep] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState<number[]>([0, 1, 2, 3, 4, 5]); // Default selections
  const [productPrices, setProductPrices] = useState<Record<number, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Custom product form removed - using main inventory screen instead

  useEffect(() => {
    // Initialize prices with suggested values
    const initialPrices: Record<number, string> = {};
    DEFAULT_PRODUCTS.forEach((product, index) => {
      initialPrices[index] = product.suggestedPrice.toString();
    });
    setProductPrices(initialPrices);
  }, []);

  const toggleProductSelection = (index: number) => {
    if (selectedProducts.includes(index)) {
      setSelectedProducts(selectedProducts.filter(i => i !== index));
    } else {
      setSelectedProducts([...selectedProducts, index]);
    }
  };

  const calculateUnitPrice = (totalPrice: string, size: number, _unitType: string): number => {
    const price = parseFloat(totalPrice) || 0;
    return price / size;
  };

  const handleSaveProducts = async () => {
    setIsLoading(true);

    try {
      // Save selected default products in parallel for better performance
      const productCreationPromises = selectedProducts.map(async index => {
        const template = DEFAULT_PRODUCTS[index];
        const purchasePrice = parseFloat(productPrices[index] || '0');

        if (purchasePrice > 0) {
          return addProduct({
            name: template.name,
            brand: template.brand,
            category: template.category,
            currentStock: 0, // Start with 0, user will add stock later
            minStock: template.unitType === 'ml' ? 200 : 100,
            unitType: template.unitType,
            unitSize: template.defaultSize,
            purchasePrice,
            costPerUnit: calculateUnitPrice(
              productPrices[index],
              template.defaultSize,
              template.unitType
            ),
            isActive: true,
          });
        }
        return Promise.resolve();
      });

      await Promise.all(productCreationPromises);

      setIsLoading(false);
      onComplete();
      Alert.alert(
        'Configuración Completada',
        'Los productos se han configurado correctamente. Ahora puedes gestionar tu inventario.',
        [{ text: 'OK', onPress: onClose }]
      );
    } catch {
      setIsLoading(false);
      Alert.alert('Error', 'No se pudieron guardar los productos. Por favor, intenta de nuevo.');
    }
  };

  // Removed handleAddCustomProduct - using main inventory screen instead

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Selecciona los productos que utilizas</Text>
      <Text style={styles.stepDescription}>
        Estos son los productos más comunes en coloración. Selecciona los que tienes en tu salón.
      </Text>

      <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
        {DEFAULT_PRODUCTS.map((product, index) => (
          <BeautyCard
            key={index}
            variant={selectedProducts.includes(index) ? 'outlined' : 'default'}
            style={[
              styles.productItem,
              selectedProducts.includes(index) && styles.productItemSelected,
            ]}
            onPress={() => toggleProductSelection(index)}
          >
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productDetails}>
                {product.brand} • {product.defaultSize}
                {product.unitType}
              </Text>
            </View>
            <View style={styles.checkbox}>
              {selectedProducts.includes(index) && (
                <Check size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
              )}
            </View>
          </BeautyCard>
        ))}
      </ScrollView>

      <BeautyButton
        variant="ghost"
        title="Agregar producto personalizado"
        icon={Plus}
        onPress={() => {
          onClose(); // Close the modal first
          router.push('/inventory/new'); // Navigate to the main inventory form
        }}
        style={styles.customProductButton}
      />
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Configura los precios de compra</Text>
      <Text style={styles.stepDescription}>
        Ingresa el precio que pagas por cada producto. El sistema calculará automáticamente el costo
        por unidad.
      </Text>

      <ScrollView style={styles.priceList} showsVerticalScrollIndicator={false}>
        {selectedProducts.map(index => {
          const product = DEFAULT_PRODUCTS[index];
          const price = productPrices[index] || '';
          const unitPrice = price
            ? calculateUnitPrice(price, product.defaultSize, product.unitType)
            : 0;

          return (
            <BeautyCard key={index} variant="default" style={styles.priceItem}>
              <View style={styles.priceItemHeader}>
                <Text style={styles.priceItemName}>{product.name}</Text>
                <Text style={styles.priceItemSize}>
                  {product.defaultSize}
                  {product.unitType}
                </Text>
              </View>

              <View style={styles.priceInputContainer}>
                <View style={styles.priceInputWrapper}>
                  <Euro
                    size={16}
                    color={BeautyMinimalTheme.semantic.text.tertiary}
                    style={styles.euroIcon}
                  />
                  <TextInput
                    style={styles.priceInput}
                    value={price}
                    onChangeText={text => setProductPrices({ ...productPrices, [index]: text })}
                    placeholder="0.00"
                    keyboardType="decimal-pad"
                    placeholderTextColor={BeautyMinimalTheme.semantic.text.tertiary}
                  />
                </View>

                {unitPrice > 0 && (
                  <Text style={styles.unitPrice}>
                    {formatCurrency(unitPrice)}/{product.unitType}
                  </Text>
                )}
              </View>
            </BeautyCard>
          );
        })}
      </ScrollView>

      <BeautyCard variant="subtle" style={styles.infoBox}>
        <View style={styles.infoContent}>
          <Info size={16} color={BeautyMinimalTheme.semantic.interactive.primary.default} />
          <Text style={styles.infoText}>
            Los precios por unidad se usarán para calcular el costo exacto de cada servicio
          </Text>
        </View>
      </BeautyCard>
    </View>
  );

  // Removed renderCustomProductForm - using main inventory screen instead

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={BeautyMinimalTheme.semantic.text.secondary} />
          </TouchableOpacity>
          <Text style={styles.title}>Configuración de Precios</Text>
          <View style={commonStyles.width24} />
        </View>

        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${step * 50}%` }]} />
        </View>

        {step === 1 ? renderStep1() : renderStep2()}

        <View style={styles.footer}>
          {step > 1 && (
            <BeautyButton
              variant="ghost"
              title="Atrás"
              onPress={() => setStep(step - 1)}
              style={styles.backButton}
            />
          )}

          {step === 1 ? (
            <BeautyButton
              variant="primary"
              title="Siguiente"
              onPress={() => selectedProducts.length > 0 && setStep(2)}
              disabled={selectedProducts.length === 0}
              style={styles.nextButton}
            />
          ) : (
            <BeautyButton
              variant="primary"
              title={isLoading ? 'Guardando...' : 'Finalizar'}
              onPress={handleSaveProducts}
              disabled={isLoading}
              loading={isLoading}
              style={styles.nextButton}
            />
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: BeautyMinimalTheme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: BeautyMinimalTheme.semantic.border.default,
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  progressBar: {
    height: 4,
    backgroundColor: BeautyMinimalTheme.neutrals.mist,
  },
  progressFill: {
    height: '100%',
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  stepContent: {
    flex: 1,
    padding: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  stepDescription: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xl,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.relaxed,
  },
  productList: {
    flex: 1,
  },
  productItem: {
    // BeautyCard provides base styling, we only add specific layout
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  productItemSelected: {
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs / 2,
  },
  productDetails: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BeautyMinimalTheme.radius.full,
    borderWidth: 2,
    borderColor: BeautyMinimalTheme.semantic.border.default,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customProductButton: {
    // BeautyButton provides the styling, we add specific overrides
    marginTop: BeautyMinimalTheme.spacing.lg,
    borderStyle: 'dashed',
  },
  priceList: {
    flex: 1,
  },
  priceItem: {
    // BeautyCard provides base styling, we only add specific layout
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  priceItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  priceItemName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    flex: 1,
  },
  priceItemSize: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderRadius: BeautyMinimalTheme.radius.sm,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    flex: 1,
    marginRight: BeautyMinimalTheme.spacing.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
  euroIcon: {
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  priceInput: {
    flex: 1,
    height: BeautyMinimalTheme.spacing.touchTarget.minimum,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
  },
  unitPrice: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.interactive.professional.default,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
  },
  infoBox: {
    // BeautyCard provides base styling, we add info-specific colors
    marginTop: BeautyMinimalTheme.spacing.lg,
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.hover,
    borderColor: BeautyMinimalTheme.semantic.interactive.primary.default + '40',
  },
  infoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
    marginLeft: BeautyMinimalTheme.spacing.md,
    flex: 1,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.relaxed,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: BeautyMinimalTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.default,
    gap: BeautyMinimalTheme.spacing.md,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
    minWidth: 120,
  },
});
