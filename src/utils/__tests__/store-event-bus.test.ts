/**
 * Tests for Store Event Bus - Critical for eliminating circular dependencies
 */

import { storeEventBus, STORE_EVENTS } from '../store-event-bus';

describe('StoreEventBus', () => {
  beforeEach(() => {
    storeEventBus.clear();
  });

  afterEach(() => {
    storeEventBus.clear();
  });

  describe('Basic Event Handling', () => {
    it('should emit and receive events correctly', async () => {
      const mockHandler = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, mockHandler);
      
      await storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { test: 'data' }, 'test-source');
      
      expect(mockHandler).toHaveBeenCalledWith({ test: 'data' });
    });

    it('should handle multiple listeners for same event', async () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler1);
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler2);
      
      await storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { test: 'data' }, 'test');
      
      expect(handler1).toHaveBeenCalledWith({ test: 'data' });
      expect(handler2).toHaveBeenCalledWith({ test: 'data' });
    });

    it('should return unsubscribe function', () => {
      const handler = jest.fn();
      const unsubscribe = storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler);
      
      expect(typeof unsubscribe).toBe('function');
      
      unsubscribe();
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in event handlers gracefully', async () => {
      const errorHandler = jest.fn(() => {
        throw new Error('Handler error');
      });
      const goodHandler = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, errorHandler);
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, goodHandler);
      
      // Should not throw despite error in first handler
      await expect(
        storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, { test: 'data' }, 'test')
      ).resolves.not.toThrow();
      
      expect(errorHandler).toHaveBeenCalled();
      expect(goodHandler).toHaveBeenCalled();
    });
  });

  describe('Async Handlers', () => {
    it('should wait for async handlers to complete', async () => {
      let completed = false;
      
      const asyncHandler = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        completed = true;
      });
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, asyncHandler);
      
      await storeEventBus.emit(STORE_EVENTS.SYNC_REQUIRED, {}, 'test');
      
      expect(completed).toBe(true);
      expect(asyncHandler).toHaveBeenCalled();
    });
  });

  describe('Listener Management', () => {
    it('should track listener counts correctly', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler1);
      storeEventBus.on(STORE_EVENTS.USER_SIGNED_IN, handler2);
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(1);
      expect(storeEventBus.getListenerCount(STORE_EVENTS.USER_SIGNED_IN)).toBe(1);
      
      const counts = storeEventBus.getListenerCount() as Record<string, number>;
      expect(counts[STORE_EVENTS.SYNC_REQUIRED]).toBe(1);
      expect(counts[STORE_EVENTS.USER_SIGNED_IN]).toBe(1);
    });

    it('should remove all listeners for event type', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler1);
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, handler2);
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(2);
      
      storeEventBus.off(STORE_EVENTS.SYNC_REQUIRED);
      
      expect(storeEventBus.getListenerCount(STORE_EVENTS.SYNC_REQUIRED)).toBe(0);
    });

    it('should clear all listeners', () => {
      storeEventBus.on(STORE_EVENTS.SYNC_REQUIRED, jest.fn());
      storeEventBus.on(STORE_EVENTS.USER_SIGNED_IN, jest.fn());
      
      const counts = storeEventBus.getListenerCount() as Record<string, number>;
      expect(Object.keys(counts)).toHaveLength(2);
      
      storeEventBus.clear();
      
      const countsAfter = storeEventBus.getListenerCount() as Record<string, number>;
      expect(Object.keys(countsAfter)).toHaveLength(0);
    });
  });

  describe('Store Event Types', () => {
    it('should have all required event types defined', () => {
      expect(STORE_EVENTS.USER_SIGNED_IN).toBeDefined();
      expect(STORE_EVENTS.USER_SIGNED_OUT).toBeDefined();
      expect(STORE_EVENTS.SYNC_REQUIRED).toBeDefined();
      expect(STORE_EVENTS.SALON_CONFIG_UPDATED).toBeDefined();
      expect(STORE_EVENTS.INVENTORY_UPDATED).toBeDefined();
    });
  });
});
