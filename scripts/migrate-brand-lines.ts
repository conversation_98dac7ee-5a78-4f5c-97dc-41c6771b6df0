/**
 * Script para migrar las líneas de marcas existentes y clasificarlas correctamente
 * Este script actualiza todas las líneas para incluir category e isColorLine
 */

import { ProductLine } from '../src/constants/brands-data';

// Clasificación de líneas por tipo
const LINE_CLASSIFICATIONS: Record<string, { category: ProductLine['category']; isColorLine: boolean }> = {
  // Wella
  'illumina': { category: 'hair-color', isColorLine: true },
  'koleston': { category: 'hair-color', isColorLine: true },
  'color-touch': { category: 'hair-color', isColorLine: true },
  'blondor': { category: 'bleaching', isColorLine: true },
  'welloxon': { category: 'developer', isColorLine: true },
  'color-fresh': { category: 'hair-color', isColorLine: true },
  'magma': { category: 'bleaching', isColorLine: true },
  'shinefinity': { category: 'hair-color', isColorLine: true },

  // <PERSON>hwarzkopf
  'igora-royal': { category: 'hair-color', isColorLine: true },
  'blondme': { category: 'bleaching', isColorLine: true },
  'color10': { category: 'hair-color', isColorLine: true },
  'igora-vibrance': { category: 'hair-color', isColorLine: true },
  'essensity': { category: 'hair-color', isColorLine: true },

  // L'Oreal
  'majirel': { category: 'hair-color', isColorLine: true },
  'inoa': { category: 'hair-color', isColorLine: true },
  'dia-light': { category: 'hair-color', isColorLine: true },
  'dia-richesse': { category: 'hair-color', isColorLine: true },
  'majirouge': { category: 'hair-color', isColorLine: true },
  'platinium': { category: 'bleaching', isColorLine: true },
  'smartbond': { category: 'treatment', isColorLine: false },

  // Salerm
  'vison': { category: 'hair-color', isColorLine: true },
  'hi-repair': { category: 'treatment', isColorLine: false },
  'technique': { category: 'styling', isColorLine: false },
  'biokera': { category: 'treatment', isColorLine: false },
  'color-reverse': { category: 'other', isColorLine: false },

  // Redken
  'chromatics': { category: 'hair-color', isColorLine: true },
  'color-extend': { category: 'treatment', isColorLine: false },
  'shades-eq': { category: 'hair-color', isColorLine: true },

  // Matrix
  'socolor': { category: 'hair-color', isColorLine: true },
  'color-sync': { category: 'hair-color', isColorLine: true },
  'light-master': { category: 'bleaching', isColorLine: true },
  'biolage': { category: 'treatment', isColorLine: false },

  // Goldwell
  'topchic': { category: 'hair-color', isColorLine: true },
  'colorance': { category: 'hair-color', isColorLine: true },
  'elumen': { category: 'hair-color', isColorLine: true },
  'kerasilk': { category: 'treatment', isColorLine: false },

  // Joico
  'lumishine': { category: 'hair-color', isColorLine: true },
  'vero-k-pak': { category: 'hair-color', isColorLine: true },
  'k-pak': { category: 'treatment', isColorLine: false },

  // Alfaparf
  'evolution': { category: 'hair-color', isColorLine: true },
  'revolution': { category: 'hair-color', isColorLine: true },
  'semi-di-lino': { category: 'treatment', isColorLine: false },

  // Pravana
  'chromasilk': { category: 'hair-color', isColorLine: true },
  'vivids': { category: 'hair-color', isColorLine: true },
  'nevo': { category: 'treatment', isColorLine: false },

  // Revlon
  'revlonissimo': { category: 'hair-color', isColorLine: true },
  'young-color': { category: 'hair-color', isColorLine: true },
  'nutri-color': { category: 'treatment', isColorLine: false },

  // Indola
  'profession': { category: 'hair-color', isColorLine: true },
  'rapid-blond': { category: 'bleaching', isColorLine: true },
  'innova': { category: 'treatment', isColorLine: false },

  // Marcas españolas adicionales
  'lendan-color': { category: 'hair-color', isColorLine: true },
  'tahe': { category: 'hair-color', isColorLine: true },
  'kemon': { category: 'hair-color', isColorLine: true },

  // Marcas italianas
  'davines': { category: 'hair-color', isColorLine: true },
  'inebrya': { category: 'hair-color', isColorLine: true },

  // Marcas americanas
  'paul-mitchell': { category: 'hair-color', isColorLine: true },
  'kenra': { category: 'hair-color', isColorLine: true },
  'guy-tang': { category: 'hair-color', isColorLine: true },

  // Marcas asiáticas
  'milbon': { category: 'hair-color', isColorLine: true },
  'shiseido': { category: 'hair-color', isColorLine: true },

  // Marcas brasileñas
  'lowell': { category: 'hair-color', isColorLine: true },
  'felps': { category: 'hair-color', isColorLine: true },

  // Marcas argentinas
  'kativa': { category: 'hair-color', isColorLine: true },
  'capill': { category: 'hair-color', isColorLine: true },

  // Marcas mexicanas
  'keratina': { category: 'treatment', isColorLine: false },
  'tio-nacho': { category: 'treatment', isColorLine: false },

  // Marcas indias
  'streax-professional': { category: 'hair-color', isColorLine: true },
  'insta-shine': { category: 'hair-color', isColorLine: true },
  'hair-serum': { category: 'treatment', isColorLine: false },

  // Líneas genéricas que suelen ser de coloración
  'professional': { category: 'hair-color', isColorLine: true },
  'color': { category: 'hair-color', isColorLine: true },
  'permanent': { category: 'hair-color', isColorLine: true },
  'semi-permanent': { category: 'hair-color', isColorLine: true },
  'demi-permanent': { category: 'hair-color', isColorLine: true },

  // Líneas genéricas que NO son de coloración
  'care': { category: 'treatment', isColorLine: false },
  'repair': { category: 'treatment', isColorLine: false },
  'treatment': { category: 'treatment', isColorLine: false },
  'styling': { category: 'styling', isColorLine: false },
  'finish': { category: 'styling', isColorLine: false },
  'protection': { category: 'treatment', isColorLine: false },
  'nutrition': { category: 'treatment', isColorLine: false },
  'hydration': { category: 'treatment', isColorLine: false },
  'reconstruction': { category: 'treatment', isColorLine: false },
};

/**
 * Clasifica una línea basándose en su ID y descripción
 */
export function classifyLine(lineId: string, description?: string): { category: ProductLine['category']; isColorLine: boolean } {
  // Buscar clasificación exacta por ID
  if (LINE_CLASSIFICATIONS[lineId]) {
    return LINE_CLASSIFICATIONS[lineId];
  }

  // Buscar por palabras clave en el ID
  const lowerLineId = lineId.toLowerCase();
  for (const [keyword, classification] of Object.entries(LINE_CLASSIFICATIONS)) {
    if (lowerLineId.includes(keyword)) {
      return classification;
    }
  }

  // Buscar por palabras clave en la descripción
  if (description) {
    const lowerDescription = description.toLowerCase();
    
    // Palabras clave que indican coloración
    const colorKeywords = ['color', 'tinte', 'permanent', 'semi-permanent', 'demi-permanent', 'lightening', 'bleach', 'developer', 'oxidante'];
    const treatmentKeywords = ['treatment', 'repair', 'care', 'nutrition', 'hydration', 'reconstruction', 'tratamiento', 'reparación'];
    const stylingKeywords = ['styling', 'finish', 'gel', 'mousse', 'spray', 'wax', 'peinado'];

    if (colorKeywords.some(keyword => lowerDescription.includes(keyword))) {
      return { category: 'hair-color', isColorLine: true };
    }
    
    if (treatmentKeywords.some(keyword => lowerDescription.includes(keyword))) {
      return { category: 'treatment', isColorLine: false };
    }
    
    if (stylingKeywords.some(keyword => lowerDescription.includes(keyword))) {
      return { category: 'styling', isColorLine: false };
    }
  }

  // Por defecto, asumir que es coloración si no se puede clasificar
  console.warn(`No se pudo clasificar la línea: ${lineId} - ${description}`);
  return { category: 'hair-color', isColorLine: true };
}

/**
 * Función para generar el código TypeScript actualizado de una línea
 */
export function generateLineCode(line: any): string {
  const classification = classifyLine(line.id, line.description);
  
  return `      {
        id: '${line.id}',
        name: '${line.name}',
        description: '${line.description || ''}',
        category: '${classification.category}',
        isColorLine: ${classification.isColorLine}
      },`;
}

console.log('Script de migración de líneas de marcas cargado.');
console.log('Usa classifyLine(lineId, description) para clasificar líneas individuales.');
console.log('Usa generateLineCode(line) para generar código TypeScript actualizado.');
