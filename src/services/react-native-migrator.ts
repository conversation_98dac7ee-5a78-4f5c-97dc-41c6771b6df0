#!/usr/bin/env npx tsx

/**
 * React Native Structure Migrator - Phase 2
 * Reorganizes React Native components from app/ and other directories
 * into the new src/ structure while maintaining Expo Router compatibility
 */

import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';

interface MigrationAction {
  id: string;
  type: 'MOVE' | 'CREATE_DIRECTORY' | 'UPDATE_IMPORT' | 'SYMLINK';
  source: string;
  target: string;
  reason: string;
  category: 'screen' | 'component' | 'store' | 'service' | 'util' | 'hook' | 'type' | 'constant';
}

export class ReactNativeMigrator {
  private readonly projectRoot: string;
  private readonly dryRun: boolean;
  private readonly preserveExpoRouter: boolean = true;

  constructor(projectRoot: string = process.cwd(), dryRun = false) {
    this.projectRoot = projectRoot;
    this.dryRun = dryRun;
  }

  async executeReactNativeMigration(): Promise<void> {
    console.info('🚀 Starting React Native structure migration - Phase 2...\n');

    const actions = await this.generatePhase2MigrationPlan();

    if (this.dryRun) {
      console.info('📋 DRY RUN - React Native migration actions:\n');
      this.displayActions(actions);
      return;
    }

    // Create target directories first
    await this.createPhase2Directories();

    // Execute migrations by category (order matters)
    const categories = [
      'type',
      'constant',
      'util',
      'hook',
      'service',
      'store',
      'component',
      'screen',
    ];

    let completed = 0;
    for (const category of categories) {
      const categoryActions = actions.filter(a => a.category === category);

      if (categoryActions.length > 0) {
        console.info(`\n📂 Migrating ${category}s (${categoryActions.length} files)...`);

        for (const action of categoryActions) {
          try {
            await this.executeAction(action);
            completed++;
            console.info(`  ✅ [${completed}/${actions.length}] ${basename(action.source)}`);
          } catch (error) {
            console.error(`  ❌ Failed: ${action.source}`, error);
          }
        }
      }
    }

    // Update imports after all files are moved
    console.info('\n🔄 Updating import statements...');
    await this.updateImportStatements();

    console.info(
      `\n🎉 Phase 2 migration completed! ${completed}/${actions.length} actions successful.`
    );

    if (this.preserveExpoRouter) {
      console.info('\n📱 Expo Router structure preserved for routing');
    }
  }

  private async generatePhase2MigrationPlan(): Promise<MigrationAction[]> {
    const actions: MigrationAction[] = [];

    // 1. Move existing stores to src/stores/
    await this.addStoreActions(actions);

    // 2. Move utilities to src/utils/
    await this.addUtilActions(actions);

    // 3. Move hooks to src/hooks/
    await this.addHookActions(actions);

    // 4. Move services to src/services/
    await this.addServiceActions(actions);

    // 5. Move types to src/types/
    await this.addTypeActions(actions);

    // 6. Move constants to src/constants/
    await this.addConstantActions(actions);

    // 7. Move reusable components to src/components/
    await this.addComponentActions(actions);

    // 8. Handle screens (keep some in app/ for Expo Router, move others to src/screens/)
    await this.addScreenActions(actions);

    return actions;
  }

  private async addStoreActions(actions: MigrationAction[]): Promise<void> {
    const storeFiles = await this.findFiles('stores/', /\.ts$/);

    for (const file of storeFiles) {
      const filename = basename(file);
      actions.push({
        id: `store-${filename}`,
        type: 'MOVE',
        source: file,
        target: `src/stores/${filename}`,
        reason: 'Consolidate all stores in src/stores/',
        category: 'store',
      });
    }
  }

  private async addUtilActions(actions: MigrationAction[]): Promise<void> {
    const utilFiles = await this.findFiles('utils/', /\.(ts|tsx)$/);

    for (const file of utilFiles) {
      const filename = basename(file);
      actions.push({
        id: `util-${filename}`,
        type: 'MOVE',
        source: file,
        target: `src/utils/${filename}`,
        reason: 'Consolidate utilities in src/utils/',
        category: 'util',
      });
    }
  }

  private async addHookActions(actions: MigrationAction[]): Promise<void> {
    const hookFiles = await this.findFiles('hooks/', /\.(ts|tsx)$/);

    for (const file of hookFiles) {
      const filename = basename(file);
      if (filename.startsWith('use') || filename.includes('hook')) {
        actions.push({
          id: `hook-${filename}`,
          type: 'MOVE',
          source: file,
          target: `src/hooks/${filename}`,
          reason: 'Consolidate custom hooks in src/hooks/',
          category: 'hook',
        });
      }
    }
  }

  private async addServiceActions(actions: MigrationAction[]): Promise<void> {
    const serviceFiles = await this.findFiles('services/', /\.(ts|tsx)$/);

    for (const file of serviceFiles) {
      const filename = basename(file);
      // Skip our migration tools
      if (!filename.includes('migration-executor') && !filename.includes('react-native-migrator')) {
        actions.push({
          id: `service-${filename}`,
          type: 'MOVE',
          source: file,
          target: `src/services/${filename}`,
          reason: 'Consolidate services in src/services/',
          category: 'service',
        });
      }
    }
  }

  private async addTypeActions(actions: MigrationAction[]): Promise<void> {
    const typeFiles = await this.findFiles('types/', /\.ts$/);

    for (const file of typeFiles) {
      const filename = basename(file);
      // Skip our own types
      if (!filename.includes('file-category') && !filename.includes('file-system')) {
        actions.push({
          id: `type-${filename}`,
          type: 'MOVE',
          source: file,
          target: `src/types/${filename}`,
          reason: 'Consolidate type definitions in src/types/',
          category: 'type',
        });
      }
    }
  }

  private async addConstantActions(actions: MigrationAction[]): Promise<void> {
    const constantFiles = await this.findFiles('constants/', /\.(ts|tsx)$/);

    for (const file of constantFiles) {
      const filename = basename(file);
      actions.push({
        id: `constant-${filename}`,
        type: 'MOVE',
        source: file,
        target: `src/constants/${filename}`,
        reason: 'Consolidate constants in src/constants/',
        category: 'constant',
      });
    }
  }

  private async addComponentActions(actions: MigrationAction[]): Promise<void> {
    // Move root-level components/ directory
    const componentFiles = await this.findFiles('components/', /\.(ts|tsx)$/);

    for (const file of componentFiles) {
      const filename = basename(file);
      const relativePath = file.replace('components/', '');
      const targetPath = `src/components/${relativePath}`;

      actions.push({
        id: `component-${filename}`,
        type: 'MOVE',
        source: file,
        target: targetPath,
        reason: 'Consolidate reusable components in src/components/',
        category: 'component',
      });
    }

    // Move reusable components from app/components/ (but keep app-specific ones)
    const appComponentFiles = await this.findFiles('app/components/', /\.(ts|tsx)$/);

    for (const file of appComponentFiles) {
      const filename = basename(file);

      // Only move truly reusable components
      if (this.isReusableComponent(filename)) {
        const relativePath = file.replace('app/components/', '');
        actions.push({
          id: `app-component-${filename}`,
          type: 'MOVE',
          source: file,
          target: `src/components/${relativePath}`,
          reason: 'Move reusable components to src/components/',
          category: 'component',
        });
      }
    }
  }

  private async addScreenActions(actions: MigrationAction[]): Promise<void> {
    // For Expo Router, we need to be careful about what we move
    // Keep routing structure in app/, but move screen components to src/screens/

    const screenDirs = [
      'app/auth',
      'app/inventory',
      'app/onboarding',
      'app/service',
      'app/settings',
      'app/team',
      'app/client',
    ];

    for (const dir of screenDirs) {
      if (await this.directoryExists(dir)) {
        const screenFiles = await this.findFiles(dir, /\.(ts|tsx)$/);

        for (const file of screenFiles) {
          const filename = basename(file);
          const screenName = dirname(file).split('/').pop();

          // Move actual screen components, keep route files
          if (!filename.includes('_layout') && !filename.startsWith('index.')) {
            actions.push({
              id: `screen-${screenName}-${filename}`,
              type: 'MOVE',
              source: file,
              target: `src/screens/${screenName}/${filename}`,
              reason: 'Organize screen components in src/screens/',
              category: 'screen',
            });
          }
        }
      }
    }
  }

  private isReusableComponent(filename: string): boolean {
    const reusablePatterns = [
      /^[A-Z][a-zA-Z]*Button/,
      /^[A-Z][a-zA-Z]*Input/,
      /^[A-Z][a-zA-Z]*Modal/,
      /^[A-Z][a-zA-Z]*Card/,
      /^[A-Z][a-zA-Z]*Form/,
      /^[A-Z][a-zA-Z]*Loading/,
      /^[A-Z][a-zA-Z]*Icon/,
      /UI/,
      /Common/,
      /Shared/,
    ];

    return reusablePatterns.some(pattern => pattern.test(filename));
  }

  private async createPhase2Directories(): Promise<void> {
    const directories = [
      // Extend existing src structure
      'src/screens/auth',
      'src/screens/inventory',
      'src/screens/onboarding',
      'src/screens/service',
      'src/screens/settings',
      'src/screens/team',
      'src/screens/client',
      'src/components/ui',
      'src/components/forms',
      'src/components/navigation',
      'src/components/layout',
    ];

    for (const dir of directories) {
      await this.ensureDirectory(dir);
    }
  }

  private async executeAction(action: MigrationAction): Promise<void> {
    switch (action.type) {
      case 'MOVE':
        await this.moveFile(action.source, action.target);
        break;
      case 'CREATE_DIRECTORY':
        await this.ensureDirectory(action.target);
        break;
      case 'SYMLINK':
        await this.createSymlink(action.source, action.target);
        break;
    }
  }

  private async updateImportStatements(): Promise<void> {
    // This is a simplified version - in production you'd want a more sophisticated AST-based approach
    const filesToUpdate = await this.findFiles('.', /\.(ts|tsx)$/, {
      exclude: ['node_modules', '.git', 'docs', 'archive', 'target-structure'],
    });

    for (const file of filesToUpdate) {
      try {
        await this.updateImportsInFile(file);
      } catch (error) {
        console.warn(`  ⚠️ Could not update imports in ${file}:`, error);
      }
    }
  }

  private async updateImportsInFile(filePath: string): Promise<void> {
    const content = await fs.readFile(join(this.projectRoot, filePath), 'utf-8');

    // Common import patterns to update
    const updatedContent = content
      .replace(/from ['"]\.\.\/stores\//g, "from '../src/stores/")
      .replace(/from ['"]\.\.\/utils\//g, "from '../src/utils/")
      .replace(/from ['"]\.\.\/hooks\//g, "from '../src/hooks/")
      .replace(/from ['"]\.\.\/services\//g, "from '../src/services/")
      .replace(/from ['"]\.\.\/types\//g, "from '../src/types/")
      .replace(/from ['"]\.\.\/constants\//g, "from '../src/constants/")
      .replace(/from ['"]\.\.\/components\//g, "from '../src/components/")
      .replace(/from ['"]\.\/stores\//g, "from './src/stores/")
      .replace(/from ['"]\.\/utils\//g, "from './src/utils/")
      .replace(/from ['"]\.\/hooks\//g, "from './src/hooks/")
      .replace(/from ['"]\.\/services\//g, "from './src/services/")
      .replace(/from ['"]\.\/types\//g, "from './src/types/")
      .replace(/from ['"]\.\/constants\//g, "from './src/constants/")
      .replace(/from ['"]\.\/components\//g, "from './src/components/");

    if (updatedContent !== content) {
      await fs.writeFile(join(this.projectRoot, filePath), updatedContent);
    }
  }

  private displayActions(actions: MigrationAction[]): void {
    const grouped = actions.reduce(
      (acc, action) => {
        if (!acc[action.category]) acc[action.category] = [];
        acc[action.category].push(action);
        return acc;
      },
      {} as Record<string, MigrationAction[]>
    );

    Object.entries(grouped).forEach(([category, categoryActions]) => {
      console.info(`\n📂 ${category.toUpperCase()} (${categoryActions.length} files):`);
      categoryActions.forEach(action => {
        console.info(`  ${action.type}: ${action.source} → ${action.target}`);
        console.info(`     Reason: ${action.reason}\n`);
      });
    });
  }

  // Utility methods (similar to migration-executor)
  private async moveFile(source: string, target: string): Promise<void> {
    const sourcePath = join(this.projectRoot, source);
    const targetPath = join(this.projectRoot, target);

    await this.ensureDirectory(dirname(target));

    if (!(await this.fileExists(source))) {
      throw new Error(`Source file does not exist: ${source}`);
    }

    if (await this.fileExists(target)) {
      console.warn(`⚠️ Target already exists, skipping: ${target}`);
      return;
    }

    await fs.rename(sourcePath, targetPath);
  }

  private async createSymlink(source: string, target: string): Promise<void> {
    const sourcePath = join(this.projectRoot, source);
    const targetPath = join(this.projectRoot, target);

    await this.ensureDirectory(dirname(target));
    await fs.symlink(sourcePath, targetPath);
  }

  private async ensureDirectory(dirPath: string): Promise<void> {
    const fullPath = join(this.projectRoot, dirPath);
    try {
      await fs.access(fullPath);
    } catch {
      await fs.mkdir(fullPath, { recursive: true });
    }
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(join(this.projectRoot, filePath));
      return true;
    } catch {
      return false;
    }
  }

  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stat = await fs.stat(join(this.projectRoot, dirPath));
      return stat.isDirectory();
    } catch {
      return false;
    }
  }

  private async findFiles(
    dir: string,
    pattern: RegExp,
    options: { exclude?: string[] } = {}
  ): Promise<string[]> {
    const files: string[] = [];
    const { exclude = [] } = options;

    async function scan(currentDir: string): Promise<void> {
      try {
        const entries = await fs.readdir(join(process.cwd(), currentDir), { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = join(currentDir, entry.name);

          if (exclude.some(ex => fullPath.includes(ex))) continue;

          if (entry.isFile() && pattern.test(entry.name)) {
            files.push(fullPath);
          } else if (
            entry.isDirectory() &&
            !['node_modules', '.git', '.expo'].includes(entry.name)
          ) {
            await scan(fullPath);
          }
        }
      } catch (error) {
        // Directory might not exist, skip silently
      }
    }

    await scan(dir);
    return files;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const migrator = new ReactNativeMigrator(process.cwd(), dryRun);

  migrator.executeReactNativeMigration().catch(error => {
    console.error('React Native migration failed:', error);
    process.exit(1);
  });
}
