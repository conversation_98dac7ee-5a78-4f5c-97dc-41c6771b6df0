// Main orchestrator for the formula generation process.
import { AIResponse } from '../../types.ts';
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { logger } from '../../src/utils/security.ts';
import { generateCacheKey, saveToCache } from '../../src/utils/caching.ts';
import { generateQuickExplanation } from '../../src/utils/simple-explainer.ts';
import { extractTonesFromFormula, generateMockInventoryTones, checkForMixingOpportunities } from './utils.ts';
import {
  findProvenFormula,
  generateScenarioHash,
  createDiagnosisSummary,
  createDesiredResultSummary,
  saveAsProvenFormula,
  generateContentHash
} from './proven-formulas.ts';
import { generateAiFormula } from './ai.ts';
import { handleAiResponse } from './response-handler.ts';
import { validateWithTimeout } from '../../src/utils/basic-validator.ts';
import { evaluateCatalogConsistency } from './catalog.ts';

export async function generateFormula(payload: any, salonId: string, supabase: SupabaseClient, openaiApiKey: string): Promise<AIResponse> {
    const { diagnosis, desiredResult, brand, line, inventoryLevel } = payload;

    try {
        const scenarioHash = await generateScenarioHash(diagnosis, desiredResult, brand || '', line);
        const diagnosisSummary = createDiagnosisSummary(diagnosis);
        const desiredResultSummary = createDesiredResultSummary(desiredResult);

        logger.info('Checking for proven formulas:', { scenarioHash, diagnosisSummary, desiredResultSummary, brand, line });

        const provenFormula = await findProvenFormula(supabase, scenarioHash, diagnosisSummary, desiredResultSummary, brand || '', salonId);

        if (provenFormula) {
            logger.info('Using proven formula instead of generating new one:', { formulaId: provenFormula.id });
            const formulationData = provenFormula.formula;
            formulationData.provenFormula = {
                id: provenFormula.id,
                successCount: provenFormula.success_count,
                avgRating: provenFormula.avg_rating,
                totalUses: provenFormula.total_uses,
                isProven: true,
                confidence: Math.min(0.95, 0.7 + (provenFormula.avg_rating / 5.0) * 0.25)
            };
            if (!formulationData.warnings) {
                formulationData.warnings = [];
            }
            formulationData.warnings.unshift(
                `✅ FÓRMULA PROBADA: Esta fórmula ha sido exitosa ${provenFormula.success_count} veces con una calificación promedio de ${provenFormula.avg_rating}/5.0`
            );

            const basicWarnings = await validateWithTimeout(formulationData, diagnosis, brand, 1000, desiredResult);
            if (basicWarnings.length > 0) {
                formulationData.warnings.push(...basicWarnings);
            }

            let formulaText = `# ${formulationData.formulaTitle}\n\n`;
            // ... (generate markdown text for proven formula)

            return {
                success: true,
                data: {
                    formulaText,
                    formulationData,
                    isProvenFormula: true,
                    totalTokens: 0,
                },
            };
        }

        logger.info('No suitable proven formula found, generating new formula with AI');

        const aiData = await generateAiFormula(payload, salonId, supabase, openaiApiKey);
        const aiResponse = aiData.choices[0].message.content;

        const { formulationData, formulaText } = await handleAiResponse(aiResponse, payload, supabase);
        
        const inputTokens = aiData.usage.prompt_tokens;
        const outputTokens = aiData.usage.completion_tokens;
        const totalTokens = aiData.usage.total_tokens;

        if (formulationData && formulationData.steps) {
            await saveAsProvenFormula(supabase, scenarioHash, diagnosisSummary, desiredResultSummary, formulationData, brand || 'Generic', line);
            formulationData.provenFormula = {
                isProven: false,
                isPending: true,
                confidence: 0.75,
                needsValidation: true
            };
            if (!formulationData.warnings) {
                formulationData.warnings = [];
            }
            formulationData.warnings.push(
                'Fórmula calculada según diagnóstico: Revisa los pasos y ajusta según tu criterio profesional.'
            );
        }

        let simpleExplanations: string[] = [];
        let mixingSuggestions: string[] = [];
        let enhancedQuickSummary = '';

        const [explanationResult, mixingResult] = await Promise.allSettled([
            formulationData && diagnosis ? Promise.race([
                generateQuickExplanation(formulationData, diagnosis),
                new Promise<string[]>((_, reject) => setTimeout(() => reject(new Error('Timeout')), 500))
            ]) : Promise.resolve([]),
            (inventoryLevel && formulationData && formulationData.steps) ? (async () => {
                const extractedTones = extractTonesFromFormula(formulationData);
                if (extractedTones.length > 0) {
                    const mockAvailableTones = generateMockInventoryTones(brand, line);
                    return await checkForMixingOpportunities(extractedTones, mockAvailableTones, 300);
                }
                return [];
            })() : Promise.resolve([])
        ]);

        if (explanationResult.status === 'fulfilled' && explanationResult.value.length > 0) {
            simpleExplanations = explanationResult.value;
            if (simpleExplanations.length > 0) {
                enhancedQuickSummary = simpleExplanations[0];
            }
            formulationData.explanations = simpleExplanations;
            formulationData.quickSummary = enhancedQuickSummary;
        }

        if (mixingResult.status === 'fulfilled' && mixingResult.value.length > 0) {
            mixingSuggestions = mixingResult.value;
            formulationData.mixingSuggestions = mixingSuggestions;
        }

        const contentHash = await generateContentHash(formulationData);
        const catalogEvaluation = await evaluateCatalogConsistency(supabase, brand || null, line || null, formulationData);

        const inputHash = generateCacheKey('generate_formula', { diagnosis, desiredResult, brand, line });
        await saveToCache(supabase, salonId, 'generate_formula', inputHash, payload, 
            { formulaText, formulationData, quickSummary: enhancedQuickSummary, explanations: simpleExplanations, scenarioHash, contentHash, catalogEvaluation },
            'gpt-4o', totalTokens, 0
        );

        return {
            success: true,
            data: {
                formulaText,
                formulationData,
                explanations: simpleExplanations,
                quickSummary: enhancedQuickSummary,
                mixingSuggestions,
                totalTokens,
                isProvenFormula: false,
                scenarioHash,
                contentHash,
                catalogEvaluation,
            },
        };

    } catch (error: any) {
        logger.error('Error in generateFormula orchestrator', { error: error.message, stack: error.stack });
        return { success: false, error: error.message };
    }
}