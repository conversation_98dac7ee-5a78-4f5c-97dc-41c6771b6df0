// Example usage of the new inventory analytics store
// This file demonstrates how to use the analytics functionality
// in React components after the refactoring

import React, { useEffect, useMemo } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useInventoryStore } from '../src/stores/inventory-store';
import {
  useInventoryAnalyticsStore,
  inventoryAnalytics,
} from '../src/stores/inventory-analytics-store';

export function InventoryAnalyticsExample() {
  // Get data from main inventory store
  const { products, movements, loadProducts } = useInventoryStore();

  // Get analytics state and functions
  const {
    currentReport,
    isLoadingReport,
    lowStockProducts,
    isLoadingLowStock,
    reportSettings,
    _updateReportSettings,
  } = useInventoryAnalyticsStore();

  // Load inventory data when component mounts
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  // Generate analytics on data change
  useEffect(() => {
    if (products.length > 0) {
      // Load analytics data
      inventoryAnalytics.loadReport(products, movements);
      inventoryAnalytics.loadLowStock();
    }
  }, [products, movements]);

  // Calculate real-time analytics
  const liveAnalytics = useMemo(() => {
    if (products.length === 0) return null;

    return {
      totalValue: inventoryAnalytics.getTotalValue(products),
      frequentlyUsed: inventoryAnalytics.getFrequentlyUsed(products, movements, 5),
      costByCategory: inventoryAnalytics.getCostByCategory(products),
      stockAlerts: inventoryAnalytics.getStockAlerts(products),
      efficiencyMetrics: inventoryAnalytics.getEfficiencyMetrics(products, movements),
    };
  }, [products, movements]);

  // Example of consumption analysis for a specific product
  const exampleConsumptionAnalysis = useMemo(() => {
    if (products.length === 0) return null;

    const firstProduct = products[0];
    return inventoryAnalytics.getConsumptionAnalysis(firstProduct.id, 'monthly', movements, id =>
      products.find(p => p.id === id)
    );
  }, [products, movements]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Inventory Analytics Dashboard</Text>

      {/* Main Report Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Main Report</Text>
        {isLoadingReport ? (
          <Text>Loading report...</Text>
        ) : currentReport ? (
          <View>
            <Text>Total Value: ${currentReport.totalValue.toFixed(2)}</Text>
            <Text>Low Stock Items: {currentReport.lowStockCount}</Text>
            <Text>Out of Stock Items: {currentReport.outOfStockCount}</Text>
            <Text>Overstock Items: {currentReport.overstockCount}</Text>
            <Text>Most Used Products: {currentReport.mostUsedProducts.length}</Text>
            <Text>Generated: {new Date(currentReport.generatedAt).toLocaleDateString()}</Text>
          </View>
        ) : (
          <Text>No report available</Text>
        )}
      </View>

      {/* Low Stock Section */}
      <View style={styles.analyticsSection}>
        <Text style={styles.analyticsTitle}>Low Stock Products</Text>
        {isLoadingLowStock ? (
          <Text>Loading low stock data...</Text>
        ) : lowStockProducts.length > 0 ? (
          lowStockProducts.map((product, index) => (
            <View key={index} style={styles.lowStockItem}>
              <Text>
                {product.name} - {product.brand}
              </Text>
              <Text>
                Stock: {product.stock_ml}ml / Min: {product.minimum_stock_ml}ml
              </Text>
              <Text>Remaining: {product.percentage_remaining.toFixed(1)}%</Text>
            </View>
          ))
        ) : (
          <Text>No low stock products</Text>
        )}
      </View>

      {/* Live Analytics Section */}
      <View style={styles.analyticsSection}>
        <Text style={styles.analyticsTitle}>Live Analytics</Text>
        {liveAnalytics && (
          <View>
            <Text>Total Inventory Value: ${liveAnalytics.totalValue.toFixed(2)}</Text>
            <Text>Stock Alerts: {liveAnalytics.stockAlerts.length}</Text>
            <Text>
              Fast Moving Products: {liveAnalytics.efficiencyMetrics.fastMovingProducts.length}
            </Text>
            <Text>
              Dead Stock Value: ${liveAnalytics.efficiencyMetrics.deadStockValue.toFixed(2)}
            </Text>
            <Text>Stock Accuracy: {liveAnalytics.efficiencyMetrics.stockAccuracy.toFixed(1)}%</Text>
          </View>
        )}
      </View>

      {/* Category Breakdown */}
      <View style={styles.analyticsSection}>
        <Text style={styles.analyticsTitle}>Cost by Category</Text>
        {liveAnalytics?.costByCategory.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <Text>
              {category.category}: ${category.totalCost.toFixed(2)} (
              {category.percentage.toFixed(1)}%)
            </Text>
          </View>
        ))}
      </View>

      {/* Consumption Analysis Example */}
      <View style={styles.analyticsSection}>
        <Text style={styles.analyticsTitle}>Sample Consumption Analysis</Text>
        {exampleConsumptionAnalysis ? (
          <View>
            <Text>Product: {exampleConsumptionAnalysis.productName}</Text>
            <Text>Total Consumed: {exampleConsumptionAnalysis.totalConsumed}</Text>
            <Text>Total Cost: ${exampleConsumptionAnalysis.totalCost.toFixed(2)}</Text>
            <Text>Services: {exampleConsumptionAnalysis.servicesCount}</Text>
            <Text>
              Average per Service: {exampleConsumptionAnalysis.averagePerService.toFixed(2)}
            </Text>
          </View>
        ) : (
          <Text>No consumption data available</Text>
        )}
      </View>

      {/* Settings Section */}
      <View style={styles.analyticsSection}>
        <Text style={styles.analyticsTitle}>Analytics Settings</Text>
        <Text>Include Least Used: {reportSettings.includeLeastUsed ? 'Yes' : 'No'}</Text>
        <Text>Include Overstock: {reportSettings.includeOverstock ? 'Yes' : 'No'}</Text>
        <Text>Most Used Limit: {reportSettings.mostUsedLimit}</Text>
        <Text>Cache Duration: {reportSettings.cacheDurationMinutes} minutes</Text>
      </View>
    </ScrollView>
  );
}

// Example of how to use analytics in a custom hook
export function useInventoryAnalyticsSummary() {
  const { products, movements } = useInventoryStore();
  const { currentReport, loadInventoryReport } = useInventoryAnalyticsStore();

  // Auto-generate report when data changes
  useEffect(() => {
    if (products.length > 0) {
      loadInventoryReport(products, movements);
    }
  }, [products, movements, loadInventoryReport]);

  return useMemo(
    () => ({
      totalValue: inventoryAnalytics.getTotalValue(products),
      lowStockCount: inventoryAnalytics
        .getStockAlerts(products)
        .filter(a => a.alertType === 'low_stock').length,
      outOfStockCount: inventoryAnalytics
        .getStockAlerts(products)
        .filter(a => a.alertType === 'out_of_stock').length,
      topProducts: inventoryAnalytics.getFrequentlyUsed(products, movements, 5),
      categoryBreakdown: inventoryAnalytics.getCostByCategory(products),
      reportGenerated: currentReport?.generatedAt,
    }),
    [products, movements, currentReport]
  );
}

// Example of backward compatibility - using the inventory store's delegated methods
export function BackwardCompatibilityExample() {
  const {
    _products,
    _movements,
    getTotalInventoryValue,
    getFrequentlyUsedProducts,
    generateInventoryReport,
    _loadInventoryReport,
    _clearInventoryReport,
    getProductsByCategory,
  } = useInventoryStore();

  // These methods still work exactly as before
  const totalValue = getTotalInventoryValue();
  const frequentProducts = getFrequentlyUsedProducts(5);
  const tintProducts = getProductsByCategory('tinte');
  const report = generateInventoryReport();

  return (
    <View>
      <Text>Total Value: ${totalValue.toFixed(2)}</Text>
      <Text>Frequently Used: {frequentProducts.length}</Text>
      <Text>Tint Products: {tintProducts.length}</Text>
      <Text>Report Generated: {report.generatedAt}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: BeautyMinimalTheme.spacing.lg,
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
  },
  title: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xl,
  },
  section: {
    marginBottom: BeautyMinimalTheme.spacing.xl,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.md,
    ...BeautyMinimalTheme.shadows.soft,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  analyticsSection: {
    marginBottom: BeautyMinimalTheme.spacing.xl,
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.md,
    ...BeautyMinimalTheme.shadows.soft,
  },
  analyticsTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.heading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  lowStockItem: {
    padding: BeautyMinimalTheme.spacing.sm,
    backgroundColor: BeautyMinimalTheme.semantic.transparency.status.warning.transparent10,
    borderRadius: BeautyMinimalTheme.radius.md,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    borderLeftWidth: 3,
    borderLeftColor: BeautyMinimalTheme.beautyColors.amber,
  },
  categoryItem: {
    padding: BeautyMinimalTheme.spacing.sm,
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderRadius: BeautyMinimalTheme.radius.md,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },
});
