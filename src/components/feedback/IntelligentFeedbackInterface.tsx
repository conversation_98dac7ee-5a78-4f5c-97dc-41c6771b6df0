/**
 * Intelligent Feedback Interface Component
 *
 * This component provides a comprehensive interface for the Intelligent Feedback System,
 * showing AI performance insights, learning patterns, and confidence scoring.
 *
 * Features:
 * - Real-time performance metrics
 * - Learning insights and patterns
 * - Confidence scoring visualization
 * - Performance alerts and recommendations
 * - Trend analysis and improvements
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
// import { LineChart, BarChart } from 'react-native-chart-kit'; // TODO: Install react-native-chart-kit or replace with Skia charts

// Placeholder component for charts
const ChartPlaceholder: React.FC<{ title: string; height?: number }> = ({
  title,
  height = 200,
}) => (
  <View
    style={[
      {
        height,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        margin: 8,
      },
    ]}
  >
    <Text style={{ color: '#666', fontSize: 14 }}>📊 {title} Chart</Text>
    <Text style={{ color: '#666', fontSize: 12, marginTop: 4 }}>
      Install react-native-chart-kit to view
    </Text>
  </View>
);
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { logger } from '@/utils/logger';
import { intelligentFeedbackSystem } from '@/services/intelligentFeedbackSystem';
import { useAuthStore } from '@/stores/auth-store';

// =====================================================================
// TYPES
// =====================================================================

interface PerformanceMetrics {
  accuracy: number;
  satisfactionScore: number;
  adjustmentRate: number;
  processingTimeAccuracy: number;
  trendAnalysis: {
    weekOverWeek: number;
    monthOverMonth: number;
    quarterOverQuarter: number;
  };
}

interface LearningInsight {
  topSuccessFactors: Array<{ factor: string; impact: number; examples: string[] }>;
  commonFailurePatterns: Array<{ pattern: string; frequency: number; prevention: string[] }>;
  optimizationOpportunities: Array<{
    area: string;
    potentialImprovement: number;
    actionItems: string[];
  }>;
  seasonalTrends: Array<{ period: string; trend: string; recommendation: string }>;
}

interface AlertItem {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
}

// =====================================================================
// MAIN COMPONENT
// =====================================================================

export default function IntelligentFeedbackInterface() {
  const { currentSalon } = useAuthStore();
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [insights, setInsights] = useState<LearningInsight | null>(null);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'insights' | 'alerts' | 'trends'>(
    'overview'
  );

  const loadData = useCallback(async () => {
    if (!currentSalon?.id) return;

    try {
      setIsLoading(true);

      // Load performance metrics
      const performanceData = await intelligentFeedbackSystem.getPerformanceMetrics(
        currentSalon.id
      );
      setMetrics(performanceData);

      // Load learning insights
      const insightData = await intelligentFeedbackSystem.generateLearningInsights(currentSalon.id);
      setInsights(insightData);

      // Load alerts (mock for now)
      setAlerts([
        {
          id: '1',
          type: 'LOW_SATISFACTION',
          severity: 'medium',
          message: 'Client satisfaction dropped to 3.2 this week',
          timestamp: new Date().toISOString(),
          resolved: false,
        },
        {
          id: '2',
          type: 'HIGH_ADJUSTMENT_RATE',
          severity: 'high',
          message: '45% of formulas needed adjustments - review environmental factors',
          timestamp: new Date().toISOString(),
          resolved: false,
        },
      ]);
    } catch (error) {
      logger.error('Failed to load feedback data', 'IntelligentFeedbackInterface', {
        error: error.message,
      });
      Alert.alert('Error', 'Failed to load performance data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [currentSalon?.id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const _screenWidth = Dimensions.get('window').width;
  const _chartConfig = {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    backgroundGradientFrom: BeautyMinimalTheme.semantic.background.primary,
    backgroundGradientTo: BeautyMinimalTheme.semantic.background.primary,
    decimalPlaces: 1,
    color: (opacity = 1) =>
      BeautyMinimalTheme.semantic.transparency.professional.transparent20.replace(
        '0.2',
        opacity.toString()
      ),
    labelColor: (opacity = 1) =>
      BeautyMinimalTheme.semantic.transparency.neutral.shadow.replace('0.12', opacity.toString()),
    style: {
      borderRadius: BeautyMinimalTheme.radius.lg,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: BeautyMinimalTheme.semantic.interactive.primary.default,
    },
  };

  // =====================================================================
  // RENDER METHODS
  // =====================================================================

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'overview', label: 'Overview', icon: 'analytics' },
        { key: 'insights', label: 'Insights', icon: 'bulb' },
        { key: 'alerts', label: 'Alerts', icon: 'warning' },
        { key: 'trends', label: 'Trends', icon: 'trending-up' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[styles.tab, activeTab === tab.key && styles.activeTab]}
          onPress={() => setActiveTab(tab.key as 'overview' | 'insights' | 'alerts' | 'trends')}
        >
          <Ionicons
            name={tab.icon as keyof typeof Ionicons.glyphMap}
            size={20}
            color={
              activeTab === tab.key
                ? BeautyMinimalTheme.semantic.interactive.primary.default
                : BeautyMinimalTheme.semantic.text.secondary
            }
          />
          <Text style={[styles.tabLabel, activeTab === tab.key && styles.activeTabLabel]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverviewTab = () => (
    <ScrollView style={styles.tabContent}>
      {/* Performance Metrics Cards */}
      <View style={styles.metricsGrid}>
        <View style={styles.metricCard}>
          <Text style={styles.metricValue}>{metrics?.accuracy.toFixed(1)}%</Text>
          <Text style={styles.metricLabel}>Accuracy</Text>
          <View
            style={[
              styles.trendIndicator,
              { backgroundColor: getTrendColor(metrics?.trendAnalysis.weekOverWeek || 0) },
            ]}
          >
            <Ionicons
              name={getTrendIcon(metrics?.trendAnalysis.weekOverWeek || 0)}
              size={12}
              color={BeautyMinimalTheme.neutrals.pure}
            />
            <Text style={styles.trendText}>
              {Math.abs(metrics?.trendAnalysis.weekOverWeek || 0).toFixed(1)}%
            </Text>
          </View>
        </View>

        <View style={styles.metricCard}>
          <Text style={styles.metricValue}>{metrics?.satisfactionScore.toFixed(1)}</Text>
          <Text style={styles.metricLabel}>Satisfaction</Text>
          <View
            style={[
              styles.trendIndicator,
              { backgroundColor: getTrendColor(metrics?.trendAnalysis.monthOverMonth || 0) },
            ]}
          >
            <Ionicons
              name={getTrendIcon(metrics?.trendAnalysis.monthOverMonth || 0)}
              size={12}
              color={BeautyMinimalTheme.neutrals.pure}
            />
            <Text style={styles.trendText}>
              {Math.abs(metrics?.trendAnalysis.monthOverMonth || 0).toFixed(1)}%
            </Text>
          </View>
        </View>

        <View style={styles.metricCard}>
          <Text style={styles.metricValue}>{metrics?.adjustmentRate.toFixed(1)}%</Text>
          <Text style={styles.metricLabel}>Adjustments</Text>
          <View
            style={[
              styles.trendIndicator,
              { backgroundColor: getTrendColor(-(metrics?.trendAnalysis.quarterOverQuarter || 0)) },
            ]}
          >
            <Ionicons
              name={getTrendIcon(-(metrics?.trendAnalysis.quarterOverQuarter || 0))}
              size={12}
              color={BeautyMinimalTheme.neutrals.pure}
            />
            <Text style={styles.trendText}>
              {Math.abs(metrics?.trendAnalysis.quarterOverQuarter || 0).toFixed(1)}%
            </Text>
          </View>
        </View>

        <View style={styles.metricCard}>
          <Text style={styles.metricValue}>{metrics?.processingTimeAccuracy.toFixed(1)}%</Text>
          <Text style={styles.metricLabel}>Time Accuracy</Text>
          <View style={[styles.trendIndicator, { backgroundColor: Colors.success }]}>
            <Ionicons name="checkmark" size={12} color={BeautyMinimalTheme.neutrals.pure} />
            <Text style={styles.trendText}>Good</Text>
          </View>
        </View>
      </View>

      {/* Performance Chart */}
      {metrics && (
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Performance Trends (Last 30 Days)</Text>
          <ChartPlaceholder title="Performance Trends" height={220} />
        </View>
      )}

      {/* Quick Insights */}
      <View style={styles.quickInsights}>
        <Text style={styles.sectionTitle}>Quick Insights</Text>
        {insights?.topSuccessFactors.slice(0, 3).map((factor, index) => (
          <View key={index} style={styles.insightCard}>
            <View style={styles.insightIcon}>
              <Ionicons name="trending-up" size={16} color={Colors.success} />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>{factor.factor}</Text>
              <Text style={styles.insightDescription}>Impact: +{factor.impact}% success rate</Text>
            </View>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderInsightsTab = () => (
    <ScrollView style={styles.tabContent}>
      {/* Success Factors */}
      <View style={styles.insightSection}>
        <Text style={styles.sectionTitle}>Top Success Factors</Text>
        {insights?.topSuccessFactors.map((factor, index) => (
          <View key={index} style={styles.factorCard}>
            <View style={styles.factorHeader}>
              <Text style={styles.factorTitle}>{factor.factor}</Text>
              <Text style={styles.factorImpact}>+{factor.impact}%</Text>
            </View>
            <Text style={styles.factorExample}>Example: {factor.examples[0]}</Text>
          </View>
        ))}
      </View>

      {/* Failure Patterns */}
      <View style={styles.insightSection}>
        <Text style={styles.sectionTitle}>Common Failure Patterns</Text>
        {insights?.commonFailurePatterns.map((pattern, index) => (
          <View key={index} style={styles.patternCard}>
            <View style={styles.patternHeader}>
              <Text style={styles.patternTitle}>{pattern.pattern}</Text>
              <Text style={styles.patternFrequency}>{pattern.frequency} times</Text>
            </View>
            <Text style={styles.patternPrevention}>Prevention: {pattern.prevention[0]}</Text>
          </View>
        ))}
      </View>

      {/* Optimization Opportunities */}
      <View style={styles.insightSection}>
        <Text style={styles.sectionTitle}>Optimization Opportunities</Text>
        {insights?.optimizationOpportunities.map((opportunity, index) => (
          <View key={index} style={styles.opportunityCard}>
            <View style={styles.opportunityHeader}>
              <Text style={styles.opportunityTitle}>{opportunity.area}</Text>
              <Text style={styles.opportunityImprovement}>
                +{opportunity.potentialImprovement}%
              </Text>
            </View>
            <Text style={styles.opportunityAction}>Action: {opportunity.actionItems[0]}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderAlertsTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Active Alerts</Text>
      {alerts
        .filter(alert => !alert.resolved)
        .map(alert => (
          <View
            key={alert.id}
            style={[styles.alertCard, { borderLeftColor: getAlertColor(alert.severity) }]}
          >
            <View style={styles.alertHeader}>
              <Ionicons
                name={getAlertIcon(alert.severity)}
                size={20}
                color={getAlertColor(alert.severity)}
              />
              <Text style={[styles.alertType, { color: getAlertColor(alert.severity) }]}>
                {alert.type.replace('_', ' ')}
              </Text>
              <Text style={styles.alertTime}>{new Date(alert.timestamp).toLocaleDateString()}</Text>
            </View>
            <Text style={styles.alertMessage}>{alert.message}</Text>
            <TouchableOpacity style={styles.resolveButton}>
              <Text style={styles.resolveButtonText}>Mark as Resolved</Text>
            </TouchableOpacity>
          </View>
        ))}

      {alerts.filter(alert => !alert.resolved).length === 0 && (
        <View style={styles.noAlertsContainer}>
          <Ionicons name="checkmark-circle" size={48} color={Colors.success} />
          <Text style={styles.noAlertsText}>No active alerts</Text>
          <Text style={styles.noAlertsSubtext}>Your AI system is performing well!</Text>
        </View>
      )}
    </ScrollView>
  );

  const renderTrendsTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Performance Trends</Text>

      {/* Trend Chart */}
      {metrics && (
        <View style={styles.chartContainer}>
          <ChartPlaceholder title="Performance Metrics" height={220} />
        </View>
      )}

      {/* Seasonal Trends */}
      <View style={styles.trendsSection}>
        <Text style={styles.sectionTitle}>Seasonal Trends</Text>
        {insights?.seasonalTrends.map((trend, index) => (
          <View key={index} style={styles.trendCard}>
            <Text style={styles.trendPeriod}>{trend.period}</Text>
            <Text style={styles.trendDescription}>{trend.trend}</Text>
            <Text style={styles.trendRecommendation}>{trend.recommendation}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  // =====================================================================
  // HELPER FUNCTIONS
  // =====================================================================

  const getTrendColor = (value: number) => {
    if (value > 0) return Colors.success;
    if (value < 0) return Colors.error;
    return Colors.warning;
  };

  const getTrendIcon = (value: number) => {
    if (value > 0) return 'trending-up';
    if (value < 0) return 'trending-down';
    return 'remove';
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return Colors.error;
      case 'high':
        return Colors.warning;
      case 'medium':
        return Colors.light.warning;
      default:
        return Colors.text.secondary;
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'alert-circle';
      case 'high':
        return 'warning';
      case 'medium':
        return 'information-circle';
      default:
        return 'checkmark-circle';
    }
  };

  // =====================================================================
  // MAIN RENDER
  // =====================================================================

  if (isLoading && !metrics) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading AI Performance Data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderTabBar()}
      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'insights' && renderInsightsTab()}
        {activeTab === 'alerts' && renderAlertsTab()}
        {activeTab === 'trends' && renderTrendsTab()}
      </ScrollView>
    </View>
  );
}

// =====================================================================
// STYLES
// =====================================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.light.backgroundTertiary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingHorizontal: 16,
  },
  tab: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: 4,
  },
  activeTabLabel: {
    color: Colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  metricCard: {
    width: '48%',
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 8,
  },
  trendIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendText: {
    fontSize: 10,
    color: BeautyMinimalTheme.neutrals.pure,
    fontWeight: '600',
    marginLeft: 4,
  },
  chartContainer: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  quickInsights: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  insightIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  insightDescription: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  insightSection: {
    marginBottom: 24,
  },
  factorCard: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.success,
  },
  factorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  factorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  factorImpact: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.success,
  },
  factorExample: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  patternCard: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.warning,
  },
  patternHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  patternTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  patternFrequency: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.warning,
  },
  patternPrevention: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  opportunityCard: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  opportunityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  opportunityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  opportunityImprovement: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  opportunityAction: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  alertCard: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertType: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  alertTime: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  alertMessage: {
    fontSize: 14,
    color: Colors.text.primary,
    marginBottom: 12,
  },
  resolveButton: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  resolveButtonText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '600',
  },
  noAlertsContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  noAlertsText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
  },
  noAlertsSubtext: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 8,
  },
  trendsSection: {
    marginBottom: 24,
  },
  trendCard: {
    backgroundColor: Colors.light.backgroundTertiary,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  trendPeriod: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 4,
  },
  trendDescription: {
    fontSize: 14,
    color: Colors.text.primary,
    marginBottom: 8,
  },
  trendRecommendation: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
});
