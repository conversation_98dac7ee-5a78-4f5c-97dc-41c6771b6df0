/**
 * Script para clasificar automáticamente todas las líneas sin clasificar
 * Basado en palabras clave en nombres y descripciones
 */

import { ProductLine } from '../src/constants/brands-data';

interface ClassificationRule {
  keywords: string[];
  category: ProductLine['category'];
  isColorLine: boolean;
  priority: number; // Mayor n<PERSON> = mayor prioridad
}

const classificationRules: ClassificationRule[] = [
  // Reglas de coloración (alta prioridad)
  {
    keywords: ['permanent', 'color', 'tinte', 'coloración', 'coloration', 'hair color', 'permanent color'],
    category: 'hair-color',
    isColorLine: true,
    priority: 10
  },
  {
    keywords: ['demi-permanent', 'semi-permanent', 'demi permanent', 'semi permanent'],
    category: 'hair-color',
    isColorLine: true,
    priority: 9
  },
  {
    keywords: ['ammonia-free', 'ammonia free', 'sin amoniaco', 'no ammonia'],
    category: 'hair-color',
    isColorLine: true,
    priority: 8
  },
  {
    keywords: ['fashion color', 'creative color', 'vibrant color', 'vivid', 'fashion', 'creative'],
    category: 'hair-color',
    isColorLine: true,
    priority: 7
  },

  // Reglas de decoloración
  {
    keywords: ['bleach', 'lightening', 'decolorante', 'aclarante', 'bleaching', 'lightener', 'blond', 'blonde'],
    category: 'bleaching',
    isColorLine: true,
    priority: 10
  },
  {
    keywords: ['lift', 'levels', 'niveles', 'powder', 'polvo'],
    category: 'bleaching',
    isColorLine: true,
    priority: 8
  },

  // Reglas de oxidantes/desarrolladores
  {
    keywords: ['developer', 'oxidante', 'activator', 'vol', 'volume', 'peroxide'],
    category: 'developer',
    isColorLine: true,
    priority: 10
  },

  // Reglas de tratamientos (no coloración)
  {
    keywords: ['treatment', 'tratamiento', 'care', 'cuidado', 'repair', 'reparación', 'reconstructive'],
    category: 'treatment',
    isColorLine: false,
    priority: 9
  },
  {
    keywords: ['keratin', 'protein', 'nutrition', 'hydration', 'moisturizing', 'nourishing'],
    category: 'treatment',
    isColorLine: false,
    priority: 8
  },
  {
    keywords: ['bond', 'plex', 'smartbond', 'olaplex', 'wellaplex', 'fibreplex'],
    category: 'treatment',
    isColorLine: false,
    priority: 10
  },

  // Reglas de styling (no coloración)
  {
    keywords: ['styling', 'finish', 'gel', 'mousse', 'spray', 'wax', 'peinado', 'fijación'],
    category: 'styling',
    isColorLine: false,
    priority: 9
  },

  // Reglas especiales
  {
    keywords: ['toner', 'toning', 'matizador', 'matiz', 'neutraliz'],
    category: 'hair-color',
    isColorLine: true,
    priority: 8
  },
  {
    keywords: ['remover', 'reverse', 'eliminador', 'quita'],
    category: 'other',
    isColorLine: false,
    priority: 9
  },

  // Reglas por defecto (baja prioridad)
  {
    keywords: ['professional', 'salon', 'grade'],
    category: 'hair-color',
    isColorLine: true,
    priority: 1
  }
];

/**
 * Clasifica una línea basándose en su nombre y descripción
 */
export function autoClassifyLine(line: { name: string; description?: string }): {
  category: ProductLine['category'];
  isColorLine: boolean;
  confidence: number;
} {
  const text = `${line.name} ${line.description || ''}`.toLowerCase();
  
  let bestMatch: ClassificationRule | null = null;
  let bestScore = 0;
  
  for (const rule of classificationRules) {
    let score = 0;
    let matches = 0;
    
    for (const keyword of rule.keywords) {
      if (text.includes(keyword.toLowerCase())) {
        matches++;
        score += rule.priority;
      }
    }
    
    if (matches > 0 && score > bestScore) {
      bestScore = score;
      bestMatch = rule;
    }
  }
  
  if (bestMatch) {
    return {
      category: bestMatch.category,
      isColorLine: bestMatch.isColorLine,
      confidence: Math.min(bestScore / 10, 1) // Normalizar a 0-1
    };
  }
  
  // Por defecto, asumir que es coloración si no se puede clasificar
  return {
    category: 'hair-color',
    isColorLine: true,
    confidence: 0.1
  };
}

/**
 * Genera el código TypeScript para una línea clasificada
 */
export function generateClassifiedLineCode(line: any): string {
  const classification = autoClassifyLine(line);
  
  return `      {
        id: '${line.id}',
        name: '${line.name}',
        description: '${line.description || ''}',
        category: '${classification.category}',
        isColorLine: ${classification.isColorLine}
      }`;
}

/**
 * Ejemplos de clasificación para verificar
 */
export const testClassifications = [
  // Debería ser hair-color
  { name: 'Majirel', description: 'Permanent color with Ionène G' },
  { name: 'INOA', description: 'Ammonia-free color' },
  { name: 'Igora Royal', description: 'High-performance permanent color' },
  
  // Debería ser bleaching
  { name: 'Blondor', description: 'Professional lightening system' },
  { name: 'Bleaching Powder', description: 'Up to 8 levels' },
  
  // Debería ser treatment
  { name: 'Smartbond', description: 'In-salon protective system' },
  { name: 'Hi Repair', description: 'Reconstructive treatment' },
  { name: 'Keratin Care', description: 'Protein treatment' },
  
  // Debería ser styling
  { name: 'Technique', description: 'Styling and finishing' },
  
  // Debería ser developer
  { name: 'Welloxon', description: 'Developer 10-40 vol' },
  
  // Debería ser other
  { name: 'Color Reverse', description: 'Color remover' },
];

// Función para probar las clasificaciones
export function testAutoClassification() {
  console.log('=== PRUEBAS DE CLASIFICACIÓN AUTOMÁTICA ===\n');
  
  testClassifications.forEach(line => {
    const result = autoClassifyLine(line);
    console.log(`${line.name}: ${result.category} (${result.isColorLine ? 'Coloración' : 'No coloración'}) - Confianza: ${(result.confidence * 100).toFixed(0)}%`);
  });
}

console.log('Script de clasificación automática cargado.');
console.log('Usa autoClassifyLine(line) para clasificar líneas individuales.');
console.log('Usa testAutoClassification() para probar el sistema.');
