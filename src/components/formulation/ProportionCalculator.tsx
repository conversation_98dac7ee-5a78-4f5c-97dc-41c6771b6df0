import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { Plus, Minus, Calculator } from 'lucide-react-native';
import { MixingProportions } from '@/types/visual-formulation';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';
import Colors from '@/constants/colors';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';

interface ProportionCalculatorProps {
  proportions: MixingProportions;
  zones?: Array<{
    zone: string;
    ingredients: Array<{
      name: string;
      amount: number;
      unit: string;
      type: 'color' | 'developer' | 'additive';
    }>;
  }>;
}

export default function ProportionCalculator({ proportions, zones }: ProportionCalculatorProps) {
  const { weightUnit, getTerminology } = useRegionalUnits();
  const [selectedRatio, setSelectedRatio] = useState(proportions.ratio);
  const [baseAmount, setBaseAmount] = useState(proportions.colorAmount.toString());
  const [calculatedDeveloper, setCalculatedDeveloper] = useState(proportions.developerAmount);
  const [calculatedTotal, setCalculatedTotal] = useState(proportions.totalAmount);
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  // Auto-detect ratio from zones if available
  React.useEffect(() => {
    if (zones && zones.length > 0 && !selectedZone) {
      const firstZone = zones[0];
      const colorAmount = firstZone.ingredients
        .filter(i => i.type === 'color')
        .reduce((sum, i) => sum + i.amount, 0);
      const developerAmount = firstZone.ingredients
        .filter(i => i.type === 'developer')
        .reduce((sum, i) => sum + i.amount, 0);

      if (colorAmount > 0 && developerAmount > 0) {
        const detectedRatio = developerAmount / colorAmount;
        const closestRatio = proportions.presets.reduce((prev, curr) => {
          const currRatio = parseFloat(curr.ratio.split(':')[1]);
          const prevRatio = parseFloat(prev.ratio.split(':')[1]);
          return Math.abs(currRatio - detectedRatio) < Math.abs(prevRatio - detectedRatio)
            ? curr
            : prev;
        });
        setSelectedRatio(closestRatio.ratio);
        setBaseAmount(colorAmount.toString());
        calculateAmounts(colorAmount, closestRatio.ratio);
      }
    }
  }, [zones, proportions.presets, selectedZone, calculateAmounts]);

  const calculateAmounts = useCallback((base: number, ratio: string) => {
    const ratioNum = parseFloat(ratio.split(':')[1]);
    const developer = base * ratioNum;
    setCalculatedDeveloper(developer);
    setCalculatedTotal(base + developer);
  }, []);

  const handleRatioSelect = (ratio: string) => {
    setSelectedRatio(ratio);
    const base = parseFloat(baseAmount) || 0;
    calculateAmounts(base, ratio);
  };

  const handleBaseAmountChange = (text: string) => {
    setBaseAmount(text);
    const base = parseFloat(text) || 0;
    calculateAmounts(base, selectedRatio);
  };

  const adjustAmount = (delta: number) => {
    const current = parseFloat(baseAmount) || 0;
    const newAmount = Math.max(0, current + delta);
    handleBaseAmountChange(newAmount.toString());
  };

  const getVisualBar = (amount: number, total: number, color: string) => {
    const percentage = total > 0 ? (amount / total) * 100 : 0;
    return (
      <View style={styles.visualBar}>
        <View style={[styles.visualBarFill, { width: `${percentage}%`, backgroundColor: color }]} />
        <Text style={styles.visualBarText}>
          {amount.toFixed(0)}
          {proportions.unit}
        </Text>
      </View>
    );
  };

  // Calculate visual blocks for each ingredient type
  const getIngredientBlocks = () => {
    if (!zones || zones.length === 0) return [];

    const zone = selectedZone ? zones.find(z => z.zone === selectedZone) : zones[0];
    if (!zone) return [];

    // Group ingredients by type using regional terminology
    const grouped = zone.ingredients.reduce(
      (acc, ing) => {
        const type =
          ing.type === 'color'
            ? getTerminology('color').charAt(0).toUpperCase() + getTerminology('color').slice(1)
            : ing.type === 'developer'
              ? getTerminology('developer').charAt(0).toUpperCase() +
                getTerminology('developer').slice(1)
              : 'Aditivo';
        if (!acc[type]) acc[type] = [];
        acc[type].push(ing);
        return acc;
      },
      {} as Record<string, typeof zone.ingredients>
    );

    return Object.entries(grouped).map(([type, ingredients]) => ({
      type,
      ingredients,
      total: ingredients.reduce((sum, ing) => sum + ing.amount, 0),
      color:
        type === getTerminology('color').charAt(0).toUpperCase() + getTerminology('color').slice(1)
          ? Colors.light.secondary
          : type ===
              getTerminology('developer').charAt(0).toUpperCase() +
                getTerminology('developer').slice(1)
            ? Colors.light.primary
            : Colors.light.warning,
    }));
  };

  const ingredientBlocks = getIngredientBlocks();
  const grandTotal = ingredientBlocks.reduce((sum, block) => sum + block.total, 0);

  return (
    <View style={styles.container}>
      {/* Zone Selector if multiple zones */}
      {zones && zones.length > 1 && (
        <View style={styles.zoneSection}>
          <Text style={styles.sectionTitle}>Seleccionar Zona</Text>
          <View style={styles.zoneButtons}>
            {zones.map(zone => (
              <TouchableOpacity
                key={zone.zone}
                style={[
                  styles.zoneButton,
                  (selectedZone || zones[0].zone) === zone.zone && styles.zoneButtonActive,
                ]}
                onPress={() => setSelectedZone(zone.zone)}
              >
                <Text
                  style={[
                    styles.zoneButtonText,
                    (selectedZone || zones[0].zone) === zone.zone && styles.zoneButtonTextActive,
                  ]}
                >
                  {zone.zone === HairZone.ROOTS
                    ? HairZoneDisplay[HairZone.ROOTS]
                    : zone.zone === HairZone.MIDS
                      ? HairZoneDisplay[HairZone.MIDS]
                      : zone.zone === HairZone.ENDS
                        ? HairZoneDisplay[HairZone.ENDS]
                        : zone.zone}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Visual Blocks for Zone Ingredients */}
      {zones && zones.length > 0 && (
        <View style={styles.blocksSection}>
          <Text style={styles.sectionTitle}>Composición de la Mezcla</Text>
          <View style={styles.blocksContainer}>
            {ingredientBlocks.map(block => {
              const percentage = grandTotal > 0 ? (block.total / grandTotal) * 100 : 0;
              return (
                <View
                  key={block.type}
                  style={[
                    styles.block,
                    {
                      flex: percentage,
                      backgroundColor: block.color,
                    },
                    percentage <= 10 && styles.blockMinWidth,
                  ]}
                >
                  <Text style={styles.blockPercentage}>{percentage.toFixed(0)}%</Text>
                  <Text style={styles.blockLabel}>{block.type}</Text>
                  <Text style={styles.blockAmount}>
                    {block.total}
                    {proportions.unit}
                  </Text>
                </View>
              );
            })}
          </View>

          {/* Ingredient Details */}
          <View style={styles.ingredientDetails}>
            {ingredientBlocks.map(block => (
              <View key={block.type} style={styles.ingredientGroup}>
                <View style={styles.ingredientGroupHeader}>
                  <View style={[styles.colorDot, { backgroundColor: block.color }]} />
                  <Text style={styles.ingredientGroupTitle}>{block.type}</Text>
                </View>
                {block.ingredients.map((ing, idx) => (
                  <Text key={`${block.type}-${ing.name}-${idx}`} style={styles.ingredientItem}>
                    • {ing.name}: {ing.amount}
                    {ing.unit}
                  </Text>
                ))}
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Ratio Selector */}
      <View style={styles.ratioSection}>
        <Text style={styles.sectionTitle}>Seleccionar Proporción</Text>
        <View style={styles.ratioButtons}>
          {proportions.presets.map(preset => (
            <TouchableOpacity
              key={preset.ratio}
              style={[
                styles.ratioButton,
                selectedRatio === preset.ratio && styles.ratioButtonActive,
              ]}
              onPress={() => handleRatioSelect(preset.ratio)}
            >
              <Text
                style={[
                  styles.ratioButtonText,
                  selectedRatio === preset.ratio && styles.ratioButtonTextActive,
                ]}
              >
                {preset.ratio}
              </Text>
              <Text
                style={[
                  styles.ratioButtonDescription,
                  selectedRatio === preset.ratio && styles.ratioButtonDescriptionActive,
                ]}
              >
                {preset.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Amount Input */}
      <View style={styles.inputSection}>
        <Text style={styles.sectionTitle}>
          Cantidad de{' '}
          {getTerminology('color').charAt(0).toUpperCase() + getTerminology('color').slice(1)}
        </Text>
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.adjustButton} onPress={() => adjustAmount(-5)}>
            <Minus size={20} color={Colors.light.primary} />
          </TouchableOpacity>

          <TextInput
            style={styles.amountInput}
            value={baseAmount}
            onChangeText={handleBaseAmountChange}
            keyboardType="numeric"
            placeholder="0"
          />
          <Text style={styles.unitText}>{proportions.unit}</Text>

          <TouchableOpacity style={styles.adjustButton} onPress={() => adjustAmount(5)}>
            <Plus size={20} color={Colors.light.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Visual Representation */}
      <View style={styles.visualSection}>
        <Text style={styles.sectionTitle}>Proporción Visual</Text>

        <View style={styles.visualItem}>
          <View style={styles.visualHeader}>
            <View style={[styles.colorDot, { backgroundColor: Colors.light.secondary }]} />
            <Text style={styles.visualLabel}>
              {getTerminology('color').charAt(0).toUpperCase() + getTerminology('color').slice(1)}
            </Text>
          </View>
          {getVisualBar(parseFloat(baseAmount) || 0, calculatedTotal, Colors.light.secondary)}
        </View>

        <View style={styles.visualItem}>
          <View style={styles.visualHeader}>
            <View style={[styles.colorDot, { backgroundColor: Colors.light.primary }]} />
            <Text style={styles.visualLabel}>
              {getTerminology('developer').charAt(0).toUpperCase() +
                getTerminology('developer').slice(1)}
            </Text>
          </View>
          {getVisualBar(calculatedDeveloper, calculatedTotal, Colors.light.primary)}
        </View>

        <View style={styles.totalContainer}>
          <Calculator size={20} color={Colors.light.primary} />
          <Text style={styles.totalText}>
            Total: {calculatedTotal.toFixed(0)}
            {proportions.unit}
          </Text>
        </View>
      </View>

      {/* Quick Reference Table */}
      <View style={styles.referenceSection}>
        <Text style={styles.referenceSectionTitle}>Referencia Rápida</Text>
        <View style={styles.referenceTable}>
          <View style={styles.referenceHeader}>
            <Text style={styles.referenceHeaderText}>
              {getTerminology('color').charAt(0).toUpperCase() + getTerminology('color').slice(1)}
            </Text>
            <Text style={styles.referenceHeaderText}>{selectedRatio}</Text>
            <Text style={styles.referenceHeaderText}>
              {getTerminology('developer').charAt(0).toUpperCase() +
                getTerminology('developer').slice(1)}
            </Text>
          </View>
          {[30, 45, 60, 90].map(amount => {
            const ratioNum = parseFloat(selectedRatio.split(':')[1]);
            const developer = amount * ratioNum;
            return (
              <View key={amount} style={styles.referenceRow}>
                <Text style={styles.referenceCell}>
                  {amount}
                  {weightUnit}
                </Text>
                <Text style={styles.referenceCellCenter}>→</Text>
                <Text style={styles.referenceCell}>
                  {developer.toFixed(0)}
                  {weightUnit}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.card,
    borderRadius: 15,
    padding: 20,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 10,
  },
  ratioSection: {
    marginBottom: 20,
  },
  ratioButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  ratioButton: {
    flex: 1,
    padding: 12,
    borderRadius: 10,
    backgroundColor: Colors.light.surface,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
    alignItems: 'center',
  },
  ratioButtonActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.highlight,
  },
  ratioButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  ratioButtonTextActive: {
    color: Colors.light.primary,
  },
  ratioButtonDescription: {
    fontSize: 10,
    color: Colors.light.gray,
    marginTop: 2,
  },
  ratioButtonDescriptionActive: {
    color: Colors.light.primary,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  adjustButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  amountInput: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
    minWidth: 80,
    padding: 10,
    backgroundColor: Colors.light.surface,
    borderRadius: 10,
  },
  unitText: {
    fontSize: 18,
    color: Colors.light.gray,
    marginLeft: 5,
  },
  visualSection: {
    marginBottom: 20,
  },
  visualItem: {
    marginBottom: 15,
  },
  visualHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  visualLabel: {
    fontSize: 14,
    color: Colors.light.text,
  },
  visualBar: {
    height: 30,
    backgroundColor: Colors.light.border,
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
  },
  visualBarFill: {
    height: '100%',
    borderRadius: 15,
  },
  visualBarText: {
    position: 'absolute',
    right: 10,
    top: 5,
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.text,
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    gap: 10,
  },
  totalText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  referenceSection: {
    backgroundColor: Colors.light.surface,
    padding: 15,
    borderRadius: 10,
  },
  referenceSectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 10,
    textAlign: 'center',
  },
  referenceTable: {
    gap: 5,
  },
  referenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    marginBottom: 5,
  },
  referenceHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.gray,
    flex: 1,
    textAlign: 'center',
  },
  referenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 3,
  },
  referenceCell: {
    fontSize: 12,
    color: Colors.light.text,
    flex: 1,
    textAlign: 'center',
  },
  referenceCellCenter: {
    fontSize: 12,
    color: Colors.light.gray,
    flex: 1,
    textAlign: 'center',
  },
  zoneSection: {
    marginBottom: 20,
  },
  zoneButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  zoneButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
    alignItems: 'center',
  },
  zoneButtonActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.highlight,
  },
  zoneButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  zoneButtonTextActive: {
    color: Colors.light.primary,
  },
  blocksSection: {
    marginBottom: 20,
  },
  blocksContainer: {
    flexDirection: 'row',
    height: 80,
    borderRadius: 15,
    overflow: 'hidden',
    marginBottom: 15,
    gap: 2,
  },
  block: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 5,
  },
  blockMinWidth: {
    minWidth: 50,
  },
  blockPercentage: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.textLight,
    marginBottom: 2,
  },
  blockLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.light.textLight,
    marginBottom: 2,
  },
  blockAmount: {
    fontSize: 10,
    color: Colors.light.backgroundOpacity90,
  },
  ingredientDetails: {
    backgroundColor: Colors.light.surface,
    borderRadius: 10,
    padding: 15,
    gap: 12,
  },
  ingredientGroup: {
    marginBottom: 10,
  },
  ingredientGroupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  ingredientGroupTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.light.text,
  },
  ingredientItem: {
    fontSize: 12,
    color: Colors.light.gray,
    marginLeft: 20,
    lineHeight: 18,
  },
});
