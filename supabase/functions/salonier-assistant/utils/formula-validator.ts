/**
 * Formula Validator - Simplified for Deployment
 * Ensures basic formula validation without complex dependencies
 */

// Basic validation result interface
export interface ValidationResult {
  isValid: boolean;
  warnings: string[];
  errors: string[];
  suggestions: string[];
}

// Simplified formula validator
export function validateFormula(formula: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    warnings: [],
    errors: [],
    suggestions: []
  };

  // Basic validation checks
  if (!formula) {
    result.isValid = false;
    result.errors.push('Formula is required');
    return result;
  }

  if (typeof formula === 'string') {
    // Text formula - basic checks
    if (formula.length < 10) {
      result.warnings.push('Formula seems too short');
    }
    if (!formula.toLowerCase().includes('vol') && !formula.toLowerCase().includes('developer')) {
      result.warnings.push('No developer volume specified');
    }
  } else if (typeof formula === 'object') {
    // Structured formula - basic checks
    if (!formula.steps || !Array.isArray(formula.steps)) {
      result.warnings.push('Formula should have steps');
    }
    if (!formula.totalTime || formula.totalTime < 1) {
      result.warnings.push('Processing time should be specified');
    }
  }

  return result;
}

// Simplified auto-correct function for compatibility
export function autoCorrectFormula(formula: any): any {
  // For now, just return the formula as-is
  // This can be enhanced later with real auto-correction logic
  return formula;
}

// Export for compatibility
export const FormulaValidator = {
  validate: validateFormula,
};