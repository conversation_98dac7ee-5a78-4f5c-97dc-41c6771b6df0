// Handles parsing, validating, and correcting the AI response.
import { logger } from '../../src/utils/security.ts';
import { validateAndCorrectUnknownProducts, postProcessUnknownProducts, validateBasicColorimetry } from './validators.ts';
import { attemptPartialRecovery } from './utils.ts';
import { extractJsonFromString } from '../../src/utils/json-extractor.ts';
import { mapCategoryToType } from '../../src/utils/helpers.ts';
import { autoSelectTonesInFormula, fetchLineRulesWithGrammar } from './brand-rules.ts';
import { validateWithTimeout } from '../../src/utils/basic-validator.ts';
import { convertToValidatorFormat, convertFromValidatorFormat } from './mappers.ts';
import { autoCorrectFormula } from '../../src/utils/formula-validator.ts';
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';

export async function handleAiResponse(
    aiResponse: string,
    payload: any,
    supabase: SupabaseClient
) {
    const { diagnosis, desiredResult, brand, line } = payload;
    let correctedResponse: string;

    const validationResult = await validateAndCorrectUnknownProducts(aiResponse, brand, line);

    logger.info('AI response validation completed', {
        shouldReject: validationResult.shouldReject,
        correctionsMade: validationResult.correctionsMade,
        correctionsAttempted: validationResult.correctionsAttempted,
        issuesFound: validationResult.issues.length,
        brand: brand,
        line: line
    });

    if (validationResult.shouldReject) {
        logger.error('AI response validation failed - ATTEMPTING partial processing', {
            responsePreview: aiResponse.substring(0, 300),
            brand: brand,
            line: line,
            issues: validationResult.issues,
            correctionsAttempted: validationResult.correctionsAttempted,
            correctionsMade: validationResult.correctionsMade
        });

        const partialResult = await attemptPartialRecovery(
            aiResponse,
            brand,
            line,
            validationResult.issues
        );

        if (partialResult.recoverable) {
            logger.warn('Using partial recovery from failed AI response', {
                partialContent: partialResult.partialFormula ? 'formula extracted' : 'no formula',
                warnings: partialResult.warnings.length
            });

            correctedResponse = partialResult.fallbackResponse || aiResponse;
        } else {
            logger.error('Complete validation failure - no recovery possible');
            throw new Error(
                `AI response validation failed: ${validationResult.issues.join(', ')}. ` +
                `Attempted ${validationResult.correctionsAttempted} corrections, made ${validationResult.correctionsMade}. ` +
                `No partial recovery possible. Response rejected for safety.`
            );
        }
    } else {
        correctedResponse = validationResult.correctedResponse || aiResponse;
    }

    if (validationResult.correctionsMade > 0) {
        logger.info('AI response auto-corrected successfully - PROCESSING corrected version', {
            correctionsMade: validationResult.correctionsMade,
            correctionsApplied: validationResult.correctionsApplied,
            brand: brand,
            line: line,
            originalLength: aiResponse.length,
            correctedLength: correctedResponse.length
        });
    } else {
        logger.debug('AI response validation passed without corrections needed');
    }

    let formulationData = null;
    let formulaText = '';

    try {
        const cleanJsonString = extractJsonFromString(correctedResponse);
        formulationData = JSON.parse(cleanJsonString);

        if (formulationData && formulationData.steps) {
            formulationData.steps.forEach((step: any) => {
                if (step.mix && Array.isArray(step.mix)) {
                    step.mix.forEach((product: any) => {
                        if (product.type) {
                            const validType = mapCategoryToType(product.type);
                            if (product.type !== validType) {
                                product.type = validType;
                            }
                        }
                    });
                }
            });

            const postProcessResult = postProcessUnknownProducts(formulationData, brand, line);
            if (postProcessResult.fixed) {
                logger.info('Post-processed unknown products in formula', {
                    fixesApplied: postProcessResult.fixesApplied.length,
                    fixes: postProcessResult.fixesApplied
                });

                if (!formulationData.warnings) {
                    formulationData.warnings = [];
                }
                formulationData.warnings.push(
                    `ℹ️ Productos corregidos automáticamente: ${postProcessResult.fixesApplied.join(', ')}`
                );
            }
        }

        if (formulationData && formulationData.steps) {
            try {
                const rulePack = await fetchLineRulesWithGrammar(supabase, brand, line);
                const grammar = (rulePack && (rulePack as any).grammar) || null;
                const compat = (rulePack && (rulePack as any).compat) || null;
                const autoTone = autoSelectTonesInFormula(
                    formulationData,
                    grammar,
                    compat,
                    diagnosis,
                    desiredResult,
                    brand,
                    line
                );
                if (autoTone.changed) {
                    if (!formulationData.warnings) formulationData.warnings = [];
                    formulationData.warnings.push(`🔁 Autoselección de tonos: ${autoTone.notes.join('; ')}`);
                }
                const sumTime = (formulationData.steps || []).reduce(
                    (acc: number, s: any) => acc + (Number(s?.processingTime) || 0),
                    0
                );
                if (sumTime && Number(formulationData.totalTime) !== sumTime) {
                    formulationData.totalTime = sumTime;
                    if (!formulationData.warnings) formulationData.warnings = [];
                    formulationData.warnings.push('ℹ️ Tiempo total ajustado a la suma de los pasos');
                }
            } catch (e) {
                logger.warn('Autoselection/compat pass failed', { message: (e as any)?.message || String(e) });
            }
        }

        // ... (rest of the processing logic) ...

        formulaText = `# ${formulationData.formulaTitle}\n\n`;
        formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

        if (formulationData.warnings && formulationData.warnings.length > 0) {
            formulaText += `## ⚠️ Advertencias\n`;
            formulationData.warnings.forEach((warning: string) => {
                formulaText += `- ${warning}\n`;
            });
            formulaText += `\n`;
        }

        formulaText += `## Pasos del Proceso\n\n`;
        formulationData.steps.forEach((step: any) => {
            formulaText += `### ${step.stepTitle}\n\n`;

            if (step.mix && step.mix.length > 0) {
                formulaText += `**Mezcla:**\n`;
                step.mix.forEach((product: any) => {
                    formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
                });
                formulaText += `\n`;
            }

            if (step.technique) {
                formulaText += `**Técnica:** ${step.technique.name}\n`;
                formulaText += `${step.technique.description}\n\n`;
            }

            formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

            if (step.processingTime) {
                formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
            }
        });

        formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;

    } catch (parseError: any) {
        logger.error('Failed to parse formula as JSON, falling back to markdown:', {
            error: parseError.message,
        });
        formulaText = aiResponse;
        formulationData = {
            formulaTitle: 'Fórmula (Markdown)',
            summary: 'Fórmula generada por IA (formato markdown)',
            steps: [],
            totalTime: 60,
            warnings: [
                'Esta fórmula fue generada en formato markdown debido a problemas de parseo JSON',
            ],
        };
    }

    return { formulationData, formulaText };
}