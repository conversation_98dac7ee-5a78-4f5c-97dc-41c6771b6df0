import React, { useRef, useEffect, useState } from 'react';
import { logger } from '@/utils/logger';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import { ChevronRight } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import Colors from '@/constants/colors';
import { HairZone, HairState } from '@/types/hair-diagnosis';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { useFormulation } from '@/service/hooks/useFormulation';
import { useSalonConfigStore } from '@/stores/salon-config-store';

// Import existing components
import ViabilityIndicator from '@/components/ViabilityIndicator';
import FormulaCostBreakdown from '@/components/FormulaCostBreakdown';
import { BrandSelectionModal } from '@/components/BrandSelectionModal';
import AIResultNotification from '@/components/AIResultNotification';

// Import new enhanced components
import { MaterialsSummaryCard } from '@/components/formulation/MaterialsSummaryCard';
import { QuickAdjustPanel, AdjustmentType } from '@/components/formulation/QuickAdjustPanel';
import FormulaTips from '@/components/formulation/FormulaTips';
import HairRecommendations from '@/components/HairRecommendations';
import TonePickerModal from '@/components/formulation/TonePickerModal';
import QuickFixActions from '@/components/formulation/QuickFixActions';

import { BrandConversionForm } from './BrandConversionForm';
import { ColorTransitionCard } from './ColorTransitionCard';

// Import specialized display components
import { GlobalFormulaDisplay, ZonalFormulaDisplay } from '@/service/components/displays';

interface FormulationStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
  analysisResult?: AnalysisResult; // AI analysis result from diagnosis
}

export const FormulationStep: React.FC<FormulationStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave,
  onSaveSilent,
  analysisResult,
}) => {
  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);
  const hasShownNotificationRef = useRef(false);
  const [tonePickerVisible, setTonePickerVisible] = useState(false);

  const {
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,
    formulaCost,
    viabilityAnalysis,
    generateFormulaWithAI,
    analyzeViability,
    formulationData,
    setFormulationData,
    _formulationError,
    _setFormulationError,
  } = useFormulation();

  // Initialize state from parent data only once
  React.useEffect(() => {
    if (!selectedBrand && data.selectedBrand) {
      setSelectedBrand(data.selectedBrand);
    }
    if (!selectedLine && data.selectedLine) {
      setSelectedLine(data.selectedLine);
    }
    if (!formula && data.formula) {
      setFormula(data.formula);
      setIsFormulaFromAI(data.isFormulaFromAI);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once

  // Update parent when specific state changes (avoiding infinite loops)
  React.useEffect(() => {
    if (formula || formulaCost || viabilityAnalysis || formulationData) {
      onUpdate({
        selectedBrand,
        selectedLine,
        formula,
        formulationData,
        isFormulaFromAI,
        formulaCost,
        viabilityAnalysis,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    formula,
    formulaCost,
    viabilityAnalysis,
    formulationData,
    selectedBrand,
    selectedLine,
    isFormulaFromAI,
    // onUpdate removed from deps - parent function changes on every render
  ]);

  // Analyze viability when desired analysis changes
  React.useEffect(() => {
    if (data.desiredAnalysisResult && analysisResult) {
      const viability = analyzeViability(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis
      );
      onUpdate({ viabilityAnalysis: viability });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    data.desiredAnalysisResult,
    analysisResult,
    data.zoneColorAnalysis,
    analyzeViability,
    // onUpdate removed from deps - parent function changes on every render
  ]);

  // Show notification and scroll when formula is generated
  useEffect(() => {
    if ((formula || formulationData) && scrollRef.current && !hasShownNotificationRef.current) {
      // Count what was generated
      let fieldsCount = 0;
      if (formula) fieldsCount++;
      if (formulationData) {
        if (formulationData.products?.length) fieldsCount += formulationData.products.length;
        if (formulationData.steps?.length) fieldsCount += formulationData.steps.length;
        if (formulationData.techniques?.length) fieldsCount += formulationData.techniques.length;
      }

      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Smooth scroll to formula section
      setTimeout(() => {
        scrollRef.current?.scrollTo({ y: 400, animated: true });
      }, 300);
    }

    // Reset the flag when formula is cleared
    if (!formula && !formulationData) {
      hasShownNotificationRef.current = false;
    }
  }, [formula, formulationData]);

  const handleGenerateFormula = async (adjustment?: AdjustmentType) => {
    try {
      // Required fields guard: hair length must be set to ensure accurate quantities
      if (!data.hairLength || data.hairLength <= 0) {
        Alert.alert(
          'Longitud requerida',
          'Por favor indica la longitud del cabello (cm) o selecciona Corto/Medio/Largo/Extra. Esto afecta a las cantidades de la fórmula.',
          [{ text: 'Entendido' }]
        );
        return;
      }
      // Additional essential fields: target level, technique, roots state
      const desiredLevel =
        data.desiredAnalysisResult?.general?.overallLevel ||
        data.desiredAnalysisResult?.zones?.[HairZone.ROOTS]?.desiredLevel?.toString();
      if (!desiredLevel) {
        Alert.alert(
          'Nivel objetivo requerido',
          'Indica el nivel objetivo (p. ej., 7, 8, 9) para planificar sesiones y mezclas.',
          [{ text: 'Entendido' }]
        );
        return;
      }
      const technique = data.desiredAnalysisResult?.general?.technique;
      if (!technique) {
        Alert.alert(
          'Técnica requerida',
          'Selecciona la técnica principal (global, balayage, mechas, etc.).',
          [{ text: 'Entendido' }]
        );
        return;
      }
      const rootsState = data.zoneColorAnalysis?.[HairZone.ROOTS]?.state as HairState | undefined;
      if (!rootsState) {
        Alert.alert(
          'Estado de raíces requerido',
          'Indica si las raíces están Naturales o Teñidas; esto afecta el plan de sesiones.',
          [{ text: 'Entendido' }]
        );
        return;
      }
      // If there's an adjustment, include it in the AI request
      let adjustmentContext = '';
      if (adjustment) {
        if (adjustment.type === 'temperature') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'cooler' ? 'fría' : 'cálida'}`;
        } else if (adjustment.type === 'brightness') {
          adjustmentContext = `\nAjuste solicitado: Hacer la fórmula más ${adjustment.value === 'lighter' ? 'clara' : 'oscura'}`;
        } else if (adjustment.type === 'oxidant') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value === 'increase' ? 'Aumentar' : 'Disminuir'} el volumen del oxidante`;
        } else if (adjustment.type === 'custom') {
          adjustmentContext = `\nAjuste solicitado: ${adjustment.value}`;
        } else if (adjustment.type === 'brand') {
          // Brand change is handled through existing state
        }
      }

      const message = await generateFormulaWithAI(
        analysisResult,
        data.desiredAnalysisResult,
        data.zoneColorAnalysis,
        data.clientId || undefined,
        adjustmentContext,
        {
          hairLengthCm: data.hairLength,
          hairDensity: data.hairDensity,
          hairThickness: data.hairThickness,
        }
      );

      // Save silently when formula is generated
      onSaveSilent?.();

      if (message) {
        // Debug logging removed for production
      }
    } catch (error) {
      logger.error('Error generating formula:', error);
    }
  };

  // Re-normalize quantities when hair length/density/thickness change
  useEffect(() => {
    (async () => {
      try {
        if (!data?.hairLength) return;
        const { normalizeFormulaSteps } = await import('@/services/formula-normalizer');
        setFormulationData(prevData => {
          if (!prevData) return prevData;
          const normalized = normalizeFormulaSteps(
            (prevData.steps || []) as import('@/types/formulation').FormulationStep[],
            selectedBrand,
            selectedLine,
            {
              hairLengthCm: data.hairLength,
              hairDensity: data.hairDensity,
              hairThickness: data.hairThickness,
            }
          );
          if (normalized) {
            // Basic check to prevent infinite loops if normalizer returns same object
            if (JSON.stringify(normalized) !== JSON.stringify(prevData.steps)) {
              return { ...prevData, steps: normalized };
            }
          }
          return prevData;
        });
      } catch {
        // no-op
      }
    })();
  }, [
    data.hairLength,
    data.hairDensity,
    data.hairThickness,
    selectedBrand,
    selectedLine,
    setFormulationData,
  ]);

  const handleAdjustFormula = async (adjustment: AdjustmentType) => {
    if (adjustment.type === 'brand') {
      // Brand change is handled separately
      return;
    }

    // Regenerate formula with adjustment
    await handleGenerateFormula(adjustment);
  };

  const handleBrandSelection = (brand: string, line: string) => {
    if (brandModalType === 'main') {
      setSelectedBrand(brand);
      setSelectedLine(line);
      onSave?.();

      // If in conversion mode and target brand equals source brand, clear source
      if (conversionMode && brand === originalBrand) {
        setOriginalBrand('');
        setOriginalLine('');
        Alert.alert('Atención', 'La marca de destino no puede ser igual a la marca de origen');
      }
    } else {
      // Conversion mode - setting original brand
      if (brand === selectedBrand) {
        Alert.alert('Atención', 'La marca de origen debe ser diferente a tu marca destino');
        return;
      }
      setOriginalBrand(brand);
      setOriginalLine(line);
    }
    setShowBrandModal(false);
  };

  // Enhanced error states and loading feedback
  const renderFormulationUI = () => {
    const technique = data.desiredAnalysisResult?.general?.technique;

    // Show loading state when generating formula
    if (isGeneratingFormula) {
      return (
        <BeautyCard variant="default" style={styles.loadingCard}>
          <View style={styles.loadingContent}>
            <View style={styles.loadingSpinner} />
            <Text style={styles.loadingTitle}>Generando fórmula con IA</Text>
            <Text style={styles.loadingSubtitle}>Analizando cabello y calculando mezclas...</Text>
          </View>
        </BeautyCard>
      );
    }

    // Show error state with specific feedback
    if (!formula && !isGeneratingFormula) {
      return (
        <BeautyCard variant="default" style={styles.errorCard}>
          <View style={styles.errorContent}>
            <Text style={styles.errorTitle}>🤖 ¿Listo para generar tu fórmula?</Text>
            <Text style={styles.errorSubtitle}>
              Presiona &quot;Calcular Fórmula Personalizada&quot; para obtener una mezcla específica
              con IA
            </Text>
            {/* Debug info for development */}
            {__DEV__ && (
              <View style={styles.debugInfo}>
                <Text style={styles.debugTitle}>Debug Info:</Text>
                <Text style={styles.debugText}>
                  Análisis disponible: {analysisResult ? '✅' : '❌'}
                </Text>
                <Text style={styles.debugText}>
                  Color deseado: {data.desiredAnalysisResult ? '✅' : '❌'}
                </Text>
                <Text style={styles.debugText}>
                  Marca seleccionada: {selectedBrand || 'Sin seleccionar'}
                </Text>
                <Text style={styles.debugText}>
                  Línea seleccionada: {selectedLine || 'Sin seleccionar'}
                </Text>
              </View>
            )}
          </View>
        </BeautyCard>
      );
    }

    // Common props for all display components
    const commonProps = {
      formula,
      selectedBrand,
      selectedLine,
      technique: technique || 'full_color',
      formulationData,
      clientName: data.client?.name,
      isFromAI: isFormulaFromAI,
      onEdit: setFormula,
      viabilityAnalysis,
      currentLevel: analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5,
      targetLevel:
        parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7,
    };

    switch (technique) {
      case 'full_color':
      case 'ombre':
      case 'color_correction':
      case 'money_piece':
        return <GlobalFormulaDisplay {...commonProps} />;

      case 'balayage':
      case 'highlights':
      case 'foilyage':
      case 'babylights':
      case 'chunky_highlights':
      case 'reverse_balayage':
        return <ZonalFormulaDisplay {...commonProps} />;

      default:
        // Fallback to zonal display for unknown techniques
        return <ZonalFormulaDisplay {...commonProps} />;
    }
  };

  return (
    <>
      {/* Tone picker modal */}
      <TonePickerModal
        visible={tonePickerVisible}
        brand={selectedBrand}
        line={selectedLine}
        onClose={() => setTonePickerVisible(false)}
        onSelect={shade => {
          setTonePickerVisible(false);
          if (!formulationData) return;
          try {
            const patched = { ...formulationData } as any;
            patched.steps = (patched.steps || []).map((s: any) => ({
              ...s,
              mix: (s.mix || []).map((p: any) => {
                const name = String(p?.productName || '');
                if (name.toLowerCase().includes('seleccionar tono')) {
                  return {
                    ...p,
                    productName: `${selectedBrand} ${selectedLine} ${shade}`.trim(),
                    shade,
                  };
                }
                return p;
              }),
            }));
            setFormulationData(patched);
          } catch {}
        }}
      />
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Fórmula generada con IA"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to formula display with more noticeable offset
            scrollRef.current.scrollTo({ y: 500, animated: true });
            // Add haptic feedback
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContainer}>
          <BeautyCard variant="subtle" style={styles.headerCard}>
            <Text style={styles.stepTitle}>Generación de Fórmula</Text>
            {data.client && (
              <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
            )}
            {/* Badges de estado de validación y catálogo */}
            {formulationData && (
              <View style={{ flexDirection: 'row', gap: 8, marginTop: 6, flexWrap: 'wrap' }}>
                {(() => {
                  const v: any = (formulationData as any).validation;
                  const status: string | undefined = v?.status;
                  if (!status) return null;
                  const bg =
                    status === 'passed'
                      ? '#d1fae5'
                      : status === 'corrected'
                        ? '#fef3c7'
                        : '#fee2e2';
                  const fg =
                    status === 'passed'
                      ? '#065f46'
                      : status === 'corrected'
                        ? '#92400e'
                        : '#991b1b';
                  const label =
                    status === 'passed'
                      ? 'Validación: OK'
                      : status === 'corrected'
                        ? 'Validación: Corregida'
                        : 'Validación: Revisión';
                  return (
                    <View
                      style={{
                        backgroundColor: bg,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 6,
                      }}
                    >
                      <Text style={{ color: fg, fontSize: 12 }}>{label}</Text>
                    </View>
                  );
                })()}
                {(() => {
                  const cs: string | undefined = (formulationData as any).catalogStatus;
                  if (!cs) return null;
                  const bg =
                    cs === 'matched' ? '#e0f2fe' : cs === 'partial' ? '#fef3c7' : '#fee2e2';
                  const fg =
                    cs === 'matched' ? '#075985' : cs === 'partial' ? '#92400e' : '#991b1b';
                  const map: Record<string, string> = {
                    matched: 'Catálogo: OK',
                    partial: 'Catálogo: Parcial',
                    mismatch: 'Catálogo: No coincide',
                    unverified: 'Catálogo: Sin verificar',
                    unknown_brand: 'Catálogo: Marca desconocida',
                  };
                  return (
                    <View
                      style={{
                        backgroundColor: bg,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 6,
                      }}
                    >
                      <Text style={{ color: fg, fontSize: 12 }}>{map[cs] || 'Catálogo'}</Text>
                    </View>
                  );
                })()}
              </View>
            )}
          </BeautyCard>

          {/* Acciones rápidas: elegir tono cuando existan placeholders */}
          {(() => {
            const hasPlaceholder = !!(formulationData as any)?.steps?.some((s: any) =>
              (s?.mix || []).some((p: any) =>
                String(p?.productName || '')
                  .toLowerCase()
                  .includes('seleccionar tono')
              )
            );
            if (!hasPlaceholder) return null;
            return (
              <BeautyCard variant="subtle" style={{ marginTop: 8 }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Text style={{ fontSize: 14 }}>Faltan tonos por seleccionar</Text>
                  <TouchableOpacity
                    onPress={() => setTonePickerVisible(true)}
                    style={{ paddingHorizontal: 12, paddingVertical: 8 }}
                  >
                    <Text style={styles.actionText}>Elegir tonos</Text>
                  </TouchableOpacity>
                </View>
              </BeautyCard>
            );
          })()}

          {/* Reparos rápidos cuando la validación no es OK */}
          {(() => {
            const status: string | undefined = (formulationData as any)?.validation?.status;
            if (!status || status === 'passed') return null;
            return (
              <BeautyCard variant="subtle" style={{ marginTop: 8 }}>
                <QuickFixActions
                  brand={selectedBrand}
                  line={selectedLine}
                  formulationData={formulationData}
                  onApply={next => setFormulationData(next)}
                />
              </BeautyCard>
            );
          })()}
          {/* Color Transition Summary */}
          <ColorTransitionCard
            analysisResult={analysisResult}
            desiredAnalysisResult={data.desiredAnalysisResult}
          />

          <BeautyCard variant="default" style={styles.formGroup}>
            <Text style={styles.label}>Marca</Text>
            <TouchableOpacity
              style={styles.selectContainer}
              onPress={() => {
                setBrandModalType('main');
                setShowBrandModal(true);
              }}
            >
              <Text style={styles.selectText}>{selectedBrand}</Text>
              <ChevronRight size={16} color={Colors.light.gray} />
            </TouchableOpacity>
          </BeautyCard>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea</Text>
            <TouchableOpacity
              style={styles.selectContainer}
              onPress={() => {
                setBrandModalType('main');
                setShowBrandModal(true);
              }}
            >
              <Text style={styles.selectText}>{selectedLine}</Text>
              <ChevronRight size={16} color={Colors.light.gray} />
            </TouchableOpacity>
          </View>

          {/* Datos clave (solo lectura para evitar duplicación) */}
          <BeautyCard variant="default" style={styles.formGroup}>
            <Text style={styles.label}>Datos clave</Text>
            <Text style={styles.readonlyItem}>
              Longitud: {data.hairLength ? `${data.hairLength} cm` : '—'}
            </Text>
            <Text style={styles.readonlyItem}>Densidad: {data.hairDensity || '—'}</Text>
            <Text style={styles.readonlyItem}>Grosor: {data.hairThickness || '—'}</Text>
            <Text style={styles.readonlyItem}>
              Raíces: {data.zoneColorAnalysis?.[HairZone.ROOTS]?.state || '—'}
            </Text>
            <Text style={styles.readonlyItem}>
              Nivel objetivo: {data.desiredAnalysisResult?.general?.overallLevel || '—'}
            </Text>
            <Text style={styles.readonlyItem}>
              Técnica: {data.desiredAnalysisResult?.general?.technique || '—'}
            </Text>
          </BeautyCard>

          {/* Brand Conversion Section */}
          <BrandConversionForm
            conversionMode={conversionMode}
            onConversionModeChange={setConversionMode}
            originalFormula={originalFormula}
            onOriginalFormulaChange={setOriginalFormula}
            originalBrand={originalBrand}
            originalLine={originalLine}
            selectedBrand={selectedBrand}
            selectedLine={selectedLine}
            onSelectOriginalBrand={() => {
              setBrandModalType('conversion');
              setShowBrandModal(true);
            }}
            isGenerating={isGeneratingFormula}
            onGenerate={() => handleGenerateFormula()}
          />

          {/* Materials Summary Card - Shows required products immediately after formula generation */}
          {formula && (
            <MaterialsSummaryCard
              formulationData={formulationData}
              formulaText={formula}
              selectedBrand={selectedBrand}
              selectedLine={selectedLine}
            />
          )}

          {/* Product Mapping Debug Panel - Development only */}
          {__DEV__ && formula && formulationData?.products && (
            <BeautyCard variant="subtle" style={styles.debugCard}>
              <Text style={styles.debugCardTitle}>🔍 Product Mapping Debug</Text>
              {formulationData.products.map((product, index) => {
                const isMatched = product.inventoryProductId;
                return (
                  <View key={index} style={styles.productDebugRow}>
                    <View style={styles.productDebugInfo}>
                      <Text style={styles.productAIName}>AI: {product.name}</Text>
                      <Text
                        style={[
                          styles.productMappingStatus,
                          isMatched ? styles.mappingSuccess : styles.mappingFailed,
                        ]}
                      >
                        {isMatched
                          ? `✅ Mapeado: ${product.inventoryProductName}`
                          : '❌ Sin mapear'}
                      </Text>
                      {product.matchConfidence && (
                        <Text style={styles.productConfidence}>
                          Confianza: {Math.round(product.matchConfidence * 100)}%
                        </Text>
                      )}
                    </View>
                  </View>
                );
              })}
            </BeautyCard>
          )}

          {/* Quick Adjust Panel - Always visible after formula generation */}
          {formula && (
            <QuickAdjustPanel
              onAdjustFormula={handleAdjustFormula}
              onChangeBrand={(brand, line) => {
                setSelectedBrand(brand);
                setSelectedLine(line);
                handleGenerateFormula({ type: 'brand', brand, line });
              }}
              currentBrand={selectedBrand}
              currentLine={selectedLine}
              isGenerating={isGeneratingFormula}
            />
          )}

          {/* Adaptive Formulation UI - Renders appropriate component based on technique */}
          {renderFormulationUI()}

          {/* Intelligent Tips and Recommendations - Only show after formula is generated */}
          {formula && analysisResult && data.desiredAnalysisResult && (
            <>
              <FormulaTips
                analysis={analysisResult}
                technique={data.desiredAnalysisResult.general?.technique || 'full_color'}
                targetLevel={
                  parseInt(
                    data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7'
                  ) || 7
                }
                viabilityAnalysis={data.viabilityAnalysis}
              />

              <HairRecommendations
                analysis={analysisResult}
                desiredAnalysis={data.desiredAnalysisResult}
              />
            </>
          )}

          {/* Viability Analysis */}
          {viabilityAnalysis && <ViabilityIndicator analysis={viabilityAnalysis} />}

          {/* Formula Cost Breakdown - Clean Production Version */}
          {formulaCost && (
            <FormulaCostBreakdown
              cost={formulaCost}
              isRealCost={
                formulaCost.hasAllRealCosts === true &&
                useSalonConfigStore.getState().configuration.inventoryControlLevel !==
                  'solo-formulas'
              }
            />
          )}

          {/* Continue Button */}
          {formula && (
            <BeautyButton
              title="Continuar al Resultado Final"
              onPress={onNext}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.continueButton}
            />
          )}

          {/* Brand Selection Modal */}
          <BrandSelectionModal
            visible={showBrandModal}
            onClose={() => setShowBrandModal(false)}
            onSelectBrand={handleBrandSelection}
            currentBrand={brandModalType === 'main' ? selectedBrand : originalBrand}
            currentLine={brandModalType === 'main' ? selectedLine : originalLine}
            title={
              brandModalType === 'main' ? 'Seleccionar Marca y Línea' : 'Marca y Línea Original'
            }
            isConversionMode={brandModalType === 'conversion'}
            sourceBrand={brandModalType === 'conversion' ? originalBrand : undefined}
            sourceLine={brandModalType === 'conversion' ? originalLine : undefined}
          />
        </View>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },

  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    ...getTypographyStyle('body'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },

  formGroup: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  label: {
    ...getTypographyStyle('bodyEmphasis'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  selectContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
  },
  selectText: {
    fontSize: 15,
  },
  readonlyItem: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
  },

  continueButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  // Loading states
  loadingCard: {
    marginVertical: BeautyMinimalTheme.spacing.lg,
    padding: BeautyMinimalTheme.spacing.xl,
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
  },
  loadingSpinner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderColor: Colors.light.primary + '30',
    borderTopColor: Colors.light.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  loadingTitle: {
    ...getTypographyStyle('bodyEmphasis'),
    color: Colors.light.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  loadingSubtitle: {
    ...getTypographyStyle('body'),
    color: Colors.light.gray,
    textAlign: 'center',
  },
  // Error states
  errorCard: {
    marginVertical: BeautyMinimalTheme.spacing.lg,
    padding: BeautyMinimalTheme.spacing.xl,
    borderColor: Colors.light.accent + '40',
    borderWidth: 1,
  },
  errorContent: {
    alignItems: 'center',
  },
  errorTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: Colors.light.text,
    marginBottom: BeautyMinimalTheme.spacing.sm,
    textAlign: 'center',
  },
  errorSubtitle: {
    ...getTypographyStyle('body'),
    color: Colors.light.gray,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Debug styles
  debugInfo: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    padding: BeautyMinimalTheme.spacing.md,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  debugText: {
    fontSize: 12,
    color: Colors.light.text,
    marginBottom: 2,
  },
  debugCard: {
    marginVertical: BeautyMinimalTheme.spacing.md,
    backgroundColor: Colors.light.surface + '80',
    borderColor: Colors.light.primary + '30',
    borderWidth: 1,
  },
  debugCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  productDebugRow: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
    padding: BeautyMinimalTheme.spacing.sm,
    backgroundColor: Colors.light.background,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  productDebugInfo: {
    flex: 1,
  },
  productAIName: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 2,
  },
  productMappingStatus: {
    fontSize: 12,
    marginBottom: 2,
  },
  mappingSuccess: {
    color: '#16a34a',
  },
  mappingFailed: {
    color: '#dc2626',
  },
  productConfidence: {
    fontSize: 11,
    color: Colors.light.gray,
  },
  actionText: {
    color: Colors.light.actionBlue,
    fontWeight: '600',
  },
});
