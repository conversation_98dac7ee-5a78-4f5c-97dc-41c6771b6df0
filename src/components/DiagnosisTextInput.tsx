import React from 'react';
import { StyleSheet, Text, View, TextInput, Animated } from 'react-native';
import { CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { a11y } from '@/utils/accessibility';

interface DiagnosisTextInputProps {
  label: string;
  value: string;
  onChangeText: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  isFromAI?: boolean;
  keyboardType?: 'default' | 'numeric' | 'decimal-pad';
  maxLength?: number;
  editable?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
}

export default function DiagnosisTextInput({
  label,
  value,
  onChangeText,
  placeholder = '',
  required = false,
  isFromAI = false,
  keyboardType = 'default',
  maxLength,
  editable = true,
  multiline = false,
  numberOfLines,
}: DiagnosisTextInputProps) {
  const [hasBeenEdited, setHasBeenEdited] = React.useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const borderColorAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.95)).current;

  // Animate when value appears from AI
  React.useEffect(() => {
    if (value && isFromAI && !hasBeenEdited) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(borderColorAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [value, isFromAI, hasBeenEdited, fadeAnim, borderColorAnim, scaleAnim]);

  // Pre-calculate animated styles to avoid inline interpolation
  const animatedBorderColor = borderColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.light.border, Colors.light.success],
  });

  const animatedBackgroundColor = borderColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.light.surface, Colors.light.success + '08'],
  });

  const handleChange = (text: string) => {
    onChangeText(text);
    setHasBeenEdited(true);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label} {required && <Text style={styles.required}>*</Text>}
      </Text>

      <Animated.View
        style={[
          styles.inputWrapper,
          value && isFromAI && !hasBeenEdited
            ? {
                transform: [{ scale: scaleAnim }],
                borderColor: animatedBorderColor,
                backgroundColor: animatedBackgroundColor,
              }
            : {},
        ]}
      >
        <TextInput
          style={styles.input}
          value={value}
          onChangeText={handleChange}
          placeholder={placeholder}
          placeholderTextColor={Colors.light.gray}
          keyboardType={keyboardType}
          maxLength={maxLength}
          editable={editable}
          multiline={multiline}
          numberOfLines={numberOfLines}
          {...a11y.input(
            label + (required ? ', obligatorio' : ''),
            placeholder ? `Ejemplo: ${placeholder}` : undefined,
            value
          )}
          accessibilityState={{
            required: required,
          }}
        />
        {value && isFromAI && !hasBeenEdited && (
          <Animated.View
            style={[styles.checkIcon, { opacity: fadeAnim }]}
            accessibilityLabel="Campo completado por IA"
          >
            <CheckCircle size={20} color={Colors.light.success} />
          </Animated.View>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  required: {
    color: Colors.light.error,
  },
  inputWrapper: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
  },
  checkIcon: {
    paddingRight: 16,
  },
});
