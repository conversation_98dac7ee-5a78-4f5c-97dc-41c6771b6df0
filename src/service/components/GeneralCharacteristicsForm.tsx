import React, { memo, useMemo } from 'react';
import { Text, StyleSheet } from 'react-native';
import { BeautyCard } from '@/components/beauty';
import DiagnosisSelector from '@/components/DiagnosisSelector';
import ReadOnly<PERSON>ield from '@/components/ReadOnlyField';
import { COLORIMETRY_LAWS } from '@/utils/professional-colorimetry';
import { HairZone } from '@/types/hair-diagnosis';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useDiagnosisContext } from '@/service/contexts/DiagnosisContext';

const GeneralCharacteristicsFormComponent: React.FC = () => {
  const { data, onUpdate, isDataFromAI, analysisResult } = useDiagnosisContext();
  return (
    <BeautyCard variant="default" style={styles.section}>
      <Text style={styles.sectionTitle}>Características Generales</Text>

      <DiagnosisSelector
        label="Grosor del cabello"
        value={data.hairThickness}
        options={['Fino', 'Medio', 'Grueso']}
        onValueChange={value => onUpdate({ hairThickness: value })}
        required
        isFromAI={isDataFromAI}
      />

      <DiagnosisSelector
        label="Densidad del cabello"
        value={data.hairDensity}
        options={['Baja', 'Media', 'Alta']}
        onValueChange={value => onUpdate({ hairDensity: value })}
        required
        isFromAI={isDataFromAI}
      />

      <DiagnosisSelector
        label="Tono predominante"
        value={data.overallTone}
        options={[
          'Rubio platino',
          'Rubio claro',
          'Rubio medio',
          'Rubio oscuro',
          'Castaño claro',
          'Castaño medio',
          'Castaño oscuro',
          'Negro',
        ]}
        onValueChange={value => onUpdate({ overallTone: value })}
        required
        isFromAI={isDataFromAI}
        showColorIndicator={true}
      />

      <DiagnosisSelector
        label="Reflejo predominante"
        value={data.overallReflect}
        options={['Frío', 'Cálido', 'Neutro']}
        onValueChange={value => onUpdate({ overallReflect: value })}
        required
        isFromAI={isDataFromAI}
        showColorIndicator={true}
        showTemperatureIndicator={true}
      />

      {useMemo(() => {
        const rootsLevel = data.zoneColorAnalysis?.[HairZone.ROOTS]?.level;
        const avgLevel = analysisResult?.averageLevel;
        const level = Math.max(1, Math.min(10, Math.floor(Number(rootsLevel ?? avgLevel ?? 0))));
        if (!level || isNaN(level)) return null;
        const map =
          COLORIMETRY_LAWS.NEUTRALIZATION_MAP[
            level as keyof typeof COLORIMETRY_LAWS.NEUTRALIZATION_MAP
          ];
        const lines = [
          `Pigmento subyacente: ${map?.underlying || '—'}`,
          `Neutralizar con: ${map?.neutralizer || '—'}`,
        ];
        return <ReadOnlyField label="Fondo de aclaración (auto)" lines={lines} />;
      }, [data.zoneColorAnalysis, analysisResult?.averageLevel])}
    </BeautyCard>
  );
};

// Export memoized component
export const GeneralCharacteristicsForm = memo(GeneralCharacteristicsFormComponent);

// Display name for debugging
GeneralCharacteristicsForm.displayName = 'GeneralCharacteristicsForm';

const styles = StyleSheet.create({
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
});
