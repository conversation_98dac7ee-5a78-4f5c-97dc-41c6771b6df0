import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { Product, StockMovement, ConsumptionAnalysis, InventoryReport } from '@/types/inventory';

// Type for low stock products from RPC function
interface LowStockProduct {
  product_id: string;
  brand: string;
  name: string;
  category: string;
  stock_ml: number;
  minimum_stock_ml: number;
  percentage_remaining: number;
  color_code?: string;
}

interface InventoryAnalyticsStore {
  // Report state
  currentReport: InventoryReport | null;
  isLoadingReport: boolean;

  // Low stock state
  lowStockProducts: LowStockProduct[];
  isLoadingLowStock: boolean;

  // Cached analytics data
  cachedConsumptionAnalysis: Map<string, ConsumptionAnalysis>;
  cacheExpiryTime: Map<string, number>;

  // Report generation preferences
  reportSettings: {
    includeLeastUsed: boolean;
    includeOverstock: boolean;
    mostUsedLimit: number;
    leastUsedLimit: number;
    categoryBreakdown: boolean;
    cacheDurationMinutes: number;
  };

  // Analytics Actions
  generateInventoryReport: (products: Product[], movements: StockMovement[]) => InventoryReport;
  loadInventoryReport: (products: Product[], movements: StockMovement[]) => void;
  clearInventoryReport: () => void;

  // Low Stock Intelligence
  loadLowStockProducts: () => Promise<void>;
  getLowStockProducts: () => LowStockProduct[];
  getProductStockStatus: (product: Product) => 'ok' | 'low' | 'out' | 'overstock';
  getPredictedStockOut: (product: Product, movements: StockMovement[]) => Date | null;

  // Consumption Analysis
  getConsumptionAnalysis: (
    productId: string,
    period: 'daily' | 'weekly' | 'monthly',
    movements: StockMovement[],
    getProduct: (id: string) => Product | undefined
  ) => ConsumptionAnalysis | null;

  // Advanced Analytics
  getTotalInventoryValue: (products: Product[]) => number;
  getFrequentlyUsedProducts: (
    products: Product[],
    movements: StockMovement[],
    limit?: number
  ) => Product[];
  getProductsByCategory: (products: Product[], category: Product['category']) => Product[];
  calculateCostByCategory: (products: Product[]) => Array<{
    category: Product['category'];
    totalCost: number;
    percentage: number;
  }>;

  // Business Intelligence
  getInventoryTurnover: (products: Product[], movements: StockMovement[]) => Map<string, number>;
  getSeasonalTrends: (
    movements: StockMovement[],
    months?: number
  ) => Array<{
    month: string;
    totalConsumption: number;
    averageOrderValue: number;
    topCategories: string[];
  }>;
  getStockEfficiencyMetrics: (
    products: Product[],
    movements: StockMovement[]
  ) => {
    deadStockValue: number;
    fastMovingProducts: Product[];
    slowMovingProducts: Product[];
    stockAccuracy: number;
  };

  // Alert Analytics
  getStockAlerts: (products: Product[]) => Array<{
    productId: string;
    alertType: 'low_stock' | 'out_of_stock' | 'overstock' | 'dead_stock';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    recommendedAction: string;
  }>;

  // Cache Management
  clearAnalyticsCache: () => void;
  isAnalyticsCacheValid: (key: string) => boolean;

  // Settings
  updateReportSettings: (settings: Partial<InventoryAnalyticsStore['reportSettings']>) => void;
}

const analyticsLogger = logger.withContext('InventoryAnalytics');

/**
 * Calculate stock metrics for inventory report
 */
function calculateStockMetrics(products: Product[]) {
  const activeProducts = products.filter(p => p.isActive);

  return {
    totalValue: products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0),
    lowStockCount: activeProducts.filter(p => p.currentStock > 0 && p.currentStock <= p.minStock)
      .length,
    outOfStockCount: activeProducts.filter(p => p.currentStock === 0).length,
    overstockCount: activeProducts.filter(p => p.maxStock && p.currentStock > p.maxStock).length,
    activeProducts,
  };
}

/**
 * Calculate most used products from movements
 */
function calculateMostUsedProducts(
  movements: StockMovement[],
  getProduct: (id: string) => Product | undefined
): Array<{ product: Product; usageCount: number; totalConsumed: number }> {
  const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const recentMovements = movements.filter(m => new Date(m.date) >= last30Days);

  // Handle both Spanish and English movement types for backward compatibility
  const consumptionTypes = ['consumo', 'use', 'consumption'];
  const consumptionMap = recentMovements
    .filter(m => consumptionTypes.includes(m.type))
    .reduce(
      (acc, m) => {
        if (!acc[m.productId]) {
          acc[m.productId] = { count: 0, total: 0 };
        }
        acc[m.productId]!.count += 1;
        acc[m.productId]!.total += Math.abs(m.quantity);
        return acc;
      },
      {} as Record<string, { count: number; total: number }>
    );

  return Object.entries(consumptionMap)
    .map(([productId, data]) => {
      const product = getProduct(productId);
      return product
        ? {
            product,
            usageCount: data.count,
            totalConsumed: data.total,
          }
        : null;
    })
    .filter(Boolean)
    .sort((a, b) => b!.totalConsumed - a!.totalConsumed)
    .slice(0, 5) as Array<{
    product: Product;
    usageCount: number;
    totalConsumed: number;
  }>;
}

/**
 * Calculate cost by category with enhanced analytics
 */
function calculateCostByCategory(products: Product[]): Array<{
  category: Product['category'];
  totalCost: number;
  percentage: number;
}> {
  const activeProducts = products.filter(p => p.isActive);
  const categoryTotals = new Map<string, number>();
  let totalCategoryCost = 0;

  const allCategories: Product['category'][] = [
    'tinte',
    'oxidante',
    'decolorante',
    'matizador',
    'tratamiento',
    'aditivo',
    'pre-pigmentacion',
    'otro',
  ];

  activeProducts.forEach(product => {
    const cost = product.currentStock * product.costPerUnit;
    const category = product.category || 'otro';
    categoryTotals.set(category, (categoryTotals.get(category) || 0) + cost);
    totalCategoryCost += cost;
  });

  return allCategories
    .map(category => ({
      category,
      totalCost: categoryTotals.get(category) || 0,
      percentage:
        totalCategoryCost > 0 ? ((categoryTotals.get(category) || 0) / totalCategoryCost) * 100 : 0,
    }))
    .filter(item => item.totalCost > 0)
    .sort((a, b) => b.totalCost - a.totalCost);
}

/**
 * Calculate least used products with enhanced logic
 */
function calculateLeastUsedProducts(
  movements: StockMovement[],
  products: Product[]
): Array<{ product: Product; daysSinceLastUse: number }> {
  const activeProducts = products.filter(p => p.isActive);
  const productLastUse = new Map<string, Date>();

  // Handle both Spanish and English movement types
  const consumptionTypes = ['consumo', 'use', 'consumption'];
  movements
    .filter(m => consumptionTypes.includes(m.type))
    .forEach(m => {
      const existingDate = productLastUse.get(m.productId);
      const movementDate = new Date(m.date);
      if (!existingDate || movementDate > existingDate) {
        productLastUse.set(m.productId, movementDate);
      }
    });

  const now = new Date();
  return activeProducts
    .map(product => {
      const lastUseDate = productLastUse.get(product.id);
      const daysSinceLastUse = lastUseDate
        ? Math.floor((now.getTime() - lastUseDate.getTime()) / (1000 * 60 * 60 * 24))
        : Infinity;
      return {
        product,
        daysSinceLastUse,
      };
    })
    .filter(item => item.daysSinceLastUse > 30 || item.daysSinceLastUse === Infinity)
    .sort((a, b) => b.daysSinceLastUse - a.daysSinceLastUse)
    .slice(0, 5);
}

export const useInventoryAnalyticsStore = create<InventoryAnalyticsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentReport: null,
      isLoadingReport: false,
      lowStockProducts: [],
      isLoadingLowStock: false,
      cachedConsumptionAnalysis: new Map(),
      cacheExpiryTime: new Map(),

      // Default report settings
      reportSettings: {
        includeLeastUsed: true,
        includeOverstock: true,
        mostUsedLimit: 5,
        leastUsedLimit: 5,
        categoryBreakdown: true,
        cacheDurationMinutes: 30,
      },

      // Generate comprehensive inventory report
      generateInventoryReport: (products: Product[], movements: StockMovement[]) => {
        analyticsLogger.startTimer('generateInventoryReport');

        const safeProducts = products || [];
        const safeMovements = movements || [];

        // Get basic product lookup function
        const getProduct = (id: string) => safeProducts.find(p => p.id === id);

        // Calculate stock metrics
        const { totalValue, lowStockCount, outOfStockCount, overstockCount } =
          calculateStockMetrics(safeProducts);

        // Calculate most used products
        const mostUsedProducts = calculateMostUsedProducts(safeMovements, getProduct);

        // Calculate least used products
        const leastUsedProducts = get().reportSettings.includeLeastUsed
          ? calculateLeastUsedProducts(safeMovements, safeProducts)
          : [];

        // Calculate cost by category
        const costByCategory = get().reportSettings.categoryBreakdown
          ? calculateCostByCategory(safeProducts)
          : [];

        const report: InventoryReport = {
          totalValue,
          lowStockCount,
          outOfStockCount,
          overstockCount,
          mostUsedProducts: mostUsedProducts || [],
          leastUsedProducts: leastUsedProducts || [],
          costByCategory: costByCategory || [],
          generatedAt: new Date().toISOString(),
        };

        analyticsLogger.endTimer('generateInventoryReport');
        return report;
      },

      // Load inventory report
      loadInventoryReport: (products: Product[], movements: StockMovement[]) => {
        analyticsLogger.info('Loading inventory report');
        set({ isLoadingReport: true });

        try {
          const report = get().generateInventoryReport(products, movements);
          set({
            currentReport: report,
            isLoadingReport: false,
          });
          analyticsLogger.info('Inventory report loaded successfully');
        } catch (error) {
          analyticsLogger.error('Error loading inventory report:', error);
          set({
            currentReport: null,
            isLoadingReport: false,
          });
        }
      },

      // Clear inventory report
      clearInventoryReport: () => {
        analyticsLogger.info('Clearing inventory report');
        set({
          currentReport: null,
          isLoadingReport: false,
        });
      },

      // Load low stock products from database
      loadLowStockProducts: async () => {
        analyticsLogger.info('Loading low stock products');
        set({ isLoadingLowStock: true });

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            analyticsLogger.warn('No salon ID found, skipping low stock products load');
            set({ isLoadingLowStock: false });
            return;
          }

          const { data, error } = await supabase.rpc('get_low_stock_products', {
            p_salon_id: salonId,
          });

          if (error) {
            analyticsLogger.error('Error loading low stock products:', error);
            throw error;
          }

          set({
            lowStockProducts: data || [],
            isLoadingLowStock: false,
          });

          analyticsLogger.info(`Loaded ${data?.length || 0} low stock products`);
        } catch (error) {
          analyticsLogger.error('Failed to load low stock products:', error);
          set({
            lowStockProducts: [],
            isLoadingLowStock: false,
          });
        }
      },

      // Get low stock products
      getLowStockProducts: () => {
        return get().lowStockProducts;
      },

      // Determine product stock status
      getProductStockStatus: (product: Product) => {
        if (product.currentStock === 0) return 'out';
        if (product.currentStock <= product.minStock) return 'low';
        if (product.maxStock && product.currentStock > product.maxStock) return 'overstock';
        return 'ok';
      },

      // Predict when product will run out
      getPredictedStockOut: (product: Product, movements: StockMovement[]) => {
        const consumptionTypes = ['consumo', 'use', 'consumption'];
        const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        const recentConsumption = movements
          .filter(
            m =>
              m.productId === product.id &&
              consumptionTypes.includes(m.type) &&
              new Date(m.date) >= last30Days
          )
          .reduce((total, m) => total + Math.abs(m.quantity), 0);

        if (recentConsumption === 0) return null;

        const dailyConsumption = recentConsumption / 30;
        const daysUntilEmpty = product.currentStock / dailyConsumption;

        const stockOutDate = new Date();
        stockOutDate.setDate(stockOutDate.getDate() + Math.floor(daysUntilEmpty));

        return stockOutDate;
      },

      // Get detailed consumption analysis
      getConsumptionAnalysis: (
        productId: string,
        period: 'daily' | 'weekly' | 'monthly',
        movements: StockMovement[],
        getProduct: (id: string) => Product | undefined
      ) => {
        const cacheKey = `${productId}_${period}`;

        // Check cache validity
        if (get().isAnalyticsCacheValid(cacheKey)) {
          const cached = get().cachedConsumptionAnalysis.get(cacheKey);
          if (cached) return cached;
        }

        const consumptionTypes = ['consumo', 'use', 'consumption'];
        const productMovements = movements.filter(
          m => m.productId === productId && consumptionTypes.includes(m.type)
        );

        if (productMovements.length === 0) return null;

        const now = new Date();
        const periodDays = period === 'daily' ? 1 : period === 'weekly' ? 7 : 30;
        const startDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000);

        const recentMovements = productMovements.filter(m => new Date(m.date) >= startDate);
        const totalConsumed = recentMovements.reduce((sum, m) => sum + Math.abs(m.quantity), 0);
        const avgConsumption = totalConsumed / periodDays;

        const product = getProduct(productId);
        const _daysUntilEmpty = product ? product.currentStock / avgConsumption : 0;

        const analysis: ConsumptionAnalysis = {
          productId,
          productName: product?.displayName || product?.name || 'Unknown Product',
          totalConsumed,
          totalCost: product ? totalConsumed * product.costPerUnit : 0,
          averagePerService: totalConsumed / Math.max(recentMovements.length, 1),
          servicesCount: recentMovements.length,
          period,
          topClients: [], // Could be enhanced with client data
        };

        // Cache the result
        const cache = get().cachedConsumptionAnalysis;
        const expiry = get().cacheExpiryTime;
        cache.set(cacheKey, analysis);
        expiry.set(cacheKey, Date.now() + get().reportSettings.cacheDurationMinutes * 60 * 1000);

        set({
          cachedConsumptionAnalysis: new Map(cache),
          cacheExpiryTime: new Map(expiry),
        });

        return analysis;
      },

      // Calculate total inventory value
      getTotalInventoryValue: (products: Product[]) => {
        return products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0);
      },

      // Get frequently used products
      getFrequentlyUsedProducts: (products: Product[], movements: StockMovement[], limit = 10) => {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const consumptionTypes = ['consumo', 'use', 'consumption'];
        const usageMap = new Map<string, number>();

        movements.forEach(movement => {
          if (new Date(movement.date) > thirtyDaysAgo && consumptionTypes.includes(movement.type)) {
            const count = usageMap.get(movement.productId) || 0;
            usageMap.set(movement.productId, count + 1);
          }
        });

        return products
          .map(product => ({
            product,
            usage: usageMap.get(product.id) || 0,
          }))
          .filter(item => item.usage > 0)
          .sort((a, b) => b.usage - a.usage)
          .slice(0, limit)
          .map(item => item.product);
      },

      // Get products by category
      getProductsByCategory: (products: Product[], category: Product['category']) => {
        return products.filter(p => p.category === category);
      },

      // Calculate cost distribution by category
      calculateCostByCategory: (products: Product[]) => {
        return calculateCostByCategory(products);
      },

      // Calculate inventory turnover rates
      getInventoryTurnover: (products: Product[], movements: StockMovement[]) => {
        const turnoverMap = new Map<string, number>();
        const consumptionTypes = ['consumo', 'use', 'consumption'];

        products.forEach(product => {
          const totalConsumption = movements
            .filter(m => m.productId === product.id && consumptionTypes.includes(m.type))
            .reduce((sum, m) => sum + Math.abs(m.quantity), 0);

          const averageStock = (product.currentStock + product.minStock) / 2;
          const turnover = averageStock > 0 ? totalConsumption / averageStock : 0;

          turnoverMap.set(product.id, turnover);
        });

        return turnoverMap;
      },

      // Analyze seasonal consumption trends
      getSeasonalTrends: (movements: StockMovement[], months = 12) => {
        const cutoffDate = new Date();
        cutoffDate.setMonth(cutoffDate.getMonth() - months);

        const monthlyData = new Map<
          string,
          {
            totalConsumption: number;
            orderCount: number;
            categories: Map<string, number>;
          }
        >();

        const consumptionTypes = ['consumo', 'use', 'consumption'];
        movements
          .filter(m => consumptionTypes.includes(m.type) && new Date(m.date) >= cutoffDate)
          .forEach(movement => {
            const date = new Date(movement.date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            if (!monthlyData.has(monthKey)) {
              monthlyData.set(monthKey, {
                totalConsumption: 0,
                orderCount: 0,
                categories: new Map(),
              });
            }

            const monthData = monthlyData.get(monthKey)!;
            monthData.totalConsumption += Math.abs(movement.quantity);
            monthData.orderCount += 1;
          });

        return Array.from(monthlyData.entries())
          .map(([month, data]) => ({
            month,
            totalConsumption: data.totalConsumption,
            averageOrderValue: data.totalConsumption / Math.max(data.orderCount, 1),
            topCategories: Array.from(data.categories.entries())
              .sort((a, b) => b[1] - a[1])
              .slice(0, 3)
              .map(([category]) => category),
          }))
          .sort((a, b) => a.month.localeCompare(b.month));
      },

      // Get comprehensive stock efficiency metrics
      getStockEfficiencyMetrics: (products: Product[], movements: StockMovement[]) => {
        const turnoverRates = get().getInventoryTurnover(products, movements);
        const _totalValue = get().getTotalInventoryValue(products);

        const fastMovingProducts = products.filter(p => (turnoverRates.get(p.id) || 0) > 2);
        const slowMovingProducts = products.filter(p => (turnoverRates.get(p.id) || 0) < 0.5);

        const deadStockValue = slowMovingProducts.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );

        // Calculate stock accuracy (simplified metric)
        const stockAccuracy =
          (products.filter(p => p.currentStock >= p.minStock).length /
            Math.max(products.length, 1)) *
          100;

        return {
          deadStockValue,
          fastMovingProducts,
          slowMovingProducts,
          stockAccuracy,
        };
      },

      // Generate intelligent stock alerts
      getStockAlerts: (products: Product[]) => {
        const alerts: Array<{
          productId: string;
          alertType: 'low_stock' | 'out_of_stock' | 'overstock' | 'dead_stock';
          severity: 'low' | 'medium' | 'high' | 'critical';
          message: string;
          recommendedAction: string;
        }> = [];

        products.forEach(product => {
          if (!product.isActive) return;

          // Out of stock
          if (product.currentStock === 0) {
            alerts.push({
              productId: product.id,
              alertType: 'out_of_stock',
              severity: 'critical',
              message: `${product.displayName} está agotado`,
              recommendedAction: 'Realizar pedido inmediatamente',
            });
          }
          // Low stock
          else if (product.currentStock <= product.minStock) {
            alerts.push({
              productId: product.id,
              alertType: 'low_stock',
              severity: 'high',
              message: `${product.displayName} tiene stock bajo (${product.currentStock}/${product.minStock})`,
              recommendedAction: 'Programar reposición pronto',
            });
          }
          // Overstock
          else if (product.maxStock && product.currentStock > product.maxStock) {
            alerts.push({
              productId: product.id,
              alertType: 'overstock',
              severity: 'medium',
              message: `${product.displayName} tiene exceso de stock (${product.currentStock}/${product.maxStock})`,
              recommendedAction: 'Revisar políticas de pedido',
            });
          }
        });

        return alerts.sort((a, b) => {
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return severityOrder[b.severity] - severityOrder[a.severity];
        });
      },

      // Cache management
      clearAnalyticsCache: () => {
        set({
          cachedConsumptionAnalysis: new Map(),
          cacheExpiryTime: new Map(),
        });
        analyticsLogger.info('Analytics cache cleared');
      },

      isAnalyticsCacheValid: (key: string) => {
        const expiryTime = get().cacheExpiryTime.get(key);
        return expiryTime ? Date.now() < expiryTime : false;
      },

      // Update report settings
      updateReportSettings: settings => {
        set(state => ({
          reportSettings: {
            ...state.reportSettings,
            ...settings,
          },
        }));
        analyticsLogger.info('Report settings updated', settings);
      },
    }),
    {
      name: 'inventory-analytics-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        reportSettings: state.reportSettings,
        // Don't persist cache data - it should be regenerated
      }),
    }
  )
);

// Export helper functions for easy access and better developer experience
export const getInventoryAnalytics = () => useInventoryAnalyticsStore.getState();

// Export commonly used analytics functions
export const inventoryAnalytics = {
  generateReport: (products: Product[], movements: StockMovement[]) =>
    useInventoryAnalyticsStore.getState().generateInventoryReport(products, movements),

  loadReport: (products: Product[], movements: StockMovement[]) =>
    useInventoryAnalyticsStore.getState().loadInventoryReport(products, movements),

  clearReport: () => useInventoryAnalyticsStore.getState().clearInventoryReport(),

  getLowStock: () => useInventoryAnalyticsStore.getState().getLowStockProducts(),

  loadLowStock: () => useInventoryAnalyticsStore.getState().loadLowStockProducts(),

  getTotalValue: (products: Product[]) =>
    useInventoryAnalyticsStore.getState().getTotalInventoryValue(products),

  getFrequentlyUsed: (products: Product[], movements: StockMovement[], limit?: number) =>
    useInventoryAnalyticsStore.getState().getFrequentlyUsedProducts(products, movements, limit),

  getByCategory: (products: Product[], category: Product['category']) =>
    useInventoryAnalyticsStore.getState().getProductsByCategory(products, category),

  getCostByCategory: (products: Product[]) =>
    useInventoryAnalyticsStore.getState().calculateCostByCategory(products),

  getConsumptionAnalysis: (
    productId: string,
    period: 'daily' | 'weekly' | 'monthly',
    movements: StockMovement[],
    getProduct: (id: string) => Product | undefined
  ) =>
    useInventoryAnalyticsStore
      .getState()
      .getConsumptionAnalysis(productId, period, movements, getProduct),

  getStockAlerts: (products: Product[]) =>
    useInventoryAnalyticsStore.getState().getStockAlerts(products),

  getEfficiencyMetrics: (products: Product[], movements: StockMovement[]) =>
    useInventoryAnalyticsStore.getState().getStockEfficiencyMetrics(products, movements),

  getSeasonalTrends: (movements: StockMovement[], months?: number) =>
    useInventoryAnalyticsStore.getState().getSeasonalTrends(movements, months),

  getInventoryTurnover: (products: Product[], movements: StockMovement[]) =>
    useInventoryAnalyticsStore.getState().getInventoryTurnover(products, movements),
};
