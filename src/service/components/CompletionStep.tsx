import React, { useState, useEffect, useRef } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Image,
  TextInput,
  Switch,
  Alert,
  Pressable,
} from 'react-native';
import { Camera, Upload, Sparkles, Trophy, Star } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withDelay,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import Colors from '@/constants/colors';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/service/hooks/usePhotoAnalysis';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText } from '@/utils/parseFormula';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';
import { FormulationConsumption } from '@/types/inventory';
import { useInventoryStore } from '@/stores/inventory-store';
import { MaterialsSummaryCard } from '@/components/formulation/MaterialsSummaryCard';
import {
  useCelebrationsEnabled,
  useHapticsEnabled,
  useWhimsyStore,
  useAnimationsEnabled,
} from '@/stores/whimsy-store';

interface CompletionStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack?: () => void;
  onSave?: () => void;
}

// Success celebration animation component
const SuccessCelebration: React.FC<{
  visible: boolean;
  clientName: string;
  satisfaction: number;
}> = ({ visible, clientName, satisfaction }) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const sparkleScale = useSharedValue(0);
  const confettiY = useSharedValue(-100);
  const _celebrationsEnabled = useCelebrationsEnabled();
  const hapticsEnabled = useHapticsEnabled();

  // FIXED: Move useEffect BEFORE conditional return to ensure consistent hook order
  useEffect(() => {
    // Only run animation if celebrations are enabled and component is visible
    if (!_celebrationsEnabled || !visible) return;

    // Trigger success haptic feedback only if enabled
    if (hapticsEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    // Main celebration animation
    scale.value = withSequence(withSpring(1.2, { damping: 8 }), withSpring(1, { damping: 12 }));

    opacity.value = withSpring(1);

    // Sparkles animation with stagger
    sparkleScale.value = withDelay(
      200,
      withSequence(withSpring(1, { damping: 10 }), withDelay(1000, withSpring(0)))
    );

    // Confetti falling
    confettiY.value = withDelay(100, withSpring(50, { damping: 15 }));

    // FIXED: Auto hide after 3 seconds with cleanup
    const timeoutId = setTimeout(() => {
      opacity.value = withSpring(0);
      scale.value = withSpring(0.8);
    }, 3000);

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [visible, _celebrationsEnabled, hapticsEnabled, confettiY, opacity, scale, sparkleScale]);

  const celebrationStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const sparkleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sparkleScale.value }],
  }));

  const confettiStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: confettiY.value }],
  }));

  const getSuccessMessage = () => {
    if (satisfaction >= 5) return `¡${clientName} está radiante con el resultado! 🌟`;
    if (satisfaction >= 4) return `¡Excelente transformación para ${clientName}! ✨`;
    if (satisfaction >= 3) return `¡Buen servicio para ${clientName}! 👏`;
    return `Servicio completado para ${clientName}`;
  };

  // FIXED: Conditional return AFTER all hooks to maintain consistent hook order
  if (!_celebrationsEnabled || !visible) return null;

  return (
    <Animated.View style={[styles.celebrationOverlay, celebrationStyle]}>
      <View style={styles.celebrationContent}>
        <Animated.View style={confettiStyle}>
          <View style={styles.confettiContainer}>
            {[...Array(6)].map((_, i) => (
              <View
                key={i}
                style={[
                  styles.confetti,
                  {
                    backgroundColor: i % 2 ? Colors.light.primary : Colors.light.success,
                    left: `${15 + i * 15}%`,
                    animationDelay: `${i * 100}ms`,
                  },
                ]}
              />
            ))}
          </View>
        </Animated.View>

        <View style={styles.celebrationIcon}>
          <Trophy size={48} color={Colors.light.success} />
          <Animated.View style={[styles.sparkles, sparkleStyle]}>
            <Sparkles size={24} color={Colors.light.warning} />
          </Animated.View>
        </View>

        <Text style={styles.celebrationText}>{getSuccessMessage()}</Text>

        <View style={styles.satisfactionStars}>
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              size={20}
              color={i < satisfaction ? Colors.light.warning : Colors.light.border}
              fill={i < satisfaction ? Colors.light.warning : Colors.common.transparent}
            />
          ))}
        </View>
      </View>
    </Animated.View>
  );
};

export const CompletionStep: React.FC<CompletionStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  _onSave,
}) => {
  const [consumeInventory, setConsumeInventory] = useState(false);
  const [isConsumingInventory, setIsConsumingInventory] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [_formulationAnalysis, setFormulationAnalysis] = useState<FormulationConsumption | null>(
    null
  );
  const [_isAnalyzingFormula, setIsAnalyzingFormula] = useState(false);
  const [_showCelebration, _setShowCelebration] = useState(false);
  const [_mappingModal, _setMappingModal] = useState<{
    visible: boolean;
    aiProductName: string;
    suggestedProduct: {
      id: string;
      name: string;
      brand?: string;
      line?: string;
      type?: string;
      shade?: string;
      [key: string]: unknown;
    };
    confidence: number;
    onConfirm: (productId: string) => void;
  } | null>(null);

  // FIXED: Add mounted ref to prevent navigation after unmount
  const mountedRef = useRef(true);

  // Whimsy store integration
  const hapticsEnabled = useHapticsEnabled();
  const _celebrationsEnabled = useCelebrationsEnabled();
  const animationsEnabled = useAnimationsEnabled();
  const { incrementMetric } = useWhimsyStore();

  // FIXED: Cleanup ref on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Animation values for satisfaction buttons with enhanced interactivity
  const satisfactionScale1 = useSharedValue(1);
  const satisfactionScale2 = useSharedValue(1);
  const satisfactionScale3 = useSharedValue(1);
  const satisfactionScale4 = useSharedValue(1);
  const satisfactionScale5 = useSharedValue(1);
  const satisfactionButtonScales = [
    satisfactionScale1,
    satisfactionScale2,
    satisfactionScale3,
    satisfactionScale4,
    satisfactionScale5,
  ];

  // Enhanced animation values for smooth color transitions and progress
  const selectedRatingScale = useSharedValue(1);
  const ratingProgress = useSharedValue(0);
  const buttonColorProgress = useSharedValue(0);
  const feedbackCardScale = useSharedValue(0.95);
  const photoButtonScale = useSharedValue(1);
  const starRotation = useSharedValue(0);

  // Animate feedback card entrance
  useEffect(() => {
    if (animationsEnabled) {
      feedbackCardScale.value = withSpring(1, {
        damping: 15,
        stiffness: 150,
      });
    } else {
      feedbackCardScale.value = 1;
    }
  }, [animationsEnabled, feedbackCardScale]);

  const { takePhoto, pickImage } = usePhotoAnalysis();
  const { saveProductMapping: _saveProductMapping } = useInventoryStore();

  // Animated styles for satisfaction buttons
  const animatedButtonStyle1 = useAnimatedStyle(() => ({
    transform: [{ scale: satisfactionScale1.value }],
  }));
  const animatedButtonStyle2 = useAnimatedStyle(() => ({
    transform: [{ scale: satisfactionScale2.value }],
  }));
  const animatedButtonStyle3 = useAnimatedStyle(() => ({
    transform: [{ scale: satisfactionScale3.value }],
  }));
  const animatedButtonStyle4 = useAnimatedStyle(() => ({
    transform: [{ scale: satisfactionScale4.value }],
  }));
  const animatedButtonStyle5 = useAnimatedStyle(() => ({
    transform: [{ scale: satisfactionScale5.value }],
  }));

  const _animatedButtonStyles = [
    animatedButtonStyle1,
    animatedButtonStyle2,
    animatedButtonStyle3,
    animatedButtonStyle4,
    animatedButtonStyle5,
  ];

  // Fixed animated styles for dynamic buttons (moved from render callbacks)
  const useSatisfactionButtonStyle = (buttonIndex: number) => {
    return useAnimatedStyle(() => {
      const baseScale = satisfactionButtonScales[buttonIndex].value;
      const isSelected = data.clientSatisfaction === buttonIndex + 1;
      const isHighRating = buttonIndex + 1 >= 4;
      const selectedScale = isSelected ? selectedRatingScale.value : 1;

      // Smooth color interpolation
      const backgroundColor = isSelected
        ? interpolateColor(
            buttonColorProgress.value,
            [0, 1],
            [
              BeautyMinimalTheme.semantic.background.secondary,
              isHighRating
                ? BeautyMinimalTheme.semantic.status.success
                : BeautyMinimalTheme.semantic.interactive.primary.default,
            ]
          )
        : BeautyMinimalTheme.semantic.background.secondary;

      const borderColor = isSelected
        ? interpolateColor(
            buttonColorProgress.value,
            [0, 1],
            [
              BeautyMinimalTheme.semantic.border.default,
              isHighRating
                ? BeautyMinimalTheme.semantic.status.success
                : BeautyMinimalTheme.semantic.interactive.primary.default,
            ]
          )
        : BeautyMinimalTheme.semantic.border.default;

      return {
        transform: [{ scale: baseScale * selectedScale }],
        backgroundColor,
        borderColor,
        shadowOpacity: isSelected
          ? withTiming(0.3, { duration: 300 })
          : withTiming(0.1, { duration: 300 }),
        elevation: isSelected ? 8 : 2,
      };
    });
  };

  // Pre-computed animated styles for each satisfaction button
  const satisfactionButton1Style = useSatisfactionButtonStyle(0);
  const satisfactionButton2Style = useSatisfactionButtonStyle(1);
  const satisfactionButton3Style = useSatisfactionButtonStyle(2);
  const satisfactionButton4Style = useSatisfactionButtonStyle(3);
  const satisfactionButton5Style = useSatisfactionButtonStyle(4);

  const satisfactionButtonAnimatedStyles = [
    satisfactionButton1Style,
    satisfactionButton2Style,
    satisfactionButton3Style,
    satisfactionButton4Style,
    satisfactionButton5Style,
  ];

  // Fixed star rotation style (moved from render callback)
  const starRotationStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${starRotation.value}deg` }],
  }));

  // Generate contextual result text based on rating (no longer saving here)
  const getContextualResult = (rating: number): string => {
    const clientName = data.client?.name || 'Cliente';

    if (rating >= 5)
      return `¡${clientName} está radiante con el resultado! Color perfecto logrado.`;
    if (rating >= 4) return `${clientName} muy satisfecho/a con el resultado. Color exitoso.`;
    if (rating >= 3) return `${clientName} conforme con el resultado. Color aceptable.`;
    if (rating >= 2) return `${clientName} parcialmente satisfecho/a. Resultado mejorable.`;
    return `${clientName} no completamente satisfecho/a. Requiere ajustes.`;
  };

  const handleResultImageCapture = async (uri: string) => {
    if (!data.clientId) {
      Alert.alert('Error', 'No se pudo identificar el cliente. Por favor, intenta nuevamente.');
      return;
    }

    setIsUploadingImage(true);

    try {
      // Subir imagen usando el sistema seguro
      const result = await uploadAndAnonymizeImage(uri, {
        clientId: data.clientId,
        photoType: 'after', // Las fotos 'after' no tienen filtro de privacidad
        onProgress: _progress => {
          // Debug logging removed for production
        },
      });

      if (result.success && result.publicUrl) {
        // Guardar URL firmada privada en lugar de URI local
        onUpdate({ resultImage: result.publicUrl });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      } else {
        throw new Error(result.error || 'No se pudo subir la imagen');
      }
    } catch (error: unknown) {
      logger.error('Error al subir imagen de resultado:', error);
      Alert.alert(
        'Error al subir imagen',
        error.message || 'No se pudo subir la imagen. ¿Deseas intentar nuevamente?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Reintentar',
            onPress: () => handleResultImageCapture(uri),
          },
        ]
      );
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSatisfactionChange = async (rating: number) => {
    logger.debug('Satisfaction rating selected', 'CompletionStep', {
      rating,
      clientName: data.client?.name,
    });

    // Enhanced button animation with smoother scaling (respects reduced motion)
    const buttonIndex = rating - 1;
    if (animationsEnabled) {
      satisfactionButtonScales[buttonIndex].value = withSequence(
        withSpring(1.4, { damping: 12, stiffness: 300 }),
        withSpring(1.1, { damping: 15, stiffness: 180 }),
        withSpring(1, { damping: 18, stiffness: 150 })
      );

      // Animate other buttons to show contrast
      satisfactionButtonScales.forEach((scale, index) => {
        if (index !== buttonIndex) {
          scale.value = withSequence(
            withSpring(0.9, { damping: 15 }),
            withSpring(1, { damping: 12 })
          );
        }
      });

      // Smooth color transition animation
      buttonColorProgress.value = withTiming(1, { duration: 300 });
      ratingProgress.value = withTiming(rating / 5, { duration: 400 });
      selectedRatingScale.value = withSequence(
        withSpring(1.2, { damping: 8 }),
        withSpring(1, { damping: 12 })
      );

      // Animate star rotation for 5-star rating
      if (rating === 5) {
        starRotation.value = withTiming(360, { duration: 600 });
      } else {
        starRotation.value = withTiming(0, { duration: 300 });
      }
    } else {
      // Instant feedback for reduced motion
      buttonColorProgress.value = 1;
      ratingProgress.value = rating / 5;
      starRotation.value = rating === 5 ? 360 : 0;
    }

    // Enhanced haptic feedback with better patterns
    if (hapticsEnabled) {
      if (rating === 5) {
        // Perfect rating - celebration pattern
        setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light), 0);
        setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium), 150);
        setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light), 300);
      } else if (rating >= 4) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else if (rating >= 3) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } else {
        // Low rating - subtle warning pattern
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light), 100);
      }
    }

    // Track perfect satisfaction for achievements
    if (rating === 5) {
      incrementMetric('perfectSatisfactionCount');
    }

    // Create contextual feedback data
    const feedbackData = {
      rating,
      worked_as_expected: rating >= 4,
      would_use_again: rating >= 4,
      actual_result: getContextualResult(rating),
      adjustments_made: data.adjustments || undefined,
      hair_type: data.diagnosis?.hairType || undefined,
      environmental_factors: data.diagnosis?.environmentalFactors || undefined,
    };

    // CRITICAL FIX: Update BOTH satisfaction AND feedbackData in a single call
    // This ensures they're atomically updated together, preventing timing issues
    onUpdate({
      clientSatisfaction: rating,
      feedbackData: feedbackData,
    });

    // ENHANCED: Show immediate value feedback to user
    let feedbackMessage = '';
    if (rating >= 4) {
      feedbackMessage = '✅ Fórmula marcada como exitosa - se guardará para casos similares';
    } else if (rating >= 3) {
      feedbackMessage = '📝 Feedback guardado - ayudará a mejorar futuras fórmulas';
    } else {
      feedbackMessage = '⚠️ Feedback guardado - se evitará esta combinación en el futuro';
    }

    // Provide subtle haptic feedback instead of intrusive popup
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    logger.info('✅ FEEDBACK DATA SAVED TO SERVICE STATE', 'CompletionStep', {
      rating: feedbackData.rating,
      workedAsExpected: feedbackData.worked_as_expected,
      actualResult: feedbackData.actual_result,
      clientName: data.client?.name,
      immediateValue: feedbackMessage,
      timestamp: new Date().toISOString(),
    });

    // DISABLED: onSave to prevent excessive auto-saving during user interaction
    // The interval-based auto-save will handle saving automatically
  };

  const handleNotesChange = (notes: string) => {
    onUpdate({ resultNotes: notes });
    // DISABLED: onSave to prevent excessive auto-saving during text input
    // The interval-based auto-save will handle saving automatically
  };

  const handleInventoryConsumption = async () => {
    if (!data.formula || !consumeInventory) return;

    setIsConsumingInventory(true);

    try {
      // Check if we have structured formula data (from JSON)
      let colorFormula;
      if (data.formulationData) {
        // Create a formula object with the structured data
        colorFormula = {
          brand: data.selectedBrand || 'Unknown',
          line: data.selectedLine || 'Unknown',
          formulationData: data.formulationData,
          formulaText: data.formula,
        };
      } else {
        // Fallback to parsing text formula
        colorFormula = parseFormulaText(data.formula);
      }

      const result = await InventoryConsumptionService.consumeFormulation(
        data.clientId || 'unknown',
        colorFormula,
        data.client?.name || 'Cliente',
        true // forceConsume = true cuando el usuario activa el switch
      );

      if (result.success && result.totalConsumed > 0) {
        // Éxito completo
        Alert.alert(
          'Inventario actualizado',
          `Se descontaron ${result.totalConsumed} productos correctamente:\n\n${result.consumedProducts.join('\n')}`,
          [{ text: 'OK' }]
        );
      } else if (result.notFoundProducts.length > 0) {
        // Algunos productos no se encontraron
        let message =
          result.consumedProducts.length > 0
            ? `Se descontaron ${result.consumedProducts.length} productos:\n${result.consumedProducts.join('\n')}\n\nNo se encontraron en inventario:\n${result.notFoundProducts.join('\n')}`
            : `No se pudo descontar ningún producto.\n\nProductos no encontrados en inventario:\n${result.notFoundProducts.join('\n')}`;

        // Add helpful tip about structured data
        message +=
          '\n\nSugerencia: Asegúrate de que los productos en el inventario tengan los campos estructurados correctos (marca, tipo, tono).';

        Alert.alert(
          result.consumedProducts.length > 0
            ? 'Inventario parcialmente actualizado'
            : 'No se actualizó el inventario',
          message,
          [{ text: 'OK' }]
        );
      } else if (result.errors.length > 0) {
        // Errores específicos
        Alert.alert('Error al actualizar inventario', result.errors.join('\n'), [{ text: 'OK' }]);
      }
    } catch (error) {
      logger.error('Error consuming inventory:', error);
      Alert.alert('Error', 'No se pudo actualizar el inventario. Por favor, hazlo manualmente.', [
        { text: 'OK' },
      ]);
    } finally {
      setIsConsumingInventory(false);
    }
  };

  const handleFinish = async () => {
    // Consume inventory if enabled
    if (consumeInventory && data.formula) {
      await handleInventoryConsumption();
    }

    // Track service completion for achievements
    incrementMetric('totalServicesCompleted');

    // If no rating was given, suggest rating before finishing
    if (!data.clientSatisfaction) {
      Alert.alert(
        'Calificación del Resultado',
        'Te recomendamos calificar el resultado antes de finalizar para capturar tu feedback inmediato.',
        [
          { text: 'Continuar sin calificar', onPress: () => onNext() },
          { text: 'Calificar ahora', style: 'default' },
        ]
      );
      return;
    }

    // Professional mode: No celebrations, immediate navigation
    // Note: Celebrations disabled for optimal salon workflow
    onNext();
  };

  const salonConfig = useSalonConfigStore.getState();
  const showInventoryControl =
    data.formula && salonConfig.configuration.inventoryControlLevel === 'control-total';

  // Analizar la fórmula cuando cambie
  useEffect(() => {
    const analyzeFormula = async () => {
      if (!data.formula || !showInventoryControl) {
        setFormulationAnalysis(null);
        return;
      }

      setIsAnalyzingFormula(true);
      try {
        const analysis = await InventoryConsumptionService.calculateFormulationCostFromText(
          data.formula
        );
        setFormulationAnalysis(analysis);
      } catch (error) {
        logger.error('Error analyzing formula:', error);
      } finally {
        setIsAnalyzingFormula(false);
      }
    };

    analyzeFormula();
  }, [data.formula, showInventoryControl]);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <BeautyCard variant="subtle" style={styles.headerCard}>
          <Text style={styles.stepTitle}>Resultado Final</Text>
          {data.client && (
            <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
          )}
        </BeautyCard>

        <Text style={styles.sectionTitle}>Fotos del resultado final</Text>
        <View style={styles.photoContainer}>
          {data.resultImage ? (
            <Image source={{ uri: data.resultImage }} style={styles.photoPreview} />
          ) : (
            <View style={styles.photoPlaceholder}>
              <Camera size={40} color={Colors.light.gray} />
              <Text style={styles.photoPlaceholderText}>
                {isUploadingImage ? 'Subiendo imagen...' : 'Fotografía del resultado'}
              </Text>
            </View>
          )}
          <View style={styles.photoButtons}>
            <Animated.View
              style={useAnimatedStyle(() => ({ transform: [{ scale: photoButtonScale.value }] }))}
            >
              <Pressable
                style={[styles.photoButton, isUploadingImage && styles.photoButtonDisabled]}
                onPress={() => takePhoto(handleResultImageCapture)}
                disabled={isUploadingImage}
                onPressIn={() => {
                  photoButtonScale.value = withSpring(0.95, { damping: 15 });
                  if (hapticsEnabled) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                }}
                onPressOut={() => {
                  photoButtonScale.value = withSpring(1, { damping: 12 });
                }}
              >
                <Camera size={16} color={Colors.common.white} />
                <Text style={styles.photoButtonText}>
                  {isUploadingImage ? 'Subiendo...' : 'Usar Cámara'}
                </Text>
              </Pressable>
            </Animated.View>
            <Animated.View
              style={useAnimatedStyle(() => ({ transform: [{ scale: photoButtonScale.value }] }))}
            >
              <Pressable
                style={[styles.photoButton, isUploadingImage && styles.photoButtonDisabled]}
                onPress={() => pickImage(handleResultImageCapture)}
                disabled={isUploadingImage}
                onPressIn={() => {
                  photoButtonScale.value = withSpring(0.95, { damping: 15 });
                  if (hapticsEnabled) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                }}
                onPressOut={() => {
                  photoButtonScale.value = withSpring(1, { damping: 12 });
                }}
              >
                <Upload size={16} color={Colors.common.white} />
                <Text style={styles.photoButtonText}>
                  {isUploadingImage ? 'Subiendo...' : 'Seleccionar'}
                </Text>
              </Pressable>
            </Animated.View>
          </View>
          <Text style={styles.photoTip}>
            🔒 PRIVACIDAD: Las imágenes del resultado final se suben de forma segura al servidor
            para su almacenamiento.
          </Text>
        </View>

        {/* Contextual Satisfaction Rating - Enhanced */}
        <Animated.View
          style={useAnimatedStyle(() => ({ transform: [{ scale: feedbackCardScale.value }] }))}
        >
          <BeautyCard variant="subtle" style={styles.feedbackCard}>
            <View style={styles.feedbackHeader}>
              <Text style={styles.feedbackTitle}>
                ¿Cómo quedó {data.client?.name || 'el cliente'}?
              </Text>
              <Text style={styles.feedbackSubtitle}>
                {data.clientSatisfaction
                  ? data.clientSatisfaction >= 4
                    ? '¡Cliente está radiante! ✨'
                    : data.clientSatisfaction >= 3
                      ? 'Resultado aceptable 👍'
                      : 'Necesita mejoras 🔧'
                  : 'Califica el resultado del servicio'}
              </Text>
            </View>

            <View style={styles.satisfactionContainer}>
              {[1, 2, 3, 4, 5].map((rating, index) => {
                const isSelected = data.clientSatisfaction === rating;
                const _isHighRating = rating >= 4;

                // Use pre-computed animated style
                const animatedButtonStyle = satisfactionButtonAnimatedStyles[index];

                return (
                  <Animated.View
                    key={rating}
                    style={[styles.satisfactionButton, animatedButtonStyle]}
                  >
                    <Pressable
                      style={styles.satisfactionButtonPressable}
                      onPress={() => handleSatisfactionChange(rating)}
                      onPressIn={() => {
                        if (animationsEnabled) {
                          satisfactionButtonScales[index].value = withSpring(0.95, { damping: 15 });
                        }
                        if (hapticsEnabled) {
                          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        }
                      }}
                      onPressOut={() => {
                        if (data.clientSatisfaction !== rating && animationsEnabled) {
                          satisfactionButtonScales[index].value = withSpring(1, { damping: 12 });
                        }
                      }}
                      accessibilityRole="button"
                      accessibilityLabel={`Calificar servicio con ${rating} ${rating === 1 ? 'estrella' : 'estrellas'}`}
                      accessibilityHint={
                        rating >= 4
                          ? `Resultado excelente - La fórmula funcionó muy bien`
                          : rating >= 3
                            ? `Resultado aceptable - La fórmula funcionó bien`
                            : `Resultado necesita mejorar - La fórmula requiere ajustes`
                      }
                      accessibilityState={{ selected: isSelected }}
                    >
                      <Text
                        style={[
                          styles.satisfactionButtonText,
                          isSelected && styles.satisfactionButtonTextActive,
                        ]}
                      >
                        {rating}
                      </Text>
                      {rating === 5 && (
                        <Animated.View style={[styles.starIcon, starRotationStyle]}>
                          <Star
                            size={12}
                            color={
                              isSelected
                                ? Colors.common.white
                                : BeautyMinimalTheme.semantic.status.warning
                            }
                            fill={
                              isSelected
                                ? Colors.common.white
                                : BeautyMinimalTheme.semantic.status.warning
                            }
                          />
                        </Animated.View>
                      )}
                    </Pressable>
                  </Animated.View>
                );
              })}
            </View>

            {/* Feedback Status Indicator */}
            {data.feedbackData && (
              <View style={styles.feedbackStatus}>
                <Text style={styles.feedbackStatusText}>
                  ✓ Calificación registrada - se guardará al finalizar el servicio
                </Text>
              </View>
            )}

            {/* Quick Rating Labels */}
            <View style={styles.ratingLabels}>
              <Text style={styles.ratingLabel}>Pobre</Text>
              <Text style={styles.ratingLabel}>Excelente</Text>
            </View>
          </BeautyCard>
        </Animated.View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Notas finales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={data.resultNotes}
            onChangeText={handleNotesChange}
            placeholder="Observaciones sobre el resultado, ajustes realizados, recomendaciones para el cliente, etc."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Materials Summary Card - unify inventory display */}
        {data.formula && (
          <MaterialsSummaryCard
            formulationData={data.formulationData}
            formulaText={data.formula}
            selectedBrand={data.selectedBrand || ''}
            selectedLine={data.selectedLine || ''}
          />
        )}

        {/* Inventory Consumption Section - Simplified */}
        {showInventoryControl && (
          <View style={styles.inventoryConsumptionSection}>
            <View style={styles.inventoryConsumptionHeader}>
              <Text style={styles.inventoryConsumptionTitle}>Descontar del Inventario</Text>
              <Switch
                trackColor={{
                  false: Colors.light.lightGray,
                  true: Colors.light.primary,
                }}
                thumbColor={Colors.common.white}
                ios_backgroundColor={Colors.light.lightGray}
                onValueChange={setConsumeInventory}
                value={consumeInventory}
              />
            </View>

            {consumeInventory && (
              <Text style={styles.inventoryConsumptionInfo}>
                Los productos disponibles se descontarán del inventario al finalizar el servicio.
              </Text>
            )}
          </View>
        )}

        {/* Finish Button */}
        <BeautyButton
          title={
            isConsumingInventory
              ? 'Guardando servicio...'
              : data.clientSatisfaction && data.feedbackData
                ? '¡Completar Servicio Exitoso!'
                : 'Finalizar Servicio'
          }
          onPress={handleFinish}
          disabled={isConsumingInventory}
          loading={isConsumingInventory}
          variant="primary"
          size="lg"
          icon={data.clientSatisfaction && data.clientSatisfaction >= 4 ? Sparkles : undefined}
          fullWidth
          style={[
            styles.finishButton,
            data.clientSatisfaction && data.clientSatisfaction >= 4 && styles.finishButtonSuccess,
          ]}
        />
      </View>

      {/* Success Celebration Overlay */}
      <SuccessCelebration
        visible={_showCelebration}
        clientName={data.client?.name || 'Cliente'}
        satisfaction={data.clientSatisfaction || 0}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.title,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  photoContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  photoPreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 15,
  },
  photoPlaceholder: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: Colors.light.placeholderGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  photoPlaceholderText: {
    marginTop: 10,
    color: Colors.light.gray,
  },
  photoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    flex: 1,
    marginHorizontal: 5,
    justifyContent: 'center',
  },
  photoButtonText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    marginLeft: 5,
  },
  photoButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.7,
  },
  photoTip: {
    fontSize: 12,
    color: Colors.light.success,
    marginTop: 10,
    textAlign: 'center',
    fontWeight: '500',
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 8,
  },
  satisfactionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  satisfactionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.light.border,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    shadowColor: BeautyMinimalTheme.neutrals.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  satisfactionButtonPressable: {
    width: '100%',
    height: '100%',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  satisfactionButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  satisfactionButtonTextActive: {
    color: Colors.light.textLight,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  inventoryConsumptionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  inventoryConsumptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  inventoryConsumptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  inventoryConsumptionInfo: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 8,
  },
  finishButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
  },
  finishButtonSuccess: {
    backgroundColor: Colors.light.success,
    shadowColor: Colors.light.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },

  // Enhanced Feedback Styles
  feedbackCard: {
    marginBottom: BeautyMinimalTheme.spacing.md,
    padding: BeautyMinimalTheme.spacing.md,
    backgroundColor: Colors.light.surface,
  },
  feedbackHeader: {
    marginBottom: BeautyMinimalTheme.spacing.md,
    alignItems: 'center',
  },
  feedbackTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  feedbackSubtitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    fontWeight: '500',
  },
  starIcon: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: Colors.common.transparent,
  },
  feedbackStatus: {
    marginTop: BeautyMinimalTheme.spacing.sm,
    padding: BeautyMinimalTheme.spacing.xs,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    alignItems: 'center',
  },
  feedbackStatusText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: Colors.light.success,
    fontWeight: '500',
  },
  ratingLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: BeautyMinimalTheme.spacing.xs,
    paddingHorizontal: BeautyMinimalTheme.spacing.xs,
  },
  ratingLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.semantic.text.tertiary,
    fontWeight: '400',
  },

  // Success celebration styles
  celebrationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: BeautyMinimalTheme.semantic.background.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  celebrationContent: {
    backgroundColor: BeautyMinimalTheme.semantic.background.primary,
    padding: 32,
    borderRadius: 24,
    alignItems: 'center',
    marginHorizontal: 40,
    shadowColor: BeautyMinimalTheme.neutrals.charcoal,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 10,
  },
  celebrationIcon: {
    position: 'relative',
    marginBottom: 16,
  },
  sparkles: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
  celebrationText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    color: Colors.light.text,
    marginBottom: 16,
  },
  satisfactionStars: {
    flexDirection: 'row',
    gap: 4,
  },
  confettiContainer: {
    position: 'absolute',
    width: '100%',
    height: 100,
    top: -50,
  },
  confetti: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    top: 0,
  },
});
