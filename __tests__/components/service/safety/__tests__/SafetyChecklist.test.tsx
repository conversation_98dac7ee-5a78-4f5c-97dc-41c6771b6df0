import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import SafetyChecklist, { SafetyCheckItem } from '@/components/service/safety/SafetyChecklist';

describe('SafetyChecklist', () => {
  const items: SafetyCheckItem[] = [
    { id: 'a', title: 'Item A', description: 'Desc A', required: true, checked: false },
    { id: 'b', title: 'Item B', description: 'Desc B', required: false, checked: false },
  ];

  it('renders items and toggles an item', () => {
    const onToggleItem = jest.fn();
    const onSetAll = jest.fn();
    const { getByText } = render(
      <SafetyChecklist
        items={items}
        detectedRisks={[]}
        onToggleItem={onToggleItem}
        onSetAll={onSetAll}
      />
    );

    expect(getByText('Item A')).toBeTruthy();
    expect(getByText('Item B')).toBeTruthy();

    fireEvent.press(getByText('Item A'));
    expect(onToggleItem).toHaveBeenCalledWith('a');
  });

  it('triggers select all', () => {
    const onToggleItem = jest.fn();
    const onSetAll = jest.fn();
    const { getByText } = render(
      <SafetyChecklist
        items={items}
        detectedRisks={[]}
        onToggleItem={onToggleItem}
        onSetAll={onSetAll}
      />
    );

    fireEvent.press(getByText('Seleccionar todo'));
    expect(onSetAll).toHaveBeenCalledWith(true);
  });
});
