import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, ScrollView, Switch } from 'react-native';
import { X, Bell, Calendar, Package, TrendingUp, Users, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseCard } from '@/components/base';
import { useSalonConfigStore } from '@/stores/salon-config-store';

interface NotificationsModalProps {
  visible: boolean;
  onClose: () => void;
}

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  iconColor: string;
  enabled: boolean;
}

export default function NotificationsModal({ visible, onClose }: NotificationsModalProps) {
  const { notifications, updateNotifications } = useSalonConfigStore();

  // Initialize state with current notification settings
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'appointments',
      title: 'Recordatorios de Citas',
      description: 'Notificaciones antes de las citas programadas',
      icon: Calendar,
      iconColor: Colors.light.primary,
      enabled: notifications?.appointments ?? true,
    },
    {
      id: 'lowStock',
      title: 'Stock Bajo',
      description: 'Alertas cuando los productos están por agotarse',
      icon: Package,
      iconColor: Colors.light.warning,
      enabled: notifications?.lowStock ?? true,
    },
    {
      id: 'clientAlerts',
      title: 'Alertas de Clientes',
      description: 'Avisos sobre alergias y precauciones especiales',
      icon: AlertTriangle,
      iconColor: Colors.light.error,
      enabled: notifications?.clientAlerts ?? true,
    },
    {
      id: 'teamUpdates',
      title: 'Actualizaciones del Equipo',
      description: 'Cambios en horarios y novedades del salón',
      icon: Users,
      iconColor: Colors.light.secondary,
      enabled: notifications?.teamUpdates ?? false,
    },
    {
      id: 'businessInsights',
      title: 'Insights del Negocio',
      description: 'Reportes semanales y estadísticas importantes',
      icon: TrendingUp,
      iconColor: Colors.light.success,
      enabled: notifications?.businessInsights ?? false,
    },
  ]);

  const handleSave = () => {
    const newNotifications = settings.reduce(
      (acc, setting) => {
        acc[setting.id] = setting.enabled;
        return acc;
      },
      {} as Record<string, boolean>
    );

    updateNotifications(newNotifications);
    onClose();
  };

  const toggleSetting = (id: string) => {
    setSettings(
      settings.map(setting =>
        setting.id === id ? { ...setting, enabled: !setting.enabled } : setting
      )
    );
  };

  const enabledCount = settings.filter(s => s.enabled).length;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Notificaciones</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Icon Section */}
          <View style={styles.iconSection}>
            <View style={styles.iconContainer}>
              <Bell size={48} color={Colors.light.warning} />
            </View>
            <Text style={styles.subtitle}>Controla qué notificaciones quieres recibir</Text>
          </View>

          {/* Summary */}
          <BaseCard variant="light" shadow="sm" style={styles.summaryCard}>
            <Text style={styles.summaryText}>
              {enabledCount === 0
                ? 'Todas las notificaciones están desactivadas'
                : `${enabledCount} de ${settings.length} notificaciones activas`}
            </Text>
          </BaseCard>

          {/* Notification Settings */}
          <View style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>Configuración de Notificaciones</Text>

            {settings.map((setting, index) => (
              <BaseCard
                key={setting.id}
                variant="light"
                shadow="sm"
                style={[
                  styles.settingCard,
                  index === settings.length - 1 && styles.lastSettingCard,
                ]}
              >
                <View style={styles.settingContent}>
                  <View style={[styles.settingIcon, { backgroundColor: setting.iconColor + '15' }]}>
                    <setting.icon size={24} color={setting.iconColor} />
                  </View>

                  <View style={styles.settingInfo}>
                    <Text style={styles.settingTitle}>{setting.title}</Text>
                    <Text style={styles.settingDescription}>{setting.description}</Text>
                  </View>

                  <Switch
                    trackColor={{
                      false: Colors.light.lightGray,
                      true: setting.iconColor,
                    }}
                    thumbColor="white"
                    ios_backgroundColor={Colors.light.lightGray}
                    onValueChange={() => toggleSetting(setting.id)}
                    value={setting.enabled}
                  />
                </View>
              </BaseCard>
            ))}
          </View>

          {/* Master Toggle */}
          <View style={styles.masterToggleSection}>
            <BaseCard variant="light" shadow="sm">
              <TouchableOpacity
                style={styles.masterToggleContent}
                onPress={() => {
                  const allEnabled = settings.every(s => s.enabled);
                  setSettings(settings.map(s => ({ ...s, enabled: !allEnabled })));
                }}
              >
                <View style={styles.masterToggleInfo}>
                  <Text style={styles.masterToggleTitle}>
                    {settings.every(s => s.enabled) ? 'Desactivar todas' : 'Activar todas'}
                  </Text>
                  <Text style={styles.masterToggleDescription}>
                    Cambiar todas las notificaciones de una vez
                  </Text>
                </View>
                <View
                  style={[
                    styles.masterToggleButton,
                    settings.every(s => s.enabled) && styles.masterToggleButtonActive,
                  ]}
                >
                  <Text
                    style={[
                      styles.masterToggleButtonText,
                      settings.every(s => s.enabled) && styles.masterToggleButtonTextActive,
                    ]}
                  >
                    {settings.every(s => s.enabled) ? 'Activas' : 'Inactivas'}
                  </Text>
                </View>
              </TouchableOpacity>
            </BaseCard>
          </View>

          {/* Info Note */}
          <View style={styles.infoNote}>
            <Text style={styles.infoNoteText}>
              💡 Las notificaciones te ayudan a mantener tu salón organizado y a no perder
              oportunidades importantes
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  saveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  iconSection: {
    alignItems: 'center',
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.warning + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  summaryCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    backgroundColor: Colors.light.primary + '05',
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  summaryText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    fontWeight: typography.weights.medium,
  },
  settingsSection: {
    paddingHorizontal: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  settingCard: {
    marginBottom: spacing.sm,
  },
  lastSettingCard: {
    marginBottom: 0,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  settingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  settingDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  masterToggleSection: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  masterToggleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  masterToggleInfo: {
    flex: 1,
  },
  masterToggleTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  masterToggleDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  masterToggleButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.lightGray,
    borderRadius: radius.full,
  },
  masterToggleButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  masterToggleButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
  },
  masterToggleButtonTextActive: {
    color: Colors.light.textLight,
  },
  infoNote: {
    backgroundColor: Colors.light.accent + '10',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    padding: spacing.md,
    borderRadius: radius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.accent,
  },
  infoNoteText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    lineHeight: 20,
  },
});
