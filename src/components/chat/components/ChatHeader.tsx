import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Menu, Plus } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { spacing, radius } from '@/constants/theme';

interface ChatHeaderProps {
  isTablet: boolean;
  onMenuPress: () => void;
  onNewChatPress: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ isTablet, onMenuPress, onNewChatPress }) => {
  return (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        {!isTablet && (
          <TouchableOpacity onPress={onMenuPress} style={styles.menuButton}>
            <Menu size={24} color={BeautyMinimalTheme.neutrals.slate} />
          </TouchableOpacity>
        )}
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Salonier</Text>
          <Text style={styles.headerSubtitle}>Asistente Técnico Digital</Text>
        </View>
        <TouchableOpacity
          onPress={onNewChatPress}
          style={styles.newChatButton}
          hitSlop={{ top: 8, right: 8, bottom: 8, left: 8 }}
        >
          <Plus size={18} color={BeautyMinimalTheme.neutrals.slate} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl,
    borderBottomWidth: 0,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: spacing.sm,
    borderRadius: radius.sm,
    marginRight: spacing.xs,
  },
  newChatButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud + '40',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '50',
    ...BeautyMinimalTheme.shadows.subtle,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: BeautyMinimalTheme.typography.weights.bold,
    color: BeautyMinimalTheme.beautyColors.salonier[500],
    letterSpacing: -0.3,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.neutrals.slate,
    letterSpacing: 0.2,
    textAlign: 'center',
    marginTop: 2,
    opacity: 0.8,
  },
});

export default React.memo(ChatHeader);
