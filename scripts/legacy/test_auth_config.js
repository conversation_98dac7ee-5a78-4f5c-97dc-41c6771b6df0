// Test script to check Supabase auth configuration
import { createClient } from '@supabase/supabase-js';

const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const supabaseAnonKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY';

console.log('Testing Supabase Auth Configuration...\n');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAuthConfig() {
  console.log('1. Testing connection to Supabase...');

  try {
    // Check if we can connect to the database
    const { data: _data, error } = await supabase.from('profiles').select('count').limit(1);
    if (error) {
      console.log('❌ Database connection error:', error.message);
      return;
    }
    console.log('✅ Connected to Supabase successfully');
  } catch (err) {
    console.log('❌ Connection failed:', err.message);
    return;
  }

  console.log('\n2. Testing user registration with different password strengths...');

  const testCases = [
    { email: '<EMAIL>', password: '123', name: 'Test User 1' },
    { email: '<EMAIL>', password: '123456', name: 'Test User 2' },
    { email: '<EMAIL>', password: 'password123', name: 'Test User 3' },
    { email: '<EMAIL>', password: 'StrongPassword123!', name: 'Test User 4' },
  ];

  for (const testCase of testCases) {
    console.log(`\nTesting: ${testCase.email} with password "${testCase.password}"`);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: testCase.email,
        password: testCase.password,
        options: {
          data: {
            full_name: testCase.name,
            salon_name: `${testCase.name}'s Salon`,
          },
        },
      });

      if (error) {
        console.log(`❌ Registration failed: ${error.message}`);
        if (error.message.includes('weak') || error.message.includes('Weak')) {
          console.log('   → This confirms password policy is active');
        }
      } else {
        console.log(`✅ Registration successful for ${testCase.email}`);
        if (data.user) {
          console.log(`   → User ID: ${data.user.id}`);
          console.log(`   → Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
        }

        // Wait for profile creation (triggers)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if profile was created
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, salon_id, full_name, role')
          .eq('id', data.user?.id)
          .single();

        if (profile) {
          console.log(`   → Profile created: ${profile.full_name} (${profile.role})`);
          console.log(`   → Salon ID: ${profile.salon_id}`);
        } else {
          console.log(`   → Profile creation failed: ${profileError?.message}`);
        }
      }
    } catch (err) {
      console.log(`❌ Unexpected error: ${err.message}`);
    }
  }

  console.log('\n3. Testing manual user setup function...');

  try {
    const testUserId = 'test-user-id-12345';
    const { data, error } = await supabase.rpc('manual_user_setup', {
      user_id: testUserId,
      user_email: '<EMAIL>',
      user_name: 'Manual Test User',
    });

    if (error) {
      console.log(`❌ Manual setup failed: ${error.message}`);
    } else {
      console.log(`✅ Manual setup function works: ${data}`);
    }
  } catch (err) {
    console.log(`❌ Manual setup error: ${err.message}`);
  }

  console.log('\n4. Checking database triggers and functions...');

  try {
    const { data: triggers, error: triggerError } = await supabase.rpc('exec_sql', {
      sql: `
          SELECT trigger_name, event_object_table, action_timing, event_manipulation
          FROM information_schema.triggers 
          WHERE trigger_name LIKE '%auth%';
        `,
    });

    if (triggers) {
      console.log('✅ Auth triggers found:', triggers);
    } else {
      console.log('❌ Could not query triggers:', triggerError?.message);
    }
  } catch (err) {
    console.log('ℹ️ Cannot query system tables (expected in production)');
  }

  console.log('\n✅ Auth configuration test completed!');
}

// Run the test
checkAuthConfig().catch(err => {
  console.error('Test failed:', err);
});
