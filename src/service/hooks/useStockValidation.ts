import { useState, useCallback } from 'react';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText } from '@/utils/parseFormula';

interface StockValidationState {
  isChecking: boolean;
  hasStock: boolean;
  missingProducts: string[];
  checked: boolean;
}

/**
 * Hook for managing stock validation of formulated products
 */
export const useStockValidation = () => {
  const [stockValidation, setStockValidation] = useState<StockValidationState>({
    isChecking: false,
    hasStock: true,
    missingProducts: [],
    checked: false,
  });

  const checkStockAvailability = useCallback(async (formula: string) => {
    if (!formula) return;

    setStockValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const colorFormula = parseFormulaText(formula);
      const stockCheck = await InventoryConsumptionService.checkStock(colorFormula);

      setStockValidation({
        isChecking: false,
        hasStock: stockCheck.hasStock,
        missingProducts: stockCheck.missingProducts,
        checked: true,
      });

      return stockCheck;
    } catch {
      // Stock check failed
      setStockValidation(prev => ({
        ...prev,
        isChecking: false,
        checked: true,
        hasStock: false,
        missingProducts: ['Error al verificar stock'],
      }));
    }
  }, []);

  const resetStockValidation = useCallback(() => {
    setStockValidation({
      isChecking: false,
      hasStock: true,
      missingProducts: [],
      checked: false,
    });
  }, []);

  return {
    stockValidation,
    setStockValidation,
    checkStockAvailability,
    resetStockValidation,
  };
};
