// All validation-related functions.
import { extractJsonFromString } from '../../src/utils/json-extractor.ts';

/**
 * Fast basic colorimetry validation - checks only critical rules
 */
export async function validateBasicColorimetry(
  formula: any,
  diagnosis: any,
  desiredResult: any
): Promise<{
  isValid: boolean;
  violations: Array<{
    type: string;
    severity: 'critical' | 'error' | 'warning';
    message: string;
    suggestion?: string;
    autoFixAvailable?: boolean;
  }>;
  riskLevel: 'low' | 'medium' | 'high';
  confidence: number;
}> {
  const violations: any[] = [];
  const currentLevel = diagnosis?.averageLevel || diagnosis?.level || 5;
  const desiredLevel = desiredResult?.detectedLevel || desiredResult?.targetLevel || currentLevel;
  const levelDifference = desiredLevel - currentLevel;
  const currentState = diagnosis?.state || 'natural';

  // Rule 1: Color cannot lift color (CRITICAL)
  if (levelDifference > 0 && currentState === 'colored') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach') ||
      step.ingredients?.some((ing: any) =>
        ing.product?.toLowerCase().includes('decolorante') ||
        ing.product?.toLowerCase().includes('blondor')
      )
    );

    if (!hasDecolorationStep && levelDifference > 2) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: 'Imposible elevar ' + levelDifference + ' niveles sobre cabello tenido sin decoloracion previa',
        suggestion: 'Agregar paso de decoloracion antes de la coloracion',
        autoFixAvailable: true,
      });
    }
  }

  // Rule 2: Maximum lift with permanent color (CRITICAL)
  if (levelDifference > 3 && currentState === 'natural') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach')
    );

    if (!hasDecolorationStep) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: 'Tinte permanente no puede elevar mas de 3 niveles (intentando ' + levelDifference + ')',
        suggestion: 'Agregar paso de decoloracion previa',
        autoFixAvailable: true,
      });
    }
  }

  const criticalViolations = violations.filter(v => v.severity === 'critical');
  const errorViolations = violations.filter(v => v.severity === 'error');

  const isValid = criticalViolations.length === 0 && errorViolations.length === 0;
  const riskLevel = criticalViolations.length > 0 ? 'high' :
                   errorViolations.length > 0 ? 'medium' : 'low';
  const confidence = isValid ? 0.85 : Math.max(0.3, 0.85 - (violations.length * 0.15));

  return {
    isValid,
    violations,
    riskLevel,
    confidence,
  };
}


/**
 * Intelligent validation and auto-correction for AI responses with unknown products
 */
export async function validateAndCorrectUnknownProducts(
  aiResponse: string,
  brand?: string,
  line?: string
): Promise<{
  shouldReject: boolean;
  correctedResponse?: string;
  correctionsMade: number;
  correctionsApplied: string[];
  correctionsAttempted: number;
  issues: string[];
}> {
  const issues: string[] = [];
  const correctionsApplied: string[] = [];
  let correctedResponse: string = aiResponse;
  let correctionsMade = 0;
  let correctionsAttempted = 0;

  // Stage 1: Contextual "unknown" detection (only reject if problematic)
  const criticalUnknownPatterns = [
    // Only reject if "unknown" appears as a main product brand
    /"brand"\s*:\s*"unknown"/gi,
    /"productName"\s*:\s*"unknown\s+unknown/gi,
    // Reject if multiple consecutive "unknown" values
    /unknown\s+unknown\s+unknown/gi,
    // Reject if unknown appears in critical product fields
    /"brand"\s*:\s*"(unknown|generico|generic)"/gi
  ];

  // Stage 2: Allowed "unknown" contexts (DO NOT reject these)
  const allowedUnknownContexts = [
    // "unknown" in descriptions or warnings is OK
    /"description".*unknown/gi,
    /"warning".*unknown/gi,
    /"note".*unknown/gi,
    // "unknown" in explanatory text is OK
    /para.*unknown.*casos/gi,
    /if.*unknown.*situation/gi,
    // "unknown" referring to client's unknown history
    /previous.*unknown/gi,
    /history.*unknown/gi
  ];

  // Check if unknowns are in allowed contexts
  const hasAllowedUnknowns = allowedUnknownContexts.some(pattern =>
    pattern.test(aiResponse)
  );

  const hasCriticalUnknowns = criticalUnknownPatterns.some(pattern =>
    pattern.test(aiResponse)
  );

  // Stage 3: Auto-correction attempts
  if (hasCriticalUnknowns) {
    correctionsAttempted++;

    // Attempt 1: Replace generic "unknown" with specific brand products
    if (brand && line) {
      const brandProductMap = getBrandProductMap(brand, line);

      // Replace unknown color products
      const colorReplacePattern = /"productName"\s*:\s*"unknown\s+unknown\s+([0-9\.]+)"/gi;
      const colorMatches = correctedResponse.match(colorReplacePattern);

      if (colorMatches) {
        colorMatches.forEach(match => {
          const shadeMatch = match.match(/([0-9\.]+)/);
          if (shadeMatch) {
            const shade = shadeMatch[1];
            const replacement = `"productName": "${brand} ${line} ${shade}"`;
            correctedResponse = correctedResponse.replace(match, replacement);
            correctionsMade++;
            correctionsApplied.push(`Reemplazó producto desconocido con ${brand} ${line} ${shade}`);
          }
        });
      }

      // Replace unknown developer products
      const devReplacePattern = /"productName"\s*:\s*"unknown\s+unknown\s+(\d+)\s*vol"/gi;
      const devMatches = correctedResponse.match(devReplacePattern);

      if (devMatches) {
        devMatches.forEach(match => {
          const volumeMatch = match.match(/(\d+)\s*vol/);
          if (volumeMatch) {
            const volume = volumeMatch[1];
            const replacement = `"productName": "${brand} Oxidante ${volume} vol"`;
            correctedResponse = correctedResponse.replace(match, replacement);
            correctionsMade++;
            correctionsApplied.push(`Reemplazó oxidante desconocido con ${brand} Oxidante ${volume} vol`);
          }
        });
      }

      // Replace unknown brand references
      correctedResponse = correctedResponse.replace(
        /"brand"\s*:\s*"unknown"/gi,
        `"brand": "${brand}"`
      );

      if (/"brand"\s*:\s*"unknown"/gi.test(aiResponse)) {
        correctionsMade++;
        correctionsApplied.push(`Reemplazó marca desconocida con ${brand}`);
      }
    }

    correctionsAttempted++;

    // Attempt 2: Generic fallbacks for common product types
    const genericFallbacks = {
      'tinte': brand ? `${brand} Color Professional` : 'Tinte Profesional',
      'oxidante': brand ? `${brand} Oxidante Professional` : 'Oxidante Professional',
      'decolorante': brand ? `${brand} Decolorante` : 'Decolorante Professional'
    };

    Object.entries(genericFallbacks).forEach(([type, fallback]) => {
      const pattern = new RegExp(`"productName"\s*:\s*"unknown\s+${type}`, 'gi');
      if (pattern.test(correctedResponse)) {
        correctedResponse = correctedResponse.replace(pattern, `"productName": "${fallback}`);
        correctionsMade++;
        correctionsApplied.push(`Reemplazó ${type} desconocido con ${fallback}`);
      }
    });
  }

  // Stage 4: Final validation after corrections
  const stillHasCriticalIssues = criticalUnknownPatterns.some(pattern =>
    pattern.test(correctedResponse)
  );

  if (stillHasCriticalIssues && !hasAllowedUnknowns) {
    issues.push('Productos con marca desconocida después de correcciones automáticas');
  }

  // Stage 5: Additional safety checks
  try {
    // Try to parse as JSON to ensure we didn't break the structure
    const testParse = extractJsonFromString(correctedResponse);
    JSON.parse(testParse);
  } catch (jsonError) {
    issues.push('Estructura JSON inválida después de correcciones');
    correctedResponse = aiResponse; // Revert to original
    correctionsMade = 0;
    correctionsApplied.length = 0;
  }

  // Only reject if there are critical issues AND corrections failed
  const shouldReject = issues.length > 0 && correctionsMade === 0;

  return {
    shouldReject,
    correctedResponse: correctionsMade > 0 ? correctedResponse : undefined,
    correctionsMade,
    correctionsApplied,
    correctionsAttempted,
    issues
  };
}

/**
 * Get brand-specific product mappings for auto-correction
 */
export function getBrandProductMap(brand: string, line: string): Record<string, string> {
  const brandMaps: Record<string, Record<string, string>> = {
    'Wella': {
      'Illumina': 'Wella Illumina Color',
      'Koleston': 'Wella Koleston Perfect',
      'Color Fresh': 'Wella Color Fresh'
    },
    'Loreal': {
      'Majirel': 'L\'Oréal Majirel',
      'DiaRichesse': 'L\'Oréal DiaRichesse',
      'Inoa': 'L\'Oréal Inoa'
    },
    'Schwarzkopf': {
      'Igora': 'Schwarzkopf Igora Royal',
      'BlondMe': 'Schwarzkopf BlondMe'
    }
  };

  return brandMaps[brand]?.[line] ?
    { [line]: brandMaps[brand][line] } :
    { 'Professional': `${brand} Professional` };
}

/**
 * Post-process formula to fix any remaining unknown products
 */
export function postProcessUnknownProducts(
  formulationData: any,
  brand?: string,
  line?: string
): { fixed: boolean; fixesApplied: string[] } {
  const fixesApplied: string[] = [];
  let fixed = false;

  if (!formulationData?.steps) {
    return { fixed, fixesApplied };
  }

  formulationData.steps.forEach((step: any, stepIndex: number) => {
    if (step.mix && Array.isArray(step.mix)) {
      step.mix.forEach((product: any, productIndex: number) => {
        // Fix unknown product names
        if (product.productName && (
          product.productName.toLowerCase().includes('unknown') ||
          product.productName.toLowerCase().includes('generico')
        )) {
          const originalName = product.productName;

          // Try to extract shade/volume information
          const shadeMatch = originalName.match(/([0-9]+[\.\,][0-9]+|[0-9]+)/g);
          const volumeMatch = originalName.match(/(\d+)\s*vol/i);

          if (volumeMatch) {
            // It's a developer/oxidant
            const volume = volumeMatch[1];
            product.productName = brand ? `${brand} Oxidante ${volume} vol` : `Oxidante ${volume} vol`;
            product.type = 'developer';
            fixesApplied.push(`Paso ${stepIndex + 1}: Corregió oxidante desconocido a ${product.productName}`);
            fixed = true;
          } else if (shadeMatch && shadeMatch.length > 0) {
            // It's likely a color/tint
            const shade = shadeMatch[0];
            product.productName = brand && line ? `${brand} ${line} ${shade}` :
                                brand ? `${brand} Color ${shade}` :
                                `Tinte Profesional ${shade}`;
            product.type = 'color';
            product.shade = shade;
            fixesApplied.push(`Paso ${stepIndex + 1}: Corregió tinte desconocido a ${product.productName}`);
            fixed = true;
          } else {
            // Generic product, try to infer from type
            if (product.type) {
              const typeMap = {
                'tinte': 'Color Professional',
                'color': 'Color Professional',
                'oxidante': 'Oxidante Professional',
                'developer': 'Developer Professional',
                'decolorante': 'Decolorante Professional',
                'bleach': 'Bleach Professional'
              };

              const mappedType = typeMap[product.type.toLowerCase() as keyof typeof typeMap];
              if (mappedType) {
                product.productName = brand ? `${brand} ${mappedType}` : mappedType;
                fixesApplied.push(`Paso ${stepIndex + 1}: Corregió producto desconocido a ${product.productName}`);
                fixed = true;
              }
            }
          }
        }

        // Fix unknown brands
        if (product.brand && (
          product.brand.toLowerCase() === 'unknown' ||
          product.brand.toLowerCase() === 'generico'
        )) {
          if (brand) {
            product.brand = brand;
            fixesApplied.push(`Paso ${stepIndex + 1}: Corregió marca desconocida a ${brand}`);
            fixed = true;
          }
        }

        // Ensure line is set if available
        if (line && (!product.line || product.line === 'unknown')) {
          product.line = line;
          fixesApplied.push(`Paso ${stepIndex + 1}: Estableció línea ${line}`);
          fixed = true;
        }
      });
    }
  });

  return { fixed, fixesApplied };
}