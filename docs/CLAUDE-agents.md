# CLAUDE-agents.md

Specialized agent descriptions and usage patterns for Claude Code.

## 🤖 Specialized Agent Usage

## 🧠 AI EXCELLENCE CLUSTER (Phase 1 - ACTIVE)

### **ai-prompt-optimizer**

_Subagent of ai-integration-specialist_

- Advanced prompt engineering for hair colorimetry AI
- A/B testing of prompt variations with statistical significance
- Token optimization (target: <300 chars) while maintaining 98%+ accuracy
- Chemical terminology precision and safety-first prompt design
- **PROACTIVELY use** when OpenAI costs exceed budget by 10%

### **openai-cost-controller**

_Subagent of ai-integration-specialist_

- Real-time OpenAI cost monitoring and budget management
- Intelligent model selection (GPT-3.5 vs GPT-4o vs GPT-4o-mini)
- Cost attribution per feature and ROI analysis
- Automatic throttling and budget alerts
- **MUST BE USED** before deploying AI features to production

### **ai-response-validator**

_Subagent of colorimetry-expert_

- Chemical safety validation of AI-generated hair formulas
- FDA and EU cosmetic regulation compliance checking
- Professional accuracy verification and risk assessment
- Brand-specific product compatibility validation
- **MUST BE USED** for every AI formula before client delivery

### **vision-analysis-specialist**

_Subagent of ai-integration-specialist_

- GPT-4 Vision optimization for hair analysis accuracy
- Image quality assessment and user guidance optimization
- Multi-angle analysis coordination and token optimization
- Hair color level detection accuracy (target: 95%+ within 1 level)
- **PROACTIVELY use** when vision analysis accuracy drops below 90%

### 🏗️ Infrastructure & DevOps Agents

**database-architect** - Database schema and optimization

- Schema changes, migrations, RLS policies
- Query optimization (>500ms queries)
- Use all MCP Supabase tools for analysis
- PROACTIVELY use for deployments and performance reviews

**deployment-engineer** - DevOps and production releases

- Edge Function deployments with zero-downtime
- Database migrations and version management
- CI/CD pipeline setup and rollbacks
- MUST BE USED before ANY deployment

**debug-specialist** - Bug hunting and root cause analysis

- Production errors, crashes, test failures
- Systematic debugging with stack trace analysis
- Access to logs and diagnostic tools
- PROACTIVELY use for error investigation

**security-privacy-auditor** - Security and compliance

- Code security reviews, RLS policies
- GDPR/CCPA compliance auditing
- Data anonymization and privacy controls
- Use before releases and when handling sensitive data

**offline-sync-specialist** - Offline-first architecture

- Data synchronization and conflict resolution
- Queue management for offline operations
- Optimistic UI and sync issue debugging
- Use for sync conflicts and offline features

**data-migration-specialist** - Zero-downtime data operations

- Database schema evolution and data transformations
- Multi-tenant migration strategies
- Large-scale data operations
- MUST BE USED before production migrations

### 🎨 Frontend & UX Agents

**frontend-developer** - React Native implementation

- Complete feature implementation with clean code
- Expo, Zustand, and offline-first patterns
- PROACTIVELY use for new features and refactoring

**ui-designer** - Interface design and components

- React Native UI components and design systems
- Material Design and iOS HIG compliance
- PROACTIVELY use for new screens and UI redesigns

**whimsy-injector** - Micro-interactions and polish

- Smooth animations and haptic feedback
- Delightful UI moments without performance impact
- Use when base functionality is complete

**ux-researcher** - User experience analysis

- User flow analysis and friction identification
- Mobile app UX research and improvements
- PROACTIVELY use for new features and usability issues

**performance-benchmarker** - Performance optimization

- React Native performance metrics and bottlenecks
- FPS, memory, and load time optimization
- PROACTIVELY use when detecting slowness

### 🧠 AI & Business Logic Agents

**ai-integration-specialist** - OpenAI optimization

- GPT-4 Vision integration and prompt optimization
- API cost reduction and accuracy improvements
- PROACTIVELY use when working with AI features

**colorimetry-expert** - Hair coloration expertise

- Chemical formulation validation and terminology
- AI-generated formula accuracy verification
- PROACTIVELY use when reviewing prompts or formulas

**product-ceo** - Strategic business decisions

- Feature prioritization and unit economics
- Pricing strategy and go-to-market planning
- Use every Monday for business reviews

### 🧪 Quality & Testing Agents

**test-runner** - Automated testing

- Unit, integration, and E2E test execution
- Test coverage improvement (target >80%)
- PROACTIVELY use after implementing new features

**sprint-prioritizer** - Task organization

- Sprint planning and task prioritization
- Business value and technical dependency analysis
- PROACTIVELY use weekly or when >10 tasks accumulate

## 💡 Usage Examples

```bash
# Deploy with safety checks
Task: Use deployment-engineer to deploy salonier-assistant Edge Function

# Optimize slow database queries
Task: Use database-architect to analyze and optimize products table queries

# Fix production bug
Task: Use debug-specialist to investigate crash reports in service flow

# Add micro-interactions
Task: Use whimsy-injector to add haptic feedback to button interactions

# Strategic feature decision
Task: Use product-ceo to evaluate ROI of new AI diagnosis feature
```

## 🎯 Agent Selection Guide

### By Task Type

- **Frontend Work**: frontend-developer, ui-designer, whimsy-injector
- **Backend/Database**: database-architect, deployment-engineer, data-migration-specialist
- **Debugging**: debug-specialist, performance-benchmarker, test-runner
- **AI Features**: ai-integration-specialist, colorimetry-expert
- **Business**: product-ceo, sprint-prioritizer
- **Security**: security-privacy-auditor, offline-sync-specialist
