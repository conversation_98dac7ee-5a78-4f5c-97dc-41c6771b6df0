import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Info, CheckCircle, AlertTriangle, Clock } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { Client } from '@/stores/client-store';
import { COMMON_ALLERGIES } from '@/constants/common-allergies';

export type PatchTestResult = 'negativo' | 'positivo' | 'pendiente';

interface PatchTestProps {
  client: Client | null;
  value: PatchTestResult;
  onChange: (value: PatchTestResult) => void;
}

export default function PatchTest({ client, value, onChange }: PatchTestProps) {
  const handleSelect = (result: PatchTestResult) => {
    onChange(result);
    if (result === 'positivo') {
      Alert.alert(
        '⚠️ Test Positivo',
        'Se ha detectado una reacción alérgica. No se puede proceder con el servicio. Se recomienda consultar con un dermatólogo.',
        [{ text: 'Entendido' }]
      );
    } else if (result === 'pendiente') {
      Alert.alert(
        '⚠️ Proceder bajo responsabilidad',
        'El cliente acepta proceder sin test de parche bajo su propia responsabilidad. Esto aumenta el riesgo de reacciones adversas.',
        [{ text: 'Entendido' }]
      );
    }
  };

  const renderSpecialWarning = () => {
    if (!client || (!client.knownAllergies && !client.sensitiveSkin)) return null;

    const highSeverityAllergies: string[] = [];
    if (client.knownAllergies) {
      const allergies = client.knownAllergies.split(',').map(a => a.trim());
      allergies.forEach(allergy => {
        const match = COMMON_ALLERGIES.find(
          ca => ca.name.toLowerCase().includes(allergy.toLowerCase()) && ca.severity === 'high'
        );
        if (match) highSeverityAllergies.push(match.name);
      });
    }

    return (
      <View style={styles.specialWarning}>
        <Info size={16} color={Colors.light.warning} />
        <View style={styles.specialWarningContent}>
          <Text style={styles.specialWarningTitle}>Atención Especial Requerida</Text>
          {highSeverityAllergies.length > 0 && (
            <Text style={[styles.specialWarningText, styles.errorTextBold]}>
              ⚠️ ALERGIAS DE ALTA SEVERIDAD: {highSeverityAllergies.join(', ')}
            </Text>
          )}
          {client.knownAllergies && (
            <Text style={styles.specialWarningText}>
              • Alergias registradas: {client.knownAllergies}
            </Text>
          )}
          {client.sensitiveSkin && (
            <Text style={styles.specialWarningText}>• Cuero cabelludo sensible reportado</Text>
          )}
          <Text style={[styles.specialWarningText, styles.boldTextWithMargin]}>
            Test de parche OBLIGATORIO por seguridad del cliente.
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Clock size={24} color={Colors.light.accent} />
        <Text style={styles.stepTitle}>Test de Parche</Text>
      </View>

      <Text style={styles.stepDescription}>
        Verificación obligatoria para detectar posibles reacciones alérgicas
      </Text>

      {renderSpecialWarning()}

      <View style={styles.patchTestContainer}>
        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[styles.patchTestButton, value === 'negativo' && styles.patchTestButtonActive]}
            onPress={() => handleSelect('negativo')}
          >
            <CheckCircle
              size={20}
              color={value === 'negativo' ? Colors.light.textLight : Colors.light.success}
            />
            <Text
              style={[
                styles.patchTestButtonText,
                value === 'negativo' && styles.patchTestButtonTextActive,
              ]}
            >
              Test Negativo
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>No hay signos de reacción alérgica</Text>
        </View>

        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[
              styles.patchTestButton,
              styles.patchTestButtonDanger,
              value === 'positivo' && styles.patchTestButtonActive,
            ]}
            onPress={() => handleSelect('positivo')}
          >
            <AlertTriangle
              size={20}
              color={value === 'positivo' ? Colors.light.textLight : Colors.light.error}
            />
            <Text
              style={[
                styles.patchTestButtonText,
                value === 'positivo' && styles.patchTestButtonTextActive,
              ]}
            >
              Test Positivo
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>Hay signos de reacción alérgica</Text>
        </View>

        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[
              styles.patchTestButton,
              styles.patchTestButtonWarning,
              value === 'pendiente' && styles.patchTestButtonActive,
            ]}
            onPress={() => handleSelect('pendiente')}
          >
            <Clock
              size={20}
              color={value === 'pendiente' ? Colors.light.textLight : Colors.light.warning}
            />
            <Text
              style={[
                styles.patchTestButtonText,
                value === 'pendiente' && styles.patchTestButtonTextActive,
              ]}
            >
              Sin Test (Bajo Responsabilidad)
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>Cliente acepta proceder sin test previo</Text>
        </View>
      </View>

      <View style={styles.patchTestInfo}>
        <Text style={styles.patchTestInfoTitle}>¿Qué es un test de parche?</Text>
        <Text style={styles.patchTestInfoText}>
          Se aplica una pequeña cantidad del producto detrás de la oreja o en el antebrazo 48 horas
          antes del servicio para detectar posibles reacciones alérgicas.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    padding: 16,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginBottom: 16,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
  },
  specialWarning: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    backgroundColor: Colors.light.warningBackground,
    borderColor: Colors.light.warning,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
    marginBottom: 12,
  },
  specialWarningContent: {
    flex: 1,
  },
  specialWarningTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.warning,
    marginBottom: 6,
  },
  specialWarningText: {
    fontSize: 13,
    color: Colors.light.text,
  },
  errorTextBold: {
    color: Colors.light.error,
    fontWeight: '700',
    marginBottom: 4,
  },
  boldTextWithMargin: {
    fontWeight: '600',
    marginTop: 6,
  },
  patchTestContainer: {
    gap: 12,
  },
  patchTestOption: {
    marginBottom: 4,
  },
  patchTestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  patchTestButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  patchTestButtonWarning: {
    borderColor: Colors.light.warning,
  },
  patchTestButtonDanger: {
    borderColor: Colors.light.error,
  },
  patchTestButtonText: {
    color: Colors.light.text,
    fontWeight: '600',
  },
  patchTestButtonTextActive: {
    color: Colors.light.textLight,
  },
  patchTestDescription: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 6,
    marginLeft: 4,
  },
  patchTestInfo: {
    marginTop: 12,
    backgroundColor: Colors.light.infoBackground,
    borderColor: Colors.light.info,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
  },
  patchTestInfoTitle: {
    fontWeight: '600',
    color: Colors.light.info,
    marginBottom: 6,
  },
  patchTestInfoText: {
    color: Colors.light.text,
    fontSize: 13,
  },
});
