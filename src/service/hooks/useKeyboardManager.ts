import { useState, useEffect, useCallback } from 'react';
import { Keyboard, Platform } from 'react-native';
import { useTimer } from '@/utils/memory-cleanup';

interface UseKeyboardManagerProps {
  scrollViewRef: React.RefObject<any>;
}

interface UseKeyboardManagerReturn {
  keyboardHeight: number;
  scrollToBottom: () => void;
}

export const useKeyboardManager = ({
  scrollViewRef,
}: UseKeyboardManagerProps): UseKeyboardManagerReturn => {
  const { setTimeout: safeSetTimeout } = useTimer();
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      event => {
        setKeyboardHeight(event.endCoordinates.height);
        safeSetTimeout(
          () => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          },
          Platform.OS === 'ios' ? 100 : 300
        );
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, [safeSetTimeout, scrollViewRef]);

  const scrollToBottom = useCallback(() => {
    requestAnimationFrame(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    });
  }, [scrollViewRef]);

  return {
    keyboardHeight,
    scrollToBottom,
  };
};
