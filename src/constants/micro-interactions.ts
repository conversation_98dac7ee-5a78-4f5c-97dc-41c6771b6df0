/**
 * Micro-interactions Configuration for Salonier
 * Professional hair colorist app with delightful animations
 */

import { Easing } from 'react-native-reanimated';

export const MicroInteractions = {
  // Animation durations - professional and snappy
  durations: {
    instant: 150, // Button press feedback
    fast: 300, // Quick state changes
    normal: 500, // Standard transitions
    slow: 800, // Important state changes
    gentle: 1200, // Confidence building animations
  },

  // Easing curves - natural motion
  easing: {
    gentle: Easing.out(Easing.cubic), // Confidence indicators
    snappy: Easing.out(Easing.quad), // Button interactions
    bounce: Easing.out(Easing.back(1.5)), // Success confirmations
    smooth: Easing.inOut(Easing.quad), // Layout transitions
  },

  // Spring configurations - feel-good feedback
  springs: {
    gentle: { damping: 15, stiffness: 150 }, // Subtle feedback
    bouncy: { damping: 10, stiffness: 170 }, // Success animations
    tight: { damping: 20, stiffness: 200 }, // Precise interactions
    loose: { damping: 8, stiffness: 100 }, // Playful moments
  },

  // Scale values - subtle but noticeable
  scales: {
    subtle: 1.05, // Gentle emphasis
    medium: 1.1, // Clear feedback
    prominent: 1.15, // Success celebrations
    pressed: 0.95, // Button press state
    micro: 0.98, // Very subtle feedback
  },

  // Haptic feedback timing - when to use each type
  haptics: {
    // Light - for minor interactions, toggles
    light: ['toggle', 'tab_change', 'option_select'],

    // Medium - for important actions, confirmations
    medium: ['capture_photo', 'apply_formula', 'override_ai'],

    // Heavy - for critical actions, major completions
    heavy: ['complete_service', 'emergency_stop'],

    // Success - for positive outcomes
    success: ['formula_generated', 'diagnosis_complete', 'safe_result'],

    // Warning - for caution situations
    warning: ['low_confidence', 'risky_combination'],

    // Error - for problems, failures
    error: ['capture_failed', 'unsafe_formula', 'critical_error'],
  },

  // Rotation animations - for processing indicators
  rotations: {
    slow: 4000, // Sparkles, gentle processing
    medium: 3000, // Activity indicators
    fast: 2000, // Loading spinners
    pulse: 1500, // Attention seeking
  },

  // Translation values - for shake, slide effects
  translations: {
    shake: [-3, 3, -3, 3, 0], // Error shake sequence
    microShake: [-1, 1, -1, 1, 0], // Subtle attention
    slide: 20, // Standard slide distance
    float: [-3, 3], // Floating elements
  },

  // Opacity animations - for fades and highlights
  opacity: {
    highlight: [0.7, 1.0, 0.7], // Attention highlight
    breathe: [0.6, 1.0, 0.6], // Breathing effect
    flash: [0, 0.9, 0], // Camera flash
    pulse: [0.8, 1.0, 0.8], // Gentle pulse
  },

  // Delays for staggered animations
  stagger: {
    fast: 50, // Quick list animations
    normal: 100, // Standard stagger
    slow: 200, // Dramatic reveals
  },

  // Professional color confidence mappings
  confidence: {
    high: {
      animation: 'sparkle_rotate',
      haptic: 'success',
      duration: 'gentle',
    },
    medium: {
      animation: 'subtle_pulse',
      haptic: 'medium',
      duration: 'normal',
    },
    low: {
      animation: 'attention_pulse',
      haptic: 'warning',
      duration: 'slow',
    },
    critical: {
      animation: 'gentle_shake',
      haptic: 'error',
      duration: 'slow',
    },
  },

  // Hair analysis context animations
  analysis: {
    visual: {
      icon: 'camera',
      animation: 'flash_effect',
      color: 'primary',
    },
    chemical: {
      icon: 'test_tube',
      animation: 'bubble_effect',
      color: 'success',
    },
    historical: {
      icon: 'package',
      animation: 'fade_in',
      color: 'warning',
    },
    statistical: {
      icon: 'sparkles',
      animation: 'rotate_continuous',
      color: 'primary',
    },
  },
} as const;

// Helper functions for common animation patterns
export const AnimationHelpers = {
  // Create a professional confidence animation sequence
  confidenceSequence: (level: keyof typeof MicroInteractions.confidence) => {
    const config = MicroInteractions.confidence[level];
    return {
      duration: MicroInteractions.durations[config.duration],
      haptic: config.haptic,
      animation: config.animation,
    };
  },

  // Create a staggered entrance for lists
  staggeredEntrance: (index: number, _total: number) => {
    return {
      delay: index * MicroInteractions.stagger.normal,
      duration: MicroInteractions.durations.fast,
      easing: MicroInteractions.easing.gentle,
    };
  },

  // Professional success celebration
  successCelebration: () => ({
    scale: MicroInteractions.scales.prominent,
    spring: MicroInteractions.springs.bouncy,
    haptic: 'success',
  }),

  // Subtle attention seeking
  attentionSeeker: () => ({
    scale: MicroInteractions.scales.subtle,
    duration: MicroInteractions.durations.gentle,
    repeat: 3,
  }),
} as const;
