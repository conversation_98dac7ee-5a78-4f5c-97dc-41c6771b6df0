// Salonier Assistant Edge Function - Refactored
// Version: 44 (Modularized)
// Last Updated: 2025-09-24

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { AIRequest, AIResponse } from './types.ts';
import { logger, securityUtils } from './src/utils/security.ts';
import { generateCacheKey, checkCache, getCacheStats } from './src/utils/caching.ts';
import { ensureUserHasSalonId } from './src/utils/helpers.ts';

// Import task handlers (gradual restore)
import { diagnoseImage } from './tasks/diagnoseImage.ts';
import { analyzeDesiredLook } from './tasks/analyzeDesiredLook.ts';
import { generateFormula } from './tasks/generateFormula.ts';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async req => {
  logger.debug('Edge function invoked:', {
    method: req.method,
    url: req.url,
    hasOpenAIKey: !!openaiApiKey,
  });

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      logger.authEvent('Authentication failed - missing header', { result: 'failure' });
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    logger.debug('Processing authentication', {
      authToken: securityUtils.maskJWT(authHeader),
      timestamp: new Date().toISOString()
    });

    const { data: { user }, error: authError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (authError || !user) {
      logger.authEvent('Authentication failed - invalid token', {
        result: 'failure',
        error: authError?.message || 'No user found'
      });
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    logger.authEvent('Authentication successful', {
      userId: user.id,
      result: 'success'
    });

    const { data: profile } = await supabase.from('profiles').select('salon_id').eq('id', user.id).single();
    let salonId: string;

    if (!profile?.salon_id) {
      logger.authEvent('User missing salon association, attempting repair', { userId: user.id, result: 'failure' });
      const repairedSalonId = await ensureUserHasSalonId(supabase, user.id);
      if (!repairedSalonId) {
        logger.authEvent('Salon association repair failed', { userId: user.id, result: 'failure' });
        return new Response(JSON.stringify({ error: 'User not associated with a salon' }), {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
      salonId = repairedSalonId;
      logger.authEvent('Salon association repaired successfully', { userId: user.id, salonId: repairedSalonId, result: 'success' });
    } else {
      salonId = profile.salon_id;
    }

    let requestBody;
    try {
      requestBody = await req.json();
    } catch (error) {
      return new Response(JSON.stringify({ error: 'Invalid JSON in request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { task: requestTask, action: requestAction, payload }: AIRequest = requestBody;
    let task = requestTask || requestAction;

    const actionToTaskMap: { [key: string]: string } = {
      'diagnose_image': 'diagnose_image',
      'analyze_hair': 'diagnose_image',
      'analyze_desired_look': 'analyze_desired_look',
      'generate_formula': 'generate_formula'
    };

    if (task && actionToTaskMap[task]) {
      task = actionToTaskMap[task];
    }

    logger.debug('Edge function received request', {
      task,
      payloadPreview: payload ? securityUtils.generateSafePreview(payload, 200) : null
    });

    const allowedTasks = Object.keys(actionToTaskMap);
    if (!task || !allowedTasks.includes(task)) {
      return new Response(JSON.stringify({ error: `Invalid task type: ${task}` }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (!payload || typeof payload !== 'object') {
      return new Response(JSON.stringify({ error: 'Missing or invalid payload' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (!openaiApiKey) {
      return new Response(JSON.stringify({ success: false, error: 'OpenAI API key not configured.' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const cacheKey = generateCacheKey(task, payload);
    const cachedResult = await checkCache(supabase, salonId, task, cacheKey);

    if (cachedResult) {
      return new Response(JSON.stringify({ success: true, data: cachedResult, cached: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    let result: AIResponse;

    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId, supabase, openaiApiKey);
        break;
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId, supabase, openaiApiKey);
        break;
      case 'generate_formula':
        result = await generateFormula(payload, salonId, supabase, openaiApiKey);
        break;
      case 'cache_stats':
        const cacheStats = await getCacheStats(supabase, salonId);
        result = { success: true, data: cacheStats };
        break;
      default:
        result = { success: false, error: `Unknown action: ${task}. Available: diagnose_image, analyze_desired_look, generate_formula, cache_stats` };
    }

    logger.debug('Task completed:', { task, success: result.success, error: result.error });

    if (result.success && !result.data) {
      result = { success: false, error: 'No data generated' };
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    logger.error('Edge function error occurred', error);
    const errorResponse = { success: false, error: 'Internal server error.' };
    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});