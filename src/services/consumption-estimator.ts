import { HairDensity, HairThickness } from '@/types/hair-diagnosis';

export type ApplicationArea = 'roots' | 'full' | 'mids_ends' | 'toner_global';

export interface EstimateArgs {
  hairLengthCm?: number; // total length from Diagnosis (cm)
  density?: HairDensity | string; // allow raw strings ('Baja'|'Media'|'Alta')
  thickness?: HairThickness | string; // 'Fino'|'Medio'|'Grueso'
  application: ApplicationArea;
}

function lengthBucket(cm?: number): 'short' | 'medium' | 'long' | 'xlong' {
  if (!cm || cm <= 0) return 'medium';
  if (cm <= 20) return 'short';
  if (cm <= 35) return 'medium';
  if (cm <= 50) return 'long';
  return 'xlong';
}

function toDensity(val?: HairDensity | string): HairDensity {
  if (typeof val === 'string') {
    const v = val.toLowerCase();
    if (v.includes('alta') || v.includes('high')) return HairDensity.HIGH;
    if (v.includes('baja') || v.includes('low')) return HairDensity.LOW;
  }
  return (val as HairDensity) || HairDensity.MEDIUM;
}

function toThickness(val?: HairThickness | string): HairThickness {
  if (typeof val === 'string') {
    const v = val.toLowerCase();
    if (v.includes('grues') || v.includes('thick')) return HairThickness.THICK;
    if (v.includes('fino') || v.includes('fine')) return HairThickness.FINE;
  }
  return (val as HairThickness) || HairThickness.MEDIUM;
}

/**
 * Deterministic estimation of color grams based on length, density and thickness.
 * Values are industry‑practical and rounded to 5g increments.
 */
export function estimateColorGrams(args: EstimateArgs): number {
  const bucket = lengthBucket(args.hairLengthCm);
  const density = toDensity(args.density);
  const thickness = toThickness(args.thickness);

  // Baselines by application and length bucket (grams of color/toner)
  const baseMap: Record<ApplicationArea, Record<typeof bucket, number>> = {
    roots: { short: 30, medium: 35, long: 40, xlong: 45 },
    full: { short: 40, medium: 60, long: 90, xlong: 120 },
    mids_ends: { short: 30, medium: 40, long: 60, xlong: 80 },
    toner_global: { short: 30, medium: 45, long: 60, xlong: 75 },
  };

  let grams = baseMap[args.application][bucket];

  // Multipliers
  const densityMult: Record<HairDensity, number> = {
    [HairDensity.LOW]: 0.9,
    [HairDensity.MEDIUM]: 1.0,
    [HairDensity.HIGH]: 1.2,
  };
  const thicknessMult: Record<HairThickness, number> = {
    [HairThickness.FINE]: 0.9,
    [HairThickness.MEDIUM]: 1.0,
    [HairThickness.THICK]: 1.1,
  };

  grams = grams * densityMult[density] * thicknessMult[thickness];

  // Round to nearest 5g, min 25g, max 150g (safety bounds)
  const rounded = Math.max(25, Math.min(150, Math.round(grams / 5) * 5));
  return rounded;
}
