#!/usr/bin/env node
// Seed/ensure compat rules for selected demi lines (Wella, etc.)
// Inputs: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY

import process from 'node:process';
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL_RAW = process.env.SUPABASE_URL;
const SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!SUPABASE_URL_RAW || !SERVICE_ROLE) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(2);
}
const SUPABASE_URL = SUPABASE_URL_RAW.replace(/\/(auth|rest)\/v1.*$/i, '');

const admin = createClient(SUPABASE_URL, SERVICE_ROLE, { auth: { persistSession: false } });

const TARGETS = [
  { brand: 'Wella Professionals', line: 'Color Touch', compat: { toner_line: 'Color Touch', toner_developer: 'Color Touch Emulsion' } },
  { brand: 'Wella Professionals', line: 'Shinefinity', compat: { toner_line: 'Shinefinity', toner_developer: 'Shinefinity Activator' } },
];

async function upsertRule(brand, line, compat) {
  const { data: b } = await admin.from('brands').select('id').ilike('name', brand).maybeSingle();
  if (!b?.id) { console.log('Brand not found, skipping:', brand); return; }
  const { data: l } = await admin.from('product_lines').select('id').eq('brand_id', b.id).ilike('name', line).maybeSingle();
  if (!l?.id) { console.log('Line not found, skipping:', brand, line); return; }

  // Try update first
  let { error: updErr } = await admin.from('brand_line_rules').update({ compat }).eq('brand_id', b.id).eq('line_id', l.id);
  if (updErr) {
    // Fallback to insert
    const { error: insErr } = await admin.from('brand_line_rules').insert({ brand_id: b.id, line_id: l.id, compat });
    if (insErr) {
      console.log('Upsert failed for', brand, line, insErr.message || insErr);
      return;
    }
    console.log('Inserted compat for', brand, line);
  } else {
    console.log('Updated compat for', brand, line);
  }
}

async function main() {
  // Check table exists
  const { error: probeErr } = await admin.from('brand_line_rules').select('brand_id').limit(1);
  if (probeErr) {
    console.log('brand_line_rules not available, skipping compat seed');
    return;
  }
  for (const t of TARGETS) {
    try { await upsertRule(t.brand, t.line, t.compat); } catch (e) { console.log('Error seeding', t.brand, t.line, e?.message || e); }
  }
}

main().catch(e => { console.error(e); process.exit(2); });

