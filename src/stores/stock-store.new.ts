import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { generateLocalId, useSyncQueueStore } from './sync-queue-store';
import { Database } from '@/types/database';
import { Product, StockMovement, InventoryAlert, ConsumptionAnalysis } from '@/types/inventory';
import { ColorFormula } from '@/types/formulation';
import { logger } from '@/utils/logger';

type SupabaseStockMovement = Database['public']['Tables']['stock_movements']['Row'];

// Type for low stock products from RPC function
interface LowStockProduct {
  product_id: string;
  brand: string;
  name: string;
  category: string;
  stock_ml: number;
  minimum_stock_ml: number;
  percentage_remaining: number;
  color_code?: string;
}

interface StockStore {
  movements: StockMovement[];
  alerts: InventoryAlert[];
  lowStockProducts: LowStockProduct[];
  isLoadingLowStock: boolean;

  // Current stock state (read from products)
  currentStock: { [productId: string]: number };
  lastSync: string | null;

  // Stock Actions
  updateStock: (
    productId: string,
    quantity: number,
    type: StockMovement['type'],
    reason: string,
    referenceId?: string
  ) => Promise<void>;

  consumeProducts: (
    consumptions: Array<{ productId: string; quantity: number }>,
    referenceId: string,
    clientName: string
  ) => Promise<void>;

  getStockMovements: (productId?: string, limit?: number) => StockMovement[];

  // Alert Actions
  createAlert: (
    productId: string,
    type: InventoryAlert['type'],
    message: string,
    severity: InventoryAlert['severity']
  ) => void;
  acknowledgeAlert: (alertId: string, userId: string) => void;
  getActiveAlerts: () => InventoryAlert[];
  checkLowStock: () => void;

  // Low Stock Actions
  loadLowStockProducts: () => Promise<void>;
  getLowStockProducts: () => LowStockProduct[];

  // Analysis & Reports
  getConsumptionAnalysis: (
    productId: string,
    period: 'daily' | 'weekly' | 'monthly'
  ) => ConsumptionAnalysis | null;

  calculateStockMetrics: (products: Product[]) => {
    totalValue: number;
    lowStockCount: number;
    outOfStockCount: number;
    overstockCount: number;
    activeProducts: Product[];
  };

  calculateMostUsedProducts: (
    products: Product[]
  ) => Array<{ product: Product; usageCount: number; totalConsumed: number }>;

  // Stock tracking utilities
  setCurrentStock: (productId: string, stock: number) => void;
  getCurrentStock: (productId: string) => number;

  // Sync functionality
  loadMovements: () => Promise<void>;
  syncWithSupabase: () => Promise<void>;

  // Utilities
  generateMockMovements: (products: Product[]) => void;
  clearMovements: () => void;
}

const stockLogger = logger.withContext('StockStore');

// Mapeo de tipos español → inglés para insertar en BD
export const typeSpanishToEnglish: Record<string, string> = {
  compra: 'purchase',
  consumo: 'use',
  ajuste: 'adjustment',
  devolución: 'return',
  pérdida: 'waste',
  inventario_inicial: 'purchase', // Se mapea a purchase para inventario inicial
  entrada: 'purchase', // Para StockMovementsModal
  salida: 'use', // Para StockMovementsModal
};

// Mapeo de tipos inglés → español para UI (mantener compatibilidad)
export const typeEnglishToSpanish: Record<string, string> = {
  purchase: 'compra',
  use: 'consumo',
  adjustment: 'ajuste',
  return: 'devolución',
  waste: 'pérdida',
};

export function convertSupabaseMovementToLocal(movement: SupabaseStockMovement): StockMovement {
  // La BD ahora devuelve tipos en inglés, validamos que sea un tipo válido
  const validTypes: StockMovement['type'][] = ['purchase', 'use', 'adjustment', 'return', 'waste'];
  const type = validTypes.includes(movement.type as StockMovement['type'])
    ? (movement.type as StockMovement['type'])
    : 'adjustment';

  return {
    id: movement.id,
    productId: movement.product_id,
    quantity: movement.quantity_ml,
    type,
    date: movement.created_at,
    userId: movement.created_by || 'system',
    referenceId: movement.reference_id || undefined,
    notes: movement.notes || null,
  };
}

/**
 * Handle sync errors consistently across all operations
 * @param operation - The operation that failed
 * @param error - The error that occurred
 * @param data - Optional data to add to sync queue
 */
async function handleSyncError(
  operation: 'create' | 'update' | 'delete',
  error: unknown,
  data?: Record<string, unknown>
): Promise<void> {
  stockLogger.error(`Error during ${operation} sync:`, error);

  if (data) {
    useSyncQueueStore.getState().addToQueue({
      type: operation,
      table: 'stock_movements',
      data,
    });
  }
}

/**
 * Calculate stock metrics for inventory report
 */
function calculateStockMetrics(products: Product[]) {
  const activeProducts = products.filter(p => p.isActive);

  return {
    totalValue: products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0),
    lowStockCount: activeProducts.filter(p => p.currentStock > 0 && p.currentStock <= p.minStock)
      .length,
    outOfStockCount: activeProducts.filter(p => p.currentStock === 0).length,
    overstockCount: activeProducts.filter(p => p.maxStock && p.currentStock > p.maxStock).length,
    activeProducts,
  };
}

/**
 * Calculate most used products from movements
 */
function calculateMostUsedProducts(
  movements: StockMovement[],
  products: Product[]
): Array<{ product: Product; usageCount: number; totalConsumed: number }> {
  const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const recentMovements = movements.filter(m => new Date(m.date) >= last30Days);

  const consumptionMap = recentMovements
    .filter(m => m.type === 'use')
    .reduce(
      (acc, m) => {
        if (!acc[m.productId]) {
          acc[m.productId] = { count: 0, total: 0 };
        }
        acc[m.productId].count += 1;
        acc[m.productId].total += Math.abs(m.quantity);
        return acc;
      },
      {} as Record<string, { count: number; total: number }>
    );

  const productMap = new Map(products.map(p => [p.id, p]));

  return Object.entries(consumptionMap)
    .map(([productId, data]) => {
      const product = productMap.get(productId);
      return product
        ? {
            product,
            usageCount: data.count,
            totalConsumed: data.total,
          }
        : null;
    })
    .filter(Boolean)
    .sort((a, b) => b!.totalConsumed - a!.totalConsumed)
    .slice(0, 5) as Array<{
    product: Product;
    usageCount: number;
    totalConsumed: number;
  }>;
}

export const useStockStore = create<StockStore>()(
  persist(
    (set, get) => ({
      movements: [],
      alerts: [],
      lowStockProducts: [],
      isLoadingLowStock: false,
      currentStock: {},
      lastSync: null,

      updateStock: async (productId, quantity, type, reason, referenceId) => {
        stockLogger.startTimer('updateStock');

        // Update local stock immediately
        const currentStockLevel = get().currentStock[productId] || 0;
        const newStock = currentStockLevel + quantity;

        set(state => ({
          currentStock: {
            ...state.currentStock,
            [productId]: newStock,
          },
        }));

        // Create movement record
        const movement: StockMovement = {
          id: generateLocalId('movement'),
          productId,
          quantity,
          type,
          date: new Date().toISOString(),
          userId: 'current-user', // TODO: Get from auth
          referenceId,
          notes: reason,
        };

        set(state => ({
          movements: [movement, ...state.movements],
        }));

        // Sync to Supabase if online
        const { isOnline } = useSyncQueueStore.getState();
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('No salon ID');

            const { error } = await supabase.from('stock_movements').insert({
              salon_id: salonId,
              product_id: productId,
              quantity_ml: quantity,
              type: typeSpanishToEnglish[type] || type,
              notes: reason,
              reference_id: referenceId,
            });

            if (error) throw error;

            stockLogger.endTimer('updateStock');
          } catch (error) {
            await handleSyncError('create', error, {
              salon_id: await getCurrentSalonId(),
              product_id: productId,
              quantity_ml: quantity,
              type: typeSpanishToEnglish[type] || type,
              notes: reason,
              reference_id: referenceId,
            });
          }
        }
      },

      consumeProducts: async (consumptions, referenceId, clientName) => {
        stockLogger.startTimer('consumeProducts');

        // Process each consumption in parallel for better performance
        const consumptionPromises = consumptions.map(({ productId, quantity }) =>
          get().updateStock(
            productId,
            -quantity, // Negative for consumption
            'use',
            `Servicio para ${clientName}`,
            referenceId
          )
        );

        await Promise.all(consumptionPromises);

        stockLogger.info('Products consumed', {
          count: consumptions.length,
          referenceId,
        });
        stockLogger.endTimer('consumeProducts');
      },

      getStockMovements: (productId, limit = 50) => {
        const movements = get().movements;
        const filtered = productId ? movements.filter(m => m.productId === productId) : movements;
        return filtered.slice(0, limit);
      },

      createAlert: (productId, type, message, severity = 'warning') => {
        const alert: InventoryAlert = {
          id: generateLocalId('alert'),
          productId,
          type,
          message,
          severity,
          createdAt: new Date().toISOString(),
          isActive: true,
        };

        set(state => ({
          alerts: [...state.alerts, alert],
        }));

        stockLogger.info('Alert created', { type, severity, productId });
      },

      acknowledgeAlert: (alertId, userId) => {
        set(state => ({
          alerts: state.alerts.map(a =>
            a.id === alertId
              ? {
                  ...a,
                  isActive: false,
                  acknowledgedBy: userId,
                  acknowledgedAt: new Date().toISOString(),
                }
              : a
          ),
        }));
      },

      getActiveAlerts: () => {
        return get().alerts.filter(a => a.isActive);
      },

      checkLowStock: () => {
        // This requires access to products, which will be provided by caller
        // Load low stock products from RPC
        get().loadLowStockProducts();
      },

      loadLowStockProducts: async () => {
        stockLogger.info('Loading low stock products');
        set({ isLoadingLowStock: true });

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            stockLogger.warn('No salon ID found, skipping low stock products load');
            set({ isLoadingLowStock: false });
            return;
          }

          const { data, error } = await supabase.rpc('get_low_stock_products', {
            p_salon_id: salonId,
          });

          if (error) {
            stockLogger.error('Error loading low stock products:', error);
            throw error;
          }

          set({
            lowStockProducts: data || [],
            isLoadingLowStock: false,
          });

          stockLogger.info(`Loaded ${data?.length || 0} low stock products`);
        } catch (error) {
          stockLogger.error('Failed to load low stock products:', error);
          set({
            lowStockProducts: [],
            isLoadingLowStock: false,
          });
        }
      },

      getLowStockProducts: () => {
        return get().lowStockProducts;
      },

      getConsumptionAnalysis: (productId, period) => {
        const movements = get().movements.filter(
          m => m.productId === productId && m.type === 'use'
        );

        if (movements.length === 0) return null;

        const now = new Date();
        const periodDays = period === 'daily' ? 1 : period === 'weekly' ? 7 : 30;
        const startDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000);

        const recentMovements = movements.filter(m => new Date(m.date) >= startDate);

        const totalConsumed = recentMovements.reduce((sum, m) => sum + Math.abs(m.quantity), 0);

        const avgConsumption = totalConsumed / periodDays;
        const currentStockLevel = get().currentStock[productId] || 0;
        const daysUntilEmpty = avgConsumption > 0 ? currentStockLevel / avgConsumption : 0;

        return {
          productId,
          period,
          totalConsumed,
          avgConsumption,
          daysUntilEmpty,
          lastConsumption: movements[0]?.date,
        };
      },

      calculateStockMetrics: (products: Product[]) => {
        return calculateStockMetrics(products);
      },

      calculateMostUsedProducts: (products: Product[]) => {
        return calculateMostUsedProducts(get().movements, products);
      },

      setCurrentStock: (productId, stock) => {
        set(state => ({
          currentStock: {
            ...state.currentStock,
            [productId]: stock,
          },
        }));
      },

      getCurrentStock: productId => {
        return get().currentStock[productId] || 0;
      },

      loadMovements: async () => {
        stockLogger.startTimer('loadMovements');

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            stockLogger.warn('No salon ID found, skipping movements load');
            return;
          }

          // Load recent movements
          const { data: movementsData, error: movementsError } = await supabase
            .from('stock_movements')
            .select('*')
            .eq('salon_id', salonId)
            .order('created_at', { ascending: false })
            .limit(500); // Increased limit for better analysis

          if (movementsError) throw movementsError;

          const localMovements = movementsData?.map(m => convertSupabaseMovementToLocal(m)) || [];

          set({
            movements: localMovements,
            lastSync: new Date().toISOString(),
          });

          stockLogger.info('Movements loaded', {
            movementCount: localMovements.length,
          });

          stockLogger.endTimer('loadMovements');
        } catch (error) {
          stockLogger.error('Error loading movements:', error);
        }
      },

      syncWithSupabase: async () => {
        stockLogger.startTimer('syncWithSupabase');

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          stockLogger.warn('No salon ID for sync');
          return;
        }

        await get().loadMovements();
        await get().loadLowStockProducts();

        stockLogger.endTimer('syncWithSupabase');
      },

      generateMockMovements: (products: Product[]) => {
        const movements: StockMovement[] = [];

        products.forEach(product => {
          // Generate 5-10 random movements per product
          const movementCount = Math.floor(Math.random() * 6) + 5;

          for (let i = 0; i < movementCount; i++) {
            const daysAgo = Math.floor(Math.random() * 90); // Last 90 days
            const date = new Date();
            date.setDate(date.getDate() - daysAgo);

            const types: StockMovement['type'][] = ['use', 'purchase', 'adjustment'];
            const type = types[Math.floor(Math.random() * types.length)];

            let quantity: number;
            if (type === 'use') {
              quantity = -(Math.floor(Math.random() * 50) + 10); // -10 to -60
            } else if (type === 'purchase') {
              quantity = Math.floor(Math.random() * 5) * product.unitSize; // Buy in units
            } else {
              quantity = Math.floor(Math.random() * 40) - 20; // -20 to +20
            }

            movements.push({
              id: generateLocalId('movement'),
              productId: product.id,
              quantity,
              type,
              date: date.toISOString(),
              userId: 'demo-user',
              notes: `Demo ${type}`,
            });
          }
        });

        set(state => ({
          movements: [...movements, ...state.movements].sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          ),
        }));

        stockLogger.info('Generated mock movements', {
          count: movements.length,
        });
      },

      clearMovements: () => {
        set({
          movements: [],
          alerts: [],
          lowStockProducts: [],
          currentStock: {},
          lastSync: null,
        });
        stockLogger.info('All stock data cleared');
      },
    }),
    {
      name: 'stock-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        movements: state.movements.slice(0, 200), // Keep last 200 movements
        alerts: state.alerts.filter(a => a.isActive), // Only keep active alerts
        currentStock: state.currentStock,
        lastSync: state.lastSync,
      }),
    }
  )
);

// Utility functions are now exported at their definitions above

// Enhanced consumption utilities for ColorFormula processing
export class StockConsumptionService {
  /**
   * Process a ColorFormula and consume the required products from stock
   */
  static async consumeFromFormula(
    formula: ColorFormula,
    referenceId: string,
    clientName: string
  ): Promise<void> {
    const stockStore = useStockStore.getState();
    const consumptions: Array<{ productId: string; quantity: number }> = [];

    // Process colors
    formula.colors.forEach(color => {
      // This would require AI matching or product mapping
      // For now, we'll use a placeholder approach
      const productId = `color_${color.tone.toLowerCase().replace(/\s+/g, '_')}`;
      consumptions.push({
        productId,
        quantity: color.amount,
      });
    });

    // Process developer
    if (formula.developerVolume > 0) {
      const developerId = `developer_${formula.developerVolume}vol`;
      const developerAmount =
        formula.colors.reduce((sum, c) => sum + c.amount, 0) *
        (parseFloat(formula.developerRatio.split(':')[1]) /
          parseFloat(formula.developerRatio.split(':')[0]));

      consumptions.push({
        productId: developerId,
        quantity: developerAmount,
      });
    }

    // Process additives
    formula.additives.forEach(additive => {
      const additiveId = `additive_${additive.toLowerCase().replace(/\s+/g, '_')}`;
      consumptions.push({
        productId: additiveId,
        quantity: 10, // Default amount for additives
      });
    });

    await stockStore.consumeProducts(consumptions, referenceId, clientName);
  }

  /**
   * Check if there's sufficient stock for a formula
   */
  static checkFormulaStock(formula: ColorFormula): {
    sufficient: boolean;
    shortages: Array<{ product: string; required: number; available: number }>;
  } {
    const stockStore = useStockStore.getState();
    const shortages: Array<{ product: string; required: number; available: number }> = [];

    // Check colors
    formula.colors.forEach(color => {
      const productId = `color_${color.tone.toLowerCase().replace(/\s+/g, '_')}`;
      const available = stockStore.getCurrentStock(productId);

      if (available < color.amount) {
        shortages.push({
          product: color.tone,
          required: color.amount,
          available,
        });
      }
    });

    // Check developer
    if (formula.developerVolume > 0) {
      const developerId = `developer_${formula.developerVolume}vol`;
      const required =
        formula.colors.reduce((sum, c) => sum + c.amount, 0) *
        (parseFloat(formula.developerRatio.split(':')[1]) /
          parseFloat(formula.developerRatio.split(':')[0]));
      const available = stockStore.getCurrentStock(developerId);

      if (available < required) {
        shortages.push({
          product: `Developer ${formula.developerVolume}vol`,
          required,
          available,
        });
      }
    }

    return {
      sufficient: shortages.length === 0,
      shortages,
    };
  }
}
