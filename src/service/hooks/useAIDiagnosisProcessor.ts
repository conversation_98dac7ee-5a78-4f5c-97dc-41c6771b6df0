import { useEffect, useState, useRef } from 'react';
import { Alert, type ScrollView } from 'react-native';
import * as Haptics from 'expo-haptics';
import { logger } from '@/utils/logger';
import { safeTransformAIZoneData } from '@/service/utils/aiDataTransformer';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import {
  HairZone,
  NaturalTone,
  Undertone,
  HairState,
  GrayHairType,
  GrayPattern,
  UnwantedTone,
  CuticleState,
  HairPorosity,
  HairElasticity,
  HairResistance,
} from '@/types/hair-diagnosis';
import { HairAnalysisResult, isValidHairAnalysisResult } from '@/types/ai-analysis';

export const useAIDiagnosisProcessor = (
  analysisResult: HairAnalysisResult | null,
  isAnalyzing: boolean,
  onUpdate: (updates: Partial<ServiceData>) => void,
  data: ServiceData,
  scrollRef: React.RefObject<ScrollView>
) => {
  const [isDataFromAI, setIsDataFromAI] = useState(false);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);
  const hasShownNotificationRef = useRef(false);

  useEffect(() => {
    let isComponentMounted = true;

    try {
      if (
        analysisResult &&
        !isAnalyzing &&
        !hasShownNotificationRef.current &&
        isComponentMounted
      ) {
        // Runtime validation of AI response
        if (!isValidHairAnalysisResult(analysisResult)) {
          logger.warn('Invalid AI analysis result structure', 'DiagnosisStep', {
            receivedKeys: Object.keys(analysisResult),
            analysisResult,
          });
          return;
        }
        logger.debug('Processing AI analysis result for field mapping', 'DiagnosisStep', {
          responseKeys: Object.keys(analysisResult),
          detectedChemicalProcess: analysisResult.detectedChemicalProcess,
          estimatedLastProcessDate: analysisResult.estimatedLastProcessDate,
          hasDetectedProcesses: !!analysisResult.detectedProcesses,
          hasDetectedRisks: !!analysisResult.detectedRisks,
          overallCondition: analysisResult.overallCondition,
          hairThickness: analysisResult.hairThickness,
          hairDensity: analysisResult.hairDensity,
          overallTone: analysisResult.overallTone,
          overallReflect: analysisResult.overallReflect,
          zoneAnalysisKeys: analysisResult.zoneAnalysis
            ? Object.keys(analysisResult.zoneAnalysis)
            : [],
        });

        const mappedChemicalProcess =
          analysisResult.detectedChemicalProcess ||
          (analysisResult.detectedProcesses && analysisResult.detectedProcesses.chemicalProcess) ||
          data.lastChemicalProcessType ||
          '';
        const mappedProcessDate =
          analysisResult.estimatedLastProcessDate ||
          (analysisResult.detectedProcesses && analysisResult.detectedProcesses.lastProcessDate) ||
          data.lastChemicalProcessDate ||
          '';

        logger.info('AI Field Mapping Results', 'DiagnosisStep', {
          mappedChemicalProcess,
          mappedProcessDate,
          willPopulateChemicalProcess: !!mappedChemicalProcess,
          willPopulateProcessDate: !!mappedProcessDate,
        });

        const detectedProcesses = analysisResult.detectedProcesses || {};
        const riskDetection = analysisResult.detectedRisks || {};
        const hairAnalysis = analysisResult.hairAnalysis || {};

        const overallCondition =
          analysisResult.overallCondition || hairAnalysis.overallCondition || '';
        const isDamaged =
          riskDetection.extremeDamage?.detected || overallCondition.toLowerCase().includes('dañ');

        const safeAnalysisResult = {
          hairThickness: analysisResult.hairThickness || 'Medio',
          hairDensity: analysisResult.hairDensity || 'Media',
          hairLength: analysisResult.hairLength || data.hairLength || 0,
          overallTone: analysisResult.overallTone || data.overallTone || '',
          overallReflect:
            analysisResult.overallReflect ||
            analysisResult.overallUndertone ||
            data.overallReflect ||
            '',
          lastChemicalProcessType:
            analysisResult.detectedChemicalProcess ||
            detectedProcesses.chemicalProcess ||
            data.lastChemicalProcessType ||
            '',
          lastChemicalProcessDate:
            analysisResult.estimatedLastProcessDate ||
            detectedProcesses.lastProcessDate ||
            data.lastChemicalProcessDate ||
            '',
          zoneColorAnalysis: (() => {
            try {
              const safeZoneAnalysis = analysisResult.zoneAnalysis || {};
              logger.debug(
                'Complete zone analysis structure from AI for COLOR processing',
                'DiagnosisStep',
                {
                  hasZoneAnalysis: !!analysisResult.zoneAnalysis,
                  zoneAnalysisKeys: Object.keys(safeZoneAnalysis),
                  rootsStructure: safeZoneAnalysis.roots
                    ? {
                        keys: Object.keys(safeZoneAnalysis.roots),
                        sampleData: safeZoneAnalysis.roots,
                      }
                    : null,
                  midsStructure: safeZoneAnalysis.mids
                    ? {
                        keys: Object.keys(safeZoneAnalysis.mids),
                        sampleData: safeZoneAnalysis.mids,
                      }
                    : null,
                  endsStructure: safeZoneAnalysis.ends
                    ? {
                        keys: Object.keys(safeZoneAnalysis.ends),
                        sampleData: safeZoneAnalysis.ends,
                      }
                    : null,
                }
              );

              const rootsData = safeTransformAIZoneData(
                safeZoneAnalysis.roots || safeZoneAnalysis.ROOTS,
                'roots'
              );
              const midsData = safeTransformAIZoneData(
                safeZoneAnalysis.mids || safeZoneAnalysis.MIDS,
                'mids'
              );
              const endsData = safeTransformAIZoneData(
                safeZoneAnalysis.ends || safeZoneAnalysis.ENDS,
                'ends'
              );

              const inferredDamageLevel = isDamaged
                ? overallCondition.toLowerCase().includes('alto') ||
                  overallCondition.toLowerCase().includes('severo')
                  ? 'Alto'
                  : overallCondition.toLowerCase().includes('medio') ||
                      overallCondition.toLowerCase().includes('moderado')
                    ? 'Medio'
                    : 'Bajo'
                : undefined;

              logger.debug('Complete zone analysis structure from AI', 'DiagnosisStep', {
                rootsRawData: safeZoneAnalysis.roots,
                midsRawData: safeZoneAnalysis.mids,
                endsRawData: safeZoneAnalysis.ends,
                inferredDamageLevel,
                isDamaged,
              });

              logger.info('Zone color analysis transformation completed', 'DiagnosisStep', {
                rootsTransformed: {
                  level: rootsData.level,
                  tone: rootsData.tone,
                  damage: rootsData.damage || inferredDamageLevel,
                  pigmentAccumulation: rootsData.pigmentAccumulation,
                },
                midsTransformed: {
                  level: midsData.level,
                  tone: midsData.tone,
                  damage: midsData.damage || inferredDamageLevel,
                  pigmentAccumulation: midsData.pigmentAccumulation,
                },
                endsTransformed: {
                  level: endsData.level,
                  tone: endsData.tone,
                  damage: endsData.damage || inferredDamageLevel,
                  pigmentAccumulation: endsData.pigmentAccumulation,
                },
              });

              return {
                [HairZone.ROOTS]: {
                  zone: HairZone.ROOTS,
                  level: rootsData.level,
                  tone: rootsData.tone as NaturalTone,
                  reflect: rootsData.reflect as Undertone,
                  state: rootsData.state as HairState,
                  grayPercentage: rootsData.grayPercentage,
                  grayType: rootsData.grayType as GrayHairType,
                  grayPattern: rootsData.grayPattern as GrayPattern,
                  unwantedTone: rootsData.unwantedTone as UnwantedTone,
                  cuticleState: rootsData.cuticleState as CuticleState,
                  damage: rootsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.MIDS]: {
                  zone: HairZone.MIDS,
                  level: midsData.level,
                  tone: midsData.tone as NaturalTone,
                  reflect: midsData.reflect as Undertone,
                  state: midsData.state as HairState,
                  unwantedTone: midsData.unwantedTone as UnwantedTone,
                  pigmentAccumulation: midsData.pigmentAccumulation as 'Baja' | 'Media' | 'Alta',
                  cuticleState: midsData.cuticleState as CuticleState,
                  damage: midsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.ENDS]: {
                  zone: HairZone.ENDS,
                  level: endsData.level,
                  tone: endsData.tone as NaturalTone,
                  reflect: endsData.reflect as Undertone,
                  state: endsData.state as HairState,
                  unwantedTone: endsData.unwantedTone as UnwantedTone,
                  pigmentAccumulation: endsData.pigmentAccumulation as 'Baja' | 'Media' | 'Alta',
                  cuticleState: endsData.cuticleState as CuticleState,
                  damage: endsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
              };
            } catch (error) {
              logger.error('Error processing zone color analysis data', 'DiagnosisStep', error);
              return {
                [HairZone.ROOTS]: { zone: HairZone.ROOTS, level: 5 },
                [HairZone.MIDS]: { zone: HairZone.MIDS, level: 5 },
                [HairZone.ENDS]: { zone: HairZone.ENDS, level: 5 },
              };
            }
          })(),
          zonePhysicalAnalysis: (() => {
            try {
              const safeZoneAnalysis = analysisResult.zoneAnalysis || {};
              logger.debug(
                'Complete zone analysis structure from AI for PHYSICAL processing',
                'DiagnosisStep',
                {
                  processingPhysicalAnalysis: true,
                  hasZoneAnalysis: !!analysisResult.zoneAnalysis,
                  zoneAnalysisKeys: Object.keys(safeZoneAnalysis),
                }
              );

              const rootsData = safeTransformAIZoneData(
                safeZoneAnalysis.roots || safeZoneAnalysis.ROOTS,
                'roots'
              );
              const midsData = safeTransformAIZoneData(
                safeZoneAnalysis.mids || safeZoneAnalysis.MIDS,
                'mids'
              );
              const endsData = safeTransformAIZoneData(
                safeZoneAnalysis.ends || safeZoneAnalysis.ENDS,
                'ends'
              );

              const inferredDamageLevel = isDamaged
                ? overallCondition.toLowerCase().includes('alto') ||
                  overallCondition.toLowerCase().includes('severo')
                  ? 'Alto'
                  : overallCondition.toLowerCase().includes('medio') ||
                      overallCondition.toLowerCase().includes('moderado')
                    ? 'Medio'
                    : 'Bajo'
                : undefined;

              return {
                [HairZone.ROOTS]: {
                  zone: HairZone.ROOTS,
                  porosity: rootsData.porosity as HairPorosity,
                  elasticity: rootsData.elasticity as HairElasticity,
                  resistance: rootsData.resistance as HairResistance,
                  damage: rootsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.MIDS]: {
                  zone: HairZone.MIDS,
                  porosity: midsData.porosity as HairPorosity,
                  elasticity: midsData.elasticity as HairElasticity,
                  resistance: midsData.resistance as HairResistance,
                  damage: midsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
                [HairZone.ENDS]: {
                  zone: HairZone.ENDS,
                  porosity: endsData.porosity as HairPorosity,
                  elasticity: endsData.elasticity as HairElasticity,
                  resistance: endsData.resistance as HairResistance,
                  damage: endsData.damage || (inferredDamageLevel as 'Bajo' | 'Medio' | 'Alto'),
                },
              };
            } catch (error) {
              logger.error('Error processing zone physical analysis data', 'DiagnosisStep', error);
              return {
                [HairZone.ROOTS]: { zone: HairZone.ROOTS, damage: undefined },
                [HairZone.MIDS]: { zone: HairZone.MIDS, damage: undefined },
                [HairZone.ENDS]: { zone: HairZone.ENDS, damage: undefined },
              };
            }
          })(),
        };

        logger.debug('Mapped AI values for form update', 'DiagnosisStep', {
          lastChemicalProcessType: safeAnalysisResult.lastChemicalProcessType,
          lastChemicalProcessDate: safeAnalysisResult.lastChemicalProcessDate,
          hairThickness: safeAnalysisResult.hairThickness,
          hairDensity: safeAnalysisResult.hairDensity,
          hairLength: safeAnalysisResult.hairLength,
          overallTone: safeAnalysisResult.overallTone,
          overallReflect: safeAnalysisResult.overallReflect,
          hasZoneColorAnalysis: !!safeAnalysisResult.zoneColorAnalysis,
          hasZonePhysicalAnalysis: !!safeAnalysisResult.zonePhysicalAnalysis,
        });

        if (isComponentMounted) {
          onUpdate(safeAnalysisResult);
          setIsDataFromAI(true);

          let fieldsCount = 0;
          if (analysisResult.condition) fieldsCount++;
          if (analysisResult.texture) fieldsCount++;
          if (safeAnalysisResult.hairThickness) fieldsCount++;
          if (safeAnalysisResult.hairDensity) fieldsCount++;
          if (safeAnalysisResult.hairLength && safeAnalysisResult.hairLength > 0) fieldsCount++;
          if (safeAnalysisResult.overallTone) fieldsCount++;
          if (safeAnalysisResult.overallReflect) fieldsCount++;
          if (safeAnalysisResult.lastChemicalProcessType) fieldsCount++;
          if (safeAnalysisResult.lastChemicalProcessDate) fieldsCount++;
          const zoneAnalysisCount = analysisResult.zoneAnalysis
            ? Object.keys(analysisResult.zoneAnalysis).length * 2
            : 0;
          fieldsCount += zoneAnalysisCount;

          setAIFieldsCount(fieldsCount);
          setShowAINotification(true);
          hasShownNotificationRef.current = true;

          try {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          } catch (hapticError) {
            logger.debug('Haptic feedback not available', 'DiagnosisStep', hapticError);
          }

          if (scrollRef.current && isComponentMounted) {
            setTimeout(() => {
              if (scrollRef.current && isComponentMounted) {
                scrollRef.current.scrollTo({ y: 350, animated: true });
              }
            }, 500);
          }
        }
      }

      if (!analysisResult && isComponentMounted) {
        hasShownNotificationRef.current = false;
      }
    } catch (error) {
      logger.error('Critical error processing AI analysis results', 'DiagnosisStep', {
        error,
        analysisResultStructure: analysisResult
          ? {
              hasHairThickness: !!analysisResult.hairThickness,
              hasHairDensity: !!analysisResult.hairDensity,
              hasOverallTone: !!analysisResult.overallTone,
              hasOverallReflect: !!analysisResult.overallReflect,
              hasZoneAnalysis: !!analysisResult.zoneAnalysis,
              hasDetectedProcesses: !!analysisResult.detectedProcesses,
              hasRiskDetection: !!analysisResult.riskDetection,
              hasOverallCondition: !!analysisResult.overallCondition,
              zoneAnalysisKeys: analysisResult.zoneAnalysis
                ? Object.keys(analysisResult.zoneAnalysis)
                : [],
              overallConfidence: analysisResult.overallConfidence,
            }
          : null,
        componentState: {
          isAnalyzing,
          hasShownNotification: hasShownNotificationRef.current,
          clientId: data.clientId,
          diagnosisMethod: data.diagnosisMethod,
        },
      });

      Alert.alert(
        'Error procesando análisis',
        'Hubo un problema al procesar los resultados del análisis. El sistema continuará funcionando normalmente.',
        [{ text: 'Entendido' }]
      );

      hasShownNotificationRef.current = true;

      try {
        if (isComponentMounted) {
          onUpdate({ diagnosisMethod: 'manual' });
        }
      } catch (fallbackError) {
        logger.error('Failed to trigger fallback to manual mode', 'DiagnosisStep', fallbackError);
      }
    }

    return () => {
      isComponentMounted = false;
    };
  }, [analysisResult, isAnalyzing, onUpdate, data, scrollRef]);

  return {
    isDataFromAI,
    showAINotification,
    aiFieldsCount,
    hasShownNotificationRef,
    setShowAINotification,
  };
};
