/**
 * PHASE 3 TESTING EXAMPLES: How the extracted hooks make testing easier
 *
 * These examples demonstrate how separating business logic into custom hooks
 * enables comprehensive, isolated testing of each concern.
 */

import { renderHook, act } from '@testing-library/react-native';
import { useInstructionFlow } from '@/hooks/useInstructionFlow';
import { useStepValidation } from '@/hooks/useStepValidation';
import { useStepTimer } from '@/hooks/useStepTimer';
import { useProgressTracking } from '@/hooks/useProgressTracking';
import { useAnimations } from '@/hooks/useAnimations';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';
import * as Haptics from 'expo-haptics';

// Mock external dependencies
jest.mock('expo-haptics');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('react-native', () => ({
  Animated: {
    Value: jest.fn(() => ({ setValue: jest.fn(), addListener: jest.fn() })),
    timing: jest.fn(() => ({ start: jest.fn() })),
    parallel: jest.fn(() => ({ start: jest.fn() })),
    sequence: jest.fn(() => ({ start: jest.fn() })),
    spring: jest.fn(() => ({ start: jest.fn() })),
  },
  Dimensions: {
    get: () => ({ width: 375, height: 812 }),
  },
  Platform: { OS: 'ios' },
}));

// Test data
const mockFormulaData = {
  id: 'test-formula',
  products: [
    { id: 'p1', name: 'Product 1', brand: 'Brand A' },
    { id: 'p2', name: 'Product 2', brand: 'Brand A' },
  ],
  hairAnalysis: {
    overallHealth: 'good',
    porosity: 'medium',
  },
  mixingRatios: [
    { ingredient: 'color', amount: 50 },
    { ingredient: 'developer', amount: 50 },
  ],
};

const mockSteps = [
  {
    id: 'checklist',
    title: 'Checklist',
    icon: () => null,
    color: '#007AFF',
    requirements: ['gloves', 'tools'],
    estimatedDuration: 3,
  },
  {
    id: 'mixing',
    title: 'Mixing',
    icon: () => null,
    color: '#34C759',
    requirements: ['products_ready'],
    estimatedDuration: 5,
  },
];

describe('useInstructionFlow Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with correct default state', () => {
    const { result } = renderHook(() =>
      useInstructionFlow({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    expect(result.current.currentStep).toBe(0);
    expect(result.current.totalSteps).toBe(2);
    expect(result.current.progress).toBe(0);
    expect(result.current.isComplete).toBe(false);
    expect(result.current.canGoBack).toBe(false);
  });

  test('should advance step with proper validation', async () => {
    const onStepChange = jest.fn();

    const { result } = renderHook(() =>
      useInstructionFlow({
        steps: mockSteps,
        formulaData: mockFormulaData,
        onStepChange,
      })
    );

    // Should not advance without meeting requirements
    expect(result.current.canAdvance).toBe(false);

    // Add required items
    await act(async () => {
      await result.current.toggleChecklistItem('gloves');
      await result.current.toggleChecklistItem('tools');
    });

    expect(result.current.canAdvance).toBe(true);

    // Advance step
    await act(async () => {
      await result.current.advanceStep();
    });

    expect(result.current.currentStep).toBe(1);
    expect(result.current.completedSteps.has(0)).toBe(true);
    expect(onStepChange).toHaveBeenCalledWith(1);
    expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Light);
  });

  test('should handle checklist item toggling', async () => {
    const { result } = renderHook(() =>
      useInstructionFlow({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    expect(result.current.isItemChecked('gloves')).toBe(false);

    await act(async () => {
      await result.current.toggleChecklistItem('gloves');
    });

    expect(result.current.isItemChecked('gloves')).toBe(true);
    expect(result.current.checkedItems.has('gloves')).toBe(true);
  });

  test('should calculate progress correctly', () => {
    const { result } = renderHook(() =>
      useInstructionFlow({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    // Initially at 0%
    expect(result.current.progress).toBe(0);

    act(() => {
      result.current.markStepComplete(0);
    });

    // Should be at 50% after completing first step
    expect(result.current.stepProgress).toBe(0.5);
  });

  test('should handle flow completion', async () => {
    const onComplete = jest.fn();

    const { result } = renderHook(() =>
      useInstructionFlow({
        steps: mockSteps,
        formulaData: mockFormulaData,
        onComplete,
      })
    );

    // Move to last step
    act(() => {
      result.current.goToStep(1);
    });

    await act(async () => {
      await result.current.completeFlow();
    });

    expect(result.current.completedSteps.has(1)).toBe(true);
    expect(onComplete).toHaveBeenCalled();
    expect(Haptics.notificationAsync).toHaveBeenCalledWith(
      Haptics.NotificationFeedbackType.Success
    );
  });
});

describe('useStepValidation Hook', () => {
  test('should validate safety requirements', () => {
    const { result } = renderHook(() =>
      useStepValidation({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    const safetyValidation = result.current.validateSafetyRequirements();

    expect(safetyValidation.errors.length).toBeGreaterThan(0);
    expect(safetyValidation.errors.some(error => error.ruleId === 'gloves_required')).toBe(true);
  });

  test('should validate step completion requirements', () => {
    const { result } = renderHook(() =>
      useStepValidation({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    const validation = result.current.validateStep(0, {
      checkedItems: new Set(['gloves', 'tools']),
      completedSteps: new Set(),
      elapsedTime: 0,
    });

    expect(validation.canProceed).toBe(true);
    expect(validation.errors.length).toBeLessThan(3); // Should have fewer errors with items checked
  });

  test('should calculate professional score correctly', () => {
    const { result } = renderHook(() =>
      useStepValidation({
        steps: mockSteps,
        formulaData: mockFormulaData,
        enableProfessionalScoring: true,
      })
    );

    const score = result.current.calculateProfessionalScore({
      formulaData: mockFormulaData,
      checkedItems: new Set(['gloves', 'tools', 'eye_protection']),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 600, // 10 minutes
    });

    expect(score).toBeGreaterThan(70); // Should be decent score with safety items
    expect(score).toBeLessThanOrEqual(100);
  });

  test('should provide actionable recommendations', () => {
    const { result } = renderHook(() =>
      useStepValidation({
        steps: mockSteps,
        formulaData: mockFormulaData,
      })
    );

    const recommendations = result.current.getProfessionalRecommendations();

    expect(recommendations).toBeInstanceOf(Array);
    expect(recommendations.length).toBeGreaterThan(0);
    expect(recommendations.every(rec => typeof rec === 'string')).toBe(true);
  });
});

describe('useStepTimer Hook', () => {
  const mockTimerPhases = [
    {
      id: 'phase1',
      name: 'Phase 1',
      duration: 10, // 10 seconds for testing
      description: 'Test phase',
    },
    {
      id: 'phase2',
      name: 'Phase 2',
      duration: 5,
      description: 'Second test phase',
    },
  ];

  test('should initialize timer with correct state', () => {
    const { result } = renderHook(() =>
      useStepTimer({
        phases: mockTimerPhases,
      })
    );

    expect(result.current.currentPhase).toEqual(mockTimerPhases[0]);
    expect(result.current.totalDuration).toBe(15);
    expect(result.current.isRunning).toBe(false);
    expect(result.current.hasStarted).toBe(false);
  });

  test('should start and track timer progress', async () => {
    jest.useFakeTimers();

    const { result } = renderHook(() =>
      useStepTimer({
        phases: mockTimerPhases,
      })
    );

    await act(async () => {
      await result.current.start();
    });

    expect(result.current.isRunning).toBe(true);
    expect(result.current.hasStarted).toBe(true);

    // Advance time
    act(() => {
      jest.advanceTimersByTime(5000); // 5 seconds
    });

    expect(result.current.currentPhaseTime).toBe(5);
    expect(result.current.totalElapsedTime).toBe(5);

    jest.useRealTimers();
  });

  test('should handle phase transitions', async () => {
    jest.useFakeTimers();

    const onPhaseChange = jest.fn();
    const { result } = renderHook(() =>
      useStepTimer({
        phases: mockTimerPhases,
        autoAdvance: true,
        onPhaseChange,
      })
    );

    await act(async () => {
      await result.current.start();
    });

    // Complete first phase
    act(() => {
      jest.advanceTimersByTime(10000); // Complete first phase
    });

    expect(result.current.currentPhaseIndex).toBe(1);
    expect(result.current.currentPhase).toEqual(mockTimerPhases[1]);
    expect(onPhaseChange).toHaveBeenCalledWith(mockTimerPhases[1], 1);

    jest.useRealTimers();
  });

  test('should pause and resume correctly', async () => {
    jest.useFakeTimers();

    const { result } = renderHook(() =>
      useStepTimer({
        phases: mockTimerPhases,
        allowPause: true,
      })
    );

    // Start timer
    await act(async () => {
      await result.current.start();
    });

    // Run for 3 seconds
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(result.current.currentPhaseTime).toBe(3);

    // Pause
    await act(async () => {
      await result.current.pause();
    });

    expect(result.current.isPaused).toBe(true);
    expect(result.current.isRunning).toBe(false);

    // Time should not advance while paused
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    expect(result.current.currentPhaseTime).toBe(3); // Should still be 3

    // Resume
    await act(async () => {
      await result.current.resume();
    });

    expect(result.current.isRunning).toBe(true);
    expect(result.current.isPaused).toBe(false);

    jest.useRealTimers();
  });

  test('should format time correctly', () => {
    const { result } = renderHook(() =>
      useStepTimer({
        phases: mockTimerPhases,
      })
    );

    expect(result.current.formatTime(65)).toBe('1:05');
    expect(result.current.formatTime(3665)).toBe('1:01:05');
    expect(result.current.formatTime(30)).toBe('0:30');
  });
});

describe('useProgressTracking Hook', () => {
  test('should track step completion progress', () => {
    const { result } = renderHook(() =>
      useProgressTracking({
        steps: mockSteps,
      })
    );

    expect(result.current.metrics.stepsCompleted).toBe(0);
    expect(result.current.metrics.stepProgress).toBe(0);

    act(() => {
      result.current.updateStepProgress(0, true);
    });

    expect(result.current.metrics.stepsCompleted).toBe(1);
    expect(result.current.metrics.stepProgress).toBe(50);
  });

  test('should calculate quality metrics', () => {
    const { result } = renderHook(() =>
      useProgressTracking({
        steps: mockSteps,
      })
    );

    act(() => {
      result.current.updateQualityScore(0, 85);
      result.current.updateQualityScore(1, 95);
    });

    expect(result.current.metrics.averageQualityScore).toBe(90);
    expect(result.current.metrics.professionalCompliance).toBe(100); // Both above 80
  });

  test('should track milestones', () => {
    const onMilestoneAchieved = jest.fn();

    const { result } = renderHook(() =>
      useProgressTracking({
        steps: mockSteps,
        onMilestoneAchieved,
      })
    );

    // Complete first step to trigger milestone
    act(() => {
      result.current.updateStepProgress(0, true);
    });

    const newMilestones = result.current.checkMilestones();
    expect(newMilestones.length).toBeGreaterThan(0);
    expect(onMilestoneAchieved).toHaveBeenCalled();
  });

  test('should calculate time estimates', () => {
    const { result } = renderHook(() =>
      useProgressTracking({
        steps: mockSteps,
      })
    );

    // Record some step timings
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const now = Date.now();

    act(() => {
      result.current.recordStepStart(0);
    });

    // Simulate time passing and completion
    act(() => {
      result.current.recordStepCompletion(0);
    });

    expect(result.current.metrics.estimatedCompletionTime).toBeInstanceOf(Date);
    expect(result.current.metrics.averageTimePerStep).toBeGreaterThan(0);
  });

  test('should generate progress breakdown', () => {
    const { result } = renderHook(() =>
      useProgressTracking({
        steps: mockSteps,
      })
    );

    const breakdown = result.current.getProgressBreakdown();

    expect(breakdown).toHaveProperty('steps');
    expect(breakdown).toHaveProperty('checklist');
    expect(breakdown).toHaveProperty('quality');
    expect(breakdown).toHaveProperty('time');

    Object.values(breakdown).forEach(value => {
      expect(value).toBeGreaterThanOrEqual(0);
      expect(value).toBeLessThanOrEqual(100);
    });
  });
});

describe('useAnimations Hook', () => {
  test('should initialize animation values', () => {
    const { result } = renderHook(() => useAnimations(5));

    expect(result.current.fadeAnim).toBeDefined();
    expect(result.current.slideAnim).toBeDefined();
    expect(result.current.scaleAnim).toBeDefined();
    expect(result.current.progressAnims).toHaveLength(5);
  });

  test('should provide animated styles', () => {
    const { result } = renderHook(() => useAnimations());

    const fadeStyle = result.current.getAnimatedStyle('fade');
    const slideStyle = result.current.getAnimatedStyle('slide');
    const scaleStyle = result.current.getAnimatedStyle('scale');

    expect(fadeStyle).toHaveProperty('opacity');
    expect(slideStyle).toHaveProperty('transform');
    expect(scaleStyle).toHaveProperty('transform');
  });

  test('should reset animations', () => {
    const { result } = renderHook(() => useAnimations());

    act(() => {
      result.current.resetAllAnimations();
    });

    // Verify setValue was called on animation values
    // Note: In a real test, you'd mock Animated.Value to verify calls
    expect(true).toBe(true); // Placeholder assertion
  });
});

describe('useHapticFeedback Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should provide haptic feedback functions', () => {
    const { result } = renderHook(() => useHapticFeedback());

    expect(typeof result.current.stepAdvance).toBe('function');
    expect(typeof result.current.validationError).toBe('function');
    expect(typeof result.current.milestone).toBe('function');
  });

  test('should execute step advance haptics', async () => {
    const { result } = renderHook(() => useHapticFeedback());

    await act(async () => {
      await result.current.stepAdvance();
    });

    expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Light);
    expect(Haptics.selectionAsync).toHaveBeenCalled();
  });

  test('should provide quality-based feedback', async () => {
    const { result } = renderHook(() => useHapticFeedback());

    // High quality score
    await act(async () => {
      await result.current.qualityFeedback(95);
    });

    expect(Haptics.notificationAsync).toHaveBeenCalledWith(
      Haptics.NotificationFeedbackType.Success
    );

    jest.clearAllMocks();

    // Low quality score
    await act(async () => {
      await result.current.qualityFeedback(40);
    });

    expect(Haptics.notificationAsync).toHaveBeenCalledWith(Haptics.NotificationFeedbackType.Error);
  });

  test('should generate progress sequences', async () => {
    const { result } = renderHook(() => useHapticFeedback());

    await act(async () => {
      await result.current.createProgressSequence(10, 5); // Halfway through 10 steps
    });

    // Should include medium intensity feedback for 50% progress
    expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Medium);
  });

  test('should handle haptics preferences', () => {
    const { result } = renderHook(() => useHapticFeedback());

    expect(result.current.isHapticsEnabled()).toBe(true);

    act(() => {
      result.current.setHapticsEnabled(false);
    });

    expect(result.current.isHapticsEnabled()).toBe(false);
  });

  test('should provide pattern descriptions', () => {
    const { result } = renderHook(() => useHapticFeedback());

    const description = result.current.getPatternDescription('stepAdvance');
    expect(typeof description).toBe('string');
    expect(description.length).toBeGreaterThan(0);
  });
});

/**
 * TESTING BENEFITS WITH PHASE 3 HOOKS:
 *
 * ✅ Isolated Testing:
 *    - Each hook can be tested independently
 *    - Business logic is separated from UI concerns
 *    - Mocking is simpler and more focused
 *
 * ✅ Comprehensive Coverage:
 *    - All business rules can be unit tested
 *    - Edge cases are easier to test in isolation
 *    - State transitions are predictable and testable
 *
 * ✅ Fast Test Execution:
 *    - No UI rendering required for business logic tests
 *    - Tests run faster without component mounting
 *    - Parallel test execution is more effective
 *
 * ✅ Clear Test Structure:
 *    - Each test focuses on a single responsibility
 *    - Test data is minimal and relevant
 *    - Assertions are specific and meaningful
 *
 * ✅ Maintainable Tests:
 *    - Changes to business logic only affect hook tests
 *    - UI tests remain stable when business rules change
 *    - Test refactoring is easier with clear boundaries
 */
