#!/usr/bin/env node
// Ensure a Supabase test user exists using the Service Role key

import process from 'node:process';
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL_RAW = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY;
const TEST_EMAIL = process.env.SUPABASE_TEST_EMAIL;
const TEST_PASSWORD = process.env.SUPABASE_TEST_PASSWORD;

if (!SUPABASE_URL_RAW || !SERVICE_ROLE || !TEST_EMAIL || !TEST_PASSWORD) {
  console.error('Missing env vars for ensure-test-user: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, SUPABASE_TEST_EMAIL, SUPABASE_TEST_PASSWORD');
  process.exit(2);
}

// Normalize URL: accept inputs like https://proj.supabase.co, and strip accidental /auth/v1 or /rest/v1 suffixes
const SUPABASE_URL = SUPABASE_URL_RAW.replace(/\/(auth|rest)\/v1.*$/i, '');

async function ensureUser() {
  try {
    const admin = createClient(SUPABASE_URL, SERVICE_ROLE, {
      auth: { persistSession: false, autoRefreshToken: false },
      global: { headers: { 'x-client-info': 'ensure-test-user' } },
    });

    // Try to create user directly; if already exists, Supabase returns 422/409
    const { data, error } = await admin.auth.admin.createUser({
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
      email_confirm: true,
    });
    if (error) {
      const msg = (error?.message || '').toLowerCase();
      const status = error?.status || 0;
      if (status !== 409 && status !== 422 && !msg.includes('already') && !msg.includes('exists')) {
        console.error('Failed creating test user:', status, error?.message);
        // Helpful diagnostics
        console.error('[ensure-test-user] Debug:', {
          supabaseUrl: SUPABASE_URL,
          serviceRolePresent: !!SERVICE_ROLE,
          serviceRoleLen: (SERVICE_ROLE || '').length,
          testEmail: TEST_EMAIL,
        });
        process.exit(2);
      }
      console.log('Test user appears to exist. Will ensure password and confirmation.');
    }

    // Ensure the user exists and has the expected password and confirmed status
    let userId = data?.user?.id || null;
    if (!userId) {
      const { data: list, error: listErr } = await admin.auth.admin.listUsers({ page: 1, perPage: 1000 });
      if (listErr) {
        console.error('Failed listing users to locate test user:', listErr?.message || listErr);
        process.exit(2);
      }
      const found = list?.users?.find(u => (u?.email || '').toLowerCase() === TEST_EMAIL.toLowerCase());
      userId = found?.id || null;
    }
    if (!userId) {
      console.log('Test user created but id not returned; continuing.');
      return;
    }

    // Force set password and confirm email to guarantee sign-in succeeds in QA
    const { error: updErr } = await admin.auth.admin.updateUserById(userId, {
      password: TEST_PASSWORD,
      email_confirm: true,
    });
    if (updErr) {
      console.error('Failed updating test user password/confirmation:', updErr?.message || updErr);
      process.exit(2);
    }
    console.log('Test user ensured (password set and email confirmed).');
  } catch (e) {
    console.error('Unexpected error ensuring test user:', e?.message || String(e));
    console.error('[ensure-test-user] Tip: verify SUPABASE_SERVICE_ROLE_KEY (Service Role) and SUPABASE_URL (base https://PROJECT.supabase.co).');
    process.exit(2);
  }
}

ensureUser().catch(e => { console.error(e); process.exit(2); });
