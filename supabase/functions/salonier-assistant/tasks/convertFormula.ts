// supabase/functions/salonier-assistant/tasks/convertFormula.ts
import { AIResponse } from '../types.ts';
import { logger } from '../src/utils/security.ts';
import { retryWithBackoff, calculateCost } from '../src/utils/helpers.ts';
import { generateCacheKey, saveToCache } from '../src/utils/caching.ts';

export async function convertFormula(payload: any, salonId: string, supabase: any, openaiApiKey: string): Promise<AIResponse> {
  const { originalBrand, originalLine, originalFormula, targetBrand, targetLine, regionalConfig } =
    payload;

  // Detectar idioma y configuración regional
  const isEnglish = regionalConfig?.language === 'en';
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';

  const prompt = isEnglish
    ? `Convert this hair color formula:
  
  Original brand: ${originalBrand} - ${originalLine}
  Formula: ${originalFormula}
  
  To target brand: ${targetBrand} - ${targetLine}
  
  Provide:
  1. Converted formula with equivalent products
  2. Necessary adjustments in ratios or timing
  3. Warnings about differences between brands
  4. Confidence level in the conversion
  
  Use ${volumeUnit} for volumes, ${weightUnit} for weights, "${developerTerm}" for developer, and "${colorTerm}" for color.`
    : `Convierte esta fórmula de coloración:
  
  Marca original: ${originalBrand} - ${originalLine}
  Fórmula: ${originalFormula}
  
  A la marca objetivo: ${targetBrand} - ${targetLine}
  
  Proporciona:
  1. Fórmula convertida con productos equivalentes
  2. Ajustes necesarios en proporciones o tiempos
  3. Advertencias sobre diferencias entre marcas
  4. Nivel de confianza en la conversión
  
  Usa ${volumeUnit} para volúmenes, ${weightUnit} para pesos, "${developerTerm}" para oxidante, y "${colorTerm}" para coloración.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'system',
              content: isEnglish
                ? 'You are an expert colorist specialized in converting formulas between different hair color brands. Always respond in English.'
                : 'Eres un experto colorista especializado en convertir fórmulas entre diferentes marcas de coloración. Siempre responde en español.',
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 1000,
          temperature: 0,
          top_p: 1,
          seed: 42,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Formula conversion OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('convert_formula', payload);
    await saveToCache(
      supabase,
      salonId,
      'convert_formula',
      inputHash,
      payload,
      result,
      'gpt-4o-mini',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Formula conversion failed
    return { success: false, error: error.message };
  }
}