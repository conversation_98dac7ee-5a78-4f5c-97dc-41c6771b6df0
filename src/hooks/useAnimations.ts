import { useRef, useCallback } from 'react';
import { Animated, Dimensions, ViewStyle, TextStyle, ImageStyle } from 'react-native';

// Animation configuration types
interface AnimationConfig {
  duration?: number;
  useNativeDriver?: boolean;
  easing?: (value: number) => number;
}

interface SpringConfig {
  tension?: number;
  friction?: number;
  useNativeDriver?: boolean;
}

interface SequenceItem {
  toValue: number;
  duration: number;
  delay?: number;
}

// Animation presets
const ANIMATION_PRESETS = {
  // Step transition animations
  stepTransition: {
    fade: { duration: 200, useNativeDriver: true },
    slide: { duration: 300, useNativeDriver: true },
  },

  // Progress animations
  progress: {
    fill: { duration: 800, useNativeDriver: false },
    pulse: { duration: 1000, useNativeDriver: true },
  },

  // Card animations
  card: {
    entrance: { duration: 300, useNativeDriver: true },
    exit: { duration: 200, useNativeDriver: true },
    flip: { duration: 400, useNativeDriver: true },
  },

  // Button animations
  button: {
    press: { duration: 100, useNativeDriver: true },
    release: { duration: 150, useNativeDriver: true },
  },

  // Modal animations
  modal: {
    slideUp: { duration: 400, useNativeDriver: true },
    slideDown: { duration: 300, useNativeDriver: true },
    fade: { duration: 250, useNativeDriver: true },
  },
};

// Screen dimensions for slide calculations
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Hook return interface
export interface UseAnimationsReturn {
  // Animation values
  fadeAnim: Animated.Value;
  slideAnim: Animated.Value;
  scaleAnim: Animated.Value;
  rotateAnim: Animated.Value;
  progressAnims: Animated.Value[];

  // Step transition animations
  animateStepTransition: (direction: 'forward' | 'backward') => Promise<void>;
  animateStepEntrance: () => Promise<void>;
  animateStepExit: () => Promise<void>;

  // Progress animations
  animateProgressUpdate: (progressValues: number[]) => Promise<void>;
  animateProgressPulse: (index: number) => Promise<void>;
  animateProgressComplete: () => Promise<void>;

  // Card animations
  animateCardEntrance: (staggerDelay?: number) => Promise<void>;
  animateCardExit: (staggerDelay?: number) => Promise<void>;
  animateCardFlip: () => Promise<void>;
  animateCardHover: (pressed: boolean) => Promise<void>;

  // Button animations
  animateButtonPress: () => Promise<void>;
  animateButtonRelease: () => Promise<void>;
  animateButtonSuccess: () => Promise<void>;
  animateButtonError: () => Promise<void>;

  // List animations
  animateListItemEntrance: (index: number, totalItems: number) => Promise<void>;
  animateListItemExit: (index: number) => Promise<void>;
  animateListReorder: () => Promise<void>;

  // Modal/Sheet animations
  animateModalOpen: (type: 'slide' | 'fade' | 'scale') => Promise<void>;
  animateModalClose: (type: 'slide' | 'fade' | 'scale') => Promise<void>;

  // Feedback animations
  animateSuccess: () => Promise<void>;
  animateError: () => Promise<void>;
  animateWarning: () => Promise<void>;
  animateLoading: (start: boolean) => Promise<void>;

  // Custom animation builders
  createFadeAnimation: (toValue: number, config?: AnimationConfig) => Animated.CompositeAnimation;
  createSlideAnimation: (toValue: number, config?: AnimationConfig) => Animated.CompositeAnimation;
  createScaleAnimation: (toValue: number, config?: SpringConfig) => Animated.CompositeAnimation;
  createRotateAnimation: (toValue: number, config?: AnimationConfig) => Animated.CompositeAnimation;
  createSequenceAnimation: (items: SequenceItem[]) => Animated.CompositeAnimation;

  // Utility functions
  resetAllAnimations: () => void;
  pauseAllAnimations: () => void;
  resumeAllAnimations: () => void;
  getAnimatedStyle: (
    type: 'fade' | 'slide' | 'scale' | 'rotate'
  ) => ViewStyle | TextStyle | ImageStyle;
}

export function useAnimations(progressDotsCount = 10): UseAnimationsReturn {
  // Core animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Progress dots animations
  const progressAnims = useRef(
    Array(progressDotsCount)
      .fill(0)
      .map(() => new Animated.Value(1))
  ).current;

  // Step transition animations
  const animateStepTransition = useCallback(
    async (direction: 'forward' | 'backward'): Promise<void> => {
      const slideDistance = direction === 'forward' ? -screenWidth * 0.1 : screenWidth * 0.1;

      return new Promise<void>(resolve => {
        // Exit animation
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: ANIMATION_PRESETS.stepTransition.fade.duration,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: slideDistance,
            duration: ANIMATION_PRESETS.stepTransition.slide.duration,
            useNativeDriver: true,
          }),
        ]).start(() => {
          // Reset position for entrance
          slideAnim.setValue(-slideDistance);

          // Entrance animation
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: ANIMATION_PRESETS.stepTransition.fade.duration * 1.2,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: ANIMATION_PRESETS.stepTransition.slide.duration * 1.2,
              useNativeDriver: true,
            }),
          ]).start(() => resolve());
        });
      });
    },
    [fadeAnim, slideAnim]
  );

  const animateStepEntrance = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);

      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: ANIMATION_PRESETS.card.entrance.duration,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: ANIMATION_PRESETS.card.entrance.duration,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [fadeAnim, slideAnim]);

  const animateStepExit = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: ANIMATION_PRESETS.card.exit.duration,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -30,
          duration: ANIMATION_PRESETS.card.exit.duration,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [fadeAnim, slideAnim]);

  // Progress animations
  const animateProgressUpdate = useCallback(
    async (progressValues: number[]): Promise<void> => {
      return new Promise<void>(resolve => {
        const animations = progressAnims.map((anim, index) => {
          const targetValue = progressValues[index] || 1;
          return Animated.timing(anim, {
            toValue: targetValue,
            duration: ANIMATION_PRESETS.progress.fill.duration,
            useNativeDriver: true,
          });
        });

        Animated.stagger(50, animations).start(() => resolve());
      });
    },
    [progressAnims]
  );

  const animateProgressPulse = useCallback(
    async (index: number): Promise<void> => {
      if (index < 0 || index >= progressAnims.length) return;

      return new Promise<void>(resolve => {
        const anim = progressAnims[index];

        Animated.sequence([
          Animated.timing(anim, {
            toValue: 1.3,
            duration: ANIMATION_PRESETS.progress.pulse.duration / 4,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 1.2,
            duration: ANIMATION_PRESETS.progress.pulse.duration / 4,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 1.1,
            duration: ANIMATION_PRESETS.progress.pulse.duration / 2,
            useNativeDriver: true,
          }),
        ]).start(() => resolve());
      });
    },
    [progressAnims]
  );

  const animateProgressComplete = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      const animations = progressAnims.map(anim =>
        Animated.sequence([
          Animated.timing(anim, {
            toValue: 1.4,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ])
      );

      Animated.stagger(100, animations).start(() => resolve());
    });
  }, [progressAnims]);

  // Card animations
  const animateCardEntrance = useCallback(
    async (staggerDelay = 100): Promise<void> => {
      return new Promise<void>(resolve => {
        fadeAnim.setValue(0);
        slideAnim.setValue(30);
        scaleAnim.setValue(0.9);

        setTimeout(() => {
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: ANIMATION_PRESETS.card.entrance.duration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: ANIMATION_PRESETS.card.entrance.duration,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start(() => resolve());
        }, staggerDelay);
      });
    },
    [fadeAnim, slideAnim, scaleAnim]
  );

  const animateCardExit = useCallback(
    async (staggerDelay = 0): Promise<void> => {
      return new Promise<void>(resolve => {
        setTimeout(() => {
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: ANIMATION_PRESETS.card.exit.duration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: -20,
              duration: ANIMATION_PRESETS.card.exit.duration,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 0.8,
              duration: ANIMATION_PRESETS.card.exit.duration,
              useNativeDriver: true,
            }),
          ]).start(() => resolve());
        }, staggerDelay);
      });
    },
    [fadeAnim, slideAnim, scaleAnim]
  );

  const animateCardFlip = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.timing(rotateAnim, {
          toValue: 0.5,
          duration: ANIMATION_PRESETS.card.flip.duration / 2,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: ANIMATION_PRESETS.card.flip.duration / 2,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [rotateAnim]);

  const animateCardHover = useCallback(
    async (pressed: boolean): Promise<void> => {
      return new Promise<void>(resolve => {
        Animated.spring(scaleAnim, {
          toValue: pressed ? 0.97 : 1,
          tension: 300,
          friction: 20,
          useNativeDriver: true,
        }).start(() => resolve());
      });
    },
    [scaleAnim]
  );

  // Button animations
  const animateButtonPress = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        tension: 300,
        friction: 20,
        useNativeDriver: true,
      }).start(() => resolve());
    });
  }, [scaleAnim]);

  const animateButtonRelease = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 20,
        useNativeDriver: true,
      }).start(() => resolve());
    });
  }, [scaleAnim]);

  const animateButtonSuccess = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.spring(scaleAnim, {
          toValue: 1.05,
          tension: 200,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [scaleAnim]);

  const animateButtonError = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: -5, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 5, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start(() => resolve());
    });
  }, [slideAnim]);

  // List animations
  const animateListItemEntrance = useCallback(
    async (index: number, totalItems: number): Promise<void> => {
      const delay = (index / totalItems) * 300; // Stagger based on position

      return new Promise<void>(resolve => {
        fadeAnim.setValue(0);
        slideAnim.setValue(50);

        setTimeout(() => {
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.spring(slideAnim, {
              toValue: 0,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start(() => resolve());
        }, delay);
      });
    },
    [fadeAnim, slideAnim]
  );

  const animateListItemExit = useCallback(
    async (index: number): Promise<void> => {
      return new Promise<void>(resolve => {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: index % 2 === 0 ? -screenWidth : screenWidth,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => resolve());
      });
    },
    [fadeAnim, slideAnim]
  );

  const animateListReorder = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.02,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [scaleAnim]);

  // Modal/Sheet animations
  const animateModalOpen = useCallback(
    async (type: 'slide' | 'fade' | 'scale'): Promise<void> => {
      return new Promise<void>(resolve => {
        switch (type) {
          case 'slide':
            slideAnim.setValue(screenHeight);
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: ANIMATION_PRESETS.modal.slideUp.duration,
              useNativeDriver: true,
            }).start(() => resolve());
            break;

          case 'fade':
            fadeAnim.setValue(0);
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: ANIMATION_PRESETS.modal.fade.duration,
              useNativeDriver: true,
            }).start(() => resolve());
            break;

          case 'scale':
            scaleAnim.setValue(0.8);
            fadeAnim.setValue(0);
            Animated.parallel([
              Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
              }),
              Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
              }),
            ]).start(() => resolve());
            break;
        }
      });
    },
    [slideAnim, fadeAnim, scaleAnim]
  );

  const animateModalClose = useCallback(
    async (type: 'slide' | 'fade' | 'scale'): Promise<void> => {
      return new Promise<void>(resolve => {
        switch (type) {
          case 'slide':
            Animated.timing(slideAnim, {
              toValue: screenHeight,
              duration: ANIMATION_PRESETS.modal.slideDown.duration,
              useNativeDriver: true,
            }).start(() => resolve());
            break;

          case 'fade':
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: ANIMATION_PRESETS.modal.fade.duration,
              useNativeDriver: true,
            }).start(() => resolve());
            break;

          case 'scale':
            Animated.parallel([
              Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
              }),
              Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
              }),
            ]).start(() => resolve());
            break;
        }
      });
    },
    [slideAnim, fadeAnim, scaleAnim]
  );

  // Feedback animations
  const animateSuccess = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          tension: 200,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.spring(rotateAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start(() => resolve());
    });
  }, [scaleAnim, rotateAnim]);

  const animateError = useCallback(async (): Promise<void> => {
    return animateButtonError();
  }, [animateButtonError]);

  const animateWarning = useCallback(async (): Promise<void> => {
    return new Promise<void>(resolve => {
      Animated.sequence([
        Animated.timing(scaleAnim, { toValue: 1.05, duration: 100, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 1.02, duration: 100, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
      ]).start(() => resolve());
    });
  }, [scaleAnim]);

  const animateLoading = useCallback(
    async (start: boolean): Promise<void> => {
      return new Promise<void>(resolve => {
        if (start) {
          rotateAnim.setValue(0);
          const rotation = Animated.loop(
            Animated.timing(rotateAnim, {
              toValue: 1,
              duration: 2000,
              useNativeDriver: true,
            })
          );
          rotation.start();
          resolve();
        } else {
          rotateAnim.stopAnimation();
          Animated.timing(rotateAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start(() => resolve());
        }
      });
    },
    [rotateAnim]
  );

  // Custom animation builders
  const createFadeAnimation = useCallback(
    (toValue: number, config?: AnimationConfig): Animated.CompositeAnimation => {
      return Animated.timing(fadeAnim, {
        toValue,
        duration: 300,
        useNativeDriver: true,
        ...config,
      });
    },
    [fadeAnim]
  );

  const createSlideAnimation = useCallback(
    (toValue: number, config?: AnimationConfig): Animated.CompositeAnimation => {
      return Animated.timing(slideAnim, {
        toValue,
        duration: 400,
        useNativeDriver: true,
        ...config,
      });
    },
    [slideAnim]
  );

  const createScaleAnimation = useCallback(
    (toValue: number, config?: SpringConfig): Animated.CompositeAnimation => {
      return Animated.spring(scaleAnim, {
        toValue,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
        ...config,
      });
    },
    [scaleAnim]
  );

  const createRotateAnimation = useCallback(
    (toValue: number, config?: AnimationConfig): Animated.CompositeAnimation => {
      return Animated.timing(rotateAnim, {
        toValue,
        duration: 500,
        useNativeDriver: true,
        ...config,
      });
    },
    [rotateAnim]
  );

  const createSequenceAnimation = useCallback(
    (items: SequenceItem[]): Animated.CompositeAnimation => {
      const animations = items.map(item =>
        Animated.timing(slideAnim, {
          toValue: item.toValue,
          duration: item.duration,
          useNativeDriver: true,
        })
      );

      return Animated.sequence(animations);
    },
    [slideAnim]
  );

  // Utility functions
  const resetAllAnimations = useCallback((): void => {
    fadeAnim.setValue(1);
    slideAnim.setValue(0);
    scaleAnim.setValue(1);
    rotateAnim.setValue(0);
    progressAnims.forEach(anim => anim.setValue(1));
  }, [fadeAnim, slideAnim, scaleAnim, rotateAnim, progressAnims]);

  const pauseAllAnimations = useCallback((): void => {
    fadeAnim.stopAnimation();
    slideAnim.stopAnimation();
    scaleAnim.stopAnimation();
    rotateAnim.stopAnimation();
    progressAnims.forEach(anim => anim.stopAnimation());
  }, [fadeAnim, slideAnim, scaleAnim, rotateAnim, progressAnims]);

  const resumeAllAnimations = useCallback((): void => {
    // This would require storing the previous animation state
    // For now, just reset to default values
    resetAllAnimations();
  }, [resetAllAnimations]);

  const getAnimatedStyle = useCallback(
    (type: 'fade' | 'slide' | 'scale' | 'rotate') => {
      switch (type) {
        case 'fade':
          return { opacity: fadeAnim };
        case 'slide':
          return { transform: [{ translateX: slideAnim }] };
        case 'scale':
          return { transform: [{ scale: scaleAnim }] };
        case 'rotate':
          return {
            transform: [
              {
                rotate: rotateAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }),
              },
            ],
          };
        default:
          return {};
      }
    },
    [fadeAnim, slideAnim, scaleAnim, rotateAnim]
  );

  return {
    // Animation values
    fadeAnim,
    slideAnim,
    scaleAnim,
    rotateAnim,
    progressAnims,

    // Step transition animations
    animateStepTransition,
    animateStepEntrance,
    animateStepExit,

    // Progress animations
    animateProgressUpdate,
    animateProgressPulse,
    animateProgressComplete,

    // Card animations
    animateCardEntrance,
    animateCardExit,
    animateCardFlip,
    animateCardHover,

    // Button animations
    animateButtonPress,
    animateButtonRelease,
    animateButtonSuccess,
    animateButtonError,

    // List animations
    animateListItemEntrance,
    animateListItemExit,
    animateListReorder,

    // Modal/Sheet animations
    animateModalOpen,
    animateModalClose,

    // Feedback animations
    animateSuccess,
    animateError,
    animateWarning,
    animateLoading,

    // Custom animation builders
    createFadeAnimation,
    createSlideAnimation,
    createScaleAnimation,
    createRotateAnimation,
    createSequenceAnimation,

    // Utility functions
    resetAllAnimations,
    pauseAllAnimations,
    resumeAllAnimations,
    getAnimatedStyle,
  };
}
