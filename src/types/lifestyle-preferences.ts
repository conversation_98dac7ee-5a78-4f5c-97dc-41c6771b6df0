// Types for lifestyle preferences and client expectations

export enum MaintenanceLevel {
  LOW = 'low', // 3-4 months between visits
  MEDIUM = 'medium', // 6-8 weeks between visits
  HIGH = 'high', // 3-4 weeks between visits
}

export enum BudgetLevel {
  ECO = 'eco',
  STANDARD = 'standard',
  PREMIUM = 'premium',
}

// Common tones that clients want to avoid
export enum AvoidableTone {
  ORANGE = 'orange',
  YELLOW = 'yellow',
  RED = 'red',
  GREEN = 'green',
  ASHY_EXTREME = 'ashy_extreme',
  BRASSY = 'brassy',
  COPPER = 'copper',
}

export interface LifestylePreferences {
  maintenanceLevel: MaintenanceLevel;
  avoidTones: AvoidableTone[];
  budgetLevel?: BudgetLevel;
  visitFrequency?: number; // in weeks
  additionalNotes?: string;
}

export interface PhotoAnalysisResult {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number; // 0-100
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
}

// Helper functions
export const getMaintenanceLevelLabel = (level: MaintenanceLevel): string => {
  switch (level) {
    case MaintenanceLevel.LOW:
      return 'Bajo (3-4 meses)';
    case MaintenanceLevel.MEDIUM:
      return 'Medio (6-8 semanas)';
    case MaintenanceLevel.HIGH:
      return 'Alto (3-4 semanas)';
  }
};

export const getBudgetLevelLabel = (level: BudgetLevel): string => {
  switch (level) {
    case BudgetLevel.ECO:
      return 'Esencial';
    case BudgetLevel.STANDARD:
      return 'Estándar';
    case BudgetLevel.PREMIUM:
      return 'Premium';
  }
};

export const getAvoidableToneLabel = (tone: AvoidableTone): string => {
  switch (tone) {
    case AvoidableTone.ORANGE:
      return 'Naranja';
    case AvoidableTone.YELLOW:
      return 'Amarillo';
    case AvoidableTone.RED:
      return 'Rojizo';
    case AvoidableTone.GREEN:
      return 'Verdoso';
    case AvoidableTone.ASHY_EXTREME:
      return 'Cenizo extremo';
    case AvoidableTone.BRASSY:
      return 'Cobrizo';
    case AvoidableTone.COPPER:
      return 'Cobre';
  }
};

export const getMaintenanceLevelOptions = () => Object.values(MaintenanceLevel);
export const getBudgetLevelOptions = () => Object.values(BudgetLevel);
export const getAvoidableToneOptions = () => Object.values(AvoidableTone);
