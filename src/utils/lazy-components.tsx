import * as React from 'react';
const { lazy } = React;

// Performance optimization: Lazy load heavy components
export const LazyComponents = {
  // Chat components (heavy due to complex state management)
  ChatGPTInterface: lazy(() => import('@/components/chat/ChatGPTInterface')),
  SmartSuggestions: lazy(() => import('@/components/chat/SmartSuggestions')),
  ConversationsList: lazy(() => import('@/components/chat/ConversationsList')),

  // Formulation components (heavy due to complex calculations)
  EnhancedFormulationView: lazy(() => import('@/components/formulation/EnhancedFormulationView')),
  FormulaVisualization: lazy(() => import('@/components/formulation/FormulaVisualization')),
  ColorTransitionVisual: lazy(() => import('@/components/formulation/ColorTransitionVisual')),
  ProportionCalculator: lazy(() => import('@/components/formulation/ProportionCalculator')),

  // Inventory components (heavy due to large datasets)
  InventoryReports: lazy(() => import('@/components/reports/InventoryReports')),
  ProductMappingModal: lazy(() => import('@/components/inventory/ProductMappingModal')),

  // Settings modals (infrequently used)
  BrandsModal: lazy(() => import('@/components/settings/BrandsModal')),
  BusinessModal: lazy(() => import('@/components/settings/BusinessModal')),
  PricingSettingsModal: lazy(() => import('@/components/settings/PricingSettingsModal')),
  SecurityModal: lazy(() => import('@/components/settings/SecurityModal')),

  // Team management (complex forms)
  AddEmployeeModal: lazy(() => import('@/components/team/AddEmployeeModal')),
  EditEmployeeModal: lazy(() => import('@/components/team/EditEmployeeModal')),

  // Camera and media (heavy native modules)
  CameraCapture: lazy(() => import('@/components/camera/CameraCapture')),
  PhotoGallery: lazy(() => import('@/components/PhotoGallery')),

  // AI and analysis (complex processing)
  AIMetricsDashboard: lazy(() => import('@/components/dashboard/AIMetricsDashboard')),
  ExplainableAI: lazy(() => import('@/components/ai/ExplainableAI')),
};

// Preload components based on user interaction patterns
export const preloadCriticalComponents = () => {
  // Preload components likely to be needed soon
  setTimeout(() => {
    // Preload chat after 2 seconds
    LazyComponents.ChatGPTInterface;
    LazyComponents.SmartSuggestions;
  }, 2000);

  setTimeout(() => {
    // Preload formulation components after 5 seconds
    LazyComponents.EnhancedFormulationView;
    LazyComponents.FormulaVisualization;
  }, 5000);
};

// Error boundary fallback for lazy components
export const LazyLoadingFallback = () => null; // Minimal fallback

// Higher-order component for lazy loading with error boundary
export function withLazyLoading<T extends Record<string, unknown>>(
  Component: React.LazyExoticComponent<React.ComponentType<T>>,
  fallback: React.ComponentType = LazyLoadingFallback
) {
  return function LazyWrapper(props: T) {
    const FallbackComponent = fallback;
    return (
      <React.Suspense fallback={<FallbackComponent />}>
        <Component {...props} />
      </React.Suspense>
    );
  };
}
