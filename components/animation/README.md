# 🎭 Salonier Micro-Interactions

Professional micro-interactions and delightful animations for the hair colorist app.

## ✨ Philosophy

> "La animación debe sentirse como mantequilla derritiéndose, no como melaza"

- **Purposeful**: Every animation serves a functional purpose
- **Performant**: Always 60 FPS, no exceptions
- **Playful**: Professional but with personality
- **Predictable**: Consistent and learnable
- **Polished**: Details make the difference

## 🛠 Components

### DelightfulButton

Enhanced button with professional micro-interactions:

```tsx
import { DelightfulButton } from '@/components/animation';

<DelightfulButton
  title="Generar Fórmula"
  variant="primary"
  size="large"
  onPress={() => generateFormula()}
  icon={<TestTube size={20} color="white" />}
  loading={isGenerating}
/>;
```

### AnimatedCounter

Smooth counter animations for metrics:

```tsx
import { AnimatedCounter } from '@/components/animation';

<AnimatedCounter
  value={confidence}
  suffix="%"
  duration={1200}
  springEffect={true}
  onComplete={() => triggerCelebration()}
/>;
```

## 🎨 Hook: useDelightfulInteractions

The main hook for consistent micro-interactions:

```tsx
import { useDelightfulInteractions } from '@/hooks/useDelightfulInteractions';

const MyComponent = () => {
  const { animatedStyle, triggerInteraction, animateConfidence } = useDelightfulInteractions();

  const handleSuccess = () => {
    triggerInteraction('success', {
      onComplete: () => console.log('Animation complete!'),
    });
  };

  const updateConfidence = level => {
    animateConfidence(level); // 'high' | 'medium' | 'low' | 'critical'
  };

  return <Animated.View style={animatedStyle}>{/* Your content */}</Animated.View>;
};
```

## 📋 Available Interactions

### Confidence Levels

- **High**: Sparkle rotation + success haptic
- **Medium**: Gentle pulse + medium haptic
- **Low**: Attention pulse + warning haptic
- **Critical**: Gentle shake + error haptic

### Interaction Types

- **press**: Button press feedback
- **success**: Celebration with scale & rotation
- **error**: Shake animation with error haptic
- **attention**: Subtle pulse to draw focus
- **processing**: Continuous rotation indicator

### Haptic Feedback

- **Light**: Minor interactions (toggles, selections)
- **Medium**: Important actions (photo capture, formula apply)
- **Heavy**: Critical actions (service completion)
- **Success**: Positive outcomes
- **Warning**: Caution situations
- **Error**: Problems and failures

## 🎯 Hair Analysis Context

Specialized animations for hair colorist workflow:

```tsx
// Confidence indicators with professional feedback
animateConfidence('high'); // Sparkles + success haptic
animateConfidence('critical'); // Shake + error haptic

// Processing states
animateProcessing(true); // Start rotation
animateProcessing(false); // Stop and reset

// Attention seeking (for important warnings)
animateAttention();
```

## 🔧 Configuration

All timing and easing values are centralized in `constants/micro-interactions.ts`:

```tsx
import { MicroInteractions } from '@/constants/micro-interactions';

// Professional durations
MicroInteractions.durations.instant; // 150ms
MicroInteractions.durations.fast; // 300ms
MicroInteractions.durations.normal; // 500ms

// Natural easing curves
MicroInteractions.easing.gentle; // Confidence building
MicroInteractions.easing.snappy; // Button interactions
MicroInteractions.easing.bounce; // Success confirmations

// Spring configurations
MicroInteractions.springs.gentle; // Subtle feedback
MicroInteractions.springs.bouncy; // Success celebrations
```

## 🎪 Examples in Action

### Enhanced ConfidenceIndicator

- High confidence: Sparkles rotate + brightness pulse
- Critical confidence: Gentle shake + pulse animation
- Medium confidence: Subtle scale pulse

### Professional CameraCapture

- Target guides: Pulsing animation
- Light indicator: Reactive pulse on good lighting
- Capture button: Scale feedback + flash effect
- Success: Haptic confirmation + reset pulse

### Intelligent ViabilityIndicator

- Safe result: Bounce scale + success haptic
- Caution: Rotation + gentle pulse
- Risky: Shake animation + error haptic

### Transparent ExplainableAI

- Processing sparkles: Continuous rotation
- Evidence search: Pulsing search icon
- Activity analysis: Rotating activity indicator

## 🚀 Performance Guidelines

- **60 FPS minimum** - All animations run on UI thread
- **Worklets used** - Critical animations use 'worklet' directive
- **Batch animations** - Multiple properties animated together
- **Memory efficient** - Shared values reused, not recreated

## 🧪 Testing Checklist

- [ ] All transitions are smooth (60 FPS)
- [ ] Haptic feedback in key moments
- [ ] Loading states are engaging
- [ ] Entrance/exit animations present
- [ ] Gestures feel natural
- [ ] Performance validated on slow devices
- [ ] No visual glitches
- [ ] Accessibility friendly

## 💡 Tips

1. **Use sparingly**: Not every element needs animation
2. **Context matters**: Match animation intensity to importance
3. **Consistency**: Use the same patterns throughout
4. **Test on device**: Always verify on real hardware
5. **Accessibility**: Respect reduced motion preferences

Remember: The best animations are the ones users don't consciously notice but would miss if they weren't there.
