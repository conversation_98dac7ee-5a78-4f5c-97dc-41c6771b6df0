# CLAUDE.md - Zustand Stores (Estado Offline-First)

## 🎯 Propósito

Sistema de estado global usando Zustand con persistencia AsyncStorage y sincronización offline-first con Supabase. Cada store maneja un dominio específico con sincronización automática.

## 🏗️ Arquitectura Offline-First

### Principio FUNDAMENTAL

**La UI SIEMPRE se actualiza optimísticamente. La sincronización es secundaria.**

```typescript
// ✅ CORRECTO: Update UI primero, sync después
const addProduct = async product => {
  // 1. Update UI inmediatamente
  set(state => ({ products: [...state.products, product] }));

  // 2. Queue para sincronización
  await syncQueue.add('products', 'insert', product);

  // 3. Sync cuando sea posible
  try {
    await syncToSupabase(product);
    syncQueue.remove(product.id);
  } catch (error) {
    // UI ya actualizada, solo log el error
    logger.error('Sync failed, will retry', { error, productId: product.id });
  }
};
```

```typescript
// ❌ INCORRECTO: Esperar sync antes de UI
const addProduct = async product => {
  await syncToSupabase(product); // NO hacer esto
  set(state => ({ products: [...state.products, product] }));
};
```

## 📁 Stores Disponibles

### Core Stores

- `auth-store.ts` - Autenticación y perfil de usuario
- `salon-config-store.ts` - Configuración del salón
- `sync-queue-store.ts` - Cola de sincronización offline

### Business Logic Stores

- `inventory-store.ts` - Gestión de productos e inventario
- `client-store.ts` - Gestión de clientes
- `service-draft-store.ts` - Borradores de servicios en progreso
- `team-store.ts` - Gestión de empleados

### UI/UX Stores

- `chat-store.ts` - Estado del chat asistente
- `photo-capture-store.ts` - Gestión de capturas fotográficas
- `ai-analysis-store.ts` - Resultados de análisis AI
- `dashboard-store.ts` - Métricas y KPIs
- `whimsy-store.ts` - Animaciones y micro-interacciones

## 🔄 Patrón de Sincronización

### 1. Operaciones Locales (Optimistic UI)

```typescript
// Todas las operaciones siguen este patrón:
const updateResource = async (id: string, updates: Partial<T>) => {
  // Generar ID local si es nuevo
  const resourceId = id || generateLocalId();

  // 1. Update UI inmediatamente
  set(state => ({
    resources: state.resources.map(r =>
      r.id === resourceId ? { ...r, ...updates, lastUpdated: Date.now() } : r
    ),
  }));

  // 2. Queue para sync
  await useSyncQueueStore.getState().addToQueue({
    entity: 'resources',
    operation: 'update',
    entityId: resourceId,
    data: updates,
    timestamp: Date.now(),
  });

  // 3. Intentar sync inmediato (si hay conexión)
  syncInBackground();
};
```

### 2. Resolución de Conflictos

```typescript
// Estrategia: Last-Write-Wins con timestamps
const resolveConflict = (local: T, remote: T): T => {
  if (local.lastUpdated > remote.lastUpdated) {
    return local; // Mantener cambios locales más recientes
  }
  return remote; // Usar datos remotos más recientes
};
```

### 3. Multi-Tenant con salon_id

```typescript
// SIEMPRE filtrar por salon_id en operaciones Supabase
const loadProducts = async () => {
  const salonId = await getCurrentSalonId();
  if (!salonId) throw new Error('No salon_id available');

  const { data } = await supabase.from('products').select('*').eq('salon_id', salonId); // OBLIGATORIO

  set({ products: data || [] });
};
```

## 🔐 Reglas de Seguridad

### 1. Aislamiento por Salón

```typescript
// ✅ CORRECTO: Siempre incluir salon_id
const query = supabase.from('table').select('*').eq('salon_id', currentSalonId);

// ❌ INCORRECTO: Query sin filtro de salón
const query = supabase.from('table').select('*'); // NUNCA hacer esto
```

### 2. Validación de Permisos

```typescript
const deleteClient = async (clientId: string) => {
  // Verificar permisos antes de cualquier operación crítica
  const hasPermission = await checkPermission('clients.delete');
  if (!hasPermission) {
    throw new Error('Insufficient permissions');
  }

  // Proceder con la operación...
};
```

## 💾 Persistencia con AsyncStorage

### Configuración Estándar

```typescript
export const useInventoryStore = create<InventoryStore>()(
  persist(
    (set, get) => ({
      // Store implementation
    }),
    {
      name: 'inventory-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Solo persistir datos esenciales, no estado temporal
      partialize: state => ({
        products: state.products,
        lastSync: state.lastSync,
        // NO persistir: isLoading, errors
      }),
    }
  )
);
```

### Datos que NUNCA persistir

- Estados de loading (`isLoading`, `isSyncing`)
- Errores temporales (`error`, `lastError`)
- Estados de UI (`selectedItem`, `modalOpen`)
- Datos sensibles (tokens, passwords)

## 🔄 Sync Queue Store (sync-queue-store.ts)

### Uso del Queue System

```typescript
// Agregar operación a la cola
await useSyncQueueStore.getState().addToQueue({
  entity: 'products',
  operation: 'insert',
  entityId: product.id,
  data: product,
  timestamp: Date.now(),
  retries: 0,
});

// Procesar cola (automático en background)
const processQueue = async () => {
  const queue = useSyncQueueStore.getState().queue;
  for (const item of queue) {
    try {
      await syncToSupabase(item);
      useSyncQueueStore.getState().removeFromQueue(item.id);
    } catch (error) {
      useSyncQueueStore.getState().incrementRetries(item.id);
    }
  }
};
```

## 📝 Patrones de Implementación

### 1. Loading States

```typescript
interface StoreState {
  isLoading: boolean;
  isInitialized: boolean;
  lastSync: string | null;
  error: string | null;
}

// Pattern para operaciones async
const loadData = async () => {
  set({ isLoading: true, error: null });
  try {
    const data = await fetchData();
    set({ data, isLoading: false, isInitialized: true, lastSync: new Date().toISOString() });
  } catch (error) {
    set({ isLoading: false, error: error.message });
  }
};
```

### 2. Selectors y Computed Values

```typescript
// ✅ CORRECTO: Computed values como getters
const store = create<Store>(() => ({
  products: [],

  // Getter para datos computados
  getLowStockProducts: () => {
    const { products } = get();
    return products.filter(p => p.stock < p.minimumStock);
  },

  // Getter con parámetros
  getProductsByBrand: (brand: string) => {
    const { products } = get();
    return products.filter(p => p.brand === brand);
  },
}));

// ❌ INCORRECTO: Estado derivado almacenado
const store = create<Store>(() => ({
  products: [],
  lowStockProducts: [], // NO hacer esto, calcular en runtime
}));
```

### 3. Error Handling

```typescript
const handleError = (error: Error, operation: string) => {
  logger.error(`Store operation failed: ${operation}`, { error });

  set({
    error: error.message,
    isLoading: false,
  });

  // Mostrar toast para errores críticos
  if (operation === 'sync' || operation === 'save') {
    // showErrorToast(error.message);
  }
};
```

## 🧪 Testing Patterns

### Store Testing

```typescript
// Mock AsyncStorage para tests
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Test pattern
test('should add product optimistically', async () => {
  const store = useInventoryStore.getState();
  const initialCount = store.products.length;

  await store.addProduct(mockProduct);

  // UI debe actualizarse inmediatamente
  expect(store.products).toHaveLength(initialCount + 1);
  expect(store.products[0]).toMatchObject(mockProduct);
});
```

## 🚨 Errores Comunes y Soluciones

### 1. Operaciones sin salon_id

```bash
Error: "Row Level Security violation"
Solución: Siempre incluir .eq('salon_id', currentSalonId) en queries
```

### 2. Estado no persistido correctamente

```bash
Error: "State lost after app restart"
Solución: Verificar partialize() excluye solo estados temporales
```

### 3. Sincronización bloqueante

```bash
Error: "UI freezes during sync"
Solución: NUNCA await sync en UI thread, usar queue en background
```

### 4. Memory leaks en subscriptions

```bash
Error: "Performance degrades over time"
Solución: Cleanup subscriptions en useEffect cleanup
```

## 📊 Performance Guidelines

### Limits y Optimizaciones

- **Productos en memoria:** <1000 items (pagination para más)
- **Queue size:** <100 operaciones pendientes
- **Sync frequency:** Cada 5 minutos o al reconectar
- **AsyncStorage size:** <10MB total por store

### Optimizaciones

```typescript
// Debounce operaciones frecuentes
const debouncedSearch = debounce((query: string) => {
  set({ searchResults: filterProducts(query) });
}, 300);

// Lazy loading para datos grandes
const loadProductDetails = async (id: string) => {
  if (detailsCache.has(id)) return detailsCache.get(id);
  const details = await fetchProductDetails(id);
  detailsCache.set(id, details);
  return details;
};
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**offline-sync-specialist** - Arquitectura offline-first

- Optimización de patterns de sincronización
- Resolución de conflicts en stores
- Queue management para operaciones pendientes
- PROACTIVAMENTE usar para problemas de sync

**database-architect** - Optimización de queries en stores

- Optimizar llamadas a Supabase desde stores
- Design de índices para queries frecuentes
- Análisis de performance en operaciones de datos
- PROACTIVAMENTE usar para queries >500ms

**frontend-developer** - Implementación de stores complejos

- Patterns avanzados con Zustand
- Optimización de subscriptions y re-renders
- Integration de stores con componentes
- PROACTIVAMENTE usar para nuevos stores

**debug-specialist** - Debugging de stores

- Memory leaks en subscriptions
- State corruption debugging
- Race conditions en async operations
- PROACTIVAMENTE usar para problemas de estado

**security-privacy-auditor** - Seguridad en stores

- Validar datos sensibles no se persistan
- Auditar filtros por salon_id en stores
- Verificar cleanup de datos temporales
- Usar antes de releases con cambios de auth

### 💡 Ejemplos de Uso

```bash
# Optimizar sincronización en inventory-store
Task: Use offline-sync-specialist to optimize product sync conflicts in inventory-store

# Debug memory leak en auth-store
Task: Use debug-specialist to investigate memory leak in auth-store subscriptions

# Optimizar queries lentas en client-store
Task: Use database-architect to optimize slow client search queries

# Implementar nuevo store para audio
Task: Use frontend-developer to implement audio-recording-store with cleanup
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para base de datos y stores:**

- `mcp__supabase__execute_sql` - Testing directo de queries
- `mcp__supabase__get_logs` - Debug de operaciones de sync
- `mcp__supabase__get_advisors` - Recomendaciones de performance

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de stores
- `mcp__serena__find_symbol` - Localizar métodos específicos
- `mcp__serena__find_referencing_symbols` - Ver dependencias de stores
- `mcp__serena__search_for_pattern` - Buscar patterns en zustand

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en stores
- `mcp__serena__write_memory` - Documentar patterns de stores

### 📝 Ejemplos MCP

```bash
# Analizar estructura de inventory-store
mcp__serena__get_symbols_overview: "stores/inventory-store.ts"
mcp__serena__find_symbol: "addProduct"

# Encontrar todos los usos de useAuthStore
mcp__serena__find_referencing_symbols: "useAuthStore" in "stores/auth-store.ts"

# Buscar todos los persist configs en stores
mcp__serena__search_for_pattern: "persist.*\\(" in "stores/"

# Debug operaciones Supabase desde stores
mcp__supabase__get_logs: "api"
mcp__supabase__execute_sql: "SELECT COUNT(*) FROM products WHERE salon_id IS NULL"

# Verificar errores TypeScript
mcp__ide__getDiagnostics: "stores/"

# Documentar pattern offline-first
mcp__serena__write_memory: "store-patterns" "Patterns de sincronización offline-first en stores"
```

### 🔄 Combinaciones Recomendadas

**Optimización de Sync:**

1. `offline-sync-specialist` + `mcp__supabase__get_logs`
2. `database-architect` + `mcp__supabase__execute_sql`

**Debugging Avanzado:**

1. `debug-specialist` + `mcp__ide__getDiagnostics`
2. `frontend-developer` + `mcp__serena__find_referencing_symbols`

**Security Audit:**

1. `security-privacy-auditor` + `mcp__serena__search_for_pattern`
2. `database-architect` + `mcp__supabase__get_advisors`

## 📊 Patterns de Stores con Agentes

### 🔄 Offline-First Sync Pattern

```typescript
// Usar offline-sync-specialist para optimizar
const syncPattern = async (operation: SyncOperation) => {
  // 1. Update UI optimísticamente (offline-sync-specialist)
  updateUIImmediately(operation.data);

  // 2. Queue para sync (offline-sync-specialist)
  await addToSyncQueue(operation);

  // 3. Sincronizar cuando sea posible
  try {
    await syncToSupabase(operation);
    removeFromQueue(operation.id);
  } catch (error) {
    // Handle conflict (offline-sync-specialist)
    handleSyncConflict(error, operation);
  }
};
```

### 🔍 Store Debugging Pattern

```typescript
// Usar debug-specialist para implementar
const debugStorePattern = (storeName: string) => {
  // 1. State logger (debug-specialist)
  const logStateChanges = (state: any, action: string) => {
    logger.debug(`Store ${storeName}:`, { action, state });
  };

  // 2. Performance monitoring (debug-specialist)
  const monitorPerformance = (operationName: string) => {
    performance.mark(`${storeName}-${operationName}-start`);
    // ... operation
    performance.mark(`${storeName}-${operationName}-end`);
  };

  // 3. Memory leak detection (debug-specialist)
  const trackSubscriptions = () => {
    // Track and cleanup subscriptions
  };
};
```

### 🛡️ Security Pattern for Stores

```typescript
// Usar security-privacy-auditor para validar
const secureStorePattern = {
  // 1. Filtro obligatorio por salon_id (security-privacy-auditor)
  filterBySalon: (data: any[], salonId: string) => {
    return data.filter(item => item.salon_id === salonId);
  },

  // 2. Sanitización de datos sensibles (security-privacy-auditor)
  sanitizeForPersistence: (state: any) => {
    const { sensitiveField, ...safeState } = state;
    return safeState;
  },

  // 3. Validación de permisos (security-privacy-auditor)
  validatePermission: async (operation: string) => {
    return await checkPermission(operation);
  },
};
```

### ⚡ Performance Pattern

```typescript
// Usar frontend-developer para optimizar
const performancePattern = {
  // 1. Memoización de selectors (frontend-developer)
  getMemoizedSelector: (selector: Function) => {
    return useMemo(() => selector(state), [state.version]);
  },

  // 2. Debounce de operaciones frecuentes (frontend-developer)
  getDebouncedOperation: (operation: Function, delay: number) => {
    return debounce(operation, delay);
  },

  // 3. Lazy loading de datos pesados (frontend-developer)
  getLazyLoader: (dataLoader: Function) => {
    return async (id: string) => {
      if (cache.has(id)) return cache.get(id);
      const data = await dataLoader(id);
      cache.set(id, data);
      return data;
    };
  },
};
```

## 🏗️ Arquitectura Modular Refactorizada (2025-09-28)

### Client History Store - Nueva Arquitectura

**Refactorización Exitosa**: De store monolítico (1,321 líneas) a arquitectura modular (68 líneas)

#### Separación de Responsabilidades

```
client-history-store.ts (68 líneas)    ←── Store simplificado
├── /services/clientHistoryService.ts   ←── Lógica de base de datos
├── /types/client-history.types.ts      ←── Definiciones de tipos
└── /utils/clientHistoryUtils.ts        ←── Funciones utilitarias
```

#### Patrón Service Delegation

```typescript
// ANTES - Lógica inline en store (1,321 líneas)
loadClientHistory: async (clientId) => {
  // 100+ líneas de lógica Supabase directa
  const { data: services } = await supabase.from('services')...
  const { data: consents } = await supabase.from('client_consents')...
  // Conversión, validación, etc.
}

// DESPUÉS - Delegación a servicio (3 líneas)
loadClientHistory: async (clientId) => {
  const profileData = await clientHistoryService.loadClientHistory(clientId);
  if (profileData) { /* actualizar estado */ }
}
```

#### Beneficios Logrados

1. **Mantenibilidad**: 95% reducción en líneas del store
2. **Testabilidad**: Servicios independientes testing-friendly
3. **Reutilización**: Utils disponibles para otros módulos
4. **Type Safety**: Tipos centralizados y bien definidos

#### Service Pattern para Otros Stores

```typescript
// Template para refactorizar otros stores monolíticos
const createModularStore = () => ({
  // 1. Solo estado y coordinación en store
  state: initialState,

  // 2. Delegar operaciones complejas a servicios
  complexOperation: async data => {
    const result = await domainService.complexOperation(data);
    set(state => updateState(state, result));
  },

  // 3. Utils para lógica pura
  helpers: {
    validate: data => validationUtils.validate(data),
    transform: data => transformUtils.transform(data),
  },
});
```

## 🔗 Archivos Relacionados

- `../lib/supabase.ts` - Cliente y helpers de Supabase
- `../utils/logger.ts` - Sistema de logging
- `../types/database.ts` - Tipos de base de datos
- `../constants/product-mappings.ts` - Mapeos de productos
- `../services/clientHistoryService.ts` - **NUEVO**: Lógica de BD del client history
- `../types/client-history.types.ts` - **NUEVO**: Tipos del client history
- `../utils/clientHistoryUtils.ts` - **NUEVO**: Utilidades del client history

---

**⚡ Recuerda:** El estado local es la fuente de verdad para la UI. La sincronización es un detalle de implementación. Usa `offline-sync-specialist` para problemas de sync y `debug-specialist` para debugging complejo.

**🏗️ Nuevo Pattern:** Para stores complejos, usar arquitectura modular: Store + Service + Types + Utils. Delegar lógica pesada a servicios manteniendo el store simple.
