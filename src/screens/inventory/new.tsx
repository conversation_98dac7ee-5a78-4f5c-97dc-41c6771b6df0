import React, { useState, useEffect, useMemo, useRef } from 'react';
import { logger } from '@/utils/logger';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Alert,
  Keyboard,
  Platform,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ChevronLeft, ChevronDown, DollarSign, Eye, Archive, Package } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, shadows, typography, radius } from '@/constants/theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { Product } from '@/types/inventory';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';
import { Brand, ProductLine } from '@/services/brandService';
import { BrandSelector } from '@/components/inventory/BrandSelector';
import { LineSelector } from '@/components/inventory/LineSelector';
import { TypeSelector } from '@/components/inventory/TypeSelector';
import { mapCategoryToType } from '@/constants/product-mappings';
import { ProductNamingService } from '@/services/productNamingService';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { ProductNameHelper } from '@/components/inventory/ProductNameHelper';
import { BaseCard } from '@/components/base';

export default function NewInventoryItemScreen() {
  const { addProduct, searchProducts } = useInventoryStore();
  const { volumeUnit, weightUnit, toBaseVolume, toBaseWeight, isMetric, developerTerm, colorTerm } =
    useRegionalUnits();
  const { regionalConfig, configuration } = useSalonConfigStore();
  const params = useLocalSearchParams();

  const [brand, setBrand] = useState('');
  const [productType, setProductType] = useState('');
  const [line, setLine] = useState('');
  const [shade, setShade] = useState(''); // New field for shade/tone
  const [_productName, _setProductName] = useState(''); // Legacy field, will be hidden
  const [packageCount, setPackageCount] = useState('');
  const [packageSize, setPackageSize] = useState('');
  const [totalStock, setTotalStock] = useState('');
  const [unit, setUnit] = useState<'ml' | 'fl oz' | 'g' | 'oz' | 'unidad'>(volumeUnit);
  const [stockInputMode, setStockInputMode] = useState<'packages' | 'total'>('packages');
  const [lowStockThreshold, setLowStockThreshold] = useState('');
  const [barcode, setBarcode] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [category, setCategory] = useState<Product['category']>('otro');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [selectedLine, setSelectedLine] = useState<ProductLine | null>(null);
  const [selectedProductType, setSelectedProductType] = useState<string | null>(null);

  // Refs for input fields
  const _scrollViewRef = useRef<ScrollView>(null);
  const _shadeInputRef = useRef<TextInput>(null);
  const packageCountRef = useRef<TextInput>(null);
  const packageSizeRef = useRef<TextInput>(null);
  const totalStockRef = useRef<TextInput>(null);
  const lowStockRef = useRef<TextInput>(null);
  const purchasePriceRef = useRef<TextInput>(null);
  const barcodeRef = useRef<TextInput>(null);

  // Pre-fill form data from URL parameters
  useEffect(() => {
    if (params.prefill === 'true') {
      if (params.name && typeof params.name === 'string') {
        _setProductName(params.name);
      }
      if (params.brand && typeof params.brand === 'string') {
        setBrand(params.brand);
      }
      if (params.line && typeof params.line === 'string') {
        setLine(params.line);
      }
      if (params.type && typeof params.type === 'string') {
        setProductType(params.type);
      }
      if (params.shade && typeof params.shade === 'string') {
        setShade(params.shade);
      }
    }
  }, [params]);

  // Generate display name based on current form values
  const displayName = useMemo(() => {
    const product = {
      type: productType,
      brand,
      line,
      shade,
    };
    return ProductNamingService.generateDisplayName(
      product,
      regionalConfig || undefined,
      configuration.inventoryControlLevel
    );
  }, [productType, brand, line, shade, regionalConfig, configuration.inventoryControlLevel]);

  // Calculate total stock when packages or size changes
  useEffect(() => {
    if (stockInputMode === 'packages' && packageCount && packageSize) {
      const count = parseFloat(packageCount);
      const size = parseFloat(packageSize);
      if (!isNaN(count) && !isNaN(size)) {
        setTotalStock((count * size).toString());
      }
    }
  }, [packageCount, packageSize, stockInputMode]);

  // Update form fields when brand/line selection changes
  useEffect(() => {
    if (selectedBrand) {
      setBrand(selectedBrand.name);
    }
  }, [selectedBrand]);

  useEffect(() => {
    if (selectedLine) {
      setLine(selectedLine.name);
    }
  }, [selectedLine]);

  const categories: Array<{ value: Product['category']; label: string }> = [
    {
      value: 'tinte',
      label: colorTerm.charAt(0).toUpperCase() + colorTerm.slice(1),
    },
    {
      value: 'oxidante',
      label: developerTerm.charAt(0).toUpperCase() + developerTerm.slice(1),
    },
    { value: 'decolorante', label: 'Decolorante' },
    { value: 'tratamiento', label: 'Tratamiento' },
    { value: 'otro', label: 'Otro' },
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!brand.trim()) {
      newErrors.brand = 'La marca es obligatoria';
    }

    if (!productType.trim()) {
      newErrors.productType = 'El tipo de producto es obligatorio';
    }

    if (stockInputMode === 'packages') {
      if (!packageCount.trim() || parseFloat(packageCount) <= 0) {
        newErrors.packageCount = 'La cantidad de envases es obligatoria';
      }
      if (!packageSize.trim() || parseFloat(packageSize) <= 0) {
        newErrors.packageSize = 'El tamaño del envase es obligatorio';
      }
    } else {
      if (!totalStock.trim() || parseFloat(totalStock) <= 0) {
        newErrors.totalStock = 'La cantidad total es obligatoria';
      }
    }

    if (!purchasePrice.trim() || parseFloat(purchasePrice) <= 0) {
      newErrors.purchasePrice = 'El precio de compra es obligatorio';
    }

    // Stock validation is handled above based on input mode

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      // Crear mensaje con los campos faltantes
      const errorMessages = [];
      if (errors.brand) errorMessages.push('• Marca');
      if (errors.productType) errorMessages.push('• Tipo de Producto');
      if (errors.packageCount) errorMessages.push('• Cantidad de Envases');
      if (errors.packageSize) errorMessages.push('• Tamaño del Envase');
      if (errors.totalStock) errorMessages.push('• Cantidad Total');
      if (errors.purchasePrice) errorMessages.push('• Precio de Compra');

      Alert.alert(
        'Campos Requeridos',
        'Por favor complete los siguientes campos:\n\n' + errorMessages.join('\n'),
        [{ text: 'OK' }]
      );

      // Scroll al primer campo con error
      // Note: This is a simplified approach. In a real app, you might use refs
      return;
    }

    setIsLoading(true);

    try {
      // Check for duplicates using the generated display name
      const searchQuery = displayName || `${brand} ${productType} ${shade}`.trim();
      const existingProducts = searchProducts(searchQuery);
      if (existingProducts.length > 0) {
        // Find products with high similarity
        const duplicates = existingProducts.filter(p => {
          const similarity = ProductNamingService.calculateSimilarity(
            displayName,
            p.displayName || p.name || ''
          );
          return similarity > 80; // 80% similarity threshold
        });

        if (duplicates.length > 0) {
          const duplicate = duplicates[0];
          Alert.alert(
            'Producto Duplicado',
            `Ya existe un producto similar:\n\n${duplicate.displayName || duplicate.name}\n\n¿Deseas continuar?`,
            [
              { text: 'Cancelar', style: 'cancel' },
              { text: 'Continuar', onPress: () => saveProduct() },
            ]
          );
          return;
        }
      }

      await saveProduct();
    } catch (error) {
      logger.error('Error saving inventory item', 'NewInventoryScreen', error);
      Alert.alert('Error', 'No se pudo guardar el producto');
    } finally {
      setIsLoading(false);
    }
  };

  const saveProduct = async () => {
    // Calculate total stock and unit size based on input mode
    let finalTotalStock =
      stockInputMode === 'packages'
        ? parseFloat(packageCount) * parseFloat(packageSize)
        : parseFloat(totalStock);

    let finalUnitSize =
      stockInputMode === 'packages' ? parseFloat(packageSize) : parseFloat(totalStock); // For direct input, unit size equals total

    // Convert to base units if necessary
    if (unit === volumeUnit && !isMetric) {
      // Convert fl oz to ml
      finalTotalStock = toBaseVolume(finalTotalStock);
      finalUnitSize = toBaseVolume(finalUnitSize);
    } else if (unit === weightUnit && !isMetric) {
      // Convert oz to g
      finalTotalStock = toBaseWeight(finalTotalStock);
      finalUnitSize = toBaseWeight(finalUnitSize);
    }

    const costPerUnit = parseFloat(purchasePrice) / finalTotalStock;

    // Store always in base units (ml/g) but keep track of original unit type
    const baseUnitType = unit === volumeUnit ? 'ml' : unit === weightUnit ? 'g' : 'unidad';

    await addProduct({
      brand: brand.trim(),
      line: line.trim() || undefined,
      type: productType.trim(),
      shade: shade.trim() || undefined,
      category,
      currentStock: finalTotalStock,
      minStock: parseFloat(lowStockThreshold) || 0,
      unitType: baseUnitType as 'ml' | 'g' | 'unidad',
      unitSize: finalUnitSize,
      purchasePrice: parseFloat(purchasePrice),
      costPerUnit,
      isActive: true,
      barcode: barcode.trim() || undefined,
      displayName: displayName, // Use the generated display name
    });

    // Small delay to ensure navigation works properly
    setTimeout(() => {
      router.push('/inventory');
    }, 100);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Añadir Nuevo Artículo</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.formContainer}>
          {/* Identificación del Producto Section */}
          <BaseCard variant="beige" shadow="md" padding="lg" style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Package size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Identificación del Producto</Text>
            </View>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Marca *</Text>
              <BrandSelector
                selectedBrand={selectedBrand}
                onBrandSelect={brand => {
                  setSelectedBrand(brand);
                  setSelectedLine(null); // Reset line when brand changes
                }}
                placeholder="Buscar marca profesional..."
              />
              {errors.brand && <Text style={styles.errorText}>{errors.brand}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Línea de Producto</Text>
              <LineSelector
                selectedBrand={selectedBrand}
                selectedLine={selectedLine}
                onLineSelect={setSelectedLine}
                placeholder="Seleccionar línea..."
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tipo de Producto *</Text>
              <TypeSelector
                selectedType={selectedProductType}
                onTypeSelect={typeId => {
                  setSelectedProductType(typeId);
                  if (typeId) {
                    // Map the selected type ID to the actual product type
                    const mappedType = mapCategoryToType(typeId);
                    setProductType(mappedType);

                    // Auto-set category based on product type
                    if (
                      typeId === 'tinte' ||
                      typeId === 'oxidante' ||
                      typeId === 'decolorante' ||
                      typeId === 'matizador' ||
                      typeId === 'pre-pigmentacion'
                    ) {
                      setCategory(
                        typeId === 'oxidante'
                          ? 'oxidante'
                          : typeId === 'decolorante'
                            ? 'decolorante'
                            : 'tinte'
                      );
                    } else if (
                      typeId === 'tratamiento' ||
                      typeId === 'protector' ||
                      typeId === 'mascarilla' ||
                      typeId === 'aceite' ||
                      typeId === 'serum' ||
                      typeId === 'champu' ||
                      typeId === 'acondicionador' ||
                      typeId === 'neutralizante'
                    ) {
                      setCategory('tratamiento');
                    } else {
                      setCategory('otro');
                    }
                  } else {
                    setProductType('');
                  }
                }}
                placeholder="Buscar tipo de producto..."
              />
              {errors.productType && <Text style={styles.errorText}>{errors.productType}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Categoría *</Text>
              <TouchableOpacity
                style={[styles.input, styles.pickerInput]}
                onPress={() => setShowCategoryPicker(!showCategoryPicker)}
              >
                <Text style={styles.pickerText}>
                  {categories.find(c => c.value === category)?.label || 'Seleccionar'}
                </Text>
                <ChevronDown size={20} color={Colors.light.text} />
              </TouchableOpacity>
              {showCategoryPicker && (
                <View style={styles.pickerOptions}>
                  {categories.map(cat => (
                    <TouchableOpacity
                      key={cat.value}
                      style={styles.pickerOption}
                      onPress={() => {
                        setCategory(cat.value);
                        setShowCategoryPicker(false);
                      }}
                    >
                      <Text
                        style={[
                          styles.pickerOptionText,
                          category === cat.value && styles.pickerOptionTextSelected,
                        ]}
                      >
                        {cat.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tono/Número (Opcional)</Text>
              <ProductNameHelper
                value={shade}
                onChangeText={setShade}
                placeholder="Ej: 7, 9.1, 30 vol, N5"
                brand={brand}
                type={productType}
                onSelectSuggestion={suggestion => {
                  // Si selecciona un producto existente, pre-llenar campos
                  if (suggestion.shade) setShade(suggestion.shade);
                  if (suggestion.barcode) setBarcode(suggestion.barcode);
                }}
              />
              <Text style={styles.helperText}>
                Para tintes: número de tono. Para oxidantes: volumen.
              </Text>
            </View>
          </BaseCard>

          {/* Product Preview */}
          {displayName && displayName.trim() && (
            <BaseCard variant="light" shadow="md" padding="lg" style={styles.previewCard}>
              <View style={styles.previewHeader}>
                <Eye size={16} color={Colors.light.primary} />
                <Text style={styles.previewLabel}>Vista previa del producto</Text>
              </View>
              <Text style={styles.previewName}>{displayName}</Text>
              <Text style={styles.previewHelper}>
                Así aparecerá en tu inventario y formulaciones
              </Text>
            </BaseCard>
          )}

          {/* Gestión de Inventario Section */}
          <BaseCard variant="light" shadow="md" padding="lg" style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Archive size={20} color={Colors.light.secondary} />
              <Text style={styles.sectionTitle}>Gestión de Inventario</Text>
            </View>

            {/* Stock Input Mode Selector */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Cómo ingresar el stock:</Text>
              <View style={styles.modeSelector}>
                <TouchableOpacity
                  style={[
                    styles.modeButton,
                    stockInputMode === 'packages' && styles.modeButtonActive,
                  ]}
                  onPress={() => setStockInputMode('packages')}
                >
                  <Text
                    style={[
                      styles.modeButtonText,
                      stockInputMode === 'packages' && styles.modeButtonTextActive,
                    ]}
                  >
                    Por Envases
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modeButton, stockInputMode === 'total' && styles.modeButtonActive]}
                  onPress={() => setStockInputMode('total')}
                >
                  <Text
                    style={[
                      styles.modeButtonText,
                      stockInputMode === 'total' && styles.modeButtonTextActive,
                    ]}
                  >
                    Cantidad Total
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Package-based input */}
            {stockInputMode === 'packages' ? (
              <>
                <View style={styles.rowContainer}>
                  <View style={[styles.formGroup, styles.rowItem]}>
                    <Text style={styles.label}>Cantidad de Envases *</Text>
                    <TextInput
                      ref={packageCountRef}
                      style={[
                        styles.input,
                        packageCount && styles.inputWithValue,
                        errors.packageCount && styles.inputError,
                      ]}
                      value={packageCount}
                      onChangeText={setPackageCount}
                      placeholder="Ej: 12"
                      keyboardType="numeric"
                      returnKeyType="next"
                      onSubmitEditing={() => packageSizeRef.current?.focus()}
                    />
                    {errors.packageCount && (
                      <Text style={styles.errorText}>{errors.packageCount}</Text>
                    )}
                  </View>

                  <View style={[styles.formGroup, styles.rowItem]}>
                    <Text style={styles.label}>Tamaño por Envase *</Text>
                    <View style={styles.inputWithUnit}>
                      <TextInput
                        ref={packageSizeRef}
                        style={[
                          styles.input,
                          styles.inputWithUnitField,
                          packageSize && styles.inputWithValue,
                          errors.packageSize && styles.inputError,
                        ]}
                        value={packageSize}
                        onChangeText={setPackageSize}
                        placeholder="Ej: 60"
                        keyboardType="numeric"
                        returnKeyType="next"
                        onSubmitEditing={() => lowStockRef.current?.focus()}
                      />
                      <Text style={styles.unitText}>{unit}</Text>
                    </View>
                    {errors.packageSize && (
                      <Text style={styles.errorText}>{errors.packageSize}</Text>
                    )}
                  </View>
                </View>

                {/* Total calculation display */}
                {packageCount && packageSize && (
                  <View style={styles.calculationDisplay}>
                    <Text style={styles.calculationText}>
                      Stock Total: {packageCount} × {packageSize} {unit} =
                      <Text style={styles.calculationResult}>
                        {' '}
                        {totalStock} {unit}
                      </Text>
                    </Text>
                  </View>
                )}
              </>
            ) : (
              /* Direct total input */
              <View style={styles.formGroup}>
                <Text style={styles.label}>Cantidad Total *</Text>
                <View style={styles.inputWithUnit}>
                  <TextInput
                    ref={totalStockRef}
                    style={[
                      styles.input,
                      styles.inputWithUnitField,
                      totalStock && styles.inputWithValue,
                      errors.totalStock && styles.inputError,
                    ]}
                    value={totalStock}
                    onChangeText={setTotalStock}
                    placeholder="Ej: 720"
                    keyboardType="numeric"
                    returnKeyType="next"
                    onSubmitEditing={() => lowStockRef.current?.focus()}
                  />
                  <Text style={styles.unitText}>{unit}</Text>
                </View>
                {errors.totalStock && <Text style={styles.errorText}>{errors.totalStock}</Text>}
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Unidad de Medida</Text>
              <View style={styles.unitSelector}>
                <TouchableOpacity
                  style={[styles.unitButton, unit === volumeUnit && styles.unitButtonActive]}
                  onPress={() => setUnit(volumeUnit)}
                >
                  <Text
                    style={[
                      styles.unitButtonText,
                      unit === volumeUnit && styles.unitButtonTextActive,
                    ]}
                  >
                    {volumeUnit}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.unitButton, unit === weightUnit && styles.unitButtonActive]}
                  onPress={() => setUnit(weightUnit)}
                >
                  <Text
                    style={[
                      styles.unitButtonText,
                      unit === weightUnit && styles.unitButtonTextActive,
                    ]}
                  >
                    {weightUnit}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.unitButton, unit === 'unidad' && styles.unitButtonActive]}
                  onPress={() => setUnit('unidad')}
                >
                  <Text
                    style={[
                      styles.unitButtonText,
                      unit === 'unidad' && styles.unitButtonTextActive,
                    ]}
                  >
                    unidad
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={[styles.formGroup, styles.lastFormGroup]}>
              <Text style={styles.label}>Umbral Bajo Stock</Text>
              <View style={styles.inputWithUnit}>
                <TextInput
                  ref={lowStockRef}
                  style={[
                    styles.input,
                    styles.inputWithUnitField,
                    lowStockThreshold && styles.inputWithValue,
                  ]}
                  value={lowStockThreshold}
                  onChangeText={setLowStockThreshold}
                  placeholder="Ej: 120"
                  keyboardType="numeric"
                  returnKeyType="next"
                  onSubmitEditing={() => purchasePriceRef.current?.focus()}
                />
                <Text style={styles.unitText}>{unit}</Text>
              </View>
              <Text style={styles.helperText}>
                Se te avisará cuando el stock baje de esta cantidad
              </Text>
            </View>
          </BaseCard>

          {/* Información Comercial Section */}
          <BaseCard variant="beige" shadow="md" padding="lg" style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <DollarSign size={20} color={Colors.light.success} />
              <Text style={styles.sectionTitle}>Información Comercial</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Precio de Compra *</Text>
              <TextInput
                ref={purchasePriceRef}
                style={[
                  styles.input,
                  purchasePrice && styles.inputWithValue,
                  errors.purchasePrice && styles.inputError,
                ]}
                value={purchasePrice}
                onChangeText={setPurchasePrice}
                placeholder="Ej: 25.50"
                keyboardType="decimal-pad"
                returnKeyType="next"
                onSubmitEditing={() => barcodeRef.current?.focus()}
              />
              {errors.purchasePrice && <Text style={styles.errorText}>{errors.purchasePrice}</Text>}

              {/* Cost calculation display */}
              {purchasePrice && (
                <View style={styles.costCalculationContainer}>
                  {(() => {
                    const price = parseFloat(purchasePrice);
                    let calculatedStock = 0;
                    let canCalculate = false;
                    const missingFields: string[] = [];

                    // Determine available stock based on input mode
                    if (stockInputMode === 'packages') {
                      if (packageCount && packageSize) {
                        calculatedStock = parseFloat(packageCount) * parseFloat(packageSize);
                        canCalculate = true;
                      } else {
                        if (!packageCount) missingFields.push('cantidad de envases');
                        if (!packageSize) missingFields.push('tamaño del envase');
                      }
                    } else {
                      if (totalStock) {
                        calculatedStock = parseFloat(totalStock);
                        canCalculate = true;
                      } else {
                        missingFields.push('cantidad total');
                      }
                    }

                    if (canCalculate && calculatedStock > 0) {
                      const costPerUnit = price / calculatedStock;

                      // Ensure we have a valid number
                      if (isNaN(costPerUnit) || !isFinite(costPerUnit)) {
                        return (
                          <Text style={styles.helperTextError}>
                            Error en el cálculo del costo. Verifique los valores.
                          </Text>
                        );
                      }

                      return (
                        <View style={styles.costDisplayContainer}>
                          <Text style={styles.costDisplayLabel}>Costo por {unit}:</Text>
                          <Text style={styles.costDisplayValue}>{costPerUnit.toFixed(4)}€</Text>
                        </View>
                      );
                    } else if (missingFields.length > 0) {
                      return (
                        <Text style={styles.helperTextPending}>
                          Para calcular el costo por {unit}, complete:{' '}
                          <Text style={styles.missingFieldsText}>{missingFields.join(' y ')}</Text>
                        </Text>
                      );
                    } else {
                      return (
                        <Text style={styles.helperTextPending}>
                          Complete la información de stock para ver el costo por {unit}
                        </Text>
                      );
                    }
                  })()}
                </View>
              )}
            </View>

            <View style={[styles.formGroup, styles.lastFormGroup]}>
              <Text style={styles.label}>Código Barras EAN (Opcional)</Text>
              <TextInput
                ref={barcodeRef}
                style={[styles.input, barcode && styles.inputWithValue]}
                value={barcode}
                onChangeText={setBarcode}
                placeholder="Ej: 8005610433172"
                keyboardType="numeric"
                returnKeyType="done"
                onSubmitEditing={() => Keyboard.dismiss()}
              />
            </View>
          </BaseCard>

          {/* Spacer for floating buttons */}
          <View style={styles.bottomSpacer} />
        </View>
      </ScrollView>

      {/* Floating Action Buttons */}
      <View style={styles.floatingButtonContainer}>
        <TouchableOpacity
          style={styles.cancelButtonNew}
          onPress={() => router.back()}
          disabled={isLoading}
        >
          <Text style={styles.cancelButtonText}>Cancelar</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveButtonNew} onPress={handleSave} disabled={isLoading}>
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Guardando...' : 'Añadir Artículo'}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  placeholder: {
    width: 34, // Same width as back button for centering
  },
  formContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  sectionCard: {
    marginBottom: spacing.lg,
  },
  formGroup: {
    marginBottom: 28,
  },
  lastFormGroup: {
    marginBottom: 0,
  },
  label: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderWidth: 2,
    borderColor: Colors.light.warning, // Borde dorado visible como en la imagen
    borderRadius: 12,
    paddingHorizontal: spacing.lg,
    paddingVertical: 14,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputWithValue: {
    borderColor: Colors.light.success,
    backgroundColor: Colors.light.success + '08',
  },
  inputError: {
    borderColor: Colors.light.error,
    borderWidth: 2,
    backgroundColor: Colors.light.error + '08',
  },
  errorText: {
    color: Colors.light.error,
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  rowItem: {
    flex: 1,
  },
  bottomSpacer: {
    height: 100, // Space for floating buttons
  },
  floatingButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
    borderTopWidth: 2,
    borderTopColor: Colors.light.primary,
    gap: spacing.sm,
    ...shadows.lg,
  },
  cancelButtonNew: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  saveButtonNew: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.md,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptions: {
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.md,
    marginTop: spacing.xs,
    overflow: 'hidden',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  pickerOption: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  helperText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
  },
  costCalculationContainer: {
    marginTop: spacing.sm,
  },
  costDisplayContainer: {
    backgroundColor: Colors.light.surface,
    padding: spacing.md,
    borderRadius: radius.md,
    marginTop: spacing.sm,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.success,
    ...shadows.sm,
  },
  costDisplayLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  costDisplayValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.success,
  },
  helperTextError: {
    fontSize: typography.sizes.xs,
    color: Colors.light.error,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  helperTextPending: {
    fontSize: typography.sizes.xs,
    color: Colors.light.warning,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  missingFieldsText: {
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  modeSelector: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  modeButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
  },
  modeButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  modeButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
  },
  modeButtonTextActive: {
    color: Colors.light.textLight,
  },
  inputWithUnit: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  inputWithUnitField: {
    flex: 1,
  },
  unitText: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    minWidth: 30,
  },
  calculationDisplay: {
    backgroundColor: Colors.light.success + '20', // Verde sutil pero visible
    padding: spacing.lg,
    borderRadius: radius.md,
    marginTop: spacing.md,
    marginBottom: spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.success,
    ...shadows.md,
  },
  calculationText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    textAlign: 'center',
  },
  calculationResult: {
    fontWeight: typography.weights.bold,
    color: Colors.light.success,
  },
  unitSelector: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  unitButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    backgroundColor: Colors.light.surface,
    borderWidth: 2,
    borderColor: Colors.light.border,
    minWidth: 60,
    alignItems: 'center',
  },
  unitButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
    ...shadows.sm,
  },
  unitButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    fontWeight: typography.weights.medium,
  },
  unitButtonTextActive: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
  },
  previewCard: {
    marginBottom: spacing.lg,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  previewLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
    marginLeft: spacing.sm,
  },
  previewName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  previewHelper: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
});
