// Demo user creation script (run after applying RLS fix)
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const supabaseAnonKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'demo123',
    name: 'Demo User',
    salon: 'Demo Salon',
  },
  {
    email: '<EMAIL>',
    password: 'stylist123',
    name: '<PERSON>',
    salon: "Sarah's Hair Studio",
  },
  {
    email: '<EMAIL>',
    password: 'color123',
    name: '<PERSON>',
    salon: 'Color Master Salon',
  },
];

async function createDemoUsers() {
  console.log('🎭 Creating demo users for Salonier...\n');

  for (const user of demoUsers) {
    console.log(`Creating user: ${user.email}`);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: {
            full_name: user.name,
            salon_name: user.salon,
          },
        },
      });

      if (error) {
        if (error.message.includes('already registered')) {
          console.log(`   ⚠️ User already exists: ${user.email}`);
        } else {
          console.log(`   ❌ Failed to create ${user.email}: ${error.message}`);
        }
      } else {
        console.log(`   ✅ Created user: ${user.email}`);
        console.log(`      → Password: ${user.password}`);
        console.log(`      → Name: ${user.name}`);
        console.log(`      → Salon: ${user.salon}`);

        if (data.user) {
          // Wait for profile creation
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Verify profile was created
          const { data: profile } = await supabase
            .from('profiles')
            .select('salon_id, full_name')
            .eq('id', data.user.id)
            .single();

          if (profile) {
            console.log(`      → Profile created with salon_id: ${profile.salon_id}`);
          }
        }
      }
    } catch (err) {
      console.log(`   ❌ Error creating ${user.email}: ${err.message}`);
    }

    console.log(''); // Add spacing
  }

  console.log('✅ Demo user creation completed!');
  console.log('\n📱 You can now login with any of these accounts:');
  demoUsers.forEach(user => {
    console.log(`   • ${user.email} / ${user.password}`);
  });
}

// Run the script
createDemoUsers().catch(console.error);
