import { useState, useCallback } from 'react';
import { ViabilityAnalysis } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { analyzeViability } from '../utils/viabilityCalculators';
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';

/**
 * Hook for managing viability analysis of hair color services
 */
export const useViabilityAnalysis = () => {
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ViabilityAnalysis | null>(null);

  const performViabilityAnalysis = useCallback(
    (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
    ): ViabilityAnalysis => {
      const analysis = analyzeViability(analysisResult, desiredAnalysisResult, zoneColorAnalysis);
      setViabilityAnalysis(analysis);
      return analysis;
    },
    []
  );

  return {
    viabilityAnalysis,
    setViabilityAnalysis,
    performViabilityAnalysis,
  };
};
