# Tasks: Mejorar la Estructura de Archivos y Carpetas

**Input**: Design documents from `/specs/002-mejorar-la-estructura/`
**Prerequisites**: plan.md (✓), research.md (✓), data-model.md (✓), contracts/ (✓), quickstart.md (✓)

## Execution Flow (main)

```
1. Load plan.md from feature directory ✓
   → Extracted: TypeScript/React Native/Expo, Zustand, Supabase
2. Load optional design documents: ✓
   → data-model.md: 6 entities identified → model tasks
   → contracts/: file-organization.yaml → API test tasks
   → research.md: React Native best practices → setup tasks
3. Generate tasks by category:
   → Setup: project structure creation, tooling
   → Tests: contract tests, validation tests
   → Core: file analysis, migration planning, execution
   → Integration: import updates, build validation
   → Polish: documentation, automation
4. Apply task rules:
   → Different files = mark [P] for parallel
   → Same file = sequential (no [P])
   → Tests before implementation (TDD)
5. Number tasks sequentially (T001, T002...)
6. Generate dependency graph
7. Create parallel execution examples
8. Validate task completeness ✓
9. Return: SUCCESS (tasks ready for execution)
```

## Format: `[ID] [P?] Description`

- **[P]**: Can run in parallel (different files, no dependencies)
- Include exact file paths in descriptions

## Path Conventions

- **Mobile project**: Repository root with `src/`, `assets/`, `supabase/`, `__tests__/`
- Paths follow React Native/Expo conventions per research.md

## Phase 3.1: Setup

- [ ] T001 Create backup of current project structure using git
- [ ] T002 Create target directory structure per research.md recommendations
- [ ] T003 [P] Set up file organization validation scripts in `scripts/validate-structure.ts`

## Phase 3.2: Analysis & Planning ⚠️ MUST COMPLETE BEFORE 3.3

**CRITICAL: Analysis must identify all files and dependencies before ANY reorganization**

- [ ] T004 [P] Contract test for /analyze-structure API in `__tests__/contract/analyze-structure.test.ts`
- [ ] T005 [P] Contract test for /generate-migration-plan API in `__tests__/contract/migration-plan.test.ts`
- [ ] T006 [P] Contract test for /validate-migration API in `__tests__/contract/validate-migration.test.ts`
- [ ] T007 [P] Contract test for /execute-migration API in `__tests__/contract/execute-migration.test.ts`
- [ ] T008 [P] Integration test for file classification in `__tests__/integration/file-classification.test.ts`
- [ ] T009 [P] Integration test for import dependency analysis in `__tests__/integration/dependency-analysis.test.ts`

## Phase 3.3: Core Models & Services (ONLY after tests are failing)

- [ ] T010 [P] FileSystemNode model in `src/types/file-system.ts`
- [ ] T011 [P] FileCategory enum in `src/types/file-category.ts`
- [ ] T012 [P] OrganizationRule model in `src/types/organization-rule.ts`
- [ ] T013 [P] MigrationPlan model in `src/types/migration-plan.ts`
- [ ] T014 [P] ProjectStructure model in `src/types/project-structure.ts`
- [ ] T015 [P] NamingConvention model in `src/types/naming-convention.ts`
- [ ] T016 [P] File analysis service in `src/services/file-analyzer.ts`
- [ ] T017 [P] Migration planning service in `src/services/migration-planner.ts`
- [ ] T018 [P] Structure validation service in `src/services/structure-validator.ts`

## Phase 3.4: File Organization Implementation

- [ ] T019 Analyze current Salonier project structure using file-analyzer service
- [ ] T020 Generate migration plan for React Native best practices structure
- [ ] T021 Validate migration plan for conflicts and safety
- [ ] T022 Create new directory structure according to target design
- [ ] T023 Implement file categorization logic for React Native components
- [ ] T024 Implement import path analysis and dependency mapping
- [ ] T025 Execute file migrations in dependency-safe order

## Phase 3.5: Integration & Import Updates

- [ ] T026 Update import statements throughout codebase after file moves
- [ ] T027 Update asset references in React Native components
- [ ] T028 Update Expo configuration for new asset locations
- [ ] T029 Update test imports to match new structure
- [ ] T030 Update build configuration (Metro, Babel) for new paths
- [ ] T031 Validate TypeScript compilation after reorganization

## Phase 3.6: Quality Validation

- [ ] T032 [P] Unit tests for FileSystemNode in `__tests__/unit/file-system-node.test.ts`
- [ ] T033 [P] Unit tests for migration planner in `__tests__/unit/migration-planner.test.ts`
- [ ] T034 [P] Unit tests for structure validator in `__tests__/unit/structure-validator.test.ts`
- [ ] T035 Run quickstart.md validation scenarios
- [ ] T036 Verify app builds and runs on all platforms (iOS, Android, Web)
- [ ] T037 Performance test: ensure build times haven't increased significantly
- [ ] T038 [P] Update project documentation in `docs/project-structure.md`

## Phase 3.7: Polish & Automation

- [ ] T039 [P] Create ESLint rules to enforce new structure in `.eslintrc-structure.js`
- [ ] T040 [P] Create development scripts for structure validation in `scripts/check-structure.ts`
- [ ] T041 [P] Update CLAUDE.md with new project organization
- [ ] T042 Create developer onboarding guide for new structure
- [ ] T043 Set up git hooks to validate structure on commits

## Dependencies

- Backup (T001) before any analysis (T004-T009)
- Tests (T004-T009) before implementation (T010-T025)
- Models (T010-T015) before services (T016-T018)
- Services (T016-T018) before implementation (T019-T025)
- File migration (T019-T025) before integration (T026-T031)
- Structure complete (T031) before validation (T032-T038)
- Core complete before polish (T039-T043)

## Parallel Example

```bash
# Phase 3.2 - Launch contract tests together:
Task: "Contract test /analyze-structure API in __tests__/contract/analyze-structure.test.ts"
Task: "Contract test /generate-migration-plan API in __tests__/contract/migration-plan.test.ts"
Task: "Contract test /validate-migration API in __tests__/contract/validate-migration.test.ts"
Task: "Contract test /execute-migration API in __tests__/contract/execute-migration.test.ts"

# Phase 3.3 - Launch model creation together:
Task: "FileSystemNode model in src/types/file-system.ts"
Task: "FileCategory enum in src/types/file-category.ts"
Task: "OrganizationRule model in src/types/organization-rule.ts"
Task: "MigrationPlan model in src/types/migration-plan.ts"
```

## Special Considerations for React Native

- **Asset handling**: Assets must maintain Expo-compatible paths
- **Platform-specific files**: iOS/Android overrides need careful migration
- **Metro bundler**: Import paths must work with Metro's resolution
- **Hot reload**: Structure changes may require development server restart
- **TypeScript paths**: Update tsconfig.json path mappings if needed

## Rollback Strategy

- Git backup created in T001 allows safe rollback
- Each phase creates checkpoint commits
- Critical path: T019-T025 (actual file moves) - most risky
- Validation in T035-T037 confirms successful migration

## Success Criteria Validation

Per quickstart.md requirements:

- [ ] New developer can understand structure in 15 minutes (T035)
- [ ] Files can be located following logical conventions (T035)
- [ ] New features can be added following established patterns (T035)
- [ ] Dependencies and relationships are clear (T035)
- [ ] App builds and runs on all platforms (T036)

## Notes

- [P] tasks = different files, no dependencies
- Verify tests fail before implementing
- Commit after each phase completion
- Focus on maintaining React Native/Expo compatibility
- Preserve offline-first architecture patterns
- Consider Zustand store organization in file moves

## Task Generation Rules Applied

1. **From Contracts**: 4 contract endpoints → 4 contract test tasks [P]
2. **From Data Model**: 6 entities → 6 model creation tasks [P]
3. **From User Stories**: Quickstart scenarios → validation tasks
4. **Ordering**: Setup → Tests → Models → Services → Implementation → Integration → Polish
5. **File-level parallelism**: Different TypeScript files can be created in parallel

## Validation Checklist

- [✓] All contracts have corresponding tests (T004-T007)
- [✓] All entities have model tasks (T010-T015)
- [✓] All tests come before implementation (T004-T009 before T010+)
- [✓] Parallel tasks truly independent (different files)
- [✓] Each task specifies exact file path
- [✓] No task modifies same file as another [P] task
