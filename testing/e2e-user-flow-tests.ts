/**
 * End-to-End User Flow Tests for Salonier Transformation
 *
 * Comprehensive testing of complete user workflows that leverage
 * the enhanced AI system and brand intelligence integration.
 */

import { brandService } from '../src/services/brandService';
import { brandInventoryIntegration } from '../src/services/brandInventoryIntegration';
import { BrandContextEnhancer } from '../supabase/functions/salonier-assistant/services/brand-context-enhancer';
import { EnhancedPrompts } from '../supabase/functions/salonier-assistant/utils/enhanced-prompts';

export class E2EUserFlowTests {
  /**
   * Test complete service creation workflow
   */
  async testCompleteServiceFlow(): Promise<E2ETestResult> {
    console.log('🎯 Testing Complete Service Flow...\n');

    const testResult: E2ETestResult = {
      testName: 'Complete Service Flow',
      steps: [],
      passed: false,
      totalDuration: 0,
      startTime: Date.now(),
    };

    try {
      // Step 1: Client Selection & Hair Analysis
      const step1 = await this.executeStep('Client Selection & Hair Analysis', async () => {
        return {
          client: {
            id: 'test-client-123',
            name: '<PERSON>',
            history: ['Previous bleaching', 'Sensitive scalp'],
          },
          hairAnalysis: {
            natural_level: 5,
            desired_level: 8,
            porosity: 'high',
            texture: 'fine',
            previous_color: 'level 7 blonde',
            damage_level: 'medium',
          },
        };
      });
      testResult.steps.push(step1);

      // Step 2: Product Selection with Brand Intelligence
      const step2 = await this.executeStep(
        'Product Selection with Brand Intelligence',
        async () => {
          const selectedProducts = [
            'Wella Koleston Perfect 8.0 Light Blonde',
            'Wella Color Perfect Developer 20 vol',
            'Wella SP Luxe Oil Protective Argan Oil',
          ];

          // Validate products against inventory
          const validations = [];
          for (const product of selectedProducts) {
            const productInfo = this.parseProductName(product);
            const validation = await brandInventoryIntegration.validateProductBrand(productInfo);
            validations.push({ product, validation });
          }

          return {
            selectedProducts,
            validations,
            allValid: validations.every(v => v.validation.isValid),
          };
        }
      );
      testResult.steps.push(step2);

      // Step 3: Brand Context Generation
      const step3 = await this.executeStep('Brand Context Generation', async () => {
        const products = step2.result.selectedProducts;
        const context = await BrandContextEnhancer.generateBrandContext(products);

        return {
          context,
          detectedBrands: context?.detectedBrands || [],
          hasRecommendations: (context?.recommendations?.length || 0) > 0,
          hasTerminology: (context?.terminology?.length || 0) > 0,
        };
      });
      testResult.steps.push(step3);

      // Step 4: Enhanced AI Prompt Generation
      const step4 = await this.executeStep('Enhanced AI Prompt Generation', async () => {
        const hairDiagnosis = step1.result.hairAnalysis;
        const brandContext = step3.result.context;

        const prompt = EnhancedPrompts.generateFormulaPrompt(
          {
            hairDiagnosis,
            brands: step3.result.detectedBrands,
            context: brandContext,
          },
          'complex'
        );

        return {
          prompt,
          promptLength: prompt.length,
          containsBrandTerms: step3.result.detectedBrands.some(brand =>
            prompt.toLowerCase().includes(brand.toLowerCase())
          ),
          containsRecommendations:
            brandContext?.recommendations?.some(rec => prompt.includes(rec)) || false,
        };
      });
      testResult.steps.push(step4);

      // Step 5: Simulated AI Response Processing
      const step5 = await this.executeStep('AI Response Processing', async () => {
        // Simulate AI response with enhanced context
        const simulatedResponse = {
          formula: `
Base Formula for María García:
- Wella Koleston Perfect 8.0 (30ml)
- Wella Color Perfect Developer 20 vol (30ml)
- Processing time: 35 minutes with heat

Given the high porosity and previous bleaching, I recommend:
1. Apply Wella SP Luxe Oil as pre-treatment
2. Use lower heat settings due to damage level
3. Check every 10 minutes to prevent over-processing

Professional Notes:
- Wella /0 series provides excellent grey coverage
- 20 vol developer is appropriate for 3-level lift
- Post-service: Use Wella INVIGO blonde toner if needed
          `,
          confidence: 0.95,
          warnings: [
            'Monitor closely due to previous bleaching',
            'Perform strand test if unsure about processing time',
          ],
          products_used: step2.result.selectedProducts,
        };

        return simulatedResponse;
      });
      testResult.steps.push(step5);

      // Step 6: Service Documentation & Completion
      const step6 = await this.executeStep('Service Documentation', async () => {
        return {
          service_record: {
            client_id: step1.result.client.id,
            formula: step5.result.formula,
            products: step2.result.selectedProducts,
            processing_time: '35 minutes',
            result_level: 8,
            satisfaction: 'high',
            notes: 'Client very happy with result. Schedule follow-up in 6 weeks.',
          },
          feedback_collected: true,
          photos_taken: true,
          next_appointment: '2024-04-15',
        };
      });
      testResult.steps.push(step6);

      // Validate overall flow success
      const allStepsPassed = testResult.steps.every(step => step.passed);
      const hasValidProducts = step2.result.allValid;
      const hasGoodContext = step3.result.hasRecommendations && step3.result.hasTerminology;
      const hasEnhancedPrompt = step4.result.containsBrandTerms && step4.result.promptLength > 500;

      testResult.passed = allStepsPassed && hasValidProducts && hasGoodContext && hasEnhancedPrompt;
      testResult.totalDuration = Date.now() - testResult.startTime;

      console.log(
        `${testResult.passed ? '✅' : '❌'} Complete Service Flow: ${testResult.passed ? 'PASSED' : 'FAILED'}`
      );
      console.log(`   Duration: ${testResult.totalDuration}ms`);
      console.log(
        `   Steps completed: ${testResult.steps.filter(s => s.passed).length}/${testResult.steps.length}`
      );
    } catch (error) {
      testResult.passed = false;
      testResult.error = error.message;
      testResult.totalDuration = Date.now() - testResult.startTime;
      console.log(`❌ Service Flow Failed: ${error.message}`);
    }

    return testResult;
  }

  /**
   * Test brand discovery and validation workflow
   */
  async testBrandDiscoveryFlow(): Promise<E2ETestResult> {
    console.log('🔍 Testing Brand Discovery Flow...\n');

    const testResult: E2ETestResult = {
      testName: 'Brand Discovery Flow',
      steps: [],
      passed: false,
      totalDuration: 0,
      startTime: Date.now(),
    };

    try {
      // Step 1: Brand Search & Discovery
      const step1 = await this.executeStep('Brand Search & Discovery', async () => {
        const searchQueries = ['Well', 'Loreal', 'Matrix'];
        const searchResults = [];

        for (const query of searchQueries) {
          const brands = await brandService.searchBrands(query);
          const autocomplete = await brandInventoryIntegration.getBrandAutocomplete(query);
          searchResults.push({
            query,
            brandsFound: brands.length,
            autocompleteOptions: autocomplete.length,
            topSuggestion: autocomplete[0]?.name,
          });
        }

        return { searchResults };
      });
      testResult.steps.push(step1);

      // Step 2: Brand Details & Product Lines
      const step2 = await this.executeStep('Brand Details & Product Lines', async () => {
        const selectedBrand = 'Wella Professionals';
        const brand = await brandService.getBrandByName(selectedBrand);

        if (!brand) throw new Error(`Brand ${selectedBrand} not found`);

        const productLines = await brandService.getLinesByBrandId(brand.id);
        const lineAutocomplete = await brandInventoryIntegration.getLineAutocomplete(
          selectedBrand,
          'Kol'
        );

        return {
          brand,
          productLinesCount: productLines.length,
          sampleLines: productLines.slice(0, 5).map(l => l.name),
          autocompleteLines: lineAutocomplete.map(l => l.name),
        };
      });
      testResult.steps.push(step2);

      // Step 3: Product Validation & Recommendations
      const step3 = await this.executeStep('Product Validation & Recommendations', async () => {
        const testProducts = [
          { brand: 'Wella', line: 'Koleston Perfect', type: 'Tinte', shade: '7.0' },
          { brand: 'Wella', line: 'Color Perfect', type: 'Oxidante', shade: '20 vol' },
          { brand: 'Wella', line: 'Invalid Line', type: 'Tinte', shade: '5.0' }, // Should fail
        ];

        const validationResults = [];
        for (const product of testProducts) {
          const validation = await brandInventoryIntegration.validateProductBrand(product);
          validationResults.push({
            product: `${product.brand} ${product.line} ${product.shade}`,
            isValid: validation.isValid,
            suggestions: validation.suggestions?.length || 0,
            warnings: validation.warnings?.length || 0,
          });
        }

        return {
          validationResults,
          validProducts: validationResults.filter(v => v.isValid).length,
          invalidProducts: validationResults.filter(v => !v.isValid).length,
        };
      });
      testResult.steps.push(step3);

      // Step 4: Brand Intelligence Integration
      const step4 = await this.executeStep('Brand Intelligence Integration', async () => {
        const productNames = [
          'Wella Koleston Perfect 7.0',
          'Wella Color Perfect Developer 20 vol',
          "L'Oréal Majirel 6.35",
        ];

        const context = await BrandContextEnhancer.generateBrandContext(productNames);

        return {
          context,
          brandsDetected: context?.detectedBrands?.length || 0,
          recommendationsGenerated: context?.recommendations?.length || 0,
          terminologyProvided: context?.terminology?.length || 0,
          hasWellaContext: context?.detectedBrands?.includes('Wella Professionals') || false,
          hasLorealContext: context?.detectedBrands?.includes("L'Oréal Professionnel") || false,
        };
      });
      testResult.steps.push(step4);

      // Validate flow success
      const searchWorked = step1.result.searchResults.every(r => r.brandsFound > 0);
      const brandDetailsRetrieved = step2.result.productLinesCount > 0;
      const validationWorked = step3.result.validProducts > 0 && step3.result.invalidProducts > 0; // Mixed results expected
      const intelligenceWorked =
        step4.result.brandsDetected > 0 && step4.result.recommendationsGenerated > 0;

      testResult.passed =
        searchWorked && brandDetailsRetrieved && validationWorked && intelligenceWorked;
      testResult.totalDuration = Date.now() - testResult.startTime;

      console.log(
        `${testResult.passed ? '✅' : '❌'} Brand Discovery Flow: ${testResult.passed ? 'PASSED' : 'FAILED'}`
      );
    } catch (error) {
      testResult.passed = false;
      testResult.error = error.message;
      testResult.totalDuration = Date.now() - testResult.startTime;
      console.log(`❌ Brand Discovery Flow Failed: ${error.message}`);
    }

    return testResult;
  }

  /**
   * Test inventory integration workflow
   */
  async testInventoryIntegrationFlow(): Promise<E2ETestResult> {
    console.log('📦 Testing Inventory Integration Flow...\n');

    const testResult: E2ETestResult = {
      testName: 'Inventory Integration Flow',
      steps: [],
      passed: false,
      totalDuration: 0,
      startTime: Date.now(),
    };

    try {
      // Step 1: Inventory Product Creation
      const step1 = await this.executeStep('Inventory Product Creation', async () => {
        const newProducts = [
          {
            name: 'Wella Koleston Perfect 7.0',
            brand: 'Wella Professionals',
            line: 'Koleston Perfect',
            type: 'Tinte Permanente',
            shade: '7.0',
            stock: 5,
            price: 15.5,
          },
          {
            name: "L'Oréal Majirel 6.35",
            brand: "L'Oréal Professionnel",
            line: 'Majirel',
            type: 'Tinte Permanente',
            shade: '6.35',
            stock: 3,
            price: 16.0,
          },
        ];

        const creationResults = [];
        for (const product of newProducts) {
          // Simulate product creation with validation
          const validation = await brandInventoryIntegration.validateProductBrand({
            brand: product.brand.split(' ')[0], // First word
            line: product.line,
            type: product.type,
            shade: product.shade,
          });

          creationResults.push({
            product: product.name,
            brandValidated: validation.isValid,
            suggestions: validation.suggestions?.length || 0,
            created: validation.isValid, // Only create if valid
          });
        }

        return { creationResults };
      });
      testResult.steps.push(step1);

      // Step 2: AI Formula to Inventory Matching
      const step2 = await this.executeStep('AI Formula to Inventory Matching', async () => {
        const aiGeneratedFormula = {
          products: [
            'Wella Koleston Perfect 7.0 Light Blonde',
            'Wella Color Perfect Developer 20 volume',
            'Olaplex No. 1 Bond Multiplier',
            'Some Unknown Product Brand XYZ',
          ],
        };

        const matchingResults = [];
        for (const aiProduct of aiGeneratedFormula.products) {
          const productInfo = this.parseProductName(aiProduct);
          const validation = await brandInventoryIntegration.validateProductBrand(productInfo);

          // Simulate inventory lookup
          const inventoryMatch = step1.result.creationResults.find(
            created =>
              created.product.toLowerCase().includes(productInfo.shade.toLowerCase()) &&
              created.product.toLowerCase().includes(productInfo.brand.toLowerCase())
          );

          matchingResults.push({
            aiProduct,
            brandRecognized: validation.isValid,
            inventoryMatch: !!inventoryMatch,
            confidence: validation.isValid ? 0.9 : 0.3,
          });
        }

        return { matchingResults };
      });
      testResult.steps.push(step2);

      // Step 3: Stock Management & Consumption
      const step3 = await this.executeStep('Stock Management & Consumption', async () => {
        const usedProducts = [
          { product: 'Wella Koleston Perfect 7.0', quantityUsed: 30, unit: 'ml' },
          { product: "L'Oréal Majirel 6.35", quantityUsed: 15, unit: 'ml' },
        ];

        const consumptionResults = [];
        for (const usage of usedProducts) {
          // Simulate stock consumption
          const inventoryItem = step1.result.creationResults.find(
            item => item.product === usage.product
          );

          if (inventoryItem && inventoryItem.created) {
            consumptionResults.push({
              product: usage.product,
              consumed: usage.quantityUsed,
              unit: usage.unit,
              stockUpdated: true,
              remainingStock: 4, // Simulated remaining stock
            });
          }
        }

        return { consumptionResults };
      });
      testResult.steps.push(step3);

      // Step 4: Reporting & Analytics
      const step4 = await this.executeStep('Reporting & Analytics', async () => {
        const report = {
          totalProductsCreated: step1.result.creationResults.filter(r => r.created).length,
          aiMatchingAccuracy:
            step2.result.matchingResults.filter(r => r.brandRecognized).length /
            step2.result.matchingResults.length,
          stockMovements: step3.result.consumptionResults.length,
          brandCoveragePercentage: 85, // Simulated
          inventoryValue: 157.5, // Simulated
        };

        return report;
      });
      testResult.steps.push(step4);

      // Validate flow success
      const productCreationWorked = step1.result.creationResults.some(r => r.created);
      const aiMatchingWorked = step2.result.matchingResults.some(r => r.brandRecognized);
      const stockManagementWorked = step3.result.consumptionResults.length > 0;
      const reportingWorked = step4.result.aiMatchingAccuracy > 0.5;

      testResult.passed =
        productCreationWorked && aiMatchingWorked && stockManagementWorked && reportingWorked;
      testResult.totalDuration = Date.now() - testResult.startTime;

      console.log(
        `${testResult.passed ? '✅' : '❌'} Inventory Integration Flow: ${testResult.passed ? 'PASSED' : 'FAILED'}`
      );
    } catch (error) {
      testResult.passed = false;
      testResult.error = error.message;
      testResult.totalDuration = Date.now() - testResult.startTime;
      console.log(`❌ Inventory Integration Flow Failed: ${error.message}`);
    }

    return testResult;
  }

  /**
   * Test complete transformation validation
   */
  async testCompleteTransformationValidation(): Promise<TransformationTestReport> {
    console.log('🚀 Testing Complete Transformation Validation...\n');

    const report: TransformationTestReport = {
      timestamp: new Date().toISOString(),
      flows: [],
      summary: {
        totalFlows: 0,
        passedFlows: 0,
        totalDuration: 0,
      },
      transformation_status: 'testing',
    };

    const startTime = Date.now();

    try {
      // Run all E2E flows
      const flows = [
        await this.testCompleteServiceFlow(),
        await this.testBrandDiscoveryFlow(),
        await this.testInventoryIntegrationFlow(),
      ];

      report.flows = flows;
      report.summary.totalFlows = flows.length;
      report.summary.passedFlows = flows.filter(f => f.passed).length;
      report.summary.totalDuration = Date.now() - startTime;

      // Determine transformation status
      const allFlowsPassed = flows.every(f => f.passed);
      const criticalFlowsPassed = flows
        .filter(
          f => f.testName === 'Complete Service Flow' || f.testName === 'Brand Discovery Flow'
        )
        .every(f => f.passed);

      if (allFlowsPassed) {
        report.transformation_status = 'successful';
      } else if (criticalFlowsPassed) {
        report.transformation_status = 'partial';
      } else {
        report.transformation_status = 'failed';
      }

      console.log('\n' + '='.repeat(60));
      console.log('📊 TRANSFORMATION VALIDATION SUMMARY');
      console.log('='.repeat(60));
      console.log(`Status: ${report.transformation_status.toUpperCase()}`);
      console.log(`Flows Passed: ${report.summary.passedFlows}/${report.summary.totalFlows}`);
      console.log(`Total Duration: ${report.summary.totalDuration}ms`);

      flows.forEach(flow => {
        console.log(`\n${flow.passed ? '✅' : '❌'} ${flow.testName}`);
        console.log(`   Duration: ${flow.totalDuration}ms`);
        console.log(
          `   Steps: ${flow.steps.filter(s => s.passed).length}/${flow.steps.length} passed`
        );

        if (flow.error) {
          console.log(`   Error: ${flow.error}`);
        }
      });
    } catch (error) {
      report.transformation_status = 'failed';
      report.error = error.message;
      console.log(`❌ Transformation validation failed: ${error.message}`);
    }

    return report;
  }

  private async executeStep(stepName: string, operation: () => Promise<any>): Promise<E2EStep> {
    const startTime = Date.now();

    try {
      console.log(`   🔄 ${stepName}...`);
      const result = await operation();
      const duration = Date.now() - startTime;

      console.log(`   ✅ ${stepName} completed (${duration}ms)`);

      return {
        stepName,
        passed: true,
        duration,
        result,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`   ❌ ${stepName} failed: ${error.message}`);

      return {
        stepName,
        passed: false,
        duration,
        error: error.message,
      };
    }
  }

  private parseProductName(productName: string): {
    brand: string;
    line: string;
    type: string;
    shade: string;
  } {
    // Simple product name parser for testing
    const parts = productName.split(' ');

    if (productName.toLowerCase().includes('wella')) {
      return {
        brand: 'Wella',
        line: 'Koleston Perfect',
        type: 'Tinte',
        shade: parts.find(p => p.match(/\d/)) || '7.0',
      };
    }

    if (
      productName.toLowerCase().includes('oreal') ||
      productName.toLowerCase().includes("l'oreal")
    ) {
      return {
        brand: "L'Oreal",
        line: 'Majirel',
        type: 'Tinte',
        shade: parts.find(p => p.match(/\d/)) || '6.35',
      };
    }

    return {
      brand: parts[0] || 'Unknown',
      line: parts[1] || 'Unknown',
      type: 'Tinte',
      shade: parts.find(p => p.match(/\d/)) || '0',
    };
  }
}

// Interfaces
interface E2EStep {
  stepName: string;
  passed: boolean;
  duration: number;
  result?: any;
  error?: string;
}

interface E2ETestResult {
  testName: string;
  steps: E2EStep[];
  passed: boolean;
  totalDuration: number;
  startTime: number;
  error?: string;
}

interface TransformationTestReport {
  timestamp: string;
  flows: E2ETestResult[];
  summary: {
    totalFlows: number;
    passedFlows: number;
    totalDuration: number;
  };
  transformation_status: 'testing' | 'successful' | 'partial' | 'failed';
  error?: string;
}

// Export for CLI usage
export const runE2ETests = async (): Promise<TransformationTestReport> => {
  const tester = new E2EUserFlowTests();
  return await tester.testCompleteTransformationValidation();
};
