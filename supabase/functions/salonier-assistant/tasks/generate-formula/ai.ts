// <PERSON>les building the prompt and calling the OpenAI API.
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { logger } from '../../src/utils/security.ts';
import { retryWithBackoff, calculateCost } from '../../src/utils/helpers.ts';
import { getBrandExpertise, getBrandExampleFormulas } from '../../src/utils/brand-expertise.ts';
import { validateColorProcess, getColorimetryInstructions, ColorProcess } from '../../src/utils/colorimetry-rules.ts';

export async function generateAiFormula(
  payload: any,
  salonId: string,
  supabase: SupabaseClient,
  openaiApiKey: string
) {
    const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig, inventoryLevel } = payload;

    // Determinar configuración regional
    const volumeUnit = regionalConfig?.volumeUnit || 'ml';
    const weightUnit = regionalConfig?.weightUnit || 'g';
    const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
    const colorTerm = regionalConfig?.colorTerminology || 'tinte';
    const maxDeveloperVolume = regionalConfig?.maxDeveloperVolume || 40;
    const currencySymbol = regionalConfig?.currencySymbol || '€';
    const measurementSystem = regionalConfig?.measurementSystem || 'metric';
    const decimalSeparator = regionalConfig?.decimalSeparator || ',';

    // Detectar idioma
    const isEnglish = regionalConfig?.language === 'en';

    // Extraer técnica seleccionada
    const selectedTechnique = desiredResult?.general?.technique || 'full_color';
    const customTechnique = desiredResult?.general?.customTechnique;

      // Obtener instrucciones específicas por técnica
  const getTechniqueInstructions = () => {
    if (selectedTechnique === 'custom' && customTechnique) {
      return isEnglish
        ? `Custom technique described as: "${customTechnique}". Adapt the formula accordingly.`
        : `Técnica personalizada descrita como: "${customTechnique}". Adapta la fórmula según corresponda.`;
    }

    const techniquePrompts = {
      full_color: isEnglish
        ? `- Single formula for complete coverage\n- Ensure uniform application from roots to ends\n- Consider natural regrowth for maintenance`
        : `- Fórmula única para cobertura completa\n- Asegurar aplicación uniforme de raíces a puntas\n- Considerar crecimiento natural para mantenimiento`,

      highlights: isEnglish
        ? `- Use foil technique for precision\n- Create multiple formulas if needed (base + highlights)\n- Thicker consistency to prevent bleeding\n- Maximum ${developerTerm} 30 vol for highlights\n- Consider placement pattern (full head, partial, face-framing)`
        : `- Usar técnica con papel aluminio para precisión\n- Crear múltiples fórmulas si es necesario (base + mechas)\n- Consistencia más espesa para evitar sangrado\n- Máximo ${developerTerm} 30 vol para mechas\n- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,

      balayage: isEnglish
        ? `- Free-hand painting technique\n- Gradual transition from dark to light\n- Use lower ${developerTerm} volume (20 vol max recommended)\n- Creamy consistency for controlled application\n- Natural, sun-kissed effect\n- Consider using clay or cream lightener`
        : `- Técnica de pintado a mano alzada\n- Transición gradual de oscuro a claro\n- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)\n- Consistencia cremosa para aplicación controlada\n- Efecto natural, como besado por el sol\n- Considerar usar decolorante en crema o arcilla`,

      ombre: isEnglish
        ? `- Clear horizontal gradient\n- Multiple formulas for different zones\n- Seamless blending is crucial\n- Start application from ends, work up\n- Consider toner for perfect transition`
        : `- Degradado horizontal claro\n- Múltiples fórmulas para diferentes zonas\n- La mezcla perfecta es crucial\n- Comenzar aplicación desde puntas, subir gradualmente\n- Considerar toner para transición perfecta`,

      babylights: isEnglish
        ? `- Ultra-fine sections (max 1-2mm)\n- Low ${developerTerm} volume (10-20 vol)\n- Natural, subtle effect\n- Longer processing time due to fine sections\n- Mimic natural sun-lightened strands`
        : `- Secciones ultrafinas (máx 1-2mm)\n- ${developerTerm} de bajo volumen (10-20 vol)\n- Efecto natural y sutil\n- Mayor tiempo de procesamiento por secciones finas\n- Imitar mechones aclarados naturalmente por el sol`,

      color_correction: isEnglish
        ? `- Analyze underlying pigments carefully\n- May need pre-pigmentation or color removal\n- Multiple steps might be required\n- Use appropriate neutralizing tones\n- Consider strand test mandatory\n- Document each step for future reference`
        : `- Analizar cuidadosamente pigmentos subyacentes\n- Puede necesitar pre-pigmentación o remoción de color\n- Pueden requerirse múltiples pasos\n- Usar tonos neutralizantes apropiados\n- Considerar prueba de mechón obligatoria\n- Documentar cada paso para referencia futura`,

      foilyage: isEnglish
        ? `- Combine foil and balayage techniques\n- Use foil for stronger lift at top sections\n- Free-hand painting for natural flow\n- Varying ${developerTerm} volumes by section\n- Creates maximum dimension`
        : `- Combinar técnicas de aluminio y balayage\n- Usar aluminio para mayor aclarado en secciones superiores\n- Pintado a mano para flujo natural\n- Variar volúmenes de ${developerTerm} por sección\n- Crea máxima dimensión`,

      money_piece: isEnglish
        ? `- Focus on face-framing sections\n- High contrast for impact\n- Protect surrounding hair\n- Consider client's skin tone\n- Easy maintenance placement`
        : `- Enfoque en secciones que enmarcan el rostro\n- Alto contraste para impacto\n- Proteger cabello circundante\n- Considerar tono de piel del cliente\n- Colocación de fácil mantenimiento`,

      chunky_highlights: isEnglish
        ? `- Thick sections (1cm or more)\n- Bold contrast recommended\n- Strategic placement for maximum effect\n- Higher ${developerTerm} volume acceptable (up to 40 vol)\n- 90s-inspired dramatic look`
        : `- Secciones gruesas (1cm o más)\n- Contraste audaz recomendado\n- Colocación estratégica para máximo efecto\n- ${developerTerm} de mayor volumen aceptable (hasta 40 vol)\n- Look dramático inspirado en los 90s`,

      reverse_balayage: isEnglish
        ? `- Add depth to over-lightened hair\n- Use demi-permanent or semi-permanent color\n- Focus on roots and mid-lengths\n- Create natural shadow root\n- Low ${developerTerm} volume or no ${developerTerm}`
        : `- Agregar profundidad a cabello sobre-aclarado\n- Usar color demi-permanente o semi-permanente\n- Enfoque en raíces y medios\n- Crear raíz sombreada natural\n- ${developerTerm} de bajo volumen o sin ${developerTerm}`,
    };

    return (
      techniquePrompts[selectedTechnique as keyof typeof techniquePrompts] ||
      techniquePrompts.full_color
    );
  };

    const techniqueInstructions = getTechniqueInstructions();
    const techniqueName = 
    selectedTechnique === 'custom' && customTechnique
      ? customTechnique
      : {
          full_color: isEnglish ? 'Full Color' : 'Tinte Completo',
          highlights: isEnglish ? 'Highlights' : 'Mechas',
          balayage: 'Balayage',
          ombre: 'Ombré',
          babylights: 'Babylights',
          color_correction: isEnglish ? 'Color Correction' : 'Corrección de Color',
          foilyage: 'Foilyage',
          money_piece: 'Money Piece',
          chunky_highlights: isEnglish ? 'Chunky Highlights' : 'Mechas Gruesas',
          reverse_balayage: 'Reverse Balayage',
        }[selectedTechnique] || (isEnglish ? 'Full Color' : 'Tinte Completo');


    // Validate color process according to colorimetry principles
    let colorimetryInstructions = '';
    let colorimetryWarnings: string[] = [];

    try {
        // Extract current and desired levels from diagnosis
        const currentLevel = 
        diagnosis?.averageDepthLevel || 
        diagnosis?.level || 
        diagnosis?.zoneAnalysis?.roots?.depth || 
        5; // Default to level 5 if not found

        const desiredLevel = 
        desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || currentLevel; // Default to current if not specified

        // Determine current hair state
        const currentState = 
        diagnosis?.detectedChemicalProcess?.toLowerCase().includes('color') || 
        diagnosis?.state?.toLowerCase().includes('teñido') || 
        diagnosis?.state?.toLowerCase().includes('colored')
            ? 'colored'
            : diagnosis?.detectedChemicalProcess?.toLowerCase().includes('bleach') || 
            diagnosis?.state?.toLowerCase().includes('decolorado') || 
            diagnosis?.state?.toLowerCase().includes('bleached')
            ? 'bleached'
            : 'natural';

        const colorProcess: ColorProcess = {
        currentLevel: Math.round(currentLevel),
        desiredLevel: Math.round(desiredLevel),
        currentState: currentState as 'natural' | 'colored' | 'bleached',
        hasMetallicSalts: diagnosis?.detectedRisks?.metallic || false,
        hasHenna: diagnosis?.detectedRisks?.henna || false,
        };

        // Validating color process

        const validation = validateColorProcess(colorProcess, maxDeveloperVolume);
        colorimetryInstructions = getColorimetryInstructions(validation, isEnglish ? 'en' : 'es');
        colorimetryWarnings = validation.warnings;

        // Colorimetry validation completed
    } catch (error) {
        // Colorimetry validation failed
        // Continue without colorimetry validation if there's an error
    }

    // JSON structure definition for the prompt
    const jsonStructure = `interface ProductMix {\n  productId: string;\n  productName: string;\n  brand: string;\n  line?: string;\n  type: string;\n  shade?: string;\n  quantity: number;\n  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';\n}\n\ninterface ApplicationTechnique {\n  name: string;\n  description: string;\n}\n
interface FormulationStep {\n  stepNumber: number;\n  stepTitle: string;\n  mix?: ProductMix[];\n  technique?: ApplicationTechnique;\n  instructions: string;\n  processingTime?: number;\n}\n\ninterface Formulation {\n  formulaTitle: string;\n  summary: string;\n  steps: FormulationStep[];\n  totalTime: number;\n  warnings?: string[];\n}`;

    let brandExpertiseSection = '';
    let brandExamples = '';

    if (brand) {
        try {
            const brandExpertise = getBrandExpertise(brand, line || '', isEnglish ? 'en' : 'es');
            brandExamples = getBrandExampleFormulas(
                brand,
                line || '',
                selectedTechnique,
                isEnglish ? 'en' : 'es'
            );

            if (isEnglish) {
                brandExpertiseSection = `\n**BRAND EXPERTISE - ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''} SPECIALIST:**\n\n${brandExpertise.personality}\n\n**Technical Knowledge:**\n${brandExpertise.technicalKnowledge}\n\n**Brand-Specific Rules:**\n${brandExpertise.specificRules}\n\n**Mixing Ratios:**\n${brandExpertise.mixingRatios}\n\n**Special Products & Additives:**\n${brandExpertise.specialProducts}\n\n**Processing Times:**\n${brandExpertise.processingTimes}\n\n**Professional Tips:**\n${brandExpertise.proTips}\n\n**Nomenclature System:**\n${brandExpertise.nomenclature}\n\n${brandExamples ? `**Reference Formulas:**\n${brandExamples}` : ''}\n\nIMPORTANT: As a ${brand} expert, you MUST follow their specific standards and recommendations exactly.\n`;
            } else {
                brandExpertiseSection = `\n**EXPERTISE DE MARCA - ESPECIALISTA ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''}:**\n\n${brandExpertise.personality}\n\n**Conocimiento Técnico:**\n${brandExpertise.technicalKnowledge}\n\n**Reglas Específicas de la Marca:**\n${brandExpertise.specificRules}\n\n**Proporciones de Mezcla:**\n${brandExpertise.mixingRatios}\n\n**Productos Especiales y Aditivos:**\n${brandExpertise.specialProducts}\n\n**Tiempos de Procesamiento:**\n${brandExpertise.processingTimes}\n\n**Tips Profesionales:**\n${brandExpertise.proTips}\n\n**Sistema de Nomenclatura:**\n${brandExpertise.nomenclature}\n\n${brandExamples ? `**Fórmulas de Referencia:**\n${brandExamples}` : ''}\n\nIMPORTANTE: Como experto en ${brand}, DEBES seguir exactamente sus estándares y recomendaciones específicas.\n`;
            }
        } catch (error) {
        }
    }

    let inventoryContext = '';
    if (inventoryLevel && inventoryLevel !== 'solo-formulas' && salonId) {
        try {
            const { data: products, error } = await supabase
                .from('products')
                .select('brand, line, type, shade, display_name, stock_ml')
                .eq('salon_id', salonId)
                .eq('is_active', true)
                .order('brand', { ascending: true })
                .order('shade', { ascending: true });

            if (!error && products && products.length > 0) {
                const productsByType = products.reduce((acc: any, product) => {
                    const type = product.type || 'otro';
                    if (!acc[type]) acc[type] = [];
                    const productDesc = 
                        product.display_name || 
                        `${product.brand} ${product.line || ''} ${product.shade || ''}`.trim();
                    acc[type].push(productDesc);
                    return acc;
                }, {});

                const inventoryLines = [];

                if (isEnglish) {
                    inventoryLines.push('\n**CRITICAL INVENTORY INSTRUCTIONS:**');
                    inventoryLines.push('1. You MUST use the EXACT product names below, including specific shade/tone numbers');
                    inventoryLines.push('2. CORRECT example: "Wella Illumina Color 7.81" NOT "Illumina Color"');
                    inventoryLines.push('3. For each product in your formula, include: brand, line, shade/volume, and quantity');
                    inventoryLines.push('');
                    inventoryLines.push('**AVAILABLE PRODUCTS (USE THESE EXACT NAMES):**');
                } else {
                    inventoryLines.push('\n**INSTRUCCIONES CRÍTICAS DE INVENTARIO:**');
                    inventoryLines.push('1. DEBES usar los nombres EXACTOS de productos abajo, incluyendo números específicos de tono/matiz');
                    inventoryLines.push('2. Ejemplo CORRECTO: "Wella Illumina Color 7.81" NO "Illumina Color"');
                    inventoryLines.push('3. Para cada producto en tu fórmula, incluye: marca, línea, tono/volumen y cantidad');
                    inventoryLines.push('');
                    inventoryLines.push('**PRODUCTOS DISPONIBLES (USA ESTOS NOMBRES EXACTOS):**');
                }

                Object.entries(productsByType).forEach(([type, productList]: [string, any]) => {
                    const typeLabel = 
                        { 
                        color: isEnglish ? 'Hair Colors (WITH EXACT SHADE)' : 'Tintes (CON TONO EXACTO)',
                        developer: isEnglish
                            ? 'Developers (WITH EXACT VOLUME)'
                            : 'Oxidantes (CON VOLUMEN EXACTO)',
                        bleach: isEnglish ? 'Bleaching Products' : 'Decolorantes',
                        treatment: isEnglish ? 'Treatments' : 'Tratamientos',
                        toner: isEnglish ? 'Toners' : 'Matizadores',
                        additive: isEnglish ? 'Additives' : 'Aditivos',
                        pre_pigment: isEnglish ? 'Pre-pigmentation' : 'Pre-pigmentación',
                        other: isEnglish ? 'Other' : 'Otros',
                        }[type] || type;

                    inventoryLines.push(`\n${typeLabel}:`);
                    productList.forEach((product: string) => {
                        inventoryLines.push(`  • ${product}`);
                    });
                });

                inventoryLines.push('');

                if (brand) {
                    if (isEnglish) {
                        inventoryLines.push(`**${brand.toUpperCase()} INVENTORY INTEGRATION:**`);
                        inventoryLines.push(`- As a ${brand} expert, select the most appropriate products from the available inventory`);
                        inventoryLines.push(`- Apply ${brand}'s specific mixing ratios and recommendations`);
                        inventoryLines.push(`- If a recommended ${brand} product is not available, suggest the closest alternative and mark it clearly`);
                    } else {
                        inventoryLines.push(`**INTEGRACIÓN DE INVENTARIO ${brand.toUpperCase()}:**`);
                        inventoryLines.push(`- Como experto en ${brand}, selecciona los productos más apropiados del inventario disponible`);
                        inventoryLines.push(`- Aplica las proporciones de mezcla y recomendaciones específicas de ${brand}`);
                        inventoryLines.push(`- Si un producto recomendado de ${brand} no está disponible, sugiere la alternativa más cercana y márcalo claramente`);
                    }
                    inventoryLines.push('');
                }

                inventoryLines.push(
                    isEnglish
                        ? 'IMPORTANT: Products NOT listed above must be marked as "(Not in stock)" in your formula.'
                        : 'IMPORTANTE: Los productos NO listados arriba deben marcarse como "(No en stock)" en tu fórmula.'
                );

                inventoryContext = inventoryLines.join('\n');
            }
        } catch (error) {
        }
    }

    const prompt = isEnglish
    ? `You are "Salonier Assistant", a world-renowned Master Colorist expert, specialized in creating precise and safe color formulas.\n\n**MISSION:**\nGenerate a detailed professional coloring formula based on the client's diagnosis, desired color, and salon preferences.\n\n**GOLDEN RULES:**\n1. **MANDATORY OUTPUT FORMAT:** Your response MUST be ONLY a valid JSON object. No introductory text, no markdown, no explanations. IMPORTANT: Do NOT wrap the JSON in code blocks (\\\\
\\\\
json) or any markdown formatting. The response must start directly with { and end with }. The JSON must strictly comply with this TypeScript interface:\n   \\\\
   ${jsonStructure}
   \\\\
2. **EXPERT THINKING:** Consider underlying pigments. Adapt formula to ${brand} ${line}. Be explicit about mixing ratios.\n3. **SAFETY FIRST:** Add clear warnings if risky.\n4. **PROFESSIONAL LANGUAGE:** Use colorist terminology.\n5. **PRODUCT SPECIFICITY (CRITICAL):** You MUST be specific with EVERY product:\n   - ALWAYS include the exact shade/tone number for colors (e.g., "7.81", "9.03")\n   - ALWAYS include the exact volume for developers (e.g., "20 vol", "30 vol")\n   - NEVER use generic names like "Illumina Color" without a shade\n   - NEVER use "unknown", "generic", or placeholder brand names\n   - Each product in ProductMix MUST include ALL these fields:\n     * brand: Always use "${brand}" (NEVER "unknown" or "generic")\n     * line: Use "${line}" if specified\n     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.\n     * shade: The SPECIFIC shade/tone/volume number (MANDATORY)\n     * productName: Full product name including shade (e.g., "Wella Illumina Color 7.81")\n   - EXAMPLE CORRECT: {"productName": "Wella Illumina Color 7.81", "brand": "Wella", "shade": "7.81"}\n   - EXAMPLE WRONG: {"productName": "unknown unknown 7/1", "brand": "unknown"} ← ABSOLUTELY FORBIDDEN\n\n   **🚨 CRITICAL ANTI-UNKNOWN VALIDATION:**\n   - If your response contains "unknown", "Unknown", "generic" or any variation: AUTOMATIC REJECTION\n   - EVERY product MUST have a real brand name from ${brand} ${line}\n   - Use SPECIFIC shade numbers like "7.81", "9.03", "10.16" - never generic numbers\n   - For developers: "Wella Welloxon Perfect 20 vol" NOT "Oxidante 20 vol"\n   - VALIDATION CHECKPOINT: Response scanned for forbidden words - violations = FAILURE\n6. **COLORIMETRY PRINCIPLES (MANDATORY):**\n   - COLOR CANNOT LIFT COLOR: If hair has artificial color, you MUST include color removal before lightening\n   - DEVELOPER VOLUME RULES:\n     * Darkening only: Use 10 volume developer or demi-permanent activator\n     * Gray coverage/same level: Use 20 volume\n     * Lifting 1-2 levels: Use 20-30 volume\n     * Lifting 3+ levels: Requires bleaching, not color\n   - PRE-PIGMENTATION: Required when going 3+ levels darker to avoid green/muddy results\n   - If the process is not technically viable, include the necessary preparatory steps\n**CONTEXT:**\n* **Client Diagnosis:** ${JSON.stringify(diagnosis)}\n* **Desired Color:** ${JSON.stringify(desiredResult)}\n* **Products:** ${brand} ${line}\n* **Regional Config:** ${measurementSystem} system, ${volumeUnit}/${weightUnit} units, "${developerTerm}" for developer, "${colorTerm}" for color\n* **Technique Requirements:** ${techniqueInstructions}\n${colorimetryInstructions}\n${brandExpertiseSection}\n${inventoryContext}\n\nGenerate the JSON formula now.`
    : `Eres "Salonier Assistant", un Maestro Colorista experto de renombre mundial, especializado en crear fórmulas de coloración precisas y seguras.\n\n**MISIÓN:**\nGenerar una fórmula de coloración profesional detallada basada en el diagnóstico del cliente, color deseado y preferencias del salón.\n\n**REGLAS DE ORO:**\n1. **FORMATO OBLIGATORIO:** Tu respuesta DEBE ser SOLO un objeto JSON válido. Sin texto introductorio, sin markdown, sin explicaciones. IMPORTANTE: NO envuelvas el JSON en bloques de código (\\\\
\\\\
json) ni ningún formato markdown. La respuesta debe comenzar directamente con { y terminar con }. El JSON debe cumplir estrictamente con esta interfaz TypeScript:\n   \\\\
   ${jsonStructure}
   \\\\
2. **PENSAMIENTO EXPERTO:** Considera pigmentos subyacentes. Adapta fórmula a ${brand} ${line}. Sé explícito con proporciones.\n3. **SEGURIDAD PRIMERO:** Añade advertencias claras si hay riesgo.\n4. **LENGUAJE PROFESIONAL:** Usa terminología de colorista.\n5. **ESPECIFICIDAD DE PRODUCTOS (CRÍTICO):** DEBES ser específico con CADA producto:\n   - SIEMPRE incluye el número exacto de tono/matiz para tintes (ej: "7.81", "9.60")\n   - SIEMPRE incluye el volumen exacto para oxidantes (ej: "20 vol", "30 vol")\n   - NUNCA uses nombres genéricos como "Illumina Color" sin un tono\n   - NUNCA uses "unknown", "genérico", o nombres de marca placeholder\n   - Cada producto en ProductMix DEBE incluir TODOS estos campos:\n     * brand: Siempre usa "${brand}" (NUNCA "unknown" o "genérico")\n     * line: Usa "${line}" si está especificada\n     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.\n     * shade: El número ESPECÍFICO de tono/matiz/volumen (OBLIGATORIO)\n     * productName: Nombre completo del producto incluyendo tono (ej: "Wella Illumina Color 7.81")\n   - EJEMPLO CORRECTO: {"productName": "Wella Illumina Color 7.81", "brand": "Wella", "shade": "7.81"}\n   - EJEMPLO INCORRECTO: {"productName": "unknown unknown 7/1", "brand": "unknown"} ← ABSOLUTAMENTE PROHIBIDO\n\n   **🚨 CRITICAL ANTI-UNKNOWN VALIDATION:**\n   - Si tu respuesta contiene "unknown", "Unknown", "genérico" o cualquier variación: RECHAZO AUTOMÁTICO\n   - TODOS los productos DEBEN tener una marca real de ${brand} ${line}\n   - Usa números de tono ESPECÍFICOS como "7.81", "9.03", "10.16" - nunca números genéricos\n   - Para oxidantes: "Wella Welloxon Perfect 20 vol" NO "Oxidante 20 vol"\n   - CHECKPOINT DE VALIDACIÓN: Respuesta escaneada por palabras prohibidas - violaciones = FALLO\n6. **PRINCIPIOS DE COLORIMETRÍA (OBLIGATORIO):**\n   - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial, DEBES incluir decapado antes de aclarar\n   - REGLAS DE VOLUMEN DE OXIDANTE:\n     * Solo oscurecer: Usa oxidante 10 volúmenes o activador demipermanente\n     * Cobertura canas/mismo nivel: Usa 20 volúmenes\n     * Aclarar 1-2 niveles: Usa 20-30 volúmenes\n     * Aclarar 3+ niveles: Requiere decoloración, no tinte\n   - PRE-PIGMENTACIÓN: Requerida al oscurecer 3+ niveles para evitar resultados verdes/cenizos\n   - Si el proceso no es técnicamente viable, incluye los pasos preparatorios necesarios\n**CONTEXTO:**\n* **Diagnóstico Cliente:** ${JSON.stringify(diagnosis)}\n* **Color Deseado:** ${JSON.stringify(desiredResult)}\n* **Productos:** ${brand} ${line}\n* **Config Regional:** Sistema ${measurementSystem}, unidades ${volumeUnit}/${weightUnit}, "${developerTerm}" para oxidante, "${colorTerm}" para tinte\n* **Requisitos Técnica:** ${techniqueInstructions}\n${colorimetryInstructions}\n${brandExpertiseSection}\n${inventoryContext}\n\nGenera el JSON de la fórmula ahora.`;

    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content:
                regionalConfig?.language === 'en'
                  ? `You are "expert colorist" with 20 years of experience specializing in ${techniqueName}. ${brand ? `You are also a certified ${brand} ${line || ''} Technical Expert with deep knowledge of their specific products, formulation rules, and application techniques.` : ''} Always respond in English and use the terminology and units specified in the prompt. You understand the nuances of different application techniques and adapt formulas accordingly. Consider factors like hair porosity, elasticity, and previous chemical processes when creating formulas. ${brand ? `As a ${brand} specialist, you must follow their specific mixing ratios, processing times, and product recommendations exactly.` : ''}`
                  : `Eres un experto colorista con 20 años de experiencia especializado en ${techniqueName}. ${brand ? `También eres un Experto Técnico certificado en ${brand} ${line || ''} con conocimiento profundo de sus productos específicos, reglas de formulación y técnicas de aplicación.` : ''} Siempre responde en español y usa la terminología y unidades especificadas en el prompt. Comprendes los matices de diferentes técnicas de aplicación y adaptas las fórmulas según corresponda. Consideras factores como porosidad, elasticidad y procesos químicos previos al crear fórmulas. ${brand ? `Como especialista en ${brand}, debes seguir exactamente sus proporciones de mezcla específicas, tiempos de procesamiento y recomendaciones de productos.` : ''}`,
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 2000,
          temperature: 0,
          top_p: 1,
          seed: 42,
        }),
      });
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    return data;
}