/**
 * Formula Validator
 * Ensures compliance with colorimetry rules and brand-specific guidelines
 * Provides intelligent auto-correction for detected issues
 */

import { validateColorProcess, COLORIMETRY_PRINCIPLES } from './colorimetry-rules.ts';
import { getBrandValidationRules } from './brand-expertise.ts';
import {
  FormulaIngredient,
  FormulaStep,
  Formula,
  ProductAvailability,
  MixingComponent,
  MixingSolution,
  ReflectCompatibility,
  BrandMixingRules,
  ValidationResult,
  ValidationViolation,
  ProcessType,
} from './types.ts';
import { validateColorimetryRules } from './validators/colorimetry.validator.ts';
import { validateBrandRules } from './validators/brand.validator.ts';
import { validateSafety } from './validators/safety.validator.ts';
import { validateOptimization } from './validators/optimization.validator.ts';
import { validateProductAvailability, correctAvailabilityIssue } from './validators/availability.validator';
import { validateAndSuggestMixing, correctMixingIssue } from './validators/mixing.validator';
import { calculateExpectedVolume } from './helpers';

/**
 * Main Formula Validator Class
 */
export class FormulaValidator {
  private readonly brandRules: Record<string, any>;
  
  constructor(brand: string) {
    this.brandRules = getBrandValidationRules(brand);
  }

  /**
   * Validate complete formula against all rules
   */
  validate(formula: Formula): ValidationResult {
    const violations: ValidationViolation[] = [];
    
    // 1. Product availability validation (CRITICAL - before everything else)
    violations.push(...validateProductAvailability(formula));
    
    // 2. Core colorimetry validation
    violations.push(...validateColorimetryRules(formula));
    
    // 3. Brand-specific validation
    violations.push(...validateBrandRules(formula, this.brandRules));
    
    // 4. Safety validation
    violations.push(...validateSafety(formula));
    
    // 5. Performance optimization checks
    violations.push(...validateOptimization(formula));
    
    // 6. Intelligent mixing validation and suggestions
    violations.push(...validateAndSuggestMixing(formula, this.brandRules));

    const criticalViolations = violations.filter(v => v.severity === 'critical');
    const errorViolations = violations.filter(v => v.severity === 'error');
    
    const isValid = criticalViolations.length === 0 && errorViolations.length === 0;
    const riskLevel = this.calculateRiskLevel(violations);
    const confidence = this.calculateConfidence(formula, violations);
    
    let correctedFormula: Formula | undefined;
    if (!isValid && violations.some(v => v.autoFixAvailable)) {
      correctedFormula = this.autoCorrect(formula, violations);
    }

    return {
      isValid,
      violations,
      correctedFormula,
      riskLevel,
      confidence,
    };
  }

  /**
   * Validate core colorimetry principles
   */





  /**
   * Auto-correct formula based on violations
   */
  autoCorrect(formula: Formula, violations: ValidationViolation[]): Formula {
    let correctedFormula = { ...formula };
    
    for (const violation of violations.filter(v => v.autoFixAvailable)) {
      switch (violation.type) {
        case 'colorimetry':
          correctedFormula = this.correctColorimetryIssue(correctedFormula, violation);
          break;
        case 'brand':
          correctedFormula = this.correctBrandIssue(correctedFormula, violation);
          break;
        case 'safety':
          correctedFormula = this.correctSafetyIssue(correctedFormula, violation);
          break;
        case 'optimization':
          correctedFormula = this.correctOptimizationIssue(correctedFormula, violation);
          break;
        case 'availability':
          correctedFormula = correctAvailabilityIssue(correctedFormula, violation);
          break;
        case 'mixing':
          correctedFormula = correctMixingIssue(correctedFormula, violation);
          break;
      }
    }

    // Add correction warnings
    correctedFormula.warnings = [
      ...(correctedFormula.warnings || []),
      'Fórmula auto-corregida para cumplir estándares de seguridad y calidad'
    ];

    return correctedFormula;
  }

  /**
   * Correct colorimetry violations
   */
  private correctColorimetryIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Color no puede levantar color')) {
      // Add color removal step
      const colorRemovalStep: FormulaStep = {
        id: 'color-removal-auto',
        title: 'Decapado Previo (Auto-añadido)',
        ingredients: [
          {
            product: 'Decapante suave',
            amount: 50,
            unit: 'ml',
            type: 'additive',
          },
        ],
        processingTime: 20,
        instructions: 'Aplicar decapante según instrucciones del fabricante. Evaluar resultado antes de continuar.',
        type: ProcessType.COLOR_REMOVAL,
      };
      
      corrected.steps.unshift(colorRemovalStep);
      corrected.totalTime += 20;
    }

    if (violation.message.includes('Pre-pigmentación requerida')) {
      // Add pre-pigmentation step
      const prePigmentStep: FormulaStep = {
        id: 'pre-pigment-auto',
        title: 'Pre-pigmentación (Auto-añadida)',
        ingredients: [
          {
            product: 'Pigmento cálido',
            shade: `${Math.floor((formula.currentLevel + formula.desiredLevel) / 2)}/43`,
            amount: 30,
            unit: 'ml',
            type: 'color',
          },
          {
            product: 'Oxidante',
            amount: 30,
            unit: 'ml',
            type: 'developer',
            volume: 10,
          },
        ],
        processingTime: 15,
        instructions: 'Aplicar pre-pigmento uniformemente. No enjuagar.',
        type: ProcessType.PRE_PIGMENTATION,
      };

      // Insert before main color step
      const colorStepIndex = corrected.steps.findIndex(step => step.type === ProcessType.DIRECT_COLOR);
      if (colorStepIndex > -1) {
        corrected.steps.splice(colorStepIndex, 0, prePigmentStep);
      } else {
        corrected.steps.push(prePigmentStep);
      }
      
      corrected.totalTime += 15;
    }

    if (violation.message.includes('Volumen de oxidante demasiado alto')) {
      // Correct developer volume
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      if (step) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer) {
          const expectedVolume = calculateExpectedVolume(formula, step.type);
          developer.volume = expectedVolume;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct brand-specific violations
   */
  private correctBrandIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Proporción incorrecta')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        const colorAmount = step.ingredients
          .filter(ing => ing.type === 'color')
          .reduce((sum, ing) => sum + ing.amount, 0);
        
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer && colorAmount > 0) {
          // Adjust developer amount to match brand ratio
          developer.amount = colorAmount * this.brandRules.maxDeveloperRatio;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct safety violations
   */
  private correctSafetyIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('cabello decolorado')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer && developer.volume && developer.volume > 20) {
          developer.volume = 20;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct optimization violations
   */
  private correctOptimizationIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Cantidad muy pequeña')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        // Scale up all ingredients proportionally
        const scaleFactor = 30 / step.ingredients.reduce((sum, ing) => sum + ing.amount, 0);
        step.ingredients.forEach(ing => {
          ing.amount = Math.round(ing.amount * scaleFactor);
        });
      }
    }

    return corrected;
  }












  /**
   * Helper methods
   */
  private calculateRiskLevel(violations: ValidationViolation[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalViolations = violations.filter(v => v.severity === 'critical').length;
    const errorViolations = violations.filter(v => v.severity === 'error').length;
    const warningViolations = violations.filter(v => v.severity === 'warning').length;

    if (criticalViolations > 0) return 'critical';
    if (errorViolations > 2) return 'high';
    if (errorViolations > 0 || warningViolations > 3) return 'medium';
    return 'low';
  }

  private calculateConfidence(formula: Formula, violations: ValidationViolation[]): number {
    let baseConfidence = 100;
    
    violations.forEach(violation => {
      switch (violation.severity) {
        case 'critical':
          baseConfidence -= 30;
          break;
        case 'error':
          baseConfidence -= 15;
          break;
        case 'warning':
          baseConfidence -= 5;
          break;
      }
    });

    // Bonus for proper colorimetry process validation
    const colorProcess = validateColorProcess({
      currentLevel: formula.currentLevel,
      desiredLevel: formula.desiredLevel,
      currentState: formula.currentState,
    });

    if (colorProcess.isViable) {
      baseConfidence += 10;
    }

    return Math.max(0, Math.min(100, baseConfidence));
  }
}

/**
 * Convenience function for quick validation
 */
export function validateFormula(formula: Formula): ValidationResult {
  const validator = new FormulaValidator(formula.brand);
  return validator.validate(formula);
}

/**
 * Convenience function for auto-correction
 */
export function autoCorrectFormula(formula: Formula): Formula {
  const validator = new FormulaValidator(formula.brand);
  const validation = validator.validate(formula);
  
  if (!validation.isValid && validation.correctedFormula) {
    return validation.correctedFormula;
  }
  
  return formula;
}

/**
 * Validate formula with product availability check
 */
export function validateFormulaWithAvailability(
  formula: Formula, 
  availableProducts: ProductAvailability[]
): ValidationResult {
  const formulaWithAvailability = {
    ...formula,
    availableProducts,
  };
  
  const validator = new FormulaValidator(formula.brand);
  return validator.validate(formulaWithAvailability);
}

/**
 * Generate product availability suggestions for impossible formulas
 */
export function generateAvailabilitySuggestions(
  formula: Formula,
  availableProducts: ProductAvailability[]
): {
  canProceed: boolean;
  criticalIssues: string[];
  suggestions: string[];
  alternativeFormula?: Formula;
} {
  const validation = validateFormulaWithAvailability(formula, availableProducts);
  
  const availabilityViolations = validation.violations.filter(v => v.type === 'availability');
  const criticalIssues = availabilityViolations
    .filter(v => v.severity === 'critical')
    .map(v => v.message);
  
  const suggestions = availabilityViolations
    .filter(v => v.suggestion)
    .map(v => v.suggestion!);
  
  const canProceed = criticalIssues.length === 0;
  
  return {
    canProceed,
    criticalIssues,
    suggestions,
    alternativeFormula: validation.correctedFormula,
  };
}

/**
 * Helper function to create ProductAvailability from inventory data
 */
export function createProductAvailabilityFromInventory(inventoryProducts: any[]): ProductAvailability[] {
  return inventoryProducts.map(product => {
    // Determine product type from category
    const typeMapping: Record<string, ProductAvailability['type']> = {
      'tinte': 'color',
      'oxidante': 'developer', 
      'decolorante': 'bleach',
      'matizador': 'toner',
      'aditivo': 'additive',
      'pre-pigmentacion': 'pre-pigment',
    };

    const type = typeMapping[product.category] || 'color';
    
    // Extract available shades for color products
    const availableShades: string[] = [];
    if (type === 'color' && product.shade) {
      availableShades.push(product.shade);
    }
    
    // Extract developer volumes
    const availableDeveloperVolumes: number[] = [];
    if (type === 'developer' && product.shade) {
      const volumeMatch = product.shade.match(/(\d+)\s*vol/);
      if (volumeMatch) {
        availableDeveloperVolumes.push(parseInt(volumeMatch[1]));
      }
    }

    return {
      id: product.id,
      brand: product.brand,
      line: product.line || '',
      type,
      shade: product.shade,
      maxShadeLevel: type === 'color' ? extractMaxLevel(availableShades) : undefined,
      maxDeveloperVolume: type === 'developer' ? Math.max(...availableDeveloperVolumes) : undefined,
      hasDecolorante: type === 'bleach',
      hasToners: type === 'toner',
      hasPrePigmentation: type === 'pre-pigment',
      availableShades: availableShades.length > 0 ? availableShades : undefined,
      availableDeveloperVolumes: availableDeveloperVolumes.length > 0 ? availableDeveloperVolumes : undefined,
      stock: product.currentStock,
      isActive: product.isActive,
    };
  });
}

/**
 * Extract maximum shade level from available shades
 */
function extractMaxLevel(shades: string[]): number {
  return Math.max(
    ...shades.map(shade => {
      const match = shade.match(/^(\d+)/);
      return match ? parseInt(match[1]) : 0;
    })
  );
}

/**
 * Check if specific products are available in inventory
 */
export function checkProductsAvailability(
  requiredProducts: { type: string; shade?: string; volume?: number }[],
  availableProducts: ProductAvailability[]
): { available: boolean; missing: string[]; alternatives: string[] } {
  const missing: string[] = [];
  const alternatives: string[] = [];
  
  for (const required of requiredProducts) {
    const isAvailable = availableProducts.some(available => {
      if (available.type !== required.type) return false;
      
      if (required.shade && !available.availableShades?.includes(required.shade)) {
        return false;
      }
      
      if (required.volume && !available.availableDeveloperVolumes?.includes(required.volume)) {
        return false;
      }
      
      return available.isActive !== false;
    });
    
    if (!isAvailable) {
      const productDesc = `${required.type}${required.shade ? ' ' + required.shade : ''}${required.volume ? ' ' + required.volume + 'vol' : ''}`;
      missing.push(productDesc);
      
      // Find alternatives
      const similarProducts = availableProducts.filter(p => p.type === required.type && p.isActive !== false);
      if (similarProducts.length > 0) {
        alternatives.push(`Para ${productDesc}: ${similarProducts.slice(0, 3).map(p => `${p.brand} ${p.line}`).join(', ')}`);
      }
    }
  }
  
  return {
    available: missing.length === 0,
    missing,
    alternatives,
  };
}

/**
 * Generate intelligent mixing solution for a specific shade
 */
export function generateSmartMixingSolution(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): MixingSolution | null {
  const { generateMixingSolution, getBrandMixingRules } = require('./validators/mixing.validator');
  const brandMixingRules = getBrandMixingRules(brand);

  return generateMixingSolution(
    targetShade,
    availableProducts,
    brand,
    line,
    brandMixingRules
  );
}

/**
 * Check if two color reflects can be mixed together
 */
export function checkReflectMixingCompatibility(
  reflect1: string,
  reflect2: string,
  brand?: string
): {
  compatible: boolean;
  warning?: string;
  expectedResult?: string;
  riskLevel: 'low' | 'medium' | 'high';
} {
  const { checkReflectCompatibility, getBrandMixingRules } = require('./validators/mixing.validator');
  const brandRules = getBrandMixingRules(brand || 'generic');

  const compatibility = checkReflectCompatibility(
    reflect1,
    reflect2,
    brandRules
  );

  let riskLevel: 'low' | 'medium' | 'high' = 'low';

  if (!compatibility.compatible) {
    riskLevel = 'high';
  } else if (compatibility.warning) {
    riskLevel = 'medium';
  }

  return {
    compatible: compatibility.compatible,
    warning: compatibility.warning,
    expectedResult: compatibility.resultReflect,
    riskLevel,
  };
}

/**
 * Get brand-specific mixing limitations and rules
 */
export function getBrandMixingLimitations(brand: string): {
  maxComponents: number;
  maxSpecialMixPercentage: number;
  restrictions: string[];
  recommendations: string[];
} {
  const { getBrandMixingRules, getBrandSpecificMixingInstructions } = require('./validators/mixing.validator');
  const rules = getBrandMixingRules(brand);
  const instructions = getBrandSpecificMixingInstructions(brand);

  return {
    maxComponents: rules.maxComponents,
    maxSpecialMixPercentage: rules.maxSpecialMixPercentage,
    restrictions: rules.restrictions,
    recommendations: instructions,
  };
}

/**
 * Analyze available shades and suggest possible mixing combinations
 */
export function analyzeAvailableMixingOptions(
  availableProducts: ProductAvailability[],
  brand: string,
  line: string,
  targetLevel?: number
): {
  possibleTargets: string[];
  compatibleCombinations: Array<{
    shade1: string;
    shade2: string;
    compatibilityScore: number;
    expectedResults: string[];
  }>;
  recommendations: string[];
} {
  const { getAvailableShades, parseShade, getBrandMixingRules, checkReflectCompatibility } = require('./validators/mixing.validator');
  const availableShades = getAvailableShades(availableProducts, brand, line);
  const brandRules = getBrandMixingRules(brand);

  const compatibleCombinations: any[] = [];
  const possibleTargets: string[] = [];

  // Analyze all possible two-shade combinations
  for (let i = 0; i < availableShades.length; i++) {
    for (let j = i + 1; j < availableShades.length; j++) {
      const shade1 = parseShade(availableShades[i]);
      const shade2 = parseShade(availableShades[j]);

      if (!shade1 || !shade2) continue;

      // Filter by target level if specified
      if (targetLevel &&
          Math.abs(shade1.level - targetLevel) > 1 &&
          Math.abs(shade2.level - targetLevel) > 1) {
        continue;
      }

      const compatibility = checkReflectCompatibility(
        shade1.primaryReflect,
        shade2.primaryReflect,
        brandRules
      );

      if (compatibility.compatible) {
        const compatibilityScore = compatibility.warning ? 0.7 : 1.0;

        compatibleCombinations.push({
          shade1: availableShades[i],
          shade2: availableShades[j],
          compatibilityScore,
          expectedResults: [
            `Nivel ${Math.round((shade1.level + shade2.level) / 2)}`,
            `Reflejo ${compatibility.resultReflect || 'mixto'}`,
          ],
        });

        // Generate possible target shades
        const avgLevel = Math.round((shade1.level + shade2.level) / 2);
        const resultReflect = compatibility.resultReflect || '0';
        possibleTargets.push(`${avgLevel}.${resultReflect}`);
      }
    }
  }

  // Remove duplicate targets
  const uniqueTargets = Array.from(new Set(possibleTargets));

  // Generate recommendations
  const recommendations = [
    `Con ${availableShades.length} tonos disponibles, puedes crear aproximadamente ${compatibleCombinations.length} mezclas diferentes`,
    `Las combinaciones más exitosas son entre tonos del mismo nivel`,
    `${brand} permite hasta ${brandRules.maxComponents} componentes por mezcla`,
  ];

  if (compatibleCombinations.length === 0) {
    recommendations.push('Considera conseguir tonos con reflejos más compatibles para ampliar opciones de mezcla');
  }

  return {
    possibleTargets: uniqueTargets,
    compatibleCombinations: compatibleCombinations.sort((a, b) => b.compatibilityScore - a.compatibilityScore),
    recommendations,
  };
}

/**
 * Calculate exact amounts needed for a mixing formula
 */
export function calculateMixingAmounts(
  mixingSolution: MixingSolution,
  totalAmount: number
): Array<{
  shade: string;
  amount: number;
  unit: string;
  percentage: number;
}> {
  return mixingSolution.components.map(component => ({
    shade: component.shade,
    amount: Math.round((totalAmount * component.percentage) / 100),
    unit: 'ml',
    percentage: component.percentage,
  }));
}

/**
 * Validate a proposed mixing formula before application
 */
export function validateMixingFormula(
  components: Array<{ shade: string; percentage: number }>,
  brand: string
): {
  valid: boolean;
  warnings: string[];
  errors: string[];
  suggestions: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];
  const suggestions: string[] = [];
  
  // Check total percentage
  const totalPercentage = components.reduce((sum, comp) => sum + comp.percentage, 0);
  if (Math.abs(totalPercentage - 100) > 1) {
    errors.push(`Porcentaje total incorrecto: ${totalPercentage}% (debe ser 100%)`);
  }
  
  // Check brand limits
  const brandLimits = getBrandMixingLimitations(brand);
  if (components.length > brandLimits.maxComponents) {
    errors.push(`Demasiados componentes: ${components.length} (máximo para ${brand}: ${brandLimits.maxComponents})`);
  }
  
  // Check percentage ranges
  components.forEach(component => {
    if (component.percentage < 10) {
      warnings.push(`${component.shade}: ${component.percentage}% muy bajo, puede ser difícil de medir`);
    }
    if (component.percentage > 90) {
      warnings.push(`${component.shade}: ${component.percentage}% muy alto, dominará la mezcla`);
    }
  });
  
  // Check reflect compatibility
  if (components.length === 2) {
    const shade1 = components[0].shade.match(/\.(\d)/)?.[1];
    const shade2 = components[1].shade.match(/\.(\d)/)?.[1];
    
    if (shade1 && shade2) {
      const compatibility = checkReflectMixingCompatibility(shade1, shade2, brand);
      if (!compatibility.compatible) {
        errors.push(`Reflejos incompatibles: ${shade1} y ${shade2}`);
      } else if (compatibility.warning) {
        warnings.push(compatibility.warning);
      }
    }
  }
  
  // Generate suggestions
  if (components.length === 2 && Math.abs(components[0].percentage - 50) > 25) {
    suggestions.push('Para mejor control del resultado, considera proporciones más equilibradas (60/40 máximo)');
  }
  
  if (warnings.length === 0 && errors.length === 0) {
    suggestions.push('Realiza prueba mecha antes de la aplicación completa');
  }
  
  return {
    valid: errors.length === 0,
    warnings,
    errors,
    suggestions,
  };
}

/**
 * Quick helper to check if a shade can be achieved with available products
 */
export function canAchieveShade(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): {
  achievable: boolean;
  method: 'direct' | 'mixing' | 'alternative' | 'impossible';
  solution?: MixingSolution;
  alternatives?: string[];
} {
  const { isShadeDirectlyAvailable, suggestCreativeAlternatives } = require('./validators/mixing.validator');

  // Check direct availability first
  const directlyAvailable = isShadeDirectlyAvailable(
    targetShade,
    availableProducts,
    brand,
    line
  );

  if (directlyAvailable) {
    return {
      achievable: true,
      method: 'direct',
    };
  }

  // Try mixing solution
  const mixingSolution = generateSmartMixingSolution(
    targetShade,
    availableProducts,
    brand,
    line
  );

  if (mixingSolution) {
    return {
      achievable: true,
      method: 'mixing',
      solution: mixingSolution,
    };
  }

  // Try creative alternatives
  const alternatives = suggestCreativeAlternatives(
    targetShade,
    availableProducts,
    brand,
    line
  );

  if (alternatives.length > 0) {
    return {
      achievable: true,
      method: 'alternative',
      alternatives,
    };
  }

  return {
    achievable: false,
    method: 'impossible',
  };
}