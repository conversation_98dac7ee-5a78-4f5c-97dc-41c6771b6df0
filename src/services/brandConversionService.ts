import { ColorFormula } from '@/types/formulation';
import { conversionDatabase } from '@/data/conversionDatabase';

export interface ConversionResult {
  originalFormula: ColorFormula;
  targetFormula: ColorFormula;
  adjustments: {
    toneMapping: string; // "7/1 → 7.13"
    mixRatio: string; // "1:1 → 1:1.5"
    processingTime: number; // +5 min
    additionalNotes: string[];
  };
  confidence: number; // 0-100
  warnings?: string[];
}

export interface ConversionService {
  convert(
    formula: ColorFormula,
    targetBrand: string,
    targetLine: string
  ): Promise<ConversionResult>;
  hasConversion(
    sourceBrand: string,
    sourceLine: string,
    targetBrand: string,
    targetLine: string
  ): boolean;
}

export class MockConversionService implements ConversionService {
  private conversions = {
    ...conversionDatabase,
    // Wella Koleston → L'Oréal Majirel
    'Wella:Koleston:7/1': {
      "L'Oréal:Majirel": {
        tone: '7.13',
        adjustments: {
          mixRatio: '1:1 → 1:1.5',
          processingTime: 5,
          notes: [
            'Majirel 7.1 es ceniza puro, usar 7.13 para incluir beige',
            'Mayor proporción de oxidante para mejor cobertura',
          ],
        },
        confidence: 85,
      },
      'Schwarzkopf:Igora Royal': {
        tone: '7-12',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: ['Igora tiene mayor poder de cobertura, vigilar el tiempo'],
        },
        confidence: 90,
      },
    },
    'Wella:Koleston:6/73': {
      "L'Oréal:Majirel": {
        tone: '6.73',
        adjustments: {
          mixRatio: '1:1 → 1:1.5',
          processingTime: 5,
          notes: ['Formato de numeración directamente compatible'],
        },
        confidence: 95,
      },
      'Schwarzkopf:Igora Royal': {
        tone: '6-77',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: [
            'Igora 6-77 es más intenso en dorado',
            'Considerar pre-pigmentación si el cabello está muy poroso',
          ],
        },
        confidence: 80,
      },
    },
    // L'Oréal Majirel → Wella Koleston
    "L'Oréal:Majirel:8.3": {
      'Wella:Koleston': {
        tone: '8/3',
        adjustments: {
          mixRatio: '1:1.5 → 1:1',
          processingTime: -5,
          notes: ['Koleston procesa más rápido, reducir tiempo'],
        },
        confidence: 90,
      },
      'Schwarzkopf:Igora Royal': {
        tone: '8-3',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: ['Equivalencia directa en tonos dorados'],
        },
        confidence: 95,
      },
    },
    // Schwarzkopf Igora → otras marcas
    'Schwarzkopf:Igora Royal:9-1': {
      "L'Oréal:Majirel": {
        tone: '9.1',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 5,
          notes: ['Majirel necesita más tiempo para alcanzar la misma claridad'],
        },
        confidence: 85,
      },
      'Wella:Koleston': {
        tone: '9/1',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: ['Ambas marcas tienen potencia similar en rubios claros'],
        },
        confidence: 90,
      },
    },
    // Conversiones para tonos rojizos/cobrizos
    "L'Oréal:Majirel:7.44": {
      'Wella:Koleston': {
        tone: '7/44',
        adjustments: {
          mixRatio: '1:1.5 → 1:1',
          processingTime: -5,
          notes: [
            'Koleston tiene pigmentos rojizos más intensos',
            'Reducir cantidad de tinte en 10% para evitar sobresaturación',
          ],
        },
        confidence: 80,
      },
      'Schwarzkopf:Igora Royal': {
        tone: '7-77',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: [
            'Igora 7-77 es cobrizo intenso, equivalente al 7.44',
            'Resultado ligeramente más cálido',
          ],
        },
        confidence: 85,
      },
    },
    // Conversiones para bases oscuras
    'Wella:Koleston:3/0': {
      "L'Oréal:Majirel": {
        tone: '3',
        adjustments: {
          mixRatio: '1:1 → 1:1.5',
          processingTime: 10,
          notes: [
            'Bases oscuras de Majirel requieren más tiempo de procesamiento',
            'Usar oxidante de 20 vol para mejor cobertura de canas',
          ],
        },
        confidence: 90,
      },
      'Schwarzkopf:Igora Royal': {
        tone: '3-0',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 5,
          notes: ['Igora tiene excelente cobertura en bases oscuras'],
        },
        confidence: 95,
      },
    },
    // Alfaparf Evolution → otras marcas
    'Alfaparf:Evolution:8.21': {
      "L'Oréal:Majirel": {
        tone: '8.12',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: 0,
          notes: [
            'Invertir los reflejos: 21 (irisado-ceniza) → 12 (ceniza-irisado)',
            'Resultado muy similar',
          ],
        },
        confidence: 88,
      },
      'Wella:Koleston': {
        tone: '8/18',
        adjustments: {
          mixRatio: 'Sin cambios',
          processingTime: -5,
          notes: ['Koleston 8/18 es perlado ceniza, equivalente cercano'],
        },
        confidence: 82,
      },
    },
  };

  async convert(
    formula: ColorFormula,
    targetBrand: string,
    targetLine: string
  ): Promise<ConversionResult> {
    const sourceKey = `${formula.brand}:${formula.line}:${formula.colors[0].tone}`;
    const targetKey = `${targetBrand}:${targetLine}`;

    const conversionData = this.conversions[sourceKey]?.[targetKey];

    if (!conversionData) {
      // Si no hay conversión específica, intentar conversión genérica
      return this.genericConversion(formula, targetBrand, targetLine);
    }

    const targetFormula: ColorFormula = {
      ...formula,
      brand: targetBrand,
      line: targetLine,
      colors: [
        {
          tone: conversionData.tone,
          amount: formula.colors[0].amount,
        },
      ],
      processingTime: formula.processingTime + conversionData.adjustments.processingTime,
    };

    return {
      originalFormula: formula,
      targetFormula,
      adjustments: {
        toneMapping: `${formula.colors[0].tone} → ${conversionData.tone}`,
        mixRatio: conversionData.adjustments.mixRatio,
        processingTime: conversionData.adjustments.processingTime,
        additionalNotes: conversionData.adjustments.notes,
      },
      confidence: conversionData.confidence,
      warnings:
        conversionData.confidence < 80
          ? ['Recomendamos realizar prueba de mechón antes de aplicación completa']
          : undefined,
    };
  }

  private async genericConversion(
    formula: ColorFormula,
    targetBrand: string,
    targetLine: string
  ): Promise<ConversionResult> {
    // Conversión genérica basada en formato de numeración
    const originalTone = formula.colors[0].tone;
    let targetTone = originalTone;

    // Ajustar formato de numeración según marca destino
    if (formula.brand === 'Wella' && targetBrand === "L'Oréal") {
      targetTone = originalTone.replace('/', '.');
    } else if (formula.brand === "L'Oréal" && targetBrand === 'Wella') {
      targetTone = originalTone.replace('.', '/');
    } else if (targetBrand === 'Schwarzkopf') {
      targetTone = originalTone.replace('/', '-').replace('.', '-');
    }

    const targetFormula: ColorFormula = {
      ...formula,
      brand: targetBrand,
      line: targetLine,
      colors: [
        {
          tone: targetTone,
          amount: formula.colors[0].amount,
        },
      ],
    };

    return {
      originalFormula: formula,
      targetFormula,
      adjustments: {
        toneMapping: `${originalTone} → ${targetTone}`,
        mixRatio: 'Mantener proporciones originales',
        processingTime: 0,
        additionalNotes: [
          'Conversión genérica basada en numeración',
          'Recomendamos verificar carta de color de la marca destino',
          'Los resultados pueden variar según la base pigmentaria de cada marca',
        ],
      },
      confidence: 60,
      warnings: [
        'Sin datos específicos de conversión para esta combinación',
        'Realizar prueba de mechón obligatoria',
        'Considerar consultar con representante técnico de la marca',
      ],
    };
  }

  hasConversion(
    sourceBrand: string,
    sourceLine: string,
    targetBrand: string,
    targetLine: string
  ): boolean {
    // Verificar si existe alguna conversión entre estas marcas/líneas
    const sourcePrefix = `${sourceBrand}:${sourceLine}`;
    const targetKey = `${targetBrand}:${targetLine}`;

    return Object.keys(this.conversions).some(
      key => key.startsWith(sourcePrefix) && this.conversions[key][targetKey]
    );
  }
}

// Singleton instance
export const brandConversionService = new MockConversionService();
