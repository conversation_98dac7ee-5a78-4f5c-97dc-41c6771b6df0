import React, { memo } from 'react';
import { View, Text, Switch, StyleSheet } from 'react-native';
import { BeautyCard } from '@/components/beauty';
import DiagnosisSelector from '@/components/DiagnosisSelector';
import DiagnosisTextInput from '@/components/DiagnosisTextInput';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { useDiagnosisContext } from '@/service/contexts/DiagnosisContext';

const ChemicalHistoryFormComponent: React.FC = () => {
  const { data, onUpdate, isDataFromAI } = useDiagnosisContext();
  return (
    <BeautyCard variant="default" style={styles.section}>
      <Text style={styles.sectionTitle}>Historial Químico</Text>

      <DiagnosisSelector
        label="Último proceso químico"
        value={data.lastChemicalProcessType || ''}
        options={[
          'Tinte permanente',
          'Demi-permanente / Toner',
          'Decoloración',
          'Mechas / Balayage',
          'Alisado químico / Formol',
          'Keratina',
          'Henna / Barros',
          'Otro',
        ]}
        onValueChange={value => onUpdate({ lastChemicalProcessType: value })}
        required
        isFromAI={isDataFromAI}
      />

      {data.lastChemicalProcessType === 'Otro' && (
        <DiagnosisTextInput
          label="Especificar proceso"
          value={data.lastChemicalProcessCustom || ''}
          onChangeText={value => onUpdate({ lastChemicalProcessCustom: value })}
          placeholder="Describe el proceso"
          isFromAI={false}
        />
      )}

      <DiagnosisTextInput
        label="Fecha del último proceso"
        value={data.lastChemicalProcessDate || ''}
        onChangeText={value => onUpdate({ lastChemicalProcessDate: value })}
        placeholder="Ej: 15/05/2024"
        isFromAI={isDataFromAI}
      />

      <View style={styles.switchGroup}>
        <Text style={styles.inputLabel}>¿Ha usado remedios caseros?</Text>
        <Switch
          value={data.hasUsedHomeRemedies || false}
          onValueChange={value => onUpdate({ hasUsedHomeRemedies: value })}
          trackColor={{
            false: BeautyMinimalTheme.semantic.background.secondary,
            true: BeautyMinimalTheme.semantic.interactive.primary.default,
          }}
          thumbColor={
            data.hasUsedHomeRemedies ? 'white' : BeautyMinimalTheme.semantic.text.secondary
          }
        />
      </View>
    </BeautyCard>
  );
};

// Export memoized component
export const ChemicalHistoryForm = memo(ChemicalHistoryFormComponent);

// Display name for debugging
ChemicalHistoryForm.displayName = 'ChemicalHistoryForm';

const styles = StyleSheet.create({
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  sectionTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  inputLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
});
