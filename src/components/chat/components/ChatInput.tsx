import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Image,
  Animated,
} from 'react-native';
import { Send, Image as ImageIcon, Camera, X } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { spacing, radius } from '@/constants/theme';
import { ChatAttachment } from '@/stores/chat-store';
import SmartSuggestions from '../SmartSuggestions';
import { ColorConstants } from '@/styles/colors';

// Define a type for the context data passed to SmartSuggestions
interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface ChatInputProps {
  isSending: boolean;
  isInputFocused: boolean;
  inputHeight: number;
  pendingAttachments: ChatAttachment[];
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextData?: ContextData;
  sendButtonScale: Animated.Value;
  attachButtonScale: Animated.Value;
  message?: string;
  setInputHeight: (height: number) => void;
  setIsInputFocused: (focused: boolean) => void;
  setMessage?: (message: string) => void;
  onSendMessage: (message: string, attachments: ChatAttachment[]) => void;
  animateButtonPress: (animValue: Animated.Value, callback?: () => void) => void;
  handleImageUpload: (source: 'camera' | 'library') => void;
  setPendingAttachments: React.Dispatch<React.SetStateAction<ChatAttachment[]>>;
  onSuggestionSelected: (suggestion: string) => void;
  scrollToBottom: () => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  isSending,
  isInputFocused,
  inputHeight,
  pendingAttachments,
  contextType,
  contextData,
  sendButtonScale,
  attachButtonScale,
  message,
  setInputHeight,
  setIsInputFocused,
  setMessage,
  onSendMessage,
  animateButtonPress,
  handleImageUpload,
  setPendingAttachments,
  onSuggestionSelected,
  scrollToBottom,
}) => {
  const [localMessage, setLocalMessage] = useState('');

  // Use external message state if provided, otherwise use local state
  const currentMessage = message !== undefined ? message : localMessage;
  const setCurrentMessage = setMessage || setLocalMessage;

  const handleLocalSend = useCallback(async () => {
    if (!currentMessage.trim() || isSending) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    animateButtonPress(sendButtonScale);

    const messageToSend = currentMessage.trim();
    const attachmentsToSend = [...pendingAttachments];

    setCurrentMessage('');
    setPendingAttachments([]);
    onSendMessage(messageToSend, attachmentsToSend);
  }, [
    currentMessage,
    isSending,
    pendingAttachments,
    onSendMessage,
    animateButtonPress,
    sendButtonScale,
    setPendingAttachments,
    setCurrentMessage,
  ]);

  const handleLocalTextChange = useCallback(
    (text: string) => {
      setCurrentMessage(text);
      // Auto-scroll when user starts typing (like Claude)
      if (text.length > 0 && isInputFocused) {
        scrollToBottom();
      }
    },
    [isInputFocused, scrollToBottom, setCurrentMessage]
  );

  const handleLocalSuggestionSelect = useCallback(
    (suggestion: string) => {
      setCurrentMessage(suggestion);
      onSuggestionSelected(suggestion);
    },
    [onSuggestionSelected, setCurrentMessage]
  );

  return (
    <View style={styles.bottomContainer}>
      {currentMessage.length > 0 && (
        <SmartSuggestions
          input={currentMessage}
          onSuggestionSelect={handleLocalSuggestionSelect}
          contextType={contextType}
          contextData={contextData}
        />
      )}

      {pendingAttachments.length > 0 && (
        <View style={styles.pendingAttachmentsContainer}>
          <View style={styles.pendingAttachmentsHeader}>
            <View style={styles.attachedImagesIcon}>
              <ImageIcon size={12} color={BeautyMinimalTheme.neutrals.slate} />
            </View>
            <Text style={styles.pendingAttachmentsTitle}>
              {pendingAttachments.length} imagen{pendingAttachments.length > 1 ? 'es' : ''}
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.pendingAttachmentsScroll}
            contentContainerStyle={styles.pendingAttachmentsContent}
          >
            {pendingAttachments.map((attachment, index) => (
              <View key={index} style={styles.pendingImageWrapper}>
                <Image
                  source={{ uri: attachment.url }}
                  style={styles.pendingImagePreview}
                  resizeMode="cover"
                />
                {attachment.uploadStatus === 'uploading' && (
                  <View style={styles.uploadingOverlay}>
                    <ActivityIndicator size="small" color={BeautyMinimalTheme.neutrals.pure} />
                  </View>
                )}
                <TouchableOpacity
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setPendingAttachments(prev => prev.filter((_, i) => i !== index));
                  }}
                  style={styles.removePendingImageButton}
                  hitSlop={{ top: 8, right: 8, bottom: 8, left: 8 }}
                >
                  <X size={10} color={BeautyMinimalTheme.neutrals.pure} />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
          <Text style={styles.pendingAttachmentsHint}>Describe el análisis que necesitas</Text>
        </View>
      )}

      <View style={styles.inputContainer}>
        <View
          style={[
            styles.inputWrapper,
            {
              borderColor: isInputFocused
                ? BeautyMinimalTheme.beautyColors.salonier[500] + '40'
                : currentMessage.length > 0
                  ? BeautyMinimalTheme.neutrals.whisper + '60'
                  : BeautyMinimalTheme.neutrals.mist + '50',
            },
          ]}
        >
          <View style={styles.attachButtonsContainer}>
            <TouchableOpacity
              style={styles.attachButton}
              onPress={() => {
                animateButtonPress(attachButtonScale);
                handleImageUpload('camera');
              }}
              disabled={isSending || pendingAttachments.length >= 3}
              activeOpacity={1}
            >
              <Animated.View style={{ transform: [{ scale: attachButtonScale }] }}>
                <Camera
                  size={20}
                  color={
                    isSending || pendingAttachments.length >= 3
                      ? BeautyMinimalTheme.neutrals.silver + '60'
                      : BeautyMinimalTheme.neutrals.slate
                  }
                />
              </Animated.View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.attachButton}
              onPress={() => {
                animateButtonPress(attachButtonScale);
                handleImageUpload('library');
              }}
              disabled={isSending || pendingAttachments.length >= 3}
              activeOpacity={1}
            >
              <Animated.View style={{ transform: [{ scale: attachButtonScale }] }}>
                <ImageIcon
                  size={20}
                  color={
                    isSending || pendingAttachments.length >= 3
                      ? BeautyMinimalTheme.neutrals.silver + '60'
                      : BeautyMinimalTheme.neutrals.slate
                  }
                />
              </Animated.View>
            </TouchableOpacity>
          </View>

          <TextInput
            style={[
              styles.messageInput,
              {
                minHeight: inputHeight,
              },
            ]}
            value={currentMessage}
            onChangeText={handleLocalTextChange}
            onContentSizeChange={event => {
              if (currentMessage.trim().length === 0) {
                if (inputHeight !== 40) setInputHeight(40);
                return;
              }
              const measured = Math.round(event.nativeEvent.contentSize.height);
              const newHeight = Math.min(Math.max(40, measured + 12), 80);
              if (Math.abs(newHeight - inputHeight) >= 2) {
                setInputHeight(newHeight);
              }
            }}
            onFocus={() => {
              setIsInputFocused(true);
              setTimeout(() => scrollToBottom(), 150);
            }}
            onBlur={() => setIsInputFocused(false)}
            placeholder={
              pendingAttachments.length > 0
                ? 'Describe el análisis que necesitas...'
                : 'Escribe tu consulta...'
            }
            placeholderTextColor={BeautyMinimalTheme.neutrals.silver + '70'}
            multiline
            scrollEnabled={inputHeight >= 80}
            editable={!isSending}
            returnKeyType="default"
            blurOnSubmit={false}
          />

          <TouchableOpacity
            style={[
              styles.sendButton,
              currentMessage.trim() && !isSending
                ? styles.sendButtonActive
                : styles.sendButtonInactive,
            ]}
            onPress={handleLocalSend}
            disabled={!currentMessage.trim() || isSending}
            activeOpacity={1}
          >
            {isSending ? (
              <ActivityIndicator size="small" color={BeautyMinimalTheme.neutrals.pure} />
            ) : (
              <Animated.View style={{ transform: [{ scale: sendButtonScale }] }}>
                <Send
                  size={14}
                  color={
                    currentMessage.trim()
                      ? BeautyMinimalTheme.neutrals.pure
                      : BeautyMinimalTheme.neutrals.silver
                  }
                />
              </Animated.View>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl,
    flexShrink: 0,
    minHeight: 'auto',
    borderTopWidth: 0.5,
    borderTopColor: BeautyMinimalTheme.neutrals.mist + '60',
  },
  inputContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pearl,
    borderTopWidth: 0,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: BeautyMinimalTheme.radius.xl,
    borderWidth: 0.5,
    paddingHorizontal: BeautyMinimalTheme.spacing.md,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    ...BeautyMinimalTheme.shadows.subtle,
  },
  attachButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  attachButton: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.xs,
    borderRadius: radius.md,
    backgroundColor: ColorConstants.TRANSPARENT,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginLeft: spacing.sm,
  },
  sendButtonActive: {
    backgroundColor: BeautyMinimalTheme.beautyColors.salonier[500],
    ...BeautyMinimalTheme.shadows.subtle,
  },
  sendButtonInactive: {
    backgroundColor: ColorConstants.TRANSPARENT,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist + '60',
  },
  pendingAttachmentsContainer: {
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    backgroundColor: BeautyMinimalTheme.neutrals.cloud + '30',
    borderTopWidth: 0.5,
    borderTopColor: BeautyMinimalTheme.neutrals.mist + '50',
    marginHorizontal: -BeautyMinimalTheme.spacing.lg,
  },
  pendingAttachmentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  attachedImagesIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: BeautyMinimalTheme.neutrals.whisper + '50',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: BeautyMinimalTheme.spacing.sm,
  },
  pendingAttachmentsTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    color: BeautyMinimalTheme.neutrals.slate,
    letterSpacing: 0.1,
  },
  pendingAttachmentsScroll: {
    marginBottom: spacing.sm,
  },
  pendingAttachmentsContent: {
    paddingHorizontal: spacing.lg,
  },
  pendingImageWrapper: {
    position: 'relative',
    marginRight: BeautyMinimalTheme.spacing.md,
    borderRadius: BeautyMinimalTheme.radius.md,
    overflow: 'hidden',
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderWidth: 0,
  },
  pendingImagePreview: {
    width: 72,
    height: 72,
    borderRadius: BeautyMinimalTheme.radius.md,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: BeautyMinimalTheme.neutrals.charcoal + '70',
    borderRadius: BeautyMinimalTheme.radius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePendingImageButton: {
    position: 'absolute',
    top: BeautyMinimalTheme.spacing.xs,
    right: BeautyMinimalTheme.spacing.xs,
    backgroundColor: BeautyMinimalTheme.neutrals.charcoal + '80',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pendingAttachmentsHint: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    color: BeautyMinimalTheme.neutrals.silver,
    textAlign: 'center',
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
    letterSpacing: 0.1,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.caption *
      BeautyMinimalTheme.typography.lineHeights.normal,
    paddingHorizontal: BeautyMinimalTheme.spacing.lg,
    opacity: 0.7,
  },
  messageInput: {
    flex: 1,
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.neutrals.charcoal,
    paddingVertical: BeautyMinimalTheme.spacing.sm,
    paddingHorizontal: spacing.sm,
    textAlignVertical: 'top',
    backgroundColor: ColorConstants.TRANSPARENT,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.body * BeautyMinimalTheme.typography.lineHeights.normal,
    maxWidth: '100%',
    fontWeight: BeautyMinimalTheme.typography.weights.regular,
  },
});

export default React.memo(ChatInput);
