import React, { useState, useRef, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet, Alert, Switch, type ScrollView } from 'react-native';
import { logger } from '@/utils/logger';
import { Shield } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { BeautyCard, BeautyButton } from '@/components/beauty';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { getTypographyStyle } from '@/constants/typography-system';
import { HairZone } from '@/types/hair-diagnosis';
import { PhotoAngle, PHOTO_GUIDES, PhotoGuide, PhotoQuality } from '@/types/photo-capture';
import { ServiceData } from '@/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/service/hooks/usePhotoAnalysis';
import { calculateDiagnosisProgress } from '@/service/utils/serviceValidations';
import { SwipeableScreen } from '@/components/navigation/SwipeableScreen';
import { ScrollableContent } from '@/components/navigation/ScrollableContent';

import { useAIDiagnosisProcessor } from '@/service/hooks/useAIDiagnosisProcessor';
import { useValidation } from '@/service/hooks/useValidation';
import { DiagnosisProvider } from '@/service/contexts/DiagnosisContext';

// Import existing components
import { ChemicalHistoryForm } from './ChemicalHistoryForm';
import { PhysicalMeasurementsForm } from './PhysicalMeasurementsForm';
import ZoneDiagnosisForm from '@/components/ZoneDiagnosisForm';
import { AIAnalysisSection } from './AIAnalysisSection';
import { DiagnosisModeTabs } from './DiagnosisModeTabs';
import AIResultNotification from '@/components/AIResultNotification';
import { GeneralCharacteristicsForm } from './GeneralCharacteristicsForm';
import { ZoneAnalysisDisplay } from '@/components/base';

interface DiagnosisStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
}

export const DiagnosisStep: React.FC<DiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave: _onSave,
  onSaveSilent: _onSaveSilent,
}) => {
  const [diagnosisTab, setDiagnosisTab] = useState<'roots' | 'mids' | 'ends'>('roots');

  // Memoize current zone calculation
  const currentZone = useMemo(() => {
    if (diagnosisTab === 'roots') return HairZone.ROOTS;
    if (diagnosisTab === 'mids') return HairZone.MIDS;
    if (diagnosisTab === 'ends') return HairZone.ENDS;
    return HairZone.ROOTS;
  }, [diagnosisTab]);

  // Memoize completed zones for performance
  const completedZones = useMemo(
    () => ({
      [HairZone.ROOTS]: !!data.zoneColorAnalysis[HairZone.ROOTS]?.level,
      [HairZone.MIDS]: !!data.zoneColorAnalysis[HairZone.MIDS]?.level,
      [HairZone.ENDS]: !!data.zoneColorAnalysis[HairZone.ENDS]?.level,
    }),
    [data.zoneColorAnalysis]
  );

  // Memoize progress calculation
  const progressData = useMemo(
    () =>
      calculateDiagnosisProgress(
        data.hairThickness,
        data.hairDensity,
        data.overallTone,
        data.overallUndertone || data.overallReflect || '',
        data.zoneColorAnalysis,
        data.zonePhysicalAnalysis,
        data.hairPhotos
      ),
    [data]
  );

  // Use validation hook
  const { validateAndProceed } = useValidation({ data, analysisResult });

  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);

  // Memoize zone change handler
  const handleZoneChange = useCallback((zone: HairZone) => {
    if (zone === HairZone.ROOTS) setDiagnosisTab('roots');
    else if (zone === HairZone.MIDS) setDiagnosisTab('mids');
    else if (zone === HairZone.ENDS) setDiagnosisTab('ends');
  }, []);

  // Memoize photo removal handler
  const handlePhotoRemove = useCallback(
    (photoId: string) => {
      const updatedPhotos = data.hairPhotos.filter(p => p.id !== photoId);
      onUpdate({ hairPhotos: updatedPhotos });
    },
    [data.hairPhotos, onUpdate]
  );

  const {
    isAnalyzing,
    analysisResult,
    privacyMode: _privacyMode,
    setPrivacyMode: _setPrivacyMode,
    clearAnalysis: _clearAnalysis,
    performAnalysis,
    pickMultipleImages,
    takePhoto,
    performImageQualityCheck,
  } = usePhotoAnalysis();

  const { isDataFromAI, showAINotification, aiFieldsCount, setShowAINotification } =
    useAIDiagnosisProcessor(analysisResult, isAnalyzing, onUpdate, data, scrollRef);

  const handleAIAnalysis = async () => {
    // Immediate haptic feedback for premium feel
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch {
      // Silently fail if haptics not available
    }

    if (data.hairPhotos.length === 0) {
      Alert.alert('Error', 'Por favor captura al menos una foto del cabello');
      return;
    }

    try {
      await performAnalysis(data.hairPhotos, data.clientId!);
    } catch (error: unknown) {
      const errorAsError = error as Error;
      if (errorAsError.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          'Análisis tardando más de lo normal',
          'El análisis está tardando más de 30 segundos. ¿Qué deseas hacer?',
          [
            {
              text: 'Continuar esperando',
              onPress: () => handleAIAnalysis(),
            },
            {
              text: 'Diagnóstico manual',
              onPress: () => {
                onUpdate({ diagnosisMethod: 'manual' });
              },
              style: 'cancel',
            },
          ]
        );
      } else {
        Alert.alert(
          'Error en el análisis',
          'No se pudo completar el análisis. Por favor intenta nuevamente.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  const handleMultipleImagePick = async () => {
    const message = await pickMultipleImages(
      data.hairPhotos,
      photos => {
        onUpdate({ hairPhotos: photos });
      },
      undefined // DISABLED: onSave to prevent excessive auto-saving during image processing
    );

    if (message) {
      // Show toast message (you might want to pass this up to parent)
      // Debug logging removed for production
    }
  };

  const handleCameraOpen = async () => {
    try {
      await takePhoto(async uri => {
        // Determine the next angle for the photo
        const capturedAngles = data.hairPhotos.map(p => p.angle);
        const nextGuide = PHOTO_GUIDES.find((g: PhotoGuide) => !capturedAngles.includes(g.angle));
        const angle = nextGuide?.angle || PhotoAngle.CROWN;

        // Create quality assessment
        const quality = performImageQualityCheck(uri);
        const qualityObj: PhotoQuality = {
          lighting: quality.isGoodLighting ? 'good' : 'fair',
          focus: quality.isInFocus ? 'good' : 'fair',
          stability: 'good',
          overall: quality.overallScore,
        };

        // Create new photo
        const newPhoto = {
          id: Date.now().toString(),
          uri,
          angle,
          quality: qualityObj,
          timestamp: new Date(),
        };

        // Update photos
        const updatedPhotos = [...data.hairPhotos, newPhoto];
        onUpdate({ hairPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      });
    } catch (error) {
      logger.error('Error taking photo:', error);
    }
  };

  // Recommendation handling removed - no longer needed without ClientHistoryPanel

  const renderPrivacyBanner = () => (
    <BeautyCard variant="subtle" style={styles.privacyBanner}>
      <Shield size={16} color={BeautyMinimalTheme.semantic.status.success} />
      <Text style={styles.privacyBannerText}>
        🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan
        inmediatamente después del análisis.
      </Text>
    </BeautyCard>
  );

  // currentZone and progressData now calculated in useMemo above

  return (
    <DiagnosisProvider
      data={data}
      onUpdate={onUpdate}
      analysisResult={analysisResult}
      isDataFromAI={isDataFromAI}
      diagnosisMethod={data.diagnosisMethod}
    >
      <>
        {/* Confirmación de valores IA */}
        {analysisResult && !isAnalyzing && (
          <BeautyCard variant="default" style={styles.section}>
            <View style={styles.switchGroup}>
              <Text style={styles.inputLabel}>
                He revisado y confirmo los valores analizados por IA
              </Text>
              <Switch
                value={!!data.aiDiagnosisConfirmed}
                onValueChange={value => onUpdate({ aiDiagnosisConfirmed: value })}
              />
            </View>
          </BeautyCard>
        )}
        <AIResultNotification
          visible={showAINotification}
          onDismiss={() => setShowAINotification(false)}
          message="Análisis completado con IA"
          fieldsCount={aiFieldsCount}
          onViewResults={() => {
            if (scrollRef.current) {
              // Scroll to the confidence indicator with smooth animation
              scrollRef.current.scrollTo({ y: 350, animated: true });
              // Add haptic feedback when scrolling to results
              setTimeout(() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }, 300);
            }
          }}
        />

        <SwipeableScreen
          onSwipeLeft={progressData.canProceed ? onNext : undefined}
          swipeEnabled={progressData.canProceed}
          scrollPriority="vertical"
          style={styles.container}
        >
          <ScrollableContent ref={scrollRef}>
            <View style={styles.stepContainer}>
              <BeautyCard variant="subtle" style={styles.headerCard}>
                <Text style={styles.stepTitle}>Análisis Capilar Profesional</Text>
                {data.client && (
                  <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
                )}
              </BeautyCard>

              {renderPrivacyBanner()}

              {/* Client History Panel removed - redundant with client detail screen */}

              <DiagnosisModeTabs />

              {/* AI Mode */}
              {data.diagnosisMethod === 'ai' && (
                <AIAnalysisSection
                  hairPhotos={data.hairPhotos}
                  onAddPhoto={handleMultipleImagePick}
                  onCameraCapture={handleCameraOpen}
                  onRemovePhoto={handlePhotoRemove}
                  handleAIAnalysis={handleAIAnalysis}
                  isAnalyzing={isAnalyzing}
                />
              )}

              {/* Manual Mode or AI Results */}
              {(data.diagnosisMethod === 'manual' || analysisResult) && (
                <>
                  {/* Chemical History */}
                  <ChemicalHistoryForm />

                  {/* Physical Measurements */}
                  <PhysicalMeasurementsForm />

                  {/* General characteristics */}
                  <GeneralCharacteristicsForm />

                  {/* Zone tabs */}
                  <ZoneAnalysisDisplay
                    currentZone={currentZone}
                    onZoneChange={handleZoneChange}
                    completedZones={completedZones}
                    showCompletionIndicators={true}
                  />

                  {/* Zone-specific diagnosis */}
                  {currentZone && (
                    <ZoneDiagnosisForm
                      zone={currentZone}
                      colorAnalysis={data.zoneColorAnalysis[currentZone] || {}}
                      physicalAnalysis={data.zonePhysicalAnalysis[currentZone] || {}}
                      onColorChange={analysis => {
                        onUpdate({
                          zoneColorAnalysis: {
                            ...data.zoneColorAnalysis,
                            [currentZone]: {
                              ...data.zoneColorAnalysis[currentZone],
                              ...analysis,
                            },
                          },
                        });
                      }}
                      onPhysicalChange={analysis => {
                        onUpdate({
                          zonePhysicalAnalysis: {
                            ...data.zonePhysicalAnalysis,
                            [currentZone]: {
                              ...data.zonePhysicalAnalysis[currentZone],
                              ...analysis,
                            },
                          },
                        });
                      }}
                      isFromAI={isDataFromAI}
                    />
                  )}
                </>
              )}

              {/* Continue Button */}
              {(data.diagnosisMethod === 'manual' || analysisResult) && (
                <BeautyButton
                  title="Continuar al Color Deseado"
                  onPress={() => validateAndProceed(onNext)}
                  variant="primary"
                  size="lg"
                  fullWidth
                  style={styles.continueButton}
                />
              )}
            </View>
          </ScrollableContent>
        </SwipeableScreen>
      </>
    </DiagnosisProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: BeautyMinimalTheme.spacing.component.screenMargin,
  },
  section: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  headerCard: {
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  stepTitle: {
    ...getTypographyStyle('sectionTitle'),
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  clientName: {
    ...getTypographyStyle('body'),
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  privacyBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.status.success + '15',
    borderLeftWidth: 4,
    borderLeftColor: BeautyMinimalTheme.semantic.status.success,
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  privacyBannerText: {
    ...getTypographyStyle('diagnosticLabel'),
    color: BeautyMinimalTheme.semantic.status.success,
    marginLeft: BeautyMinimalTheme.spacing.sm,
    flex: 1,
  },
  continueButton: {
    marginTop: BeautyMinimalTheme.spacing.lg,
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  inputLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    color: BeautyMinimalTheme.semantic.text.primary,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },
});
