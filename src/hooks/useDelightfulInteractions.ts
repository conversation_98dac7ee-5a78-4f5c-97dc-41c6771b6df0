import { useCallback } from 'react';
import {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  withRepeat,
  runOnJS,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { MicroInteractions, AnimationHelpers } from '@/constants/micro-interactions';

type ConfidenceLevel = 'high' | 'medium' | 'low' | 'critical';
type InteractionType = 'press' | 'success' | 'error' | 'attention' | 'processing';

export const useDelightfulInteractions = () => {
  // Core animation values
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const opacity = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
    opacity: opacity.value,
  }));

  // Professional confidence animation
  const animateConfidence = useCallback(
    (level: ConfidenceLevel, onComplete?: () => void) => {
      const config = AnimationHelpers.confidenceSequence(level);

      // Haptic feedback
      switch (config.haptic) {
        case 'success':
          runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Error);
          break;
        default:
          runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
      }

      // Animation based on confidence level
      switch (level) {
        case 'high':
          // Sparkle rotation for high confidence
          rotation.value = withRepeat(
            withTiming(360, { duration: MicroInteractions.rotations.slow }),
            -1,
            false
          );
          opacity.value = withRepeat(
            withSequence(withTiming(1, { duration: 1500 }), withTiming(0.7, { duration: 1500 })),
            -1,
            true
          );
          break;

        case 'medium':
          // Gentle pulse
          scale.value = withRepeat(
            withSequence(
              withTiming(MicroInteractions.scales.subtle, { duration: 1000 }),
              withTiming(1, { duration: 1000 })
            ),
            -1,
            true
          );
          break;

        case 'low':
          // Attention pulse
          scale.value = withRepeat(
            withSequence(
              withTiming(MicroInteractions.scales.medium, { duration: 800 }),
              withTiming(1, { duration: 800 })
            ),
            3,
            true
          );
          break;

        case 'critical':
          // Gentle shake
          translateX.value = withSequence(
            ...MicroInteractions.translations.shake.map(val => withTiming(val, { duration: 100 }))
          );
          break;
      }

      if (onComplete) {
        setTimeout(() => runOnJS(onComplete)(), config.duration);
      }
    },
    [scale, rotation, opacity, translateX]
  );

  // Button press animation
  const animatePress = useCallback(
    (onComplete?: () => void) => {
      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);

      scale.value = withSequence(
        withTiming(MicroInteractions.scales.pressed, {
          duration: MicroInteractions.durations.instant,
        }),
        withSpring(1, MicroInteractions.springs.gentle),
        onComplete
          ? withTiming(1, { duration: 0 }, finished => {
              'worklet';
              if (finished && onComplete) runOnJS(onComplete)();
            })
          : withTiming(1, { duration: 0 })
      );
    },
    [scale]
  );

  // Success celebration
  const animateSuccess = useCallback(
    (onComplete?: () => void) => {
      const {
        scale: successScale,
        spring,
        haptic: _haptic,
      } = AnimationHelpers.successCelebration();

      runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Success);

      scale.value = withSequence(
        withSpring(successScale, spring),
        withSpring(1, MicroInteractions.springs.gentle)
      );

      rotation.value = withSpring(360, MicroInteractions.springs.bouncy);

      if (onComplete) {
        setTimeout(() => runOnJS(onComplete)(), MicroInteractions.durations.normal);
      }
    },
    [scale, rotation]
  );

  // Error shake
  const animateError = useCallback(
    (onComplete?: () => void) => {
      runOnJS(Haptics.notificationAsync)(Haptics.NotificationFeedbackType.Error);

      translateX.value = withSequence(
        ...MicroInteractions.translations.shake.map(val => withTiming(val, { duration: 100 }))
      );

      if (onComplete) {
        setTimeout(() => runOnJS(onComplete)(), MicroInteractions.durations.fast);
      }
    },
    [translateX]
  );

  // Attention seeker
  const animateAttention = useCallback(() => {
    const { scale: attentionScale, duration, repeat } = AnimationHelpers.attentionSeeker();

    scale.value = withRepeat(
      withSequence(
        withTiming(attentionScale, { duration: duration / 2 }),
        withTiming(1, { duration: duration / 2 })
      ),
      repeat,
      true
    );
  }, [scale]);

  // Processing indicator
  const animateProcessing = useCallback(
    (start = true) => {
      if (start) {
        rotation.value = withRepeat(
          withTiming(360, { duration: MicroInteractions.rotations.medium }),
          -1,
          false
        );
        opacity.value = withRepeat(
          withSequence(withTiming(0.6, { duration: 1000 }), withTiming(1, { duration: 1000 })),
          -1,
          true
        );
      } else {
        rotation.value = withTiming(0, { duration: MicroInteractions.durations.fast });
        opacity.value = withTiming(1, { duration: MicroInteractions.durations.fast });
      }
    },
    [rotation, opacity]
  );

  // Float animation for guides
  const animateFloat = useCallback(
    (start = true) => {
      if (start) {
        translateY.value = withRepeat(
          withSequence(
            withTiming(MicroInteractions.translations.float[0], { duration: 2000 }),
            withTiming(MicroInteractions.translations.float[1], { duration: 2000 })
          ),
          -1,
          true
        );
      } else {
        translateY.value = withTiming(0, { duration: MicroInteractions.durations.fast });
      }
    },
    [translateY]
  );

  // Generic interaction handler
  const triggerInteraction = useCallback(
    (type: InteractionType, options?: { onComplete?: () => void; level?: ConfidenceLevel }) => {
      switch (type) {
        case 'press':
          animatePress(options?.onComplete);
          break;
        case 'success':
          animateSuccess(options?.onComplete);
          break;
        case 'error':
          animateError(options?.onComplete);
          break;
        case 'attention':
          animateAttention();
          break;
        case 'processing':
          animateProcessing(true);
          break;
      }
    },
    [animatePress, animateSuccess, animateError, animateAttention, animateProcessing]
  );

  // Reset all animations
  const resetAnimations = useCallback(() => {
    scale.value = withTiming(1, { duration: MicroInteractions.durations.fast });
    rotation.value = withTiming(0, { duration: MicroInteractions.durations.fast });
    opacity.value = withTiming(1, { duration: MicroInteractions.durations.fast });
    translateX.value = withTiming(0, { duration: MicroInteractions.durations.fast });
    translateY.value = withTiming(0, { duration: MicroInteractions.durations.fast });
  }, [scale, rotation, opacity, translateX, translateY]);

  return {
    // Animated style to apply to components
    animatedStyle,

    // Individual animation functions
    animateConfidence,
    animatePress,
    animateSuccess,
    animateError,
    animateAttention,
    animateProcessing,
    animateFloat,

    // Generic interaction trigger
    triggerInteraction,

    // Utility
    resetAnimations,

    // Direct access to values for custom animations
    values: {
      scale,
      rotation,
      opacity,
      translateX,
      translateY,
    },
  };
};
