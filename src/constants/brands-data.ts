/**
 * MIGRATION TO DATABASE ACCESS LAYER - PHASE 3
 *
 * This file now delegates to the new brandService.ts which provides:
 * - Dynamic database access via Supabase
 * - Smart caching with 5-minute TTL
 * - Offline-first architecture
 * - 100% backward compatibility
 *
 * CRITERIOS PARA LÍNEAS FORMULABLES
 *
 * Una línea es FORMULABLE si puede generar fórmulas de coloración profesional:
 *
 * ✅ FORMULABLES:
 * - hair-color: Coloración permanente, semipermanente, demi-permanente
 * - bleaching: Decolorantes, aclaradores, lighteners (para procesos de aclarado)
 *
 * ❌ NO FORMULABLES:
 * - treatment: Tratamientos, mascarillas, reconstructores
 * - styling: Productos de peinado, geles, mousses
 * - developer: Oxidantes, reveladores (son complementos, no productos principales)
 * - other: Otros productos no relacionados con coloración
 *
 * NOTA: Los desarrolladores son necesarios para la formulación pero no son
 * el producto principal. Se incluyen automáticamente según la marca/línea seleccionada.
 */

// Import the new service layer with backward compatibility
import {
  professionalHairColorBrands as serviceBrands,
  getLinesByBrandId as serviceGetLinesByBrandId,
  getColorLinesByBrandId as serviceGetColorLinesByBrandId,
  isFormulableLine as serviceIsFormulableLine,
  getBrandsWithFormulableLines as serviceGetBrandsWithFormulableLines,
  getBrandById as serviceGetBrandById,
  searchBrands as serviceSearchBrands,
  searchFormulableBrands as serviceSearchFormulableBrands,
  validateBrandLines as serviceValidateBrandLines,
  getBrandLinesStats as serviceGetBrandLinesStats,
  getBrandsByPopularity as serviceGetBrandsByPopularity,
  getRecommendedBrandsByRegion as serviceGetRecommendedBrandsByRegion,
  getBrandsByCountry as serviceGetBrandsByCountry,
  getAllCountries as serviceGetAllCountries,
  getBrandTechnicalInfo as serviceGetBrandTechnicalInfo,
  getCompatibleBrands as serviceGetCompatibleBrands,
  type ProductLine,
  type Brand,
  type BrandTechnicalInfo,
} from '@/services/brandService';

// Re-export interfaces for backward compatibility
export type { ProductLine, Brand, BrandTechnicalInfo };

// Re-export the service data as if it were static data
export const professionalHairColorBrands = serviceBrands;

// Delegate all functions to the service layer for consistent behavior
export const getLinesByBrandId = serviceGetLinesByBrandId;
export const getColorLinesByBrandId = serviceGetColorLinesByBrandId;
export const isFormulableLine = serviceIsFormulableLine;
export const getBrandsWithFormulableLines = serviceGetBrandsWithFormulableLines;
export const getBrandById = serviceGetBrandById;
export const searchBrands = serviceSearchBrands;
export const searchFormulableBrands = serviceSearchFormulableBrands;
export const validateBrandLines = serviceValidateBrandLines;
export const getBrandLinesStats = serviceGetBrandLinesStats;
export const getBrandsByPopularity = serviceGetBrandsByPopularity;
export const getRecommendedBrandsByRegion = serviceGetRecommendedBrandsByRegion;
export const getBrandsByCountry = serviceGetBrandsByCountry;
export const getAllCountries = serviceGetAllCountries;

// Delegate technical info functions to service layer
export const getBrandTechnicalInfo = serviceGetBrandTechnicalInfo;
export const getCompatibleBrands = serviceGetCompatibleBrands;
