import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
} from 'react-native-reanimated';
import { WifiOff, CheckCircle, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

export interface OptimisticFeedbackProps {
  isPending?: boolean;
  error?: Error | null;
  isOffline?: boolean;
  pendingCount?: number;
}

/**
 * Claude-style Optimistic Feedback Component
 * Muestra estado de sincronización de forma sutil y elegante
 */
export function OptimisticFeedback({
  isPending = false,
  error = null,
  isOffline = false,
  pendingCount = 0,
}: OptimisticFeedbackProps) {
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const translateY = useSharedValue(-10);

  const shouldShow = isPending || !!error || isOffline || pendingCount > 0;

  useEffect(() => {
    if (shouldShow) {
      // Smooth appear animation
      opacity.value = withTiming(1, { duration: 200 });
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      translateY.value = withTiming(0, { duration: 200 });
    } else {
      // Smooth disappear animation
      opacity.value = withTiming(0, { duration: 150 });
      scale.value = withTiming(0.8, { duration: 150 });
      translateY.value = withTiming(-10, { duration: 150 });
    }
  }, [shouldShow, opacity, scale, translateY]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ scale: scale.value }, { translateY: translateY.value }],
    };
  });

  const getStatusInfo = () => {
    if (error) {
      return {
        icon: <AlertCircle size={14} color={Colors.light.error} />,
        text: 'Error de sincronización',
        backgroundColor: Colors.light.errorTransparent10,
        textColor: Colors.light.error,
      };
    }

    if (isOffline && pendingCount > 0) {
      return {
        icon: <WifiOff size={14} color={Colors.light.warning} />,
        text: `${pendingCount} pendiente${pendingCount > 1 ? 's' : ''}`,
        backgroundColor: Colors.light.warningTransparent10,
        textColor: Colors.light.warning,
      };
    }

    if (isOffline) {
      return {
        icon: <WifiOff size={14} color={Colors.light.textSecondary} />,
        text: 'Sin conexión',
        backgroundColor: Colors.light.backgroundSecondary,
        textColor: Colors.light.textSecondary,
      };
    }

    if (isPending) {
      return {
        icon: <CheckCircle size={14} color={Colors.light.primary} />,
        text: 'Sincronizando...',
        backgroundColor: Colors.light.primaryTransparent10,
        textColor: Colors.light.primary,
      };
    }

    return null;
  };

  const statusInfo = getStatusInfo();

  if (!statusInfo || !shouldShow) {
    return null;
  }

  return (
    <Animated.View
      style={[styles.container, { backgroundColor: statusInfo.backgroundColor }, animatedStyle]}
    >
      {statusInfo.icon}
      <Text style={[styles.text, { color: statusInfo.textColor }]}>{statusInfo.text}</Text>
    </Animated.View>
  );
}

/**
 * Spinning Loading Indicator for Optimistic Operations
 */
export function OptimisticSpinner({ size = 16, color = Colors.light.primary }) {
  const rotation = useSharedValue(0);

  useEffect(() => {
    rotation.value = withTiming(360, { duration: 1000 }, () => {
      rotation.value = 0;
    });
  }, [rotation]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  return (
    <Animated.View style={[animatedStyle, styles.spinner]}>
      <View
        style={[
          styles.spinnerDot,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: color,
          },
        ]}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    gap: spacing.xs,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    lineHeight: 16,
  },
  spinner: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinnerDot: {
    opacity: 0.8,
  },
});
