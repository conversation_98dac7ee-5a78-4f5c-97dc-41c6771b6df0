import React from 'react';
import { StyleSheet, ScrollView, View, Text } from 'react-native';
import { BaseHeader } from '@/components/base';
import InventoryReports from '@/components/reports/InventoryReports';
import Colors from '@/constants/colors';
import { typography, spacing } from '@/constants/theme';

export default function InventoryReportsScreen() {
  return (
    <View style={styles.container}>
      <BaseHeader title="Reportes de Inventario" subtitle="Análisis y estadísticas de consumo" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.description}>
          Visualiza el consumo de productos, tendencias y estadísticas para optimizar tu inventario.
        </Text>
        <InventoryReports />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl * 2,
  },
  description: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: 20,
  },
});
