import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Alert } from 'react-native';
import PatchTest from '@/components/service/safety/PatchTest';

jest.spyOn(Alert, 'alert').mockImplementation(jest.fn());

describe('PatchTest', () => {
  it('calls onChange when selecting result', () => {
    const onChange = jest.fn();
    const { getByText } = render(<PatchTest client={null} value="pendiente" onChange={onChange} />);

    fireEvent.press(getByText('Test Negativo'));
    expect(onChange).toHaveBeenCalledWith('negativo');

    fireEvent.press(getByText('Test Positivo'));
    expect(onChange).toHaveBeenCalledWith('positivo');
    expect(Alert.alert).toHaveBeenCalled();
  });
});
