import React, { use<PERSON><PERSON>back, useMemo, useRef, useState, useEffect } from 'react';

// Performance optimization: Advanced memoization utilities
export class MemoCache {
  private static instance: MemoCache;
  private cache = new Map<string, { value: unknown; timestamp: number; ttl: number }>();
  private timers = new Map<string, NodeJS.Timeout>();

  static getInstance(): MemoCache {
    if (!MemoCache.instance) {
      MemoCache.instance = new MemoCache();
    }
    return MemoCache.instance;
  }

  set(key: string, value: unknown, ttl = 300000): void {
    // Clear existing timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key)!);
    }

    // Set value with TTL
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.cache.delete(key);
      this.timers.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  get(key: string): unknown {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key)!);
        this.timers.delete(key);
      }
      return undefined;
    }

    return entry.value;
  }

  clear(): void {
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// NUCLEAR FIX: Safer TTL memoization that prevents infinite loops
export function useMemoWithTTL<T>(
  factory: () => T,
  deps: React.DependencyList,
  ttl = 300000 // 5 minutes default
): T {
  // NUCLEAR FIX: Create stable cache key that doesn't recreate on object changes
  const cacheKey = useMemo(() => {
    try {
      // Filter out objects and functions from dependencies to prevent infinite loops
      const safeDeps = deps.filter(dep => {
        const type = typeof dep;
        return (
          type === 'string' ||
          type === 'number' ||
          type === 'boolean' ||
          dep === null ||
          dep === undefined
        );
      });
      return `memo_${JSON.stringify(safeDeps)}_${Date.now() % 1000000}`; // Add timestamp component
    } catch {
      // Fallback for non-serializable dependencies
      return `memo_fallback_${Date.now()}_${Math.random()}`;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps); // Keep original deps for comparison - intentional dynamic dependency

  const cache = MemoCache.getInstance();

  return useMemo(() => {
    const cached = cache.get(cacheKey);
    if (cached !== undefined) {
      return cached as T;
    }

    const value = factory();
    cache.set(cacheKey, value, ttl);
    return value;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cacheKey, ttl, cache]);
}

// Hook for stable callback references with deep comparison
export function useStableCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  deps: React.DependencyList
): T {
  const depsRef = useRef<React.DependencyList>();
  const callbackRef = useRef<T>();

  // Deep compare dependencies
  const depsChanged = useMemo(() => {
    if (!depsRef.current) return true;
    if (depsRef.current.length !== deps.length) return true;
    return deps.some((dep, index) => !Object.is(dep, depsRef.current![index]));
  }, [deps]);

  if (depsChanged) {
    depsRef.current = deps;
    callbackRef.current = callback;
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(callbackRef.current!, []);
}

// Higher-order component for automatic memoization
export function withAutoMemo<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  compareFn?: (prevProps: P, nextProps: P) => boolean
) {
  const displayName = Component.displayName || Component.name || 'Component';

  const MemoizedComponent = React.memo(Component, compareFn);
  MemoizedComponent.displayName = `AutoMemo(${displayName})`;

  return MemoizedComponent;
}

// Optimized comparison functions for common cases
export const shallowEqual = <T extends Record<string, unknown>>(objA: T, objB: T): boolean => {
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);

  if (keysA.length !== keysB.length) return false;

  return keysA.every(key => Object.is(objA[key], objB[key]));
};

export const deepEqual = <T>(objA: T, objB: T): boolean => {
  if (Object.is(objA, objB)) return true;

  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {
    return false;
  }

  const keysA = Object.keys(objA as Record<string, unknown>);
  const keysB = Object.keys(objB as Record<string, unknown>);

  if (keysA.length !== keysB.length) return false;

  return keysA.every(key =>
    deepEqual((objA as Record<string, unknown>)[key], (objB as Record<string, unknown>)[key])
  );
};

// Performance-optimized selectors for Zustand stores
export function createMemoizedSelector<T, R>(selector: (state: T) => R, equalityFn = Object.is) {
  let lastResult: R;
  let lastState: T;

  return (state: T): R => {
    if (lastState && equalityFn(state, lastState)) {
      return lastResult;
    }

    lastState = state;
    lastResult = selector(state);
    return lastResult;
  };
}

// Debounced memo hook for expensive operations
export function useDebouncedMemo<T>(factory: () => T, deps: React.DependencyList, delay = 300): T {
  const [debouncedDeps, setDebouncedDeps] = useState(deps);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedDeps(deps);
    }, delay);

    return () => clearTimeout(timer);
  }, [deps, delay]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(factory, [debouncedDeps]);
}
