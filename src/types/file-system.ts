/**
 * File System Node
 * Represents any file or folder in the project structure
 */

import { FileCategory } from './file-category';

export interface FileSystemNode {
  /** Relative path from project root */
  path: string;
  /** Type of file system entry */
  type: 'file' | 'directory';
  /** What type of content this represents */
  category: FileCategory;
  /** When last changed */
  lastModified: Date;
  /** File size in bytes (files only) */
  size?: number;
  /** Parent directory (null for root) */
  parent?: FileSystemNode;
  /** Child files/directories (for directories only) */
  children?: FileSystemNode[];
}

export interface FileSystemAnalysis {
  /** Total number of files scanned */
  totalFiles: number;
  /** Total number of directories */
  totalDirectories: number;
  /** Files categorized by type */
  filesByCategory: Record<FileCategory, FileSystemNode[]>;
  /** Issues found during analysis */
  issues: StructureIssue[];
  /** Suggestions for improvement */
  suggestions: OrganizationSuggestion[];
}

export interface StructureIssue {
  type: 'MISPLACED_FILE' | 'NAMING_VIOLATION' | 'CIRCULAR_DEPENDENCY' | 'DEEP_NESTING';
  path: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  suggestedFix: string;
}

export interface OrganizationSuggestion {
  type: 'MOVE_FILE' | 'RENAME_FILE' | 'CREATE_DIRECTORY' | 'MERGE_DIRECTORIES';
  currentPath: string;
  suggestedPath: string;
  reason: string;
  benefits: string[];
}
