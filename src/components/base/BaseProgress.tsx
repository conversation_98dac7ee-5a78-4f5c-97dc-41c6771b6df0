import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, ViewStyle } from 'react-native';
import Colors from '@/constants/colors';
import { radius, spacing, typography } from '@/constants/theme';

interface BaseProgressProps {
  progress: number; // 0 to 100
  showPercentage?: boolean;
  height?: number;
  animated?: boolean;
  color?: string;
  backgroundColor?: string;
  style?: ViewStyle;
}

export const BaseProgress: React.FC<BaseProgressProps> = ({
  progress,
  showPercentage = false,
  height = 8,
  animated = true,
  color = Colors.light.primary,
  backgroundColor = Colors.light.progressBackground,
  style,
}) => {
  const animatedWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      Animated.timing(animatedWidth, {
        toValue: progress,
        duration: 500,
        useNativeDriver: false,
      }).start();
    } else {
      animatedWidth.setValue(progress);
    }
  }, [progress, animated, animatedWidth]);

  const widthInterpolated = animatedWidth.interpolate({
    inputRange: [0, 100],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={[styles.container, style]}>
      <View
        style={[
          styles.track,
          {
            height,
            backgroundColor,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.fill,
            {
              width: widthInterpolated,
              backgroundColor: color,
            },
          ]}
        />
      </View>
      {showPercentage && <Text style={styles.percentage}>{Math.round(progress)}%</Text>}
    </View>
  );
};

interface SteppedProgressProps {
  steps: number;
  currentStep: number;
  labels?: string[];
  style?: ViewStyle;
}

export const SteppedProgress: React.FC<SteppedProgressProps> = ({
  steps,
  currentStep,
  labels,
  style,
}) => {
  return (
    <View style={[styles.steppedContainer, style]}>
      <View style={styles.stepsRow}>
        {Array.from({ length: steps }).map((_, index) => {
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <React.Fragment key={index}>
              {/* Step Circle */}
              <View
                style={[
                  styles.stepCircle,
                  isCompleted && styles.stepCompleted,
                  isActive && styles.stepActive,
                ]}
              >
                <Text
                  style={[styles.stepNumber, (isCompleted || isActive) && styles.stepNumberActive]}
                >
                  {index + 1}
                </Text>
              </View>

              {/* Connection Line */}
              {index < steps - 1 && (
                <View style={[styles.stepLine, isCompleted && styles.stepLineCompleted]} />
              )}
            </React.Fragment>
          );
        })}
      </View>

      {labels && (
        <View style={styles.labelsRow}>
          {labels.map((label, index) => (
            <Text
              key={index}
              style={[styles.stepLabel, index === currentStep && styles.stepLabelActive]}
            >
              {label}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  track: {
    width: '100%',
    borderRadius: radius.full,
    overflow: 'hidden',
    backgroundColor: Colors.light.progressBackground,
  },
  fill: {
    height: '100%',
    borderRadius: radius.full,
  },
  percentage: {
    marginTop: spacing.xs,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
    textAlign: 'right',
  },

  // Stepped Progress
  steppedContainer: {
    width: '100%',
  },
  stepsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.progressBackground,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  stepCompleted: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  stepActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  stepNumber: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
  },
  stepNumberActive: {
    color: Colors.light.textLight,
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: Colors.light.border,
    marginHorizontal: spacing.xs,
  },
  stepLineCompleted: {
    backgroundColor: Colors.light.success,
  },
  labelsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.sm,
  },
  stepLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    maxWidth: 60,
    textAlign: 'center',
  },
  stepLabelActive: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
});
