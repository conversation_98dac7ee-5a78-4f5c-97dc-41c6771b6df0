import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
  TextInput,
} from 'react-native';
import { Thermometer, Sun, Palette, X, Check } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { BrandSelectionModal } from '@/components/BrandSelectionModal';

interface QuickAdjustPanelProps {
  onAdjustFormula: (adjustment: AdjustmentType) => void;
  onChangeBrand: (brand: string, line: string) => void;
  currentBrand: string;
  currentLine: string;
  isGenerating?: boolean;
}

export type AdjustmentType =
  | { type: 'temperature'; value: 'warmer' | 'cooler' }
  | { type: 'oxidant'; value: 'increase' | 'decrease' }
  | { type: 'brightness'; value: 'lighter' | 'darker' }
  | { type: 'custom'; value: string }
  | { type: 'brand'; brand: string; line: string };

export const QuickAdjustPanel: React.FC<QuickAdjustPanelProps> = ({
  onAdjustFormula,
  onChangeBrand,
  currentBrand,
  currentLine,
  isGenerating = false,
}) => {
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [showCustomModal, setShowCustomModal] = useState(false);
  const [customRequest, setCustomRequest] = useState('');

  const handleBrandSelection = (brand: string, line: string) => {
    setShowBrandModal(false);
    onChangeBrand(brand, line);
    onAdjustFormula({ type: 'brand', brand, line });
  };

  const handleCustomAdjustment = () => {
    if (customRequest.trim()) {
      onAdjustFormula({ type: 'custom', value: customRequest });
      setShowCustomModal(false);
      setCustomRequest('');
    }
  };

  const adjustmentButtons = [
    {
      icon: <Thermometer size={18} />,
      label: 'Más Frío',
      action: () => onAdjustFormula({ type: 'temperature', value: 'cooler' }),
      color: Colors.light.info,
    },
    {
      icon: <Thermometer size={18} />,
      label: 'Más Cálido',
      action: () => onAdjustFormula({ type: 'temperature', value: 'warmer' }),
      color: Colors.light.warning,
    },
    {
      icon: <Sun size={18} />,
      label: 'Más Claro',
      action: () => onAdjustFormula({ type: 'brightness', value: 'lighter' }),
      color: Colors.light.primary,
    },
    {
      icon: <Sun size={18} />,
      label: 'Más Oscuro',
      action: () => onAdjustFormula({ type: 'brightness', value: 'darker' }),
      color: Colors.light.text,
    },
  ];

  return (
    <>
      <View style={styles.container}>
        <Text style={styles.title}>Ajustes Rápidos</Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.buttonsContainer}
        >
          {adjustmentButtons.map((button, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.adjustButton,
                { borderColor: button.color + '40' },
                isGenerating && styles.adjustButtonDisabled,
              ]}
              onPress={button.action}
              disabled={isGenerating}
            >
              <View style={[styles.iconContainer, { backgroundColor: button.color + '20' }]}>
                {React.cloneElement(button.icon, { color: button.color })}
              </View>
              <Text style={[styles.adjustButtonText, { color: button.color }]}>{button.label}</Text>
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            style={[styles.adjustButton, styles.brandButton]}
            onPress={() => setShowBrandModal(true)}
            disabled={isGenerating}
          >
            <View style={[styles.iconContainer, styles.brandIconContainer]}>
              <Palette size={18} color={Colors.light.accent} />
            </View>
            <Text style={[styles.adjustButtonText, styles.brandButtonText]}>Cambiar Marca</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.adjustButton, styles.customButton]}
            onPress={() => setShowCustomModal(true)}
            disabled={isGenerating}
          >
            <Text style={styles.customButtonText}>Personalizar...</Text>
          </TouchableOpacity>
        </ScrollView>

        <Text style={styles.hint}>Los ajustes regenerarán la fórmula con tus preferencias</Text>
      </View>

      {/* Brand Selection Modal */}
      <BrandSelectionModal
        visible={showBrandModal}
        onClose={() => setShowBrandModal(false)}
        onSelectBrand={handleBrandSelection}
        currentBrand={currentBrand}
        currentLine={currentLine}
      />

      {/* Custom Adjustment Modal */}
      <Modal
        visible={showCustomModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCustomModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Ajuste Personalizado</Text>
              <TouchableOpacity onPress={() => setShowCustomModal(false)}>
                <X size={24} color={Colors.light.gray} />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalDescription}>
              Describe cómo te gustaría ajustar la fórmula:
            </Text>

            <TextInput
              style={styles.customInput}
              placeholder="Ej: Añadir más violeta para neutralizar amarillos"
              value={customRequest}
              onChangeText={setCustomRequest}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />

            <TouchableOpacity
              style={[styles.applyButton, !customRequest.trim() && styles.applyButtonDisabled]}
              onPress={handleCustomAdjustment}
              disabled={!customRequest.trim()}
            >
              <Check size={20} color="white" />
              <Text style={styles.applyButtonText}>Aplicar Ajuste</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    padding: 16,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  buttonsContainer: {
    paddingVertical: 8,
    gap: 12,
  },
  adjustButton: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    minWidth: 100,
  },
  adjustButtonDisabled: {
    opacity: 0.5,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  adjustButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  brandButton: {
    borderColor: Colors.light.accent + '40',
  },
  brandIconContainer: {
    backgroundColor: Colors.light.accent + '20',
  },
  brandButtonText: {
    color: Colors.light.accent,
  },
  customButton: {
    borderColor: Colors.light.primary + '40',
    justifyContent: 'center',
  },
  customButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  hint: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: 'center',
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.light.modalOverlay,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    minHeight: 300,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  modalDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 16,
  },
  customInput: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 14,
    minHeight: 100,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginBottom: 20,
  },
  applyButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  applyButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.5,
  },
  applyButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
});
