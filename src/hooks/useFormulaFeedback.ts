import { useState, useCallback, useEffect } from 'react';
import {
  useFormulaFeedbackStore,
  type FormulaFeedback,
  type SyncConflict,
} from '@/stores/formula-feedback-store';
import { useSyncQueueStore } from '@/stores/sync-queue-store';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import NetInfo from '@react-native-community/netinfo';

interface FeedbackRequest {
  id: string;
  serviceId: string;
  scheduledFor: Date;
  isActive: boolean;
}

export interface UseFormulaFeedbackResult {
  // State
  isSubmitting: boolean;
  error: string | null;
  hasPendingRequests: boolean;
  isOnline: boolean;
  syncStatus: SyncStatus;

  // Actions
  submitQuickFeedback: (params: QuickFeedbackParams) => Promise<boolean>;
  submitDetailedFeedback: (params: DetailedFeedbackParams) => Promise<boolean>;
  scheduleServiceFeedback: (params: ServiceFeedbackParams) => void;
  updateFeedback: (id: string, updates: Partial<FormulaFeedback>) => Promise<boolean>;
  deleteFeedback: (id: string) => Promise<boolean>;

  // Data
  getFormulaStats: (formulaId: string) => FormulaStats;
  getServiceFeedback: (serviceId: string) => FormulaFeedback | undefined;
  getAllFeedbacks: () => FormulaFeedback[];

  // Pending requests
  getActiveFeedbackRequests: () => FeedbackRequest[];
  dismissRequest: (requestId: string) => void;
  snoozeRequest: (requestId: string, minutes: number) => void;

  // Sync operations
  forceSyncToServer: () => Promise<boolean>;
  resolveConflict: (
    conflict: SyncConflict,
    strategy?: 'local_wins' | 'remote_wins' | 'merge'
  ) => Promise<void>;
  getConflicts: () => SyncConflict[];
  batchUpdateFeedbacks: (
    updates: Array<{ id: string; data: Partial<FormulaFeedback> }>
  ) => Promise<boolean>;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncTimestamp: number | null;
  pendingSync: number;
  hasConflicts: boolean;
  hasIssues: boolean;
}

export interface QuickFeedbackParams {
  formulaId: string;
  serviceId: string;
  workedAsExpected: boolean;
  rating: number;
  wouldUseAgain: boolean;
}

export interface DetailedFeedbackParams extends QuickFeedbackParams {
  actualResult?: string;
  adjustmentsMade?: string;
  hairType?: string;
  environmentalFactors?: string;
}

export interface ServiceFeedbackParams {
  formulaId: string;
  serviceId: string;
  clientName: string;
  formulaSummary: string;
  delayMinutes?: number;
}

export interface FormulaStats {
  totalUses: number;
  successRate: number;
  avgRating: number;
  wouldUseAgainRate: number;
  lastUsed?: string;
  recentFeedbacks: FormulaFeedback[];
}

/**
 * Custom hook for managing formula feedback operations
 * Provides a clean interface for feedback submission, scheduling, and data retrieval
 */
export const useFormulaFeedback = (): UseFormulaFeedbackResult => {
  const {
    feedbacks,
    conflicts: _conflicts,
    isOnline,
    lastSyncTimestamp,
    addFeedback,
    updateFeedback: updateFeedbackStore,
    deleteFeedback: deleteFeedbackStore,
    scheduleFeedbackRequest,
    dismissFeedbackRequest,
    snoozeRequest: snoozeRequestStore,
    getActiveFeedbackRequests: getActiveRequests,
    getFeedbackByService,
    getFormulaStats: getFormulaStatsStore,
    syncFeedbacks,
    syncFromServer,
    handleSyncConflict,
    getConflicts,
    batchUpdateFeedbacks: batchUpdateFeedbacksStore,
    pendingSyncCount,
  } = useFormulaFeedbackStore();

  const { initializeNetworkListener, getQueueStatus } = useSyncQueueStore();

  const { user } = useAuthStore();
  const { currentSalon } = useSalonConfigStore();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPendingRequests, setHasPendingRequests] = useState(false);

  // Initialize network monitoring and sync
  useEffect(() => {
    const unsubscribe = initializeNetworkListener();

    // Initial server sync if online
    NetInfo.fetch().then(state => {
      if (state.isConnected) {
        syncFromServer().catch(error => {
          logger.warn('Initial server sync failed', 'useFormulaFeedback', {
            error: error.message,
          });
        });
      }
    });

    return unsubscribe;
  }, [initializeNetworkListener, syncFromServer]);

  // Auto-sync on network reconnection
  useEffect(() => {
    if (isOnline && pendingSyncCount() > 0) {
      syncFeedbacks().catch(error => {
        logger.warn('Auto-sync failed', 'useFormulaFeedback', {
          error: error.message,
        });
      });
    }
  }, [isOnline, syncFeedbacks, pendingSyncCount]);

  // Check for pending requests periodically
  useEffect(() => {
    const checkPendingRequests = () => {
      const activeRequests = getActiveRequests();
      setHasPendingRequests(activeRequests.length > 0);
    };

    checkPendingRequests();
    const interval = setInterval(checkPendingRequests, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [getActiveRequests]);

  // Compute sync status
  const syncStatus: SyncStatus = {
    isOnline,
    lastSyncTimestamp,
    pendingSync: pendingSyncCount(),
    hasConflicts: getConflicts().length > 0,
    hasIssues: getQueueStatus().failed > 0 || getConflicts().length > 0,
  };

  const submitQuickFeedback = useCallback(
    async (params: QuickFeedbackParams): Promise<boolean> => {
      if (!user || !currentSalon) {
        setError('Usuario o salón no disponible');
        return false;
      }

      setIsSubmitting(true);
      setError(null);

      try {
        await addFeedback({
          formula_id: params.formulaId,
          salon_id: currentSalon.id,
          user_id: user.id,
          service_id: params.serviceId,
          worked_as_expected: params.workedAsExpected,
          rating: params.rating,
          would_use_again: params.wouldUseAgain,
        });

        logger.info('Quick feedback submitted successfully', 'useFormulaFeedback', {
          formulaId: params.formulaId,
          rating: params.rating,
        });

        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        logger.error('Failed to submit quick feedback', 'useFormulaFeedback', { error: err });
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [addFeedback, user, currentSalon]
  );

  const submitDetailedFeedback = useCallback(
    async (params: DetailedFeedbackParams): Promise<boolean> => {
      if (!user || !currentSalon) {
        setError('Usuario o salón no disponible');
        return false;
      }

      setIsSubmitting(true);
      setError(null);

      try {
        await addFeedback({
          formula_id: params.formulaId,
          salon_id: currentSalon.id,
          user_id: user.id,
          service_id: params.serviceId,
          worked_as_expected: params.workedAsExpected,
          rating: params.rating,
          would_use_again: params.wouldUseAgain,
          actual_result: params.actualResult?.trim() || undefined,
          adjustments_made: params.adjustmentsMade?.trim() || undefined,
          hair_type: params.hairType?.trim() || undefined,
          environmental_factors: params.environmentalFactors?.trim() || undefined,
        });

        logger.info('Detailed feedback submitted successfully', 'useFormulaFeedback', {
          formulaId: params.formulaId,
          rating: params.rating,
          hasAdjustments: !!params.adjustmentsMade?.trim(),
        });

        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        logger.error('Failed to submit detailed feedback', 'useFormulaFeedback', { error: err });
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [addFeedback, user, currentSalon]
  );

  const scheduleServiceFeedback = useCallback(
    (params: ServiceFeedbackParams) => {
      const delayMs = (params.delayMinutes || 30) * 60 * 1000;
      const scheduledTime = new Date(Date.now() + delayMs).toISOString();

      scheduleFeedbackRequest({
        formula_id: params.formulaId,
        service_id: params.serviceId,
        client_name: params.clientName,
        formula_summary: params.formulaSummary,
        scheduled_time: scheduledTime,
      });

      logger.info('Feedback request scheduled', 'useFormulaFeedback', {
        serviceId: params.serviceId,
        delayMinutes: params.delayMinutes || 30,
      });
    },
    [scheduleFeedbackRequest]
  );

  const getFormulaStats = useCallback(
    (formulaId: string): FormulaStats => {
      const stats = getFormulaStatsStore(formulaId);
      const formulaFeedbacks = feedbacks.filter(f => f.formula_id === formulaId);

      const recentFeedbacks = formulaFeedbacks
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5);

      const lastUsed = formulaFeedbacks.length > 0 ? formulaFeedbacks[0].created_at : undefined;

      return {
        totalUses: stats.total_uses,
        successRate: stats.success_rate,
        avgRating: stats.avg_rating,
        wouldUseAgainRate: stats.would_use_again_rate,
        lastUsed,
        recentFeedbacks,
      };
    },
    [getFormulaStatsStore, feedbacks]
  );

  const getServiceFeedback = useCallback(
    (serviceId: string) => {
      return getFeedbackByService(serviceId);
    },
    [getFeedbackByService]
  );

  const getAllFeedbacks = useCallback(() => {
    return feedbacks;
  }, [feedbacks]);

  const getActiveFeedbackRequests = useCallback(() => {
    return getActiveRequests();
  }, [getActiveRequests]);

  const dismissRequest = useCallback(
    (requestId: string) => {
      dismissFeedbackRequest(requestId);
      logger.info('Feedback request dismissed', 'useFormulaFeedback', { requestId });
    },
    [dismissFeedbackRequest]
  );

  const snoozeRequest = useCallback(
    (requestId: string, minutes: number) => {
      snoozeRequestStore(requestId, minutes);
      logger.info('Feedback request snoozed', 'useFormulaFeedback', { requestId, minutes });
    },
    [snoozeRequestStore]
  );

  // Enhanced feedback operations with offline-first support
  const updateFeedback = useCallback(
    async (id: string, updates: Partial<FormulaFeedback>): Promise<boolean> => {
      setError(null);

      try {
        await updateFeedbackStore(id, updates, true); // Optimistic by default
        logger.info('Feedback updated successfully', 'useFormulaFeedback', { feedbackId: id });
        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        logger.error('Failed to update feedback', 'useFormulaFeedback', {
          feedbackId: id,
          error: err,
        });
        return false;
      }
    },
    [updateFeedbackStore]
  );

  const deleteFeedback = useCallback(
    async (id: string): Promise<boolean> => {
      setError(null);

      try {
        await deleteFeedbackStore(id, true); // Optimistic by default
        logger.info('Feedback deleted successfully', 'useFormulaFeedback', { feedbackId: id });
        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        logger.error('Failed to delete feedback', 'useFormulaFeedback', {
          feedbackId: id,
          error: err,
        });
        return false;
      }
    },
    [deleteFeedbackStore]
  );

  const forceSyncToServer = useCallback(async (): Promise<boolean> => {
    setError(null);

    try {
      await syncFromServer();
      await syncFeedbacks();
      logger.info('Force sync completed successfully', 'useFormulaFeedback');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error de sincronización';
      setError(errorMessage);
      logger.error('Force sync failed', 'useFormulaFeedback', { error: err });
      return false;
    }
  }, [syncFromServer, syncFeedbacks]);

  const resolveConflict = useCallback(
    async (
      conflict: SyncConflict,
      strategy: 'local_wins' | 'remote_wins' | 'merge' = 'merge'
    ): Promise<void> => {
      setError(null);

      try {
        await handleSyncConflict(conflict, strategy);
        logger.info('Conflict resolved successfully', 'useFormulaFeedback', {
          conflictId: conflict.id,
          strategy,
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error al resolver conflicto';
        setError(errorMessage);
        logger.error('Failed to resolve conflict', 'useFormulaFeedback', {
          conflictId: conflict.id,
          error: err,
        });
        throw err;
      }
    },
    [handleSyncConflict]
  );

  const batchUpdateFeedbacks = useCallback(
    async (updates: Array<{ id: string; data: Partial<FormulaFeedback> }>): Promise<boolean> => {
      if (updates.length === 0) return true;

      setError(null);

      try {
        await batchUpdateFeedbacksStore(updates);
        logger.info('Batch feedback updates completed', 'useFormulaFeedback', {
          count: updates.length,
        });
        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error en actualización masiva';
        setError(errorMessage);
        logger.error('Batch feedback update failed', 'useFormulaFeedback', {
          count: updates.length,
          error: err,
        });
        return false;
      }
    },
    [batchUpdateFeedbacksStore]
  );

  return {
    // State
    isSubmitting,
    error,
    hasPendingRequests,
    isOnline,
    syncStatus,

    // Actions
    submitQuickFeedback,
    submitDetailedFeedback,
    scheduleServiceFeedback,
    updateFeedback,
    deleteFeedback,

    // Data
    getFormulaStats,
    getServiceFeedback,
    getAllFeedbacks,

    // Pending requests
    getActiveFeedbackRequests,
    dismissRequest,
    snoozeRequest,

    // Sync operations
    forceSyncToServer,
    resolveConflict,
    getConflicts,
    batchUpdateFeedbacks,
  };
};

/**
 * Helper function to determine if a formula has enough feedback to be considered "proven"
 */
export const isFormulaProven = (stats: FormulaStats): boolean => {
  return stats.totalUses >= 3 && stats.successRate >= 80 && stats.avgRating >= 4.0;
};

/**
 * Helper function to get feedback quality level
 */
export const getFeedbackQuality = (stats: FormulaStats): 'excellent' | 'good' | 'fair' | 'poor' => {
  if (stats.avgRating >= 4.5 && stats.successRate >= 90) return 'excellent';
  if (stats.avgRating >= 4.0 && stats.successRate >= 80) return 'good';
  if (stats.avgRating >= 3.0 && stats.successRate >= 60) return 'fair';
  return 'poor';
};

/**
 * Helper function to format feedback stats for display
 */
export const formatFeedbackStats = (stats: FormulaStats) => {
  return {
    rating: stats.avgRating > 0 ? stats.avgRating.toFixed(1) : '-',
    successRate: stats.successRate > 0 ? `${Math.round(stats.successRate)}%` : '-',
    reuseRate: stats.wouldUseAgainRate > 0 ? `${Math.round(stats.wouldUseAgainRate)}%` : '-',
    uses: stats.totalUses.toString(),
  };
};

/**
 * Lightweight hook for monitoring feedback sync status
 * Useful for status indicators and sync health monitoring
 */
export const useFeedbackSyncStatus = () => {
  const { isOnline, lastSyncTimestamp, pendingSyncCount, getConflicts } = useFormulaFeedbackStore();
  const { getQueueStatus } = useSyncQueueStore();

  const queueStatus = getQueueStatus();
  const conflicts = getConflicts();

  return {
    isOnline,
    lastSyncTimestamp,
    pendingCount: pendingSyncCount(),
    queueStatus,
    conflictCount: conflicts.length,
    isHealthy: pendingSyncCount() === 0 && queueStatus.failed === 0 && conflicts.length === 0,
    hasIssues: queueStatus.failed > 0 || conflicts.length > 0,
    syncStatus: {
      isOnline,
      lastSyncTimestamp,
      pendingSync: pendingSyncCount(),
      hasConflicts: conflicts.length > 0,
      hasIssues: queueStatus.failed > 0 || conflicts.length > 0,
    } as SyncStatus,
  };
};

/**
 * Hook for read-only feedback operations
 * Optimized for components that only need to display feedback data
 */
export const useFormulaFeedbackReader = () => {
  const {
    feedbacks,
    getFeedbackByFormula,
    getFeedbackByService,
    getFeedbackById,
    getFormulaStats: getFormulaStatsStore,
  } = useFormulaFeedbackStore();

  const getFormulaStats = useCallback(
    (formulaId: string): FormulaStats => {
      const stats = getFormulaStatsStore(formulaId);
      const formulaFeedbacks = feedbacks.filter(f => f.formula_id === formulaId);

      const recentFeedbacks = formulaFeedbacks
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5);

      const lastUsed = formulaFeedbacks.length > 0 ? formulaFeedbacks[0].created_at : undefined;

      return {
        totalUses: stats.total_uses,
        successRate: stats.success_rate,
        avgRating: stats.avg_rating,
        wouldUseAgainRate: stats.would_use_again_rate,
        lastUsed,
        recentFeedbacks,
      };
    },
    [getFormulaStatsStore, feedbacks]
  );

  return {
    feedbacks,
    getFeedbackByFormula,
    getFeedbackByService,
    getFeedbackById,
    getFormulaStats,
  };
};

export default useFormulaFeedback;
